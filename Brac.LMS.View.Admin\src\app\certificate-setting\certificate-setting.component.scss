// Certificate template background image
.certificate-template-image {
  max-width: 500px; // Increased from 400px
  max-height: 400px; // Increased from 300px
  object-fit: contain;
}

// Signature images - fixed size as recommended
.signature-image {
  width: 255px;
  height: 47px;
  object-fit: contain;
}

// Image container with positioned remove button
.image-container {
  position: relative;
  display: inline-block;

  .remove-btn {
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(50%, -50%);
  }
}

// Version badge styling
.version-badge {
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
}

// Course selection with version
.course-selection-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .course-select {
    flex-grow: 1;
  }
}
