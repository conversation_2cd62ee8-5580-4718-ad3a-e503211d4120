{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { DepartmentRoutingModule } from './department-routing.module';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport let DepartmentModule = /*#__PURE__*/(() => {\n  class DepartmentModule {}\n\n  DepartmentModule.ɵfac = function DepartmentModule_Factory(t) {\n    return new (t || DepartmentModule)();\n  };\n\n  DepartmentModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: DepartmentModule\n  });\n  DepartmentModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, DepartmentRoutingModule, SharedModule]]\n  });\n  return DepartmentModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}