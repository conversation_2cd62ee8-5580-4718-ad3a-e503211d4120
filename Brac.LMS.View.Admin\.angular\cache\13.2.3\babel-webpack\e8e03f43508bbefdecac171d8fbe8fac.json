{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { isValidDate } from '../util/isDate';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createErrorClass } from '../util/createErrorClass';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\nexport const TimeoutError = createErrorClass(_super => function TimeoutErrorImpl(info = null) {\n  _super(this);\n\n  this.message = 'Timeout has occurred';\n  this.name = 'TimeoutError';\n  this.info = info;\n});\nexport function timeout(config, schedulerArg) {\n  const {\n    first,\n    each,\n    with: _with = timeoutErrorFactory,\n    scheduler = schedulerArg !== null && schedulerArg !== void 0 ? schedulerArg : asyncScheduler,\n    meta = null\n  } = isValidDate(config) ? {\n    first: config\n  } : typeof config === 'number' ? {\n    each: config\n  } : config;\n\n  if (first == null && each == null) {\n    throw new TypeError('No timeout provided.');\n  }\n\n  return operate((source, subscriber) => {\n    let originalSourceSubscription;\n    let timerSubscription;\n    let lastValue = null;\n    let seen = 0;\n\n    const startTimer = delay => {\n      timerSubscription = executeSchedule(subscriber, scheduler, () => {\n        try {\n          originalSourceSubscription.unsubscribe();\n          innerFrom(_with({\n            meta,\n            lastValue,\n            seen\n          })).subscribe(subscriber);\n        } catch (err) {\n          subscriber.error(err);\n        }\n      }, delay);\n    };\n\n    originalSourceSubscription = source.subscribe(createOperatorSubscriber(subscriber, value => {\n      timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n      seen++;\n      subscriber.next(lastValue = value);\n      each > 0 && startTimer(each);\n    }, undefined, undefined, () => {\n      if (!(timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.closed)) {\n        timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n      }\n\n      lastValue = null;\n    }));\n    startTimer(first != null ? typeof first === 'number' ? first : +first - scheduler.now() : each);\n  });\n}\n\nfunction timeoutErrorFactory(info) {\n  throw new TimeoutError(info);\n}", "map": {"version": 3, "sources": ["D:/Office Projects/brac-lms/Brac.LMS.View.Admin/node_modules/rxjs/dist/esm/internal/operators/timeout.js"], "names": ["asyncScheduler", "isValidDate", "operate", "innerFrom", "createErrorClass", "createOperatorSubscriber", "executeSchedule", "TimeoutError", "_super", "TimeoutErrorImpl", "info", "message", "name", "timeout", "config", "schedulerArg", "first", "each", "with", "_with", "timeoutErrorFactory", "scheduler", "meta", "TypeError", "source", "subscriber", "originalSourceSubscription", "timerSubscription", "lastValue", "seen", "startTimer", "delay", "unsubscribe", "subscribe", "err", "error", "value", "next", "undefined", "closed", "now"], "mappings": "AAAA,SAASA,cAAT,QAA+B,oBAA/B;AACA,SAASC,WAAT,QAA4B,gBAA5B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,SAASC,gBAAT,QAAiC,0BAAjC;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,eAAT,QAAgC,yBAAhC;AACA,OAAO,MAAMC,YAAY,GAAGH,gBAAgB,CAAEI,MAAD,IAAY,SAASC,gBAAT,CAA0BC,IAAI,GAAG,IAAjC,EAAuC;AAC5FF,EAAAA,MAAM,CAAC,IAAD,CAAN;;AACA,OAAKG,OAAL,GAAe,sBAAf;AACA,OAAKC,IAAL,GAAY,cAAZ;AACA,OAAKF,IAAL,GAAYA,IAAZ;AACH,CAL2C,CAArC;AAMP,OAAO,SAASG,OAAT,CAAiBC,MAAjB,EAAyBC,YAAzB,EAAuC;AAC1C,QAAM;AAAEC,IAAAA,KAAF;AAASC,IAAAA,IAAT;AAAeC,IAAAA,IAAI,EAAEC,KAAK,GAAGC,mBAA7B;AAAkDC,IAAAA,SAAS,GAAGN,YAAY,KAAK,IAAjB,IAAyBA,YAAY,KAAK,KAAK,CAA/C,GAAmDA,YAAnD,GAAkEf,cAAhI;AAAgJsB,IAAAA,IAAI,GAAG;AAAvJ,MAAkKrB,WAAW,CAACa,MAAD,CAAX,GAAsB;AAAEE,IAAAA,KAAK,EAAEF;AAAT,GAAtB,GAA0C,OAAOA,MAAP,KAAkB,QAAlB,GAA6B;AAAEG,IAAAA,IAAI,EAAEH;AAAR,GAA7B,GAAgDA,MAAlQ;;AACA,MAAIE,KAAK,IAAI,IAAT,IAAiBC,IAAI,IAAI,IAA7B,EAAmC;AAC/B,UAAM,IAAIM,SAAJ,CAAc,sBAAd,CAAN;AACH;;AACD,SAAOrB,OAAO,CAAC,CAACsB,MAAD,EAASC,UAAT,KAAwB;AACnC,QAAIC,0BAAJ;AACA,QAAIC,iBAAJ;AACA,QAAIC,SAAS,GAAG,IAAhB;AACA,QAAIC,IAAI,GAAG,CAAX;;AACA,UAAMC,UAAU,GAAIC,KAAD,IAAW;AAC1BJ,MAAAA,iBAAiB,GAAGrB,eAAe,CAACmB,UAAD,EAAaJ,SAAb,EAAwB,MAAM;AAC7D,YAAI;AACAK,UAAAA,0BAA0B,CAACM,WAA3B;AACA7B,UAAAA,SAAS,CAACgB,KAAK,CAAC;AACZG,YAAAA,IADY;AAEZM,YAAAA,SAFY;AAGZC,YAAAA;AAHY,WAAD,CAAN,CAAT,CAIII,SAJJ,CAIcR,UAJd;AAKH,SAPD,CAQA,OAAOS,GAAP,EAAY;AACRT,UAAAA,UAAU,CAACU,KAAX,CAAiBD,GAAjB;AACH;AACJ,OAZkC,EAYhCH,KAZgC,CAAnC;AAaH,KAdD;;AAeAL,IAAAA,0BAA0B,GAAGF,MAAM,CAACS,SAAP,CAAiB5B,wBAAwB,CAACoB,UAAD,EAAcW,KAAD,IAAW;AAC1FT,MAAAA,iBAAiB,KAAK,IAAtB,IAA8BA,iBAAiB,KAAK,KAAK,CAAzD,GAA6D,KAAK,CAAlE,GAAsEA,iBAAiB,CAACK,WAAlB,EAAtE;AACAH,MAAAA,IAAI;AACJJ,MAAAA,UAAU,CAACY,IAAX,CAAiBT,SAAS,GAAGQ,KAA7B;AACAnB,MAAAA,IAAI,GAAG,CAAP,IAAYa,UAAU,CAACb,IAAD,CAAtB;AACH,KALqE,EAKnEqB,SALmE,EAKxDA,SALwD,EAK7C,MAAM;AAC3B,UAAI,EAAEX,iBAAiB,KAAK,IAAtB,IAA8BA,iBAAiB,KAAK,KAAK,CAAzD,GAA6D,KAAK,CAAlE,GAAsEA,iBAAiB,CAACY,MAA1F,CAAJ,EAAuG;AACnGZ,QAAAA,iBAAiB,KAAK,IAAtB,IAA8BA,iBAAiB,KAAK,KAAK,CAAzD,GAA6D,KAAK,CAAlE,GAAsEA,iBAAiB,CAACK,WAAlB,EAAtE;AACH;;AACDJ,MAAAA,SAAS,GAAG,IAAZ;AACH,KAVqE,CAAzC,CAA7B;AAWAE,IAAAA,UAAU,CAACd,KAAK,IAAI,IAAT,GAAiB,OAAOA,KAAP,KAAiB,QAAjB,GAA4BA,KAA5B,GAAoC,CAACA,KAAD,GAASK,SAAS,CAACmB,GAAV,EAA9D,GAAiFvB,IAAlF,CAAV;AACH,GAhCa,CAAd;AAiCH;;AACD,SAASG,mBAAT,CAA6BV,IAA7B,EAAmC;AAC/B,QAAM,IAAIH,YAAJ,CAAiBG,IAAjB,CAAN;AACH", "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { isValidDate } from '../util/isDate';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createErrorClass } from '../util/createErrorClass';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\nexport const TimeoutError = createErrorClass((_super) => function TimeoutErrorImpl(info = null) {\n    _super(this);\n    this.message = 'Timeout has occurred';\n    this.name = 'TimeoutError';\n    this.info = info;\n});\nexport function timeout(config, schedulerArg) {\n    const { first, each, with: _with = timeoutErrorFactory, scheduler = schedulerArg !== null && schedulerArg !== void 0 ? schedulerArg : asyncScheduler, meta = null, } = (isValidDate(config) ? { first: config } : typeof config === 'number' ? { each: config } : config);\n    if (first == null && each == null) {\n        throw new TypeError('No timeout provided.');\n    }\n    return operate((source, subscriber) => {\n        let originalSourceSubscription;\n        let timerSubscription;\n        let lastValue = null;\n        let seen = 0;\n        const startTimer = (delay) => {\n            timerSubscription = executeSchedule(subscriber, scheduler, () => {\n                try {\n                    originalSourceSubscription.unsubscribe();\n                    innerFrom(_with({\n                        meta,\n                        lastValue,\n                        seen,\n                    })).subscribe(subscriber);\n                }\n                catch (err) {\n                    subscriber.error(err);\n                }\n            }, delay);\n        };\n        originalSourceSubscription = source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n            seen++;\n            subscriber.next((lastValue = value));\n            each > 0 && startTimer(each);\n        }, undefined, undefined, () => {\n            if (!(timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.closed)) {\n                timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n            }\n            lastValue = null;\n        }));\n        startTimer(first != null ? (typeof first === 'number' ? first : +first - scheduler.now()) : each);\n    });\n}\nfunction timeoutErrorFactory(info) {\n    throw new TimeoutError(info);\n}\n"]}, "metadata": {}, "sourceType": "module"}