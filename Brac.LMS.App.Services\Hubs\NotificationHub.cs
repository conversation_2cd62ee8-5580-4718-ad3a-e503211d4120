﻿using Microsoft.AspNet.SignalR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.Services.Hubs
{
    public class NotificationHub : Microsoft.AspNet.SignalR.Hub
    {
        private static IHubContext hubContext = GlobalHost.ConnectionManager.GetHubContext<NotificationHub>();

        public static void NewNotification(string userType, string userId, Guid notificationId)
        {
            hubContext.Clients.All.NewNotification(userType, userId, notificationId);
        }
    
    }
}
