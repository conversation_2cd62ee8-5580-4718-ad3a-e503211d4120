﻿using Brac.LMS.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.ViewModels
{
    public class OpenMaterialModel
    {
        public Guid? Id { get; set; }
        public MaterialType MaterialType { get; set; }
        public string Title { get; set; }
        public string YoutubeID { get; set; }
        public long CategoryId { get; set; }
        public OMAllowFor AllowFor { get; set; }
        public long? DivisionId { get; set; }
        public long? DepartmentId { get; set; }
        public long? UnitId { get; set; }
        public List<Guid> Trainees { get; set; }
        public bool Active { get; set; }
        public int? Order { get; set; }
    }
}
