﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class Library : NumberAuditableEntity
    {
        public MaterialType MaterialType { get; set; }


        [Required, StringLength(250)]
        public string Title { get; set; }


        [Required, Column(TypeName = "VARCHAR"), StringLength(250)]
        public string FilePath { get; set; }

        public long FileSizeKb { get; set; }

        public int VideoDurationSecond { get; set; }


        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string S3Path { get; set; }

        public FileType FileType { get; set; }
        public bool CanDownload { get; set; }


        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string ImagePath { get; set; }

        public long? CategoryId { get; set; }
        public virtual LibraryCategory Category { get; set; }
    }
}
