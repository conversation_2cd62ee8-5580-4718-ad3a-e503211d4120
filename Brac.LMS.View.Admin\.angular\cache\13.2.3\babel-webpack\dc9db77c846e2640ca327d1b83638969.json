{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ColumnMode } from \"@swimlane/ngx-datatable\";\nimport { Validators } from \"@angular/forms\";\nimport { BlockUI } from \"ng-block-ui\";\nimport { environment } from \"../../environments/environment\";\nimport { ResponseStatus } from \"../_models/enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../app.component\";\nimport * as i2 from \"ngx-bootstrap/modal\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../_services/common.service\";\nimport * as i5 from \"ngx-toastr\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"ng-block-ui\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@ng-select/ng-select\";\nimport * as i10 from \"@angular/flex-layout/extended\";\nconst _c0 = [\"versionModal\"];\n\nfunction CertificateSettingComponent_ng_select_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 46, 47);\n    i0.ɵɵlistener(\"click\", function CertificateSettingComponent_ng_select_11_Template_ng_select_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n\n      const _r21 = i0.ɵɵreference(1);\n\n      const ctx_r22 = i0.ɵɵnextContext();\n      return ctx_r22.handleSelectClick(_r21);\n    })(\"change\", function CertificateSettingComponent_ng_select_11_Template_ng_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return ctx_r24.getItem($event.Id);\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r0.courseList);\n  }\n}\n\nfunction CertificateSettingComponent_div_12_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1, \" Course is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificateSettingComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, CertificateSettingComponent_div_12_span_1_Template, 2, 0, \"span\", 49);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.course.errors.required);\n  }\n}\n\nfunction CertificateSettingComponent_div_20_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1, \"Template is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificateSettingComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, CertificateSettingComponent_div_20_span_1_Template, 2, 0, \"span\", 49);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.f.template.errors.required);\n  }\n}\n\nfunction CertificateSettingComponent_div_29_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1, \"Course Description is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificateSettingComponent_div_29_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1, \"Course Description cannot be longer than 500 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificateSettingComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, CertificateSettingComponent_div_29_span_1_Template, 2, 0, \"span\", 49);\n    i0.ɵɵtemplate(2, CertificateSettingComponent_div_29_span_2_Template, 2, 0, \"span\", 49);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.f.courseDescription.errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.f.courseDescription.errors.maxLength);\n  }\n}\n\nfunction CertificateSettingComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelement(1, \"img\", 52);\n    i0.ɵɵelementStart(2, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function CertificateSettingComponent_div_34_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return ctx_r29.removeImage(0);\n    });\n    i0.ɵɵelement(3, \"i\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r5.templateImageURL, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction CertificateSettingComponent_div_53_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1, \" Name cannot be longer than 250 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificateSettingComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, CertificateSettingComponent_div_53_span_1_Template, 2, 0, \"span\", 49);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.f.person1Name.errors.maxLength);\n  }\n}\n\nfunction CertificateSettingComponent_div_59_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1, \" Designation cannot be longer than 250 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificateSettingComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, CertificateSettingComponent_div_59_span_1_Template, 2, 0, \"span\", 49);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.f.designation1.errors.maxLength);\n  }\n}\n\nfunction CertificateSettingComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelement(1, \"img\", 55);\n    i0.ɵɵelementStart(2, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function CertificateSettingComponent_div_64_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return ctx_r33.removeImage(1);\n    });\n    i0.ɵɵelement(3, \"i\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r9.person1SignURL, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction CertificateSettingComponent_div_78_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1, \" Name cannot be longer than 250 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificateSettingComponent_div_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, CertificateSettingComponent_div_78_span_1_Template, 2, 0, \"span\", 49);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.f.person2Name.errors.maxLength);\n  }\n}\n\nfunction CertificateSettingComponent_div_84_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1, \" Designation cannot be longer than 250 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificateSettingComponent_div_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, CertificateSettingComponent_div_84_span_1_Template, 2, 0, \"span\", 49);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.f.designation2.errors.maxLength);\n  }\n}\n\nfunction CertificateSettingComponent_div_89_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelement(1, \"img\", 55);\n    i0.ɵɵelementStart(2, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function CertificateSettingComponent_div_89_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return ctx_r37.removeImage(2);\n    });\n    i0.ɵɵelement(3, \"i\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r13.person2SignURL, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction CertificateSettingComponent_div_103_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1, \" Name cannot be longer than 250 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificateSettingComponent_div_103_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, CertificateSettingComponent_div_103_span_1_Template, 2, 0, \"span\", 49);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.f.person3Name.errors.maxLength);\n  }\n}\n\nfunction CertificateSettingComponent_div_109_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1, \" Designation cannot be longer than 250 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificateSettingComponent_div_109_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, CertificateSettingComponent_div_109_span_1_Template, 2, 0, \"span\", 49);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.f.designation3.errors.maxLength);\n  }\n}\n\nfunction CertificateSettingComponent_div_114_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelement(1, \"img\", 55);\n    i0.ɵɵelementStart(2, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function CertificateSettingComponent_div_114_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return ctx_r41.removeImage(3);\n    });\n    i0.ɵɵelement(3, \"i\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r17.person3SignURL, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction CertificateSettingComponent_ng_template_122_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵelementStart(1, \"label\", 74);\n    i0.ɵɵtext(2, \"Reason for new version \");\n    i0.ɵɵelementStart(3, \"span\", 50);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 75);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificateSettingComponent_ng_template_122_div_34_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext(2);\n      return ctx_r44.changeReason = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"small\", 66);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r43 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r43.changeReason);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Maximum 50 characters (\", ctx_r43.changeReason.length, \"/50)\");\n  }\n}\n\nfunction CertificateSettingComponent_ng_template_122_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵelementStart(1, \"h4\", 57);\n    i0.ɵɵtext(2, \"Save Certificate Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function CertificateSettingComponent_ng_template_122_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r46 = i0.ɵɵnextContext();\n      return ctx_r46.closeVersionModal();\n    });\n    i0.ɵɵelementStart(4, \"span\", 59);\n    i0.ɵɵtext(5, \"\\u00D7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 60);\n    i0.ɵɵelementStart(7, \"div\", 61);\n    i0.ɵɵelementStart(8, \"p\", 62);\n    i0.ɵɵtext(9, \"How would you like to save your changes?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 63);\n    i0.ɵɵelementStart(11, \"input\", 64);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificateSettingComponent_ng_template_122_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r48 = i0.ɵɵnextContext();\n      return ctx_r48.createNewVersion = $event;\n    })(\"change\", function CertificateSettingComponent_ng_template_122_Template_input_change_11_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r49 = i0.ɵɵnextContext();\n      return ctx_r49.onVersionChoiceChange(false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"label\", 65);\n    i0.ɵɵelementStart(13, \"strong\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"br\");\n    i0.ɵɵelementStart(16, \"small\", 66);\n    i0.ɵɵtext(17, \" Replaces the existing configuration. All future certificate downloads will use the updated version. \");\n    i0.ɵɵelement(18, \"br\");\n    i0.ɵɵelementStart(19, \"strong\");\n    i0.ɵɵtext(20, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" This affects certificates for all trainees. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 63);\n    i0.ɵɵelementStart(23, \"input\", 67);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificateSettingComponent_ng_template_122_Template_input_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return ctx_r50.createNewVersion = $event;\n    })(\"change\", function CertificateSettingComponent_ng_template_122_Template_input_change_23_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r51 = i0.ɵɵnextContext();\n      return ctx_r51.onVersionChoiceChange(true);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"label\", 68);\n    i0.ɵɵelementStart(25, \"strong\");\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"br\");\n    i0.ɵɵelementStart(28, \"small\", 66);\n    i0.ɵɵtext(29, \" Preserves historical accuracy. Existing certificates keep their original signatures. \");\n    i0.ɵɵelement(30, \"br\");\n    i0.ɵɵelementStart(31, \"strong\");\n    i0.ɵɵtext(32, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(33, \" Only new certificates will use the updated version. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, CertificateSettingComponent_ng_template_122_div_34_Template, 8, 2, \"div\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 70);\n    i0.ɵɵelementStart(36, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function CertificateSettingComponent_ng_template_122_Template_button_click_36_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r52 = i0.ɵɵnextContext();\n      return ctx_r52.closeVersionModal();\n    });\n    i0.ɵɵelement(37, \"i\", 54);\n    i0.ɵɵtext(38, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function CertificateSettingComponent_ng_template_122_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r53 = i0.ɵɵnextContext();\n      return ctx_r53.confirmSave();\n    });\n    i0.ɵɵelement(40, \"i\", 44);\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"value\", false)(\"ngModel\", ctx_r20.createNewVersion);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Update current version (v\", ctx_r20.currentVersion, \")\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"value\", true)(\"ngModel\", ctx_r20.createNewVersion);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Create new version (v\", ctx_r20.currentVersion + 1, \")\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r20.createNewVersion);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.createNewVersion ? \"Create New Version\" : \"Update Current\", \" \");\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nexport class CertificateSettingComponent {\n  constructor(appComponent, modalService, formBuilder, _service, toastr, router) {\n    this.appComponent = appComponent;\n    this.modalService = modalService;\n    this.formBuilder = formBuilder;\n    this._service = _service;\n    this.toastr = toastr;\n    this.router = router;\n    this.submitted = false;\n    this.btnSaveText = \"Save\";\n    this.modalConfig = {\n      class: \"gray modal-md\",\n      backdrop: \"static\"\n    };\n    this.createNewVersion = false;\n    this.changeReason = \"\";\n    this.currentVersion = 1;\n    this.templateImageFile = null;\n    this.person1SignFile = null;\n    this.person2SignFile = null;\n    this.person3SignFile = null; // page = new Page();\n\n    this.rows = [];\n    this.courseList = [];\n    this.templateList = [];\n    this.loadingIndicator = false;\n    this.ColumnMode = ColumnMode;\n    this.scrollBarHorizontal = window.innerWidth < 1200;\n\n    window.onresize = () => {\n      this.scrollBarHorizontal = window.innerWidth < 1200;\n    };\n  }\n\n  handleSelectClick(selectElement) {\n    this.appComponent.handleSelectClick(selectElement);\n  }\n\n  ngOnInit() {\n    this.entryForm = this.formBuilder.group({\n      id: [null],\n      course: [null, [Validators.required]],\n      template: [null, [Validators.required]],\n      courseDescription: [null, [Validators.required, Validators.maxLength(500)]],\n      designation1: [null, [Validators.maxLength(250)]],\n      designation2: [null, [Validators.maxLength(250)]],\n      designation3: [null, [Validators.maxLength(250)]],\n      person1Name: [null, [Validators.maxLength(250)]],\n      person2Name: [null, [Validators.maxLength(250)]],\n      person3Name: [null, [Validators.maxLength(250)]],\n      templateImagePath: [null],\n      person1SignPath: [null],\n      person2SignPath: [null],\n      person3SignPath: [null]\n    });\n    this.getCourseList();\n    this.getTemplateList();\n  }\n\n  get f() {\n    return this.entryForm.controls;\n  }\n\n  getCourseList() {\n    this._service.get(\"course/dropdown-list\").subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.courseList = res.Data;\n    }, () => {});\n  }\n\n  getTemplateList() {\n    this._service.get(\"certificate-configuration/get-templates\").subscribe(res => {\n      this.templateList = res;\n    }, () => {});\n  } // onCourseChange(event) {\n  //   this.getItem(event.Id)\n  // }\n\n\n  getItem(id) {\n    this.blockUI.start(\"Getting data...\");\n\n    this._service.get(\"certificate-configuration/get/\" + id).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.entryForm.reset({\n        course: this.entryForm.value.course\n      });\n      this.templateImageURL = null;\n      this.person1SignURL = null;\n      this.person2SignURL = null;\n      this.person3SignURL = null;\n      this.person1SignFile = null;\n      this.person2SignFile = null;\n      this.person3SignFile = null;\n      console.log(\"res.Data\", res.Data);\n\n      if (res.Data) {\n        const timeNow = new Date();\n        this.timeStamp = timeNow.getTime();\n        this.btnSaveText = \"Update\";\n        this.currentVersion = res.Data.Version || 1;\n        this.entryForm.controls[\"id\"].setValue(res.Data.Id);\n        this.entryForm.controls[\"template\"].setValue(res.Data.Template);\n        this.entryForm.controls[\"courseDescription\"].setValue(res.Data.CourseDescription);\n        this.entryForm.controls[\"designation1\"].setValue(res.Data.Designation1);\n        this.entryForm.controls[\"designation2\"].setValue(res.Data.Designation2);\n        this.entryForm.controls[\"designation3\"].setValue(res.Data.Designation3);\n        this.entryForm.controls[\"person1Name\"].setValue(res.Data.Person1Name);\n        this.entryForm.controls[\"person2Name\"].setValue(res.Data.Person2Name);\n        this.entryForm.controls[\"person3Name\"].setValue(res.Data.Person3Name); // this.entryForm.controls['Person1SignPath'].setValue(res.Record.Person1SignPath);\n        // this.entryForm.controls['WrittenSignPath'].setValue(res.Record.WrittenSignPath);\n        // this.entryForm.controls['Person2SignPath'].setValue(res.Record.Person2SignPath);\n\n        if (res.Data.TemplatePath) this.templateImageURL = environment.baseUrl + res.Data.TemplatePath;\n        console.log(\"this.templateImageURL\", this.templateImageURL);\n        if (res.Data.Person1SignPath) this.person1SignURL = environment.baseUrl + res.Data.Person1SignPath + \"?v=\" + this.timeStamp;\n        console.log(\"this.person1SignURL\", this.person1SignURL);\n        if (res.Data.Person2SignPath) this.person2SignURL = environment.baseUrl + res.Data.Person2SignPath + \"?v=\" + this.timeStamp;\n        if (res.Data.Person3SignPath) this.person3SignURL = environment.baseUrl + res.Data.Person3SignPath + \"?v=\" + this.timeStamp;\n      }\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, \"Error!\", {\n        timeOut: 2000\n      });\n    });\n  }\n\n  templatePreview(files) {\n    if (files.length === 0) return;\n    var mimeType = files[0].type;\n\n    if (mimeType.match(/image\\/*/) == null) {\n      this.message = \"Only images are supported.\";\n      return;\n    }\n\n    var reader = new FileReader();\n    this.templateImageFile = files[0];\n    reader.readAsDataURL(files[0]);\n\n    reader.onload = _event => {\n      this.templateImageURL = reader.result;\n    };\n  }\n\n  person1SignPreview(files) {\n    if (files.length === 0) return;\n    var mimeType = files[0].type;\n\n    if (mimeType.match(/image\\/*/) == null) {\n      this.message = \"Only images are supported.\";\n      return;\n    }\n\n    var reader = new FileReader();\n    this.person1SignFile = files[0];\n    reader.readAsDataURL(files[0]);\n\n    reader.onload = _event => {\n      this.person1SignURL = reader.result;\n    };\n  }\n\n  person2SignPreview(files) {\n    if (files.length === 0) return;\n    var mimeType = files[0].type;\n\n    if (mimeType.match(/image\\/*/) == null) {\n      this.message = \"Only images are supported.\";\n      return;\n    }\n\n    var reader = new FileReader();\n    this.person2SignFile = files[0];\n    reader.readAsDataURL(files[0]);\n\n    reader.onload = _event => {\n      this.person2SignURL = reader.result;\n    };\n  }\n\n  person3SignPreview(files) {\n    if (files.length === 0) return;\n    var mimeType = files[0].type;\n\n    if (mimeType.match(/image\\/*/) == null) {\n      this.message = \"Only images are supported.\";\n      return;\n    }\n\n    var reader = new FileReader();\n    this.person3SignFile = files[0];\n    reader.readAsDataURL(files[0]);\n\n    reader.onload = _event => {\n      this.person3SignURL = reader.result;\n    };\n  }\n\n  removeImage(imageNumber) {\n    switch (imageNumber) {\n      case 0:\n        this.templateImageFile = null;\n        this.templateImageURL = null;\n        break;\n\n      case 1:\n        this.person1SignFile = null;\n        this.person1SignURL = null;\n        break;\n\n      case 2:\n        this.person2SignFile = null;\n        this.person2SignURL = null;\n        break;\n\n      case 3:\n        this.person3SignFile = null;\n        this.person3SignURL = null;\n        break;\n    }\n  }\n\n  onFormSubmit() {\n    this.submitted = true;\n\n    if (this.templateImageURL) {\n      this.entryForm.get(\"templateImagePath\").setValidators([]);\n      this.entryForm.get(\"templateImagePath\").updateValueAndValidity();\n    }\n\n    if (!this.person1SignURL && !this.person2SignURL && !this.person3SignURL) {\n      this.toastr.warning(\"Minimum one signatory's signature must be uploaded\", \"Warning!\");\n      return;\n    }\n\n    if (this.person1SignURL) {\n      this.entryForm.get(\"person1SignPath\").setValidators([]);\n      this.entryForm.get(\"person1SignPath\").updateValueAndValidity();\n    }\n\n    if (this.person2SignURL) {\n      this.entryForm.get(\"person2SignPath\").setValidators([]);\n      this.entryForm.get(\"person2SignPath\").updateValueAndValidity();\n    }\n\n    if (this.person3SignURL) {\n      this.entryForm.get(\"person3SignPath\").setValidators([]);\n      this.entryForm.get(\"person3SignPath\").updateValueAndValidity();\n    }\n\n    if (this.entryForm.invalid) {\n      return;\n    } // Show version choice dialog for existing configurations\n\n\n    if (this.btnSaveText === \"Update\") {\n      this.showVersionDialog();\n    } else {\n      // For new configurations, save directly\n      this.saveConfiguration();\n    }\n  }\n\n  showVersionDialog() {\n    // Reset version dialog state\n    this.createNewVersion = false;\n    this.changeReason = \"\"; // Open the version modal\n\n    this.versionModalRef = this.modalService.show(this.versionModal, this.modalConfig);\n  }\n\n  saveConfiguration() {\n    this.blockUI.start(\"Saving...\");\n    const obj = {\n      Id: this.entryForm.value.id ? this.entryForm.value.id : 0,\n      CourseId: this.entryForm.value.course,\n      Template: this.entryForm.value.template,\n      CourseDescription: this.entryForm.value.courseDescription.trim(),\n      Designation1: this.entryForm.value.designation1 ? this.entryForm.value.designation1.trim() : null,\n      Designation2: this.entryForm.value.designation2 ? this.entryForm.value.designation2.trim() : null,\n      Designation3: this.entryForm.value.designation3 ? this.entryForm.value.designation3.trim() : null,\n      Person1Name: this.entryForm.value.person1Name ? this.entryForm.value.person1Name.trim() : null,\n      Person2Name: this.entryForm.value.person2Name ? this.entryForm.value.person2Name.trim() : null,\n      Person3Name: this.entryForm.value.person3Name ? this.entryForm.value.person3Name.trim() : null,\n      // Add version fields\n      CreateNewVersion: this.createNewVersion,\n      ChangeReason: this.changeReason\n    };\n    const certificateFormdata = new FormData();\n    certificateFormdata.append(\"Model\", JSON.stringify(obj));\n    if (this.templateImageFile) certificateFormdata.append(\"TemplatePath\", this.templateImageFile);\n    if (this.person1SignFile) certificateFormdata.append(\"Person1Sign\", this.person1SignFile);\n    if (this.person2SignFile) certificateFormdata.append(\"Person2Sign\", this.person2SignFile);\n    if (this.person3SignFile) certificateFormdata.append(\"Person3Sign\", this.person3SignFile);\n\n    this._service.post(\"certificate-configuration/create-or-update\", certificateFormdata).subscribe(res => {\n      var _a, _b;\n\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } // Enhanced success message\n\n\n      const message = ((_a = res.Data) === null || _a === void 0 ? void 0 : _a.IsNewVersion) ? `Successfully created version v${res.Data.Version}!` : res.Message || \"Successfully saved!\";\n      this.toastr.success(message, \"Success!\", {\n        timeOut: 3000\n      }); // Update current version if new version was created\n\n      if ((_b = res.Data) === null || _b === void 0 ? void 0 : _b.IsNewVersion) {\n        this.currentVersion = res.Data.Version;\n      } // Hide version modal if it was open\n\n\n      if (this.versionModalRef) {\n        this.versionModalRef.hide();\n      }\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, \"Error!\", {\n        timeOut: 2000\n      });\n    });\n  }\n\n  modalHide() {\n    this.entryForm.reset();\n    this.modalRef.hide();\n    this.submitted = false;\n    this.btnSaveText = \"Save\";\n  }\n\n  openModal(template) {\n    this.modalRef = this.modalService.show(template, this.modalConfig);\n  }\n\n  openVersionModal(template) {\n    this.versionModalRef = this.modalService.show(template, this.modalConfig);\n  }\n\n  closeVersionModal() {\n    if (this.versionModalRef) {\n      this.versionModalRef.hide();\n    }\n\n    this.createNewVersion = false;\n    this.changeReason = \"\";\n  }\n\n  onVersionChoiceChange(createNew) {\n    this.createNewVersion = createNew;\n\n    if (!createNew) {\n      this.changeReason = \"\";\n    }\n  }\n\n  confirmSave() {\n    // Validate change reason if creating new version\n    if (this.createNewVersion && (!this.changeReason || this.changeReason.trim().length === 0)) {\n      this.toastr.warning(\"Please provide a reason for creating new version\", \"Warning!\");\n      return;\n    }\n\n    if (this.createNewVersion && this.changeReason.trim().length > 50) {\n      this.toastr.warning(\"Change reason cannot exceed 50 characters\", \"Warning!\");\n      return;\n    }\n\n    this.saveConfiguration();\n  }\n\n}\n\nCertificateSettingComponent.ɵfac = function CertificateSettingComponent_Factory(t) {\n  return new (t || CertificateSettingComponent)(i0.ɵɵdirectiveInject(i1.AppComponent), i0.ɵɵdirectiveInject(i2.BsModalService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.CommonService), i0.ɵɵdirectiveInject(i5.ToastrService), i0.ɵɵdirectiveInject(i6.Router));\n};\n\nCertificateSettingComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: CertificateSettingComponent,\n  selectors: [[\"app-certificate-setting\"]],\n  viewQuery: function CertificateSettingComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.versionModal = _t.first);\n    }\n  },\n  decls: 124,\n  vars: 40,\n  consts: [[1, \"card\", \"card-border-primary\"], [1, \"card-body\"], [\"autocomplete\", \"off\", 3, \"formGroup\"], [1, \"mb-3\", \"row\"], [1, \"col-lg-6\", \"col-md-6\", \"col-12\"], [1, \"col-lg-4\", \"col-md-4\", \"col-12\", \"col-form-label\", \"col-form-label-sm\", \"fw-bold\", \"required\"], [1, \"col-lg-8\", \"col-md-8\", \"col-12\"], [1, \"course-selection-container\"], [\"class\", \"form-control form-control-sm course-select\", \"formControlName\", \"course\", \"bindLabel\", \"Title\", \"bindValue\", \"Id\", \"placeholder\", \"Select a course\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\", \"change\", 4, \"ngIf\"], [\"class\", \"error-text\", 4, \"ngIf\"], [1, \"col-lg-2\", \"col-md-4\", \"col-12\", \"col-form-label\", \"col-form-label-sm\", \"fw-bold\", \"required\"], [1, \"col-lg-10\", \"col-md-8\", \"col-12\"], [\"formControlName\", \"template\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Select\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"clearable\", \"clearOnBackspace\", \"items\", \"click\"], [\"selectElementT\", \"\"], [1, \"col-12\"], [1, \"col-lg-2\", \"col-md-3\", \"col-12\", \"col-form-label\", \"col-form-label-sm\", \"fw-bold\", \"required\"], [1, \"col-lg-10\", \"col-md-9\", \"col-12\"], [\"type\", \"text\", \"formControlName\", \"courseDescription\", \"placeholder\", \"e.g. has participated in an online course on\", \"rows\", \"1\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-12\", \"col-form-label\", \"col-form-label-sm\", \"fw-bold\"], [\"class\", \"image-container\", 4, \"ngIf\"], [\"type\", \"file\", \"formControlName\", \"templateImagePath\", \"accept\", \"image/png,image/jpeg\", 1, \"mt-2\", \"w-100\", 3, \"change\"], [\"templateImagePath\", \"\"], [1, \"row\"], [1, \"card-text\", \"alert\", \"alert-danger\", \"f-16\", \"p-1\", \"fw-bold\", \"text-center\", \"text-uppercase\"], [1, \"fa\", \"fa-alert\"], [1, \"col-lg-4\", \"col-md-6\", \"col-12\"], [1, \"card\", \"border-theme\", \"w-100\"], [1, \"card-header\", \"pt-3\", \"pb-3\", \"text-center\"], [1, \"card-title\"], [1, \"card-body\", \"pt-2\", \"pb-2\"], [\"type\", \"text\", \"formControlName\", \"person1Name\", \"placeholder\", \"Enter name\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"designation1\", \"placeholder\", \"Enter designation\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"file\", \"formControlName\", \"person1SignPath\", \"accept\", \"image/png,image/jpeg\", 1, \"mt-2\", \"w-100\", 3, \"change\"], [\"person1SignPath\", \"\"], [\"type\", \"text\", \"formControlName\", \"person2Name\", \"placeholder\", \"Enter name\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"designation2\", \"placeholder\", \"Enter designation\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"file\", \"formControlName\", \"person2SignPath\", \"accept\", \"image/png,image/jpeg\", 1, \"mt-2\", \"w-100\", 3, \"change\"], [\"person2SignPath\", \"\"], [\"type\", \"text\", \"formControlName\", \"person3Name\", \"placeholder\", \"Enter name\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"designation3\", \"placeholder\", \"Enter designation\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"file\", \"formControlName\", \"person3SignPath\", \"accept\", \"image/png,image/jpeg\", 1, \"mt-2\", \"w-100\", 3, \"change\"], [\"person3SignPath\", \"\"], [1, \"col-12\", \"mt-2\", \"text-center\"], [1, \"btn\", \"btn-theme\", \"btn-testz\", \"wid-150\", 3, \"click\"], [1, \"feather\", \"icon-save\"], [\"versionModal\", \"\"], [\"formControlName\", \"course\", \"bindLabel\", \"Title\", \"bindValue\", \"Id\", \"placeholder\", \"Select a course\", 1, \"form-control\", \"form-control-sm\", \"course-select\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\", \"change\"], [\"selectElement\", \"\"], [1, \"error-text\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"], [1, \"image-container\"], [1, \"img-thumbnail\", \"certificate-template-image\", 3, \"src\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"remove-btn\", 3, \"click\"], [1, \"fa\", \"fa-times\"], [1, \"img-thumbnail\", \"signature-image\", 3, \"src\"], [1, \"modal-header\"], [1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [1, \"mb-3\"], [1, \"text-muted\", \"mb-3\"], [1, \"form-check\", \"mb-3\"], [\"type\", \"radio\", \"name\", \"versionChoice\", \"id\", \"updateCurrent\", 1, \"form-check-input\", 3, \"value\", \"ngModel\", \"ngModelChange\", \"change\"], [\"for\", \"updateCurrent\", 1, \"form-check-label\"], [1, \"text-muted\"], [\"type\", \"radio\", \"name\", \"versionChoice\", \"id\", \"createNew\", 1, \"form-check-input\", 3, \"value\", \"ngModel\", \"ngModelChange\", \"change\"], [\"for\", \"createNew\", 1, \"form-check-label\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-theme\", 3, \"click\"], [1, \"mt-3\"], [1, \"form-label\", \"fw-bold\"], [\"type\", \"text\", \"placeholder\", \"e.g., Updated signatures, New management\", \"maxlength\", \"50\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"]],\n  template: function CertificateSettingComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r54 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"form\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"div\", 4);\n      i0.ɵɵelementStart(6, \"div\", 3);\n      i0.ɵɵelementStart(7, \"label\", 5);\n      i0.ɵɵtext(8, \" Course \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(9, \"div\", 6);\n      i0.ɵɵelementStart(10, \"div\", 7);\n      i0.ɵɵtemplate(11, CertificateSettingComponent_ng_select_11_Template, 2, 3, \"ng-select\", 8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(12, CertificateSettingComponent_div_12_Template, 2, 1, \"div\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(13, \"div\", 4);\n      i0.ɵɵelementStart(14, \"div\", 3);\n      i0.ɵɵelementStart(15, \"label\", 10);\n      i0.ɵɵtext(16, \"Template \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(17, \"div\", 11);\n      i0.ɵɵelementStart(18, \"ng-select\", 12, 13);\n      i0.ɵɵlistener(\"click\", function CertificateSettingComponent_Template_ng_select_click_18_listener() {\n        i0.ɵɵrestoreView(_r54);\n\n        const _r2 = i0.ɵɵreference(19);\n\n        return ctx.handleSelectClick(_r2);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(20, CertificateSettingComponent_div_20_Template, 2, 1, \"div\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(21, \"div\", 3);\n      i0.ɵɵelementStart(22, \"div\", 14);\n      i0.ɵɵelementStart(23, \"div\", 3);\n      i0.ɵɵelementStart(24, \"label\", 15);\n      i0.ɵɵtext(25, \"Course Description \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(26, \"div\", 16);\n      i0.ɵɵelementStart(27, \"textarea\", 17);\n      i0.ɵɵtext(28, \" \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(29, CertificateSettingComponent_div_29_Template, 3, 2, \"div\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(30, \"div\", 3);\n      i0.ɵɵelementStart(31, \"label\", 18);\n      i0.ɵɵtext(32, \"Certificate Image Background\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(33, \"div\", 14);\n      i0.ɵɵtemplate(34, CertificateSettingComponent_div_34_Template, 4, 1, \"div\", 19);\n      i0.ɵɵelementStart(35, \"input\", 20, 21);\n      i0.ɵɵlistener(\"change\", function CertificateSettingComponent_Template_input_change_35_listener() {\n        i0.ɵɵrestoreView(_r54);\n\n        const _r6 = i0.ɵɵreference(36);\n\n        return ctx.templatePreview(_r6.files);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(37, \"div\", 22);\n      i0.ɵɵelementStart(38, \"div\", 14);\n      i0.ɵɵelementStart(39, \"p\", 23);\n      i0.ɵɵelement(40, \"i\", 24);\n      i0.ɵɵtext(41, \" Minimum one signatory is mandatory \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(42, \"div\", 25);\n      i0.ɵɵelementStart(43, \"div\", 26);\n      i0.ɵɵelementStart(44, \"div\", 27);\n      i0.ɵɵelementStart(45, \"h5\", 28);\n      i0.ɵɵtext(46, \"Signatory 1\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(47, \"div\", 29);\n      i0.ɵɵelementStart(48, \"div\", 3);\n      i0.ɵɵelementStart(49, \"label\", 18);\n      i0.ɵɵtext(50, \"Name \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(51, \"div\", 14);\n      i0.ɵɵelement(52, \"input\", 30);\n      i0.ɵɵtemplate(53, CertificateSettingComponent_div_53_Template, 2, 1, \"div\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(54, \"div\", 3);\n      i0.ɵɵelementStart(55, \"label\", 18);\n      i0.ɵɵtext(56, \"Designation \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(57, \"div\", 14);\n      i0.ɵɵelement(58, \"input\", 31);\n      i0.ɵɵtemplate(59, CertificateSettingComponent_div_59_Template, 2, 1, \"div\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(60, \"div\", 3);\n      i0.ɵɵelementStart(61, \"label\", 18);\n      i0.ɵɵtext(62, \"Signature Image (255x47) \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(63, \"div\", 14);\n      i0.ɵɵtemplate(64, CertificateSettingComponent_div_64_Template, 4, 1, \"div\", 19);\n      i0.ɵɵelementStart(65, \"input\", 32, 33);\n      i0.ɵɵlistener(\"change\", function CertificateSettingComponent_Template_input_change_65_listener() {\n        i0.ɵɵrestoreView(_r54);\n\n        const _r10 = i0.ɵɵreference(66);\n\n        return ctx.person1SignPreview(_r10.files);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(67, \"div\", 25);\n      i0.ɵɵelementStart(68, \"div\", 26);\n      i0.ɵɵelementStart(69, \"div\", 27);\n      i0.ɵɵelementStart(70, \"h5\", 28);\n      i0.ɵɵtext(71, \"Signatory 2\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(72, \"div\", 29);\n      i0.ɵɵelementStart(73, \"div\", 3);\n      i0.ɵɵelementStart(74, \"label\", 18);\n      i0.ɵɵtext(75, \"Name \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(76, \"div\", 14);\n      i0.ɵɵelement(77, \"input\", 34);\n      i0.ɵɵtemplate(78, CertificateSettingComponent_div_78_Template, 2, 1, \"div\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(79, \"div\", 3);\n      i0.ɵɵelementStart(80, \"label\", 18);\n      i0.ɵɵtext(81, \"Designation \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(82, \"div\", 14);\n      i0.ɵɵelement(83, \"input\", 35);\n      i0.ɵɵtemplate(84, CertificateSettingComponent_div_84_Template, 2, 1, \"div\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(85, \"div\", 3);\n      i0.ɵɵelementStart(86, \"label\", 18);\n      i0.ɵɵtext(87, \"Signature Image (255x47) \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(88, \"div\", 14);\n      i0.ɵɵtemplate(89, CertificateSettingComponent_div_89_Template, 4, 1, \"div\", 19);\n      i0.ɵɵelementStart(90, \"input\", 36, 37);\n      i0.ɵɵlistener(\"change\", function CertificateSettingComponent_Template_input_change_90_listener() {\n        i0.ɵɵrestoreView(_r54);\n\n        const _r14 = i0.ɵɵreference(91);\n\n        return ctx.person2SignPreview(_r14.files);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(92, \"div\", 25);\n      i0.ɵɵelementStart(93, \"div\", 26);\n      i0.ɵɵelementStart(94, \"div\", 27);\n      i0.ɵɵelementStart(95, \"h5\", 28);\n      i0.ɵɵtext(96, \"Signatory 3\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(97, \"div\", 29);\n      i0.ɵɵelementStart(98, \"div\", 3);\n      i0.ɵɵelementStart(99, \"label\", 18);\n      i0.ɵɵtext(100, \"Name \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(101, \"div\", 14);\n      i0.ɵɵelement(102, \"input\", 38);\n      i0.ɵɵtemplate(103, CertificateSettingComponent_div_103_Template, 2, 1, \"div\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(104, \"div\", 3);\n      i0.ɵɵelementStart(105, \"label\", 18);\n      i0.ɵɵtext(106, \"Designation \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(107, \"div\", 14);\n      i0.ɵɵelement(108, \"input\", 39);\n      i0.ɵɵtemplate(109, CertificateSettingComponent_div_109_Template, 2, 1, \"div\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(110, \"div\", 3);\n      i0.ɵɵelementStart(111, \"label\", 18);\n      i0.ɵɵtext(112, \"Signature Image (255x47) \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(113, \"div\", 14);\n      i0.ɵɵtemplate(114, CertificateSettingComponent_div_114_Template, 4, 1, \"div\", 19);\n      i0.ɵɵelementStart(115, \"input\", 40, 41);\n      i0.ɵɵlistener(\"change\", function CertificateSettingComponent_Template_input_change_115_listener() {\n        i0.ɵɵrestoreView(_r54);\n\n        const _r18 = i0.ɵɵreference(116);\n\n        return ctx.person3SignPreview(_r18.files);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(117, \"div\", 22);\n      i0.ɵɵelementStart(118, \"div\", 42);\n      i0.ɵɵelementStart(119, \"button\", 43);\n      i0.ɵɵlistener(\"click\", function CertificateSettingComponent_Template_button_click_119_listener() {\n        return ctx.onFormSubmit();\n      });\n      i0.ɵɵelement(120, \"i\", 44);\n      i0.ɵɵtext(121);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(122, CertificateSettingComponent_ng_template_122_Template, 42, 8, \"ng-template\", null, 45, i0.ɵɵtemplateRefExtractor);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"formGroup\", ctx.entryForm);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngIf\", ctx.courseList.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.course.errors);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(26, _c1, ctx.submitted && ctx.f.template.errors))(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx.templateList);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.template.errors);\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.courseDescription.errors);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", ctx.templateImageURL);\n      i0.ɵɵadvance(18);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(28, _c1, ctx.submitted && ctx.f.person1Name.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.person1Name.errors);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(30, _c1, ctx.submitted && ctx.f.designation1.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.designation1.errors);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", ctx.person1SignURL);\n      i0.ɵɵadvance(13);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(32, _c1, ctx.submitted && ctx.f.person2Name.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.person2Name.errors);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(34, _c1, ctx.submitted && ctx.f.designation2.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.designation2.errors);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", ctx.person2SignURL);\n      i0.ɵɵadvance(13);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(36, _c1, ctx.submitted && ctx.f.person3Name.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.person3Name.errors);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(38, _c1, ctx.submitted && ctx.f.designation3.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.designation3.errors);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", ctx.person3SignURL);\n      i0.ɵɵadvance(7);\n      i0.ɵɵtextInterpolate1(\" \", ctx.btnSaveText, \" \");\n    }\n  },\n  directives: [i7.BlockUIComponent, i3.ɵNgNoValidate, i3.NgControlStatusGroup, i3.FormGroupDirective, i8.NgIf, i9.NgSelectComponent, i3.NgControlStatus, i3.FormControlName, i8.NgClass, i10.DefaultClassDirective, i3.DefaultValueAccessor, i3.RadioControlValueAccessor, i3.NgModel, i3.MaxLengthValidator],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], CertificateSettingComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}