﻿using Brac.LMS.App.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.WebPages;

namespace Brac.LMS.App.API.Controllers
{
    [RoutePrefix("api/dashboard")]
    public class DashboardController : ApplicationController
    {
        private readonly IDashboardService _service;

        public DashboardController()
        {
            _service = new DashboardService();
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-count-for-admin")]
        public async Task<IHttpActionResult> GetCountForAdmin()
        {
            return Ok(await _service.GetCountForAdmin());
        }
        [Authorize(Roles = "Admin, Trainer"), HttpGet, Route("get-count-for-trainer")]
        public async Task<IHttpActionResult> GetCountForTrainer()
        {
            return Ok(await _service.GetCountForTrainer(CurrentUser));
        }
        [Authorize(Roles = "Admin"), HttpGet, Route("get-belated-trainee-list")]
        public async Task<IHttpActionResult> GeBelatedTraineeList(int size, int pageNumber)
        {
            return Ok(await _service.GeBelatedTraineeList(size, pageNumber));
        }
        [Authorize(Roles = "Admin"), HttpGet, Route("get-trainee-progress")]
        public async Task<IHttpActionResult> GetTraineeProgress(int size, int pageNumber)
        {
            return Ok(await _service.GetTraineeProgress(size, pageNumber, CurrentUser));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-top-enrollment-courses")]
        public async Task<IHttpActionResult> GetTopEnrollmentCourses(int limit,DateTime? startDate,DateTime? endDate,bool? status)
        {
            return Ok(await _service.GetTopEnrollmentCourses(limit, startDate, endDate,status));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("get-month-wise-certificate-gains")]
        public async Task<IHttpActionResult> GetMonthWiseCertificateGains(int size, DateTime? startDate, DateTime? endDate, bool? status, int pageNumber)
        {
            return Ok(await _service.GetMonthWiseCertificateGains(size, (DateTime)(startDate == null ? "01/01/0001".AsDateTime(): startDate), (DateTime)(endDate == null ? "01/01/0001".AsDateTime() : endDate), status, pageNumber, CurrentUser));
        }
        [Authorize(Roles = "Admin"), HttpGet, Route("get-month-wise-enrollment")]
        public async Task<IHttpActionResult> GetMonthWiseEnrollments(int size, DateTime? startDate, DateTime? endDate, bool? status, int pageNumber)
        {
            return Ok(await _service.GetMonthWiseEnrollments(size, (DateTime)(startDate == null ? "01/01/0001".AsDateTime() : startDate), (DateTime)(endDate == null ? "01/01/0001".AsDateTime() : endDate), status, CurrentUser));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-count-for-trainee")]
        public async Task<IHttpActionResult> GetCountForTrainee()
        {
            return Ok(await _service.GetCountForTrainee(CurrentUser));
        }
    }
}
