{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SMSConfigurationComponent } from './sms-configuration.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: SMSConfigurationComponent\n}];\nexport let SMSConfigurationRoutingModule = /*#__PURE__*/(() => {\n  class SMSConfigurationRoutingModule {}\n\n  SMSConfigurationRoutingModule.ɵfac = function SMSConfigurationRoutingModule_Factory(t) {\n    return new (t || SMSConfigurationRoutingModule)();\n  };\n\n  SMSConfigurationRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SMSConfigurationRoutingModule\n  });\n  SMSConfigurationRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return SMSConfigurationRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}