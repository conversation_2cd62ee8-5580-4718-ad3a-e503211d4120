﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Microsoft.Ajax.Utilities;
using Microsoft.AspNet.Identity.Owin;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [RoutePrefix("api/course")]
    public class CourseController : ApplicationController
    {
        private readonly ICourseService _nservice;


        //public CourseController()
        //{

        //    _nservice = new CourseService();
        //}
        public ApplicationUserManager UserManager
        {
            get
            {
                return Request.GetOwinContext().GetUserManager<ApplicationUserManager>();
            }
        }

        [Authorize(Roles = "Admin"), HttpPost, Route("create-or-update")]
        public async Task<IHttpActionResult> CourseCreateOrUpdate()
        {
            try
            {
                var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
                var model = Newtonsoft.Json.JsonConvert.DeserializeObject<CourseModel>(System.Web.HttpContext.Current.Request.Form["Model"]);
                return Ok(await _service.CourseCreateOrUpdate(model, CurrentUser));
            }
            catch (Exception ex)
            {
                return Ok(new { Success = false, ex.Message });
            }
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("list")]
        public async Task<IHttpActionResult> GetCourseList(string name, long? categoryId, int size, int pageNumber)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);

            return Ok(await _service.GetCourseList(name, categoryId, size, pageNumber, CurrentUser));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("download-list-in-excel/{timeZoneOffset}")]
        public async Task<HttpResponseMessage> GetCourseListExcel(int timeZoneOffset)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            byte[] byteArray = await _service.GetCourseListExcel(timeZoneOffset);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.
            //response.Content.Headers.Add("x-filename", "Student_List.xls");
            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }

        [Authorize(Roles = "Admin"), HttpGet, Route("dropdown-list")]
        public async Task<IHttpActionResult> GetCourseDropDownList()
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);

            return Ok(await _service.GetCourseDropDownList(CurrentUser));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-materials-with-exams/{id}")]
        public async Task<IHttpActionResult> GetCourseMaterialsWithExams(Guid id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetCourseMaterialsWithExams(id));
        }

        [Authorize(Roles = "Admin"), HttpPost, Route("save-content-dependencies/{id}")]
        public async Task<IHttpActionResult> SaveContentDependencies(Guid id, List<CourseContentDependencyModel> contents)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.SaveContentDependencies(id, contents, User.Identity));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("publish-or-unpublish/{id}")]
        public async Task<IHttpActionResult> PublishOrUnpublishCourse(Guid id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.PublishOrUnpublishCourse(id, User.Identity));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-trainee-ratings/{id}")]
        public async Task<IHttpActionResult> GetCourseRatings(Guid id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetCourseRatings(id));
        }



        [Authorize(Roles = "Admin"), HttpGet, Route("get/{id}")]
        public async Task<IHttpActionResult> GetCourseById(Guid id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetCourseById(id, CurrentUser));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("material/list")]
        public async Task<IHttpActionResult> GetCourseMaterialList(string name, Guid courseId, int size, int pageNumber)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetCourseMaterialList(name, courseId, size, pageNumber));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("material/resources/{materialId}")]
        public async Task<IHttpActionResult> GetMaterialResources(Guid materialId)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetMaterialResources(materialId));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-enrollment-count/{limit}")]
        public async Task<IHttpActionResult> GetCourseEnrollmentCount(int limit)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetCourseEnrollmentCount(limit, CurrentUser));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-enrollment-count-excel")]
        public async Task<HttpResponseMessage> GetCourseEnrollmentCountExcel()
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);

            byte[] byteArray = await _service.GetCourseEnrollmentCountExcel(CurrentUser);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.
            //response.Content.Headers.Add("x-filename", "Student_List.xls");
            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-enrollment-count-excel-by-date")]
        public async Task<HttpResponseMessage> GetCourseEnrollmentByDateExcel(DateTime fromDate, DateTime toDate)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);

            byte[] byteArray = await _service.GetCourseEnrollmentByDateExcel(fromDate, toDate, CurrentUser);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.
            //response.Content.Headers.Add("x-filename", "Student_List.xls");
            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }

        [Authorize(Roles = "Admin"), HttpPost, Route("enroll-trainee")]
        public async Task<IHttpActionResult> AssignEmployee()
        {
            try
            {
                var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
                var model = Newtonsoft.Json.JsonConvert.DeserializeObject<TraineeEnrollModel>(System.Web.HttpContext.Current.Request.Form["Model"]);
                return Ok(await _service.EnrollTraineeToCourse(model, User.Identity));
            }
            catch (Exception ex)
            {
                return Ok(new { Success = false, ex.Message });
            }
        }

        [Authorize(Roles = "Admin"), HttpPost, Route("cancel-trainee-enrollment")]
        public async Task<IHttpActionResult> CancelCourseEnrollmentOfTrainee(Guid courseId, Guid traineeId)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.CancelCourseEnrollmentOfTrainee(courseId, traineeId));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-all-trainees")]
        public async Task<IHttpActionResult> GetTraineesByCourseId(Guid courseId, long divisionId)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetTraineesByCourseId(courseId, divisionId));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-all-trainees-to-notify/")]
        public async Task<IHttpActionResult> GetTraineesByCourseIdToNotify(Guid courseId, Guid examId)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetTraineesByCourseIdToNotify(courseId, examId));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-enrolled-trainee-list")]
        public async Task<IHttpActionResult> GetEnrolledTraineeList(Guid courseId, long? divisionId)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetEnrolledTraineeList(courseId, divisionId));
        }

        [Authorize(Roles = "Admin"), HttpPost, Route("notify-belated-trainees")]
        public async Task<IHttpActionResult> NotifyBelatedTrainees(NotifyTraineeModel model)
        {
            try
            {
                var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
                return Ok(await _service.NotifyTrainee(model, CurrentUser));
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        [Authorize(Roles = "Admin"), HttpGet, Route("notified-trainees/{courseId}/{examId}")]
        public async Task<IHttpActionResult> GetNotifiedTrainees(Guid courseId, Guid examId)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetNotifiedTrainees(courseId, examId));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("material/delete/{id}")]
        public async Task<IHttpActionResult> DeleteMaterialById(Guid id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.DeleteMaterialById(id));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("material/resources/dropdown-list/{materialId}")]
        public async Task<IHttpActionResult> GetMaterialResourcesDropdownList(Guid materialId)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetMaterialResourcesDropdownList(materialId));
        }

        [Authorize(Roles = "Admin"), HttpPost, Route("material/resource/create-or-update")]
        public async Task<IHttpActionResult> ResourceCreateOrUpdate()
        {
            try
            {
                var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
                var model = JsonConvert.DeserializeObject<MaterialResourceModel>(System.Web.HttpContext.Current.Request.Form["Model"]);
                return Ok(await _service.ResourceCreateOrUpdate(model, User.Identity));
            }
            catch (Exception ex)
            {
                return Ok(new { Success = false, ex.Message });
            }
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("material/resource/delete/{id}")]
        public async Task<IHttpActionResult> DeleteResourceById(Guid id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.DeleteResourceById(id));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("enrolled/dropdown-list")]
        public async Task<IHttpActionResult> GetEnrolledCourseDropDownList()
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetEnrolledCourseDropDownList(CurrentUser));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-course-wise-trainee-study-report")]
        public async Task<HttpResponseMessage> GetCourseWiseTraineeStudyReportExcel(Guid courseId, ReportType reportType)
        {
            try
            {
                var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
                byte[] byteArray = reportType == ReportType.Excel ? await _service.GetCourseWiseTraineeStudyReportExcel(courseId) : await _service.GetCourseWiseTraineeStudyReportPdf(courseId);
                //Create a new response.
                var response = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    //Assign byte array to response content.
                    Content = new ByteArrayContent(byteArray)
                };

                switch (reportType)
                {
                    case ReportType.Pdf:
                        //Set MIME type.
                        response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
                        break;
                    case ReportType.Excel:
                        //Set MIME type.
                        response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
                        break;
                }
                //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.

                //    return response;
                return response;
            }
            catch (Exception)
            {
                return new HttpResponseMessage(HttpStatusCode.BadRequest);
            }
        }
        [Authorize(Roles = "Admin"), HttpGet, Route("get-course-wise-comprehension-test-report")]
        public async Task<HttpResponseMessage> GetCourseWiseComprehensionTestReportExcel(DateTime startDate, DateTime endDate, Guid courseId, ExamType type, ReportType reportType)
        {
            try
            {
                var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
                byte[] byteArray = reportType == ReportType.Excel ? await _service.GetCourseWiseTestReportExcel(startDate, endDate, courseId, type) : throw new Exception("No PDF report provided");
                //Create a new response.
                var response = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    //Assign byte array to response content.
                    Content = new ByteArrayContent(byteArray)
                };

                switch (reportType)
                {
                    case ReportType.Pdf:
                        //Set MIME type.
                        response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
                        break;
                    case ReportType.Excel:
                        //Set MIME type.
                        response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
                        break;
                }
                //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.

                //    return response;
                return response;
            }
            catch (Exception)
            {
                return new HttpResponseMessage(HttpStatusCode.BadRequest);
            }
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-trainee-wise-course-study-report")]
        public async Task<HttpResponseMessage> GetTraineeWiseCourseStudyReport(Guid traineeId, ReportType reportType)
        {
            try
            {
                var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
                byte[] byteArray = reportType == ReportType.Excel ? await _service.GetTraineeWiseCourseStudyReportExcel(traineeId) : await _service.GetTraineeWiseCourseStudyReportPdf(traineeId);
                //Create a new response.
                var response = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    //Assign byte array to response content.
                    Content = new ByteArrayContent(byteArray)
                };

                switch (reportType)
                {
                    case ReportType.Pdf:
                        //Set MIME type.
                        response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
                        break;
                    case ReportType.Excel:
                        //Set MIME type.
                        response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
                        break;
                }
                //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.

                //    return response;
                return response;
            }
            catch (Exception)
            {
                return new HttpResponseMessage(HttpStatusCode.BadRequest);
            }
        }
        [Authorize(Roles = "Admin"), HttpGet, Route("get-time-wise-course-study-report")]
        public async Task<HttpResponseMessage> GetTimeWiseCourseStudyReport(DateTime startDate, DateTime endDate, ReportType reportType)
        {
            try
            {
                startDate = startDate.ToKindUtc();
                endDate = endDate.ToKindUtc();
                var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
                byte[] byteArray = reportType == ReportType.Excel ? await _service.GetTimeWiseCourseStudyReportExcel(startDate,endDate) : await _service.GetTimeWiseCourseStudyReportPdf(startDate, endDate);
                //Create a new response.
                var response = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    //Assign byte array to response content.
                    Content = new ByteArrayContent(byteArray)
                };

                switch (reportType)
                {
                    case ReportType.Pdf:
                        //Set MIME type.
                        response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
                        break;
                    case ReportType.Excel:
                        //Set MIME type.
                        response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                        break;
                }
                //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.

                //    return response;
                return response;
            }
            catch (Exception)
            {
                return new HttpResponseMessage(HttpStatusCode.BadRequest);
            }
        }
        [Authorize(Roles = "Admin"), HttpGet, Route("get-exam-wise-correct-answer-rate")]
        public async Task<HttpResponseMessage> GetExamWiseCorrectAnswerRateReport(Guid courseId, ReportType reportType)
        {
            try
            {
                var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
                byte[] byteArray = reportType == ReportType.Excel ? await _service.GetExamWiseCorrectAnswerRateReportExcel(courseId) : await _service.GetExamWiseCorrectAnswerRateReportPdf(courseId);
                //Create a new response.
                var response = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    //Assign byte array to response content.
                    Content = new ByteArrayContent(byteArray)
                };

                switch (reportType)
                {
                    case ReportType.Pdf:
                        //Set MIME type.
                        response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
                        break;
                    case ReportType.Excel:
                        //Set MIME type.
                        response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
                        break;
                }
                //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.

                //    return response;
                return response;
            }
            catch (Exception)
            {
                return new HttpResponseMessage(HttpStatusCode.BadRequest);
            }
        }


        [Authorize(Roles = "Admin"), HttpPost, Route("material/create-or-update")]
        public async Task<IHttpActionResult> MaterialCreateOrUpdate()
        {
            try
            {
                var model = JsonConvert.DeserializeObject<CourseMaterialModel>(System.Web.HttpContext.Current.Request.Form["Model"]);
                var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
                return Ok(await _service.MaterialCreateOrUpdate(model, User.Identity));
            }
            catch (Exception ex)
            {
                return Ok(new { Success = false, ex.Message });
            }
        }


        #region Trainee Panel APIs
        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("download-document-file")]
        public HttpResponseMessage DownloadDocumentFile(string partialPath)
        {
            var fullPath = System.Web.Hosting.HostingEnvironment.MapPath("~") + partialPath;
            if (!File.Exists(fullPath))
            {
                return Request.CreateResponse(HttpStatusCode.NotFound);
            }
            else
            {
                HttpResponseMessage result = Request.CreateResponse(HttpStatusCode.OK);
                result.Content = new StreamContent(File.OpenRead(fullPath));
                result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
                return result;
            }
        }


        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-my-courses/{limit}")]
        public async Task<IHttpActionResult> GetMyCourseList(int limit)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetMyCourseList(limit, null, CurrentUser));
        }
       
        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-my-more-courses")]
        public async Task<IHttpActionResult> GetMyMoreCourseList(int limit, DateTime keyDate)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetMyCourseList(limit,null, CurrentUser, keyDate));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-my-courses")]
        public async Task<IHttpActionResult> GetMyCourseList(string name, long? categoryId, int size, int pageNumber,int? courseStatus = null)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetMyCourseList(name, categoryId, size, pageNumber, courseStatus, CurrentUser));
        }


        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-available-courses/{limit}")]
        public async Task<IHttpActionResult> GetAvailableCourseList(int limit)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetAvailableCourseList(limit, CurrentUser));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-available-more-courses")]
        public async Task<IHttpActionResult> GetAvailableMoreCourseList(int limit, DateTime keyDate)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetAvailableCourseList(limit, CurrentUser, keyDate));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-available-courses")]
        public async Task<IHttpActionResult> GetAvailableCourseList(string name, long? categoryId, int size, int pageNumber)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetAvailableCourseList(name, categoryId, size, pageNumber, CurrentUser));
        }


        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-learning-hour-contents/{limit}")]
        public async Task<IHttpActionResult> GetLearningHourList(int limit)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetLearningHourList(limit, CurrentUser));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-learning-hour-more-contents")]
        public async Task<IHttpActionResult> GetMoreLearningHourList(int limit, DateTime keyDate)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetLearningHourList(limit, CurrentUser, keyDate));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-learning-hour-contents")]
        public async Task<IHttpActionResult> GetLearningHourList(string name, long? categoryId, int size, int pageNumber)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetLearningHourList(name, categoryId, size, pageNumber, CurrentUser));
        }
        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-learning-hour-groupby-contents")]
        public async Task<IHttpActionResult> GetLearningHourGroupByList(string name, long? categoryId, int size, int pageNumber)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetLearningHourGroupByList(name, categoryId, size, pageNumber, CurrentUser));
        }
        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-my-bookmarked-courses")]
        public async Task<IHttpActionResult> GetMyBookmarkedCourseList(string name, long? categoryId, int size, int pageNumber)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetMyBookmarkedCourseList(name, categoryId, size, pageNumber, CurrentUser));
        }


        #region Course Details APIs
        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-course-preview/{id}")]
        public async Task<IHttpActionResult> GetCoursePreview(Guid id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetCoursePreview(id, CurrentUser));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-course-details/{id}")]
        public async Task<IHttpActionResult> GetCourseDetails(Guid id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetCourseDetails(id));
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("get-my-teammates/{id}")]
        public async Task<IHttpActionResult> GetMyTraineesToEnrol(Guid id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetMyTraineesToEnrol(id, CurrentUser));
        }

        [Authorize(Roles = "Trainee"), HttpPost, Route("enroll-teammates/{id}")]
        public async Task<IHttpActionResult> EnrollTeammatesToCourse(Guid id, List<Guid> traineeIds)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.EnrollTeammatesToCourse(id, traineeIds, User.Identity));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-course-contents/{id}")]
        public async Task<IHttpActionResult> GetCourseContents(Guid id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetCourseContents(id, CurrentUser));
        }

        [Authorize(Roles = "Trainee, Admin, Guest"), HttpGet, Route("contents/dropdown-list")]
        public async Task<IHttpActionResult> GetCourseContentsDropdownList(DiscussionType disscussionType, Guid courseId)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetCourseContentsDropdownList(disscussionType, courseId));
        }

        [Authorize(Roles = "Trainee, Admin, Guest"), HttpGet, Route("get-course-discussions/{id}")]
        public async Task<IHttpActionResult> GetCourseDiscussions(Guid id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetCourseDiscussions(id, CurrentUser));
        }
        [Authorize(Roles = "Trainee, Admin, Guest"), HttpGet, Route("get-course-discussion-by-id/{id}")]
        public async Task<IHttpActionResult> GetCourseDiscussionById(int id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetCourseDiscussionById(id, CurrentUser));
        }

        [Authorize(Roles = "Trainee, Admin, Guest"), HttpPost, Route("discussion-save")]
        public async Task<IHttpActionResult> CourseDiscussionSave(CourseDiscussionModel model)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.CourseDiscussionSave(model, CurrentUser));
        }

        [Authorize(Roles = " Admin"), HttpGet, Route("discussion-delete/{id}")]
        public async Task<IHttpActionResult> DeleteCourseDiscussion(long id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.DeleteCourseDiscussion(id));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("enroll/{id}")]
        public async Task<IHttpActionResult> EnrollCourse(Guid id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.EnrollCourse(id, CurrentUser));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("bookmark-or-unbookmark/{id}")]
        public async Task<IHttpActionResult> BookmarkOrUnbookmarkCourse(Guid id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.BookmarkOrUnbookmarkCourse(id, CurrentUser));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-video-left-history/{contentId}")]
        public async Task<IHttpActionResult> VideoLeftHistory(Guid contentId)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.VideoLeftHistory(contentId, CurrentUser));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("content-study")]
        public async Task<IHttpActionResult> CourseContentStudied(Guid materialId, int studyTimeSec)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.CourseContentStudied(materialId, studyTimeSec, CurrentUser));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-faqs/{id}")]
        public async Task<IHttpActionResult> GetCourseFAQs(Guid id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetCourseFAQs(id));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-feedback-questions/{id}")]
        public async Task<IHttpActionResult> GetFeedbackQuestions(Guid id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetFeedbackQuestions(id, CurrentUser));
        }
        #endregion

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("my-progress")]
        public async Task<IHttpActionResult> GetMyCourseProgress(string name, long? categoryId, int size, int pageNumber)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetMyCourseProgress(name, categoryId, size, pageNumber, CurrentUser));
        }
        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-enrolledandavailable-course")]
        public async Task<IHttpActionResult> GetEnrolledAndAvailalbleCourse(string name, long? categoryId, int size, int pageNumber,int? courseStatus = null)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.GetEnrolledAndAvailalbleCourse(name, categoryId, size, pageNumber, courseStatus, CurrentUser));
        }
        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("download-certificate/{id}")]
        public async Task<HttpResponseMessage> GetEmployeeCourseCertificate(Guid id)
        {
            // Create result variable

            try
            {
                var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);

                // Create result variable
                HttpResponseMessage result = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new ByteArrayContent(await _service.GetCourseCertificate(id, CurrentUser))
                };

                result.Content.Headers.ContentDisposition =
                new ContentDispositionHeaderValue("attachment")
                {
                    FileName = "Certificate.pdf"
                };
                // Determine the type of content for the browser
                result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
                return result;
            }
            // We handle exceptions
            catch (Exception ex)
            {
                var result = new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent(ex.Message),
                    ReasonPhrase = "ERROR!"
                };
                return result;
            }
        }
        #endregion

        [Authorize(Roles = "Admin"), HttpGet, Route("has-certificate-exam/{id}")]
        public async Task<IHttpActionResult> HasCertificateExam(Guid id)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.HasCertificateExam(id));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("download-course-enrollment-sample-file")]
        public HttpResponseMessage DownloadSampleCourseEnrolment()
        {
            if (!File.Exists(Generator.UploadSampleCourseEnrolment))
            {
                return Request.CreateResponse(HttpStatusCode.NotFound);
            }
            else
            {
                HttpResponseMessage result = Request.CreateResponse(HttpStatusCode.OK);
                result.Content = new StreamContent(File.OpenRead(Generator.UploadSampleCourseEnrolment));
                result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
                return result;
            }
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-course-progress-report-excel")]
        public async Task<HttpResponseMessage> GetCourseProgressReportExcel(Guid? courseId, long? divisionId, DateTime? startDate, DateTime? endDate, ReportType reportType)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            byte[] byteArray = reportType == ReportType.Excel ? await _service.GetCourseProgressReportExcel(courseId, divisionId, startDate, endDate) : await _service.GetCourseProgressReportPdf(courseId, divisionId, startDate, endDate);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.
            //response.Content.Headers.Add("x-filename", "Student_List.xls");
            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-grade-summary-report-pdf")]
        public async Task<HttpResponseMessage> GetGradeSummaryPDF(Guid? courseId, long? divisionId, DateTime? startDate, DateTime? endDate)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            byte[] byteArray = await _service.GetGradeSummaryPDF(courseId, divisionId, startDate, endDate);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.
            //response.Content.Headers.Add("x-filename", "Student_List.xls");
            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
            //    return response;
            return response;

        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-grade-summary-report-excel")]
        public async Task<HttpResponseMessage> GetGradeSummaryExcel(Guid? courseId, long? divisionId, DateTime? startDate, DateTime? endDate, ReportType reportType)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            byte[] byteArray = reportType == ReportType.Excel ? await _service.GetGradeSummaryExcel(courseId, divisionId, startDate, endDate) : await _service.GetGradeSummaryPDF(courseId, divisionId, startDate, endDate);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }

        [AllowAnonymous, HttpGet, Route("get-trainee-course-history-report-excel")]
        public async Task<HttpResponseMessage> GetTraineeCourseHistoryReportExcel(Guid? courseId, long? divisionId, DateTime? startDate, DateTime? endDate, ReportType reportType)
        {
            var _service = new CourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            byte[] byteArray = reportType == ReportType.Excel ? await _service.GetTraineeCourseHistoryReportExcel(courseId, divisionId, startDate, endDate) : await _service.GetTraineeCourseHistoryReportPdf(courseId , divisionId, startDate, endDate);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.
            //response.Content.Headers.Add("x-filename", "Student_List.xls");
            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");

            //    return response;
            return response;

        }
    }
}
