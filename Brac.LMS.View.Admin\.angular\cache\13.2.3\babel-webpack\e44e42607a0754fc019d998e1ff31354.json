{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { BehaviorSubject } from 'rxjs';\nimport { MiniStore, MiniState } from 'ngx-bootstrap/mini-ngrx';\nimport * as i4 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nfunction TimepickerComponent_td_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0\\xA0\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TimepickerComponent_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵelementStart(1, \"a\", 1);\n    i0.ɵɵlistener(\"click\", function TimepickerComponent_td_7_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return ctx_r18.changeMinutes(ctx_r18.minuteStep);\n    });\n    i0.ɵɵelement(2, \"span\", 2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"disabled\", !ctx_r1.canIncrementMinutes || !ctx_r1.isEditable);\n  }\n}\n\nfunction TimepickerComponent_td_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TimepickerComponent_td_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵelementStart(1, \"a\", 1);\n    i0.ɵɵlistener(\"click\", function TimepickerComponent_td_9_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return ctx_r20.changeSeconds(ctx_r20.secondsStep);\n    });\n    i0.ɵɵelement(2, \"span\", 2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"disabled\", !ctx_r3.canIncrementSeconds || !ctx_r3.isEditable);\n  }\n}\n\nfunction TimepickerComponent_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0\\xA0\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TimepickerComponent_td_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\");\n  }\n}\n\nfunction TimepickerComponent_td_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0:\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TimepickerComponent_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"td\", 4);\n    i0.ɵɵelementStart(1, \"input\", 5);\n    i0.ɵɵlistener(\"wheel\", function TimepickerComponent_td_16_Template_input_wheel_1_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      ctx_r22.prevDef($event);\n      return ctx_r22.changeMinutes(ctx_r22.minuteStep * ctx_r22.wheelSign($event), \"wheel\");\n    })(\"keydown.ArrowUp\", function TimepickerComponent_td_16_Template_input_keydown_ArrowUp_1_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return ctx_r24.changeMinutes(ctx_r24.minuteStep, \"key\");\n    })(\"keydown.ArrowDown\", function TimepickerComponent_td_16_Template_input_keydown_ArrowDown_1_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return ctx_r25.changeMinutes(-ctx_r25.minuteStep, \"key\");\n    })(\"change\", function TimepickerComponent_td_16_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return ctx_r26.updateMinutes($event.target);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"has-error\", ctx_r7.invalidMinutes);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r7.invalidMinutes);\n    i0.ɵɵproperty(\"placeholder\", ctx_r7.minutesPlaceholder)(\"readonly\", ctx_r7.readonlyInput)(\"disabled\", ctx_r7.disabled)(\"value\", ctx_r7.minutes);\n    i0.ɵɵattribute(\"aria-label\", ctx_r7.labelMinutes);\n  }\n}\n\nfunction TimepickerComponent_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0:\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TimepickerComponent_td_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"td\", 4);\n    i0.ɵɵelementStart(1, \"input\", 5);\n    i0.ɵɵlistener(\"wheel\", function TimepickerComponent_td_18_Template_input_wheel_1_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      ctx_r27.prevDef($event);\n      return ctx_r27.changeSeconds(ctx_r27.secondsStep * ctx_r27.wheelSign($event), \"wheel\");\n    })(\"keydown.ArrowUp\", function TimepickerComponent_td_18_Template_input_keydown_ArrowUp_1_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return ctx_r29.changeSeconds(ctx_r29.secondsStep, \"key\");\n    })(\"keydown.ArrowDown\", function TimepickerComponent_td_18_Template_input_keydown_ArrowDown_1_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return ctx_r30.changeSeconds(-ctx_r30.secondsStep, \"key\");\n    })(\"change\", function TimepickerComponent_td_18_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return ctx_r31.updateSeconds($event.target);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"has-error\", ctx_r9.invalidSeconds);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r9.invalidSeconds);\n    i0.ɵɵproperty(\"placeholder\", ctx_r9.secondsPlaceholder)(\"readonly\", ctx_r9.readonlyInput)(\"disabled\", ctx_r9.disabled)(\"value\", ctx_r9.seconds);\n    i0.ɵɵattribute(\"aria-label\", ctx_r9.labelSeconds);\n  }\n}\n\nfunction TimepickerComponent_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0\\xA0\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TimepickerComponent_td_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵelementStart(1, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function TimepickerComponent_td_20_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return ctx_r32.toggleMeridian();\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"disabled\", !ctx_r11.isEditable || !ctx_r11.canToggleMeridian);\n    i0.ɵɵproperty(\"disabled\", !ctx_r11.isEditable || !ctx_r11.canToggleMeridian);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r11.meridian, \" \");\n  }\n}\n\nfunction TimepickerComponent_td_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0\\xA0\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TimepickerComponent_td_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵelementStart(1, \"a\", 1);\n    i0.ɵɵlistener(\"click\", function TimepickerComponent_td_26_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return ctx_r34.changeMinutes(-ctx_r34.minuteStep);\n    });\n    i0.ɵɵelement(2, \"span\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"disabled\", !ctx_r13.canDecrementMinutes || !ctx_r13.isEditable);\n  }\n}\n\nfunction TimepickerComponent_td_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TimepickerComponent_td_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵelementStart(1, \"a\", 1);\n    i0.ɵɵlistener(\"click\", function TimepickerComponent_td_28_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return ctx_r36.changeSeconds(-ctx_r36.secondsStep);\n    });\n    i0.ɵɵelement(2, \"span\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"disabled\", !ctx_r15.canDecrementSeconds || !ctx_r15.isEditable);\n  }\n}\n\nfunction TimepickerComponent_td_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1, \"\\xA0\\xA0\\xA0\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TimepickerComponent_td_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\");\n  }\n}\n\nconst dex = 10;\nconst hoursPerDay = 24;\nconst hoursPerDayHalf = 12;\nconst minutesPerHour = 60;\nconst secondsPerMinute = 60;\n\nfunction isValidDate(value) {\n  if (!value) {\n    return false;\n  }\n\n  if (value instanceof Date && isNaN(value.getHours())) {\n    return false;\n  }\n\n  if (typeof value === 'string') {\n    return isValidDate(new Date(value));\n  }\n\n  return true;\n}\n\nfunction isValidLimit(controls, newDate) {\n  if (controls.min && newDate < controls.min) {\n    return false;\n  }\n\n  if (controls.max && newDate > controls.max) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction toNumber(value) {\n  if (typeof value === 'undefined') {\n    return NaN;\n  }\n\n  if (typeof value === 'number') {\n    return value;\n  }\n\n  return parseInt(value, dex);\n}\n\nfunction isNumber(value) {\n  return !isNaN(toNumber(value));\n}\n\nfunction parseHours(value, isPM = false) {\n  const hour = toNumber(value);\n\n  if (isNaN(hour) || hour < 0 || hour > (isPM ? hoursPerDayHalf : hoursPerDay)) {\n    return NaN;\n  }\n\n  return hour;\n}\n\nfunction parseMinutes(value) {\n  const minute = toNumber(value);\n\n  if (isNaN(minute) || minute < 0 || minute > minutesPerHour) {\n    return NaN;\n  }\n\n  return minute;\n}\n\nfunction parseSeconds(value) {\n  const seconds = toNumber(value);\n\n  if (isNaN(seconds) || seconds < 0 || seconds > secondsPerMinute) {\n    return NaN;\n  }\n\n  return seconds;\n}\n\nfunction parseTime(value) {\n  if (typeof value === 'string') {\n    return new Date(value);\n  }\n\n  return value;\n}\n\nfunction changeTime(value, diff) {\n  if (!value) {\n    return changeTime(createDate(new Date(), 0, 0, 0), diff);\n  }\n\n  if (!diff) {\n    return value;\n  }\n\n  let hour = value.getHours();\n  let minutes = value.getMinutes();\n  let seconds = value.getSeconds();\n\n  if (diff.hour) {\n    hour = hour + toNumber(diff.hour);\n  }\n\n  if (diff.minute) {\n    minutes = minutes + toNumber(diff.minute);\n  }\n\n  if (diff.seconds) {\n    seconds = seconds + toNumber(diff.seconds);\n  }\n\n  return createDate(value, hour, minutes, seconds);\n}\n\nfunction setTime(value, opts) {\n  let hour = parseHours(opts.hour);\n  const minute = parseMinutes(opts.minute);\n  const seconds = parseSeconds(opts.seconds) || 0;\n\n  if (opts.isPM && hour !== 12) {\n    hour += hoursPerDayHalf;\n  }\n\n  if (!value) {\n    if (!isNaN(hour) && !isNaN(minute)) {\n      return createDate(new Date(), hour, minute, seconds);\n    }\n\n    return value;\n  }\n\n  if (isNaN(hour) || isNaN(minute)) {\n    return value;\n  }\n\n  return createDate(value, hour, minute, seconds);\n}\n\nfunction createDate(value, hours, minutes, seconds) {\n  const newValue = new Date(value.getFullYear(), value.getMonth(), value.getDate(), hours, minutes, seconds, value.getMilliseconds()); // #3139 ensure date part remains unchanged\n\n  newValue.setFullYear(value.getFullYear());\n  newValue.setMonth(value.getMonth());\n  newValue.setDate(value.getDate());\n  return newValue;\n}\n\nfunction padNumber(value) {\n  const _value = value.toString();\n\n  if (_value.length > 1) {\n    return _value;\n  }\n\n  return `0${_value}`;\n}\n\nfunction isHourInputValid(hours, isPM) {\n  return !isNaN(parseHours(hours, isPM));\n}\n\nfunction isMinuteInputValid(minutes) {\n  return !isNaN(parseMinutes(minutes));\n}\n\nfunction isSecondInputValid(seconds) {\n  return !isNaN(parseSeconds(seconds));\n}\n\nfunction isInputLimitValid(diff, max, min) {\n  const newDate = setTime(new Date(), diff);\n\n  if (!newDate) {\n    return false;\n  }\n\n  if (max && newDate > max) {\n    return false;\n  }\n\n  if (min && newDate < min) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction isOneOfDatesEmpty(hours, minutes, seconds) {\n  return hours.length === 0 || minutes.length === 0 || seconds.length === 0;\n}\n\nfunction isInputValid(hours, minutes = '0', seconds = '0', isPM) {\n  return isHourInputValid(hours, isPM) && isMinuteInputValid(minutes) && isSecondInputValid(seconds);\n}\n\nfunction canChangeValue(state, event) {\n  if (state.readonlyInput || state.disabled) {\n    return false;\n  }\n\n  if (event) {\n    if (event.source === 'wheel' && !state.mousewheel) {\n      return false;\n    }\n\n    if (event.source === 'key' && !state.arrowkeys) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction canChangeHours(event, controls) {\n  if (!event.step) {\n    return false;\n  }\n\n  if (event.step > 0 && !controls.canIncrementHours) {\n    return false;\n  }\n\n  if (event.step < 0 && !controls.canDecrementHours) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction canChangeMinutes(event, controls) {\n  if (!event.step) {\n    return false;\n  }\n\n  if (event.step > 0 && !controls.canIncrementMinutes) {\n    return false;\n  }\n\n  if (event.step < 0 && !controls.canDecrementMinutes) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction canChangeSeconds(event, controls) {\n  if (!event.step) {\n    return false;\n  }\n\n  if (event.step > 0 && !controls.canIncrementSeconds) {\n    return false;\n  }\n\n  if (event.step < 0 && !controls.canDecrementSeconds) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction getControlsValue(state) {\n  const {\n    hourStep,\n    minuteStep,\n    secondsStep,\n    readonlyInput,\n    disabled,\n    mousewheel,\n    arrowkeys,\n    showSpinners,\n    showMeridian,\n    showSeconds,\n    meridians,\n    min,\n    max\n  } = state;\n  return {\n    hourStep,\n    minuteStep,\n    secondsStep,\n    readonlyInput,\n    disabled,\n    mousewheel,\n    arrowkeys,\n    showSpinners,\n    showMeridian,\n    showSeconds,\n    meridians,\n    min,\n    max\n  };\n}\n\nfunction timepickerControls(value, state) {\n  const hoursPerDay = 24;\n  const hoursPerDayHalf = 12;\n  const {\n    min,\n    max,\n    hourStep,\n    minuteStep,\n    secondsStep,\n    showSeconds\n  } = state;\n  const res = {\n    canIncrementHours: true,\n    canIncrementMinutes: true,\n    canIncrementSeconds: true,\n    canDecrementHours: true,\n    canDecrementMinutes: true,\n    canDecrementSeconds: true,\n    canToggleMeridian: true\n  };\n\n  if (!value) {\n    return res;\n  } // compare dates\n\n\n  if (max) {\n    const _newHour = changeTime(value, {\n      hour: hourStep\n    });\n\n    res.canIncrementHours = max > _newHour && value.getHours() + hourStep < hoursPerDay;\n\n    if (!res.canIncrementHours) {\n      const _newMinutes = changeTime(value, {\n        minute: minuteStep\n      });\n\n      res.canIncrementMinutes = showSeconds ? max > _newMinutes : max >= _newMinutes;\n    }\n\n    if (!res.canIncrementMinutes) {\n      const _newSeconds = changeTime(value, {\n        seconds: secondsStep\n      });\n\n      res.canIncrementSeconds = max >= _newSeconds;\n    }\n\n    if (value.getHours() < hoursPerDayHalf) {\n      res.canToggleMeridian = changeTime(value, {\n        hour: hoursPerDayHalf\n      }) < max;\n    }\n  }\n\n  if (min) {\n    const _newHour = changeTime(value, {\n      hour: -hourStep\n    });\n\n    res.canDecrementHours = min < _newHour;\n\n    if (!res.canDecrementHours) {\n      const _newMinutes = changeTime(value, {\n        minute: -minuteStep\n      });\n\n      res.canDecrementMinutes = showSeconds ? min < _newMinutes : min <= _newMinutes;\n    }\n\n    if (!res.canDecrementMinutes) {\n      const _newSeconds = changeTime(value, {\n        seconds: -secondsStep\n      });\n\n      res.canDecrementSeconds = min <= _newSeconds;\n    }\n\n    if (value.getHours() >= hoursPerDayHalf) {\n      res.canToggleMeridian = changeTime(value, {\n        hour: -hoursPerDayHalf\n      }) > min;\n    }\n  }\n\n  return res;\n}\n/** Provides default configuration values for timepicker */\n\n\nclass TimepickerConfig {\n  constructor() {\n    /** hours change step */\n    this.hourStep = 1;\n    /** minutes change step */\n\n    this.minuteStep = 5;\n    /** seconds changes step */\n\n    this.secondsStep = 10;\n    /** if true works in 12H mode and displays AM/PM. If false works in 24H mode and hides AM/PM */\n\n    this.showMeridian = true;\n    /** meridian labels based on locale */\n\n    this.meridians = ['AM', 'PM'];\n    /** if true hours and minutes fields will be readonly */\n\n    this.readonlyInput = false;\n    /** if true hours and minutes fields will be disabled */\n\n    this.disabled = false;\n    /** if true emptyTime is not marked as invalid */\n\n    this.allowEmptyTime = false;\n    /** if true scroll inside hours and minutes inputs will change time */\n\n    this.mousewheel = true;\n    /** if true the values of hours and minutes can be changed using the up/down arrow keys on the keyboard */\n\n    this.arrowkeys = true;\n    /** if true spinner arrows above and below the inputs will be shown */\n\n    this.showSpinners = true;\n    /** show seconds in timepicker */\n\n    this.showSeconds = false;\n    /** show minutes in timepicker */\n\n    this.showMinutes = true;\n    /** placeholder for hours field in timepicker */\n\n    this.hoursPlaceholder = 'HH';\n    /** placeholder for minutes field in timepicker */\n\n    this.minutesPlaceholder = 'MM';\n    /** placeholder for seconds field in timepicker */\n\n    this.secondsPlaceholder = 'SS';\n    /** hours aria label */\n\n    this.ariaLabelHours = 'hours';\n    /** minutes aria label */\n\n    this.ariaLabelMinutes = 'minutes';\n    /** seconds aria label */\n\n    this.ariaLabelSeconds = 'seconds';\n  }\n\n}\n\nTimepickerConfig.ɵfac = function TimepickerConfig_Factory(t) {\n  return new (t || TimepickerConfig)();\n};\n\nTimepickerConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TimepickerConfig,\n  factory: TimepickerConfig.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TimepickerConfig, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\nclass TimepickerActions {\n  writeValue(value) {\n    return {\n      type: TimepickerActions.WRITE_VALUE,\n      payload: value\n    };\n  }\n\n  changeHours(event) {\n    return {\n      type: TimepickerActions.CHANGE_HOURS,\n      payload: event\n    };\n  }\n\n  changeMinutes(event) {\n    return {\n      type: TimepickerActions.CHANGE_MINUTES,\n      payload: event\n    };\n  }\n\n  changeSeconds(event) {\n    return {\n      type: TimepickerActions.CHANGE_SECONDS,\n      payload: event\n    };\n  }\n\n  setTime(value) {\n    return {\n      type: TimepickerActions.SET_TIME_UNIT,\n      payload: value\n    };\n  }\n\n  updateControls(value) {\n    return {\n      type: TimepickerActions.UPDATE_CONTROLS,\n      payload: value\n    };\n  }\n\n}\n\nTimepickerActions.WRITE_VALUE = '[timepicker] write value from ng model';\nTimepickerActions.CHANGE_HOURS = '[timepicker] change hours';\nTimepickerActions.CHANGE_MINUTES = '[timepicker] change minutes';\nTimepickerActions.CHANGE_SECONDS = '[timepicker] change seconds';\nTimepickerActions.SET_TIME_UNIT = '[timepicker] set time unit';\nTimepickerActions.UPDATE_CONTROLS = '[timepicker] update controls';\n\nTimepickerActions.ɵfac = function TimepickerActions_Factory(t) {\n  return new (t || TimepickerActions)();\n};\n\nTimepickerActions.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TimepickerActions,\n  factory: TimepickerActions.ɵfac,\n  providedIn: 'platform'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TimepickerActions, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'platform'\n    }]\n  }], null, null);\n})();\n\nconst initialState = {\n  value: void 0,\n  config: new TimepickerConfig(),\n  controls: {\n    canIncrementHours: true,\n    canIncrementMinutes: true,\n    canIncrementSeconds: true,\n    canDecrementHours: true,\n    canDecrementMinutes: true,\n    canDecrementSeconds: true,\n    canToggleMeridian: true\n  }\n};\n\nfunction timepickerReducer(state = initialState, action) {\n  switch (action.type) {\n    case TimepickerActions.WRITE_VALUE:\n      {\n        return Object.assign({}, state, {\n          value: action.payload\n        });\n      }\n\n    case TimepickerActions.CHANGE_HOURS:\n      {\n        if (!canChangeValue(state.config, action.payload) || !canChangeHours(action.payload, state.controls)) {\n          return state;\n        }\n\n        const _newTime = changeTime(state.value, {\n          hour: action.payload.step\n        });\n\n        if ((state.config.max || state.config.min) && !isValidLimit(state.config, _newTime)) {\n          return state;\n        }\n\n        return Object.assign({}, state, {\n          value: _newTime\n        });\n      }\n\n    case TimepickerActions.CHANGE_MINUTES:\n      {\n        if (!canChangeValue(state.config, action.payload) || !canChangeMinutes(action.payload, state.controls)) {\n          return state;\n        }\n\n        const _newTime = changeTime(state.value, {\n          minute: action.payload.step\n        });\n\n        if ((state.config.max || state.config.min) && !isValidLimit(state.config, _newTime)) {\n          return state;\n        }\n\n        return Object.assign({}, state, {\n          value: _newTime\n        });\n      }\n\n    case TimepickerActions.CHANGE_SECONDS:\n      {\n        if (!canChangeValue(state.config, action.payload) || !canChangeSeconds(action.payload, state.controls)) {\n          return state;\n        }\n\n        const _newTime = changeTime(state.value, {\n          seconds: action.payload.step\n        });\n\n        if ((state.config.max || state.config.min) && !isValidLimit(state.config, _newTime)) {\n          return state;\n        }\n\n        return Object.assign({}, state, {\n          value: _newTime\n        });\n      }\n\n    case TimepickerActions.SET_TIME_UNIT:\n      {\n        if (!canChangeValue(state.config)) {\n          return state;\n        }\n\n        const _newTime = setTime(state.value, action.payload);\n\n        return Object.assign({}, state, {\n          value: _newTime\n        });\n      }\n\n    case TimepickerActions.UPDATE_CONTROLS:\n      {\n        const _newControlsState = timepickerControls(state.value, action.payload);\n\n        const _newState = {\n          value: state.value,\n          config: action.payload,\n          controls: _newControlsState\n        };\n\n        if (state.config.showMeridian !== _newState.config.showMeridian) {\n          if (state.value) {\n            _newState.value = new Date(state.value);\n          }\n        }\n\n        return Object.assign({}, state, _newState);\n      }\n\n    default:\n      return state;\n  }\n}\n\nclass TimepickerStore extends MiniStore {\n  constructor() {\n    const _dispatcher = new BehaviorSubject({\n      type: '[mini-ngrx] dispatcher init'\n    });\n\n    const state = new MiniState(initialState, _dispatcher, timepickerReducer);\n    super(_dispatcher, timepickerReducer, state);\n  }\n\n}\n\nTimepickerStore.ɵfac = function TimepickerStore_Factory(t) {\n  return new (t || TimepickerStore)();\n};\n\nTimepickerStore.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TimepickerStore,\n  factory: TimepickerStore.ɵfac,\n  providedIn: 'platform'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TimepickerStore, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'platform'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\n\nconst TIMEPICKER_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => TimepickerComponent),\n  multi: true\n};\n\nclass TimepickerComponent {\n  constructor(_config, _cd, _store, _timepickerActions) {\n    this._cd = _cd;\n    this._store = _store;\n    this._timepickerActions = _timepickerActions;\n    /** hours change step */\n\n    this.hourStep = 1;\n    /** minutes change step */\n\n    this.minuteStep = 5;\n    /** seconds change step */\n\n    this.secondsStep = 10;\n    /** if true hours and minutes fields will be readonly */\n\n    this.readonlyInput = false;\n    /** if true hours and minutes fields will be disabled */\n\n    this.disabled = false;\n    /** if true scroll inside hours and minutes inputs will change time */\n\n    this.mousewheel = true;\n    /** if true the values of hours and minutes can be changed using the up/down arrow keys on the keyboard */\n\n    this.arrowkeys = true;\n    /** if true spinner arrows above and below the inputs will be shown */\n\n    this.showSpinners = true;\n    /** if true meridian button will be shown */\n\n    this.showMeridian = true;\n    /** show minutes in timepicker */\n\n    this.showMinutes = true;\n    /** show seconds in timepicker */\n\n    this.showSeconds = false;\n    /** meridian labels based on locale */\n\n    this.meridians = ['AM', 'PM'];\n    /** placeholder for hours field in timepicker */\n\n    this.hoursPlaceholder = 'HH';\n    /** placeholder for minutes field in timepicker */\n\n    this.minutesPlaceholder = 'MM';\n    /** placeholder for seconds field in timepicker */\n\n    this.secondsPlaceholder = 'SS';\n    /** emits true if value is a valid date */\n\n    this.isValid = new EventEmitter();\n    /** emits value of meridian*/\n\n    this.meridianChange = new EventEmitter(); // ui variables\n\n    this.hours = '';\n    this.minutes = '';\n    this.seconds = '';\n    this.meridian = ''; // min\\max validation for input fields\n\n    this.invalidHours = false;\n    this.invalidMinutes = false;\n    this.invalidSeconds = false; // aria-label variables\n\n    this.labelHours = 'hours';\n    this.labelMinutes = 'minutes';\n    this.labelSeconds = 'seconds'; // time picker controls state\n\n    this.canIncrementHours = true;\n    this.canIncrementMinutes = true;\n    this.canIncrementSeconds = true;\n    this.canDecrementHours = true;\n    this.canDecrementMinutes = true;\n    this.canDecrementSeconds = true;\n    this.canToggleMeridian = true; // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n    this.onChange = Function.prototype; // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n    this.onTouched = Function.prototype;\n    this.config = _config;\n    Object.assign(this, this.config);\n    this.timepickerSub = _store.select(state => state.value).subscribe(value => {\n      // update UI values if date changed\n      this._renderTime(value);\n\n      this.onChange(value);\n\n      this._store.dispatch(this._timepickerActions.updateControls(getControlsValue(this)));\n    });\n\n    _store.select(state => state.controls).subscribe(controlsState => {\n      const isTimepickerInputValid = isInputValid(this.hours, this.minutes, this.seconds, this.isPM());\n      const isValid = this.config.allowEmptyTime ? this.isOneOfDatesIsEmpty() || isTimepickerInputValid : isTimepickerInputValid;\n      this.isValid.emit(isValid);\n      Object.assign(this, controlsState);\n\n      _cd.markForCheck();\n    });\n  }\n  /** @deprecated - please use `isEditable` instead */\n\n\n  get isSpinnersVisible() {\n    return this.showSpinners && !this.readonlyInput;\n  }\n\n  get isEditable() {\n    return !(this.readonlyInput || this.disabled);\n  }\n\n  resetValidation() {\n    this.invalidHours = false;\n    this.invalidMinutes = false;\n    this.invalidSeconds = false;\n  }\n\n  isPM() {\n    return this.showMeridian && this.meridian === this.meridians[1];\n  }\n\n  prevDef($event) {\n    $event.preventDefault();\n  }\n\n  wheelSign($event) {\n    return Math.sign($event.deltaY || 0) * -1;\n  }\n\n  ngOnChanges() {\n    this._store.dispatch(this._timepickerActions.updateControls(getControlsValue(this)));\n  }\n\n  changeHours(step, source = '') {\n    this.resetValidation();\n\n    this._store.dispatch(this._timepickerActions.changeHours({\n      step,\n      source\n    }));\n  }\n\n  changeMinutes(step, source = '') {\n    this.resetValidation();\n\n    this._store.dispatch(this._timepickerActions.changeMinutes({\n      step,\n      source\n    }));\n  }\n\n  changeSeconds(step, source = '') {\n    this.resetValidation();\n\n    this._store.dispatch(this._timepickerActions.changeSeconds({\n      step,\n      source\n    }));\n  }\n\n  updateHours(target) {\n    this.resetValidation();\n    this.hours = target.value;\n    const isTimepickerInputValid = isHourInputValid(this.hours, this.isPM()) && this.isValidLimit();\n    const isValid = this.config.allowEmptyTime ? this.isOneOfDatesIsEmpty() || isTimepickerInputValid : isTimepickerInputValid;\n\n    if (!isValid) {\n      this.invalidHours = true;\n      this.isValid.emit(false);\n      this.onChange(null);\n      return;\n    }\n\n    this._updateTime();\n  }\n\n  updateMinutes(target) {\n    this.resetValidation();\n    this.minutes = target.value;\n    const isTimepickerInputValid = isMinuteInputValid(this.minutes) && this.isValidLimit();\n    const isValid = this.config.allowEmptyTime ? this.isOneOfDatesIsEmpty() || isTimepickerInputValid : isTimepickerInputValid;\n\n    if (!isValid) {\n      this.invalidMinutes = true;\n      this.isValid.emit(false);\n      this.onChange(null);\n      return;\n    }\n\n    this._updateTime();\n  }\n\n  updateSeconds(target) {\n    this.resetValidation();\n    this.seconds = target.value;\n    const isTimepickerInputValid = isSecondInputValid(this.seconds) && this.isValidLimit();\n    const isValid = this.config.allowEmptyTime ? this.isOneOfDatesIsEmpty() || isTimepickerInputValid : isTimepickerInputValid;\n\n    if (!isValid) {\n      this.invalidSeconds = true;\n      this.isValid.emit(false);\n      this.onChange(null);\n      return;\n    }\n\n    this._updateTime();\n  }\n\n  isValidLimit() {\n    return isInputLimitValid({\n      hour: this.hours,\n      minute: this.minutes,\n      seconds: this.seconds,\n      isPM: this.isPM()\n    }, this.max, this.min);\n  }\n\n  isOneOfDatesIsEmpty() {\n    return isOneOfDatesEmpty(this.hours, this.minutes, this.seconds);\n  }\n\n  _updateTime() {\n    const _seconds = this.showSeconds ? this.seconds : void 0;\n\n    const _minutes = this.showMinutes ? this.minutes : void 0;\n\n    const isTimepickerInputValid = isInputValid(this.hours, _minutes, _seconds, this.isPM());\n    const isValid = this.config.allowEmptyTime ? this.isOneOfDatesIsEmpty() || isTimepickerInputValid : isTimepickerInputValid;\n\n    if (!isValid) {\n      this.isValid.emit(false);\n      this.onChange(null);\n      return;\n    }\n\n    this._store.dispatch(this._timepickerActions.setTime({\n      hour: this.hours,\n      minute: this.minutes,\n      seconds: this.seconds,\n      isPM: this.isPM()\n    }));\n  }\n\n  toggleMeridian() {\n    if (!this.showMeridian || !this.isEditable) {\n      return;\n    }\n\n    const _hoursPerDayHalf = 12;\n\n    this._store.dispatch(this._timepickerActions.changeHours({\n      step: _hoursPerDayHalf,\n      source: ''\n    }));\n  }\n  /**\n   * Write a new value to the element.\n   */\n\n\n  writeValue(obj) {\n    if (isValidDate(obj)) {\n      this.resetValidation();\n\n      this._store.dispatch(this._timepickerActions.writeValue(parseTime(obj)));\n    } else if (obj == null) {\n      this._store.dispatch(this._timepickerActions.writeValue());\n    }\n  }\n  /**\n   * Set the function to be called when the control receives a change event.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  /**\n   * Set the function to be called when the control receives a touch event.\n   */\n\n\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  /**\n   * This function is called when the control status changes to or from \"disabled\".\n   * Depending on the value, it will enable or disable the appropriate DOM element.\n   *\n   * @param isDisabled\n   */\n\n\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n\n    this._cd.markForCheck();\n  }\n\n  ngOnDestroy() {\n    var _a;\n\n    (_a = this.timepickerSub) === null || _a === void 0 ? void 0 : _a.unsubscribe();\n  }\n\n  _renderTime(value) {\n    if (!value || !isValidDate(value)) {\n      this.hours = '';\n      this.minutes = '';\n      this.seconds = '';\n      this.meridian = this.meridians[0];\n      this.meridianChange.emit(this.meridian);\n      return;\n    }\n\n    const _value = parseTime(value);\n\n    if (!_value) {\n      return;\n    }\n\n    const _hoursPerDayHalf = 12;\n\n    let _hours = _value.getHours();\n\n    if (this.showMeridian) {\n      this.meridian = this.meridians[_hours >= _hoursPerDayHalf ? 1 : 0];\n      this.meridianChange.emit(this.meridian);\n      _hours = _hours % _hoursPerDayHalf; // should be 12 PM, not 00 PM\n\n      if (_hours === 0) {\n        _hours = _hoursPerDayHalf;\n      }\n    }\n\n    this.hours = padNumber(_hours);\n    this.minutes = padNumber(_value.getMinutes());\n    this.seconds = padNumber(_value.getUTCSeconds());\n  }\n\n}\n\nTimepickerComponent.ɵfac = function TimepickerComponent_Factory(t) {\n  return new (t || TimepickerComponent)(i0.ɵɵdirectiveInject(TimepickerConfig), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(TimepickerStore), i0.ɵɵdirectiveInject(TimepickerActions));\n};\n\nTimepickerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TimepickerComponent,\n  selectors: [[\"timepicker\"]],\n  inputs: {\n    hourStep: \"hourStep\",\n    minuteStep: \"minuteStep\",\n    secondsStep: \"secondsStep\",\n    readonlyInput: \"readonlyInput\",\n    disabled: \"disabled\",\n    mousewheel: \"mousewheel\",\n    arrowkeys: \"arrowkeys\",\n    showSpinners: \"showSpinners\",\n    showMeridian: \"showMeridian\",\n    showMinutes: \"showMinutes\",\n    showSeconds: \"showSeconds\",\n    meridians: \"meridians\",\n    min: \"min\",\n    max: \"max\",\n    hoursPlaceholder: \"hoursPlaceholder\",\n    minutesPlaceholder: \"minutesPlaceholder\",\n    secondsPlaceholder: \"secondsPlaceholder\"\n  },\n  outputs: {\n    isValid: \"isValid\",\n    meridianChange: \"meridianChange\"\n  },\n  features: [i0.ɵɵProvidersFeature([TIMEPICKER_CONTROL_VALUE_ACCESSOR, TimepickerStore]), i0.ɵɵNgOnChangesFeature],\n  decls: 31,\n  vars: 33,\n  consts: [[1, \"text-center\", 3, \"hidden\"], [1, \"btn\", \"btn-link\", 3, \"click\"], [1, \"bs-chevron\", \"bs-chevron-up\"], [4, \"ngIf\"], [1, \"form-group\", \"mb-3\"], [\"type\", \"text\", \"maxlength\", \"2\", 1, \"form-control\", \"text-center\", \"bs-timepicker-field\", 3, \"placeholder\", \"readonly\", \"disabled\", \"value\", \"wheel\", \"keydown.ArrowUp\", \"keydown.ArrowDown\", \"change\"], [\"class\", \"form-group mb-3\", 3, \"has-error\", 4, \"ngIf\"], [1, \"bs-chevron\", \"bs-chevron-down\"], [\"type\", \"button\", 1, \"btn\", \"btn-default\", \"text-center\", 3, \"disabled\", \"click\"]],\n  template: function TimepickerComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"table\");\n      i0.ɵɵelementStart(1, \"tbody\");\n      i0.ɵɵelementStart(2, \"tr\", 0);\n      i0.ɵɵelementStart(3, \"td\");\n      i0.ɵɵelementStart(4, \"a\", 1);\n      i0.ɵɵlistener(\"click\", function TimepickerComponent_Template_a_click_4_listener() {\n        return ctx.changeHours(ctx.hourStep);\n      });\n      i0.ɵɵelement(5, \"span\", 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(6, TimepickerComponent_td_6_Template, 2, 0, \"td\", 3);\n      i0.ɵɵtemplate(7, TimepickerComponent_td_7_Template, 3, 2, \"td\", 3);\n      i0.ɵɵtemplate(8, TimepickerComponent_td_8_Template, 2, 0, \"td\", 3);\n      i0.ɵɵtemplate(9, TimepickerComponent_td_9_Template, 3, 2, \"td\", 3);\n      i0.ɵɵtemplate(10, TimepickerComponent_td_10_Template, 2, 0, \"td\", 3);\n      i0.ɵɵtemplate(11, TimepickerComponent_td_11_Template, 1, 0, \"td\", 3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(12, \"tr\");\n      i0.ɵɵelementStart(13, \"td\", 4);\n      i0.ɵɵelementStart(14, \"input\", 5);\n      i0.ɵɵlistener(\"wheel\", function TimepickerComponent_Template_input_wheel_14_listener($event) {\n        ctx.prevDef($event);\n        return ctx.changeHours(ctx.hourStep * ctx.wheelSign($event), \"wheel\");\n      })(\"keydown.ArrowUp\", function TimepickerComponent_Template_input_keydown_ArrowUp_14_listener() {\n        return ctx.changeHours(ctx.hourStep, \"key\");\n      })(\"keydown.ArrowDown\", function TimepickerComponent_Template_input_keydown_ArrowDown_14_listener() {\n        return ctx.changeHours(-ctx.hourStep, \"key\");\n      })(\"change\", function TimepickerComponent_Template_input_change_14_listener($event) {\n        return ctx.updateHours($event.target);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(15, TimepickerComponent_td_15_Template, 2, 0, \"td\", 3);\n      i0.ɵɵtemplate(16, TimepickerComponent_td_16_Template, 2, 9, \"td\", 6);\n      i0.ɵɵtemplate(17, TimepickerComponent_td_17_Template, 2, 0, \"td\", 3);\n      i0.ɵɵtemplate(18, TimepickerComponent_td_18_Template, 2, 9, \"td\", 6);\n      i0.ɵɵtemplate(19, TimepickerComponent_td_19_Template, 2, 0, \"td\", 3);\n      i0.ɵɵtemplate(20, TimepickerComponent_td_20_Template, 3, 4, \"td\", 3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(21, \"tr\", 0);\n      i0.ɵɵelementStart(22, \"td\");\n      i0.ɵɵelementStart(23, \"a\", 1);\n      i0.ɵɵlistener(\"click\", function TimepickerComponent_Template_a_click_23_listener() {\n        return ctx.changeHours(-ctx.hourStep);\n      });\n      i0.ɵɵelement(24, \"span\", 7);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(25, TimepickerComponent_td_25_Template, 2, 0, \"td\", 3);\n      i0.ɵɵtemplate(26, TimepickerComponent_td_26_Template, 3, 2, \"td\", 3);\n      i0.ɵɵtemplate(27, TimepickerComponent_td_27_Template, 2, 0, \"td\", 3);\n      i0.ɵɵtemplate(28, TimepickerComponent_td_28_Template, 3, 2, \"td\", 3);\n      i0.ɵɵtemplate(29, TimepickerComponent_td_29_Template, 2, 0, \"td\", 3);\n      i0.ɵɵtemplate(30, TimepickerComponent_td_30_Template, 1, 0, \"td\", 3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"hidden\", !ctx.showSpinners);\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassProp(\"disabled\", !ctx.canIncrementHours || !ctx.isEditable);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.showMinutes);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showMinutes);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showSeconds);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showSeconds);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showMeridian);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showMeridian);\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassProp(\"has-error\", ctx.invalidHours);\n      i0.ɵɵadvance(1);\n      i0.ɵɵclassProp(\"is-invalid\", ctx.invalidHours);\n      i0.ɵɵproperty(\"placeholder\", ctx.hoursPlaceholder)(\"readonly\", ctx.readonlyInput)(\"disabled\", ctx.disabled)(\"value\", ctx.hours);\n      i0.ɵɵattribute(\"aria-label\", ctx.labelHours);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showMinutes);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showMinutes);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showSeconds);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showSeconds);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showMeridian);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showMeridian);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"hidden\", !ctx.showSpinners);\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassProp(\"disabled\", !ctx.canDecrementHours || !ctx.isEditable);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.showMinutes);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showMinutes);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showSeconds);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showSeconds);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showMeridian);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showMeridian);\n    }\n  },\n  directives: [i4.NgIf],\n  styles: [\".bs-chevron{border-style:solid;display:block;width:9px;height:9px;position:relative;border-width:3px 0px 0 3px}.bs-chevron-up{transform:rotate(45deg);top:2px}.bs-chevron-down{transform:rotate(-135deg);top:-2px}.bs-timepicker-field{width:65px;padding:.375rem .55rem}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TimepickerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'timepicker',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [TIMEPICKER_CONTROL_VALUE_ACCESSOR, TimepickerStore],\n      styles: [`\n    .bs-chevron {\n      border-style: solid;\n      display: block;\n      width: 9px;\n      height: 9px;\n      position: relative;\n      border-width: 3px 0px 0 3px;\n    }\n\n    .bs-chevron-up {\n      -webkit-transform: rotate(45deg);\n      transform: rotate(45deg);\n      top: 2px;\n    }\n\n    .bs-chevron-down {\n      -webkit-transform: rotate(-135deg);\n      transform: rotate(-135deg);\n      top: -2px;\n    }\n\n    .bs-timepicker-field {\n      width: 65px;\n      padding: .375rem .55rem;\n    }\n  `],\n      encapsulation: ViewEncapsulation.None,\n      template: \"<table>\\n  <tbody>\\n  <tr class=\\\"text-center\\\" [hidden]=\\\"!showSpinners\\\">\\n    <!-- increment hours button-->\\n    <td>\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canIncrementHours || !isEditable\\\"\\n         (click)=\\\"changeHours(hourStep)\\\"\\n      ><span class=\\\"bs-chevron bs-chevron-up\\\"></span></a>\\n    </td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showMinutes\\\">&nbsp;&nbsp;&nbsp;</td>\\n    <!-- increment minutes button -->\\n    <td *ngIf=\\\"showMinutes\\\">\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canIncrementMinutes || !isEditable\\\"\\n         (click)=\\\"changeMinutes(minuteStep)\\\"\\n      ><span class=\\\"bs-chevron bs-chevron-up\\\"></span></a>\\n    </td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showSeconds\\\">&nbsp;</td>\\n    <!-- increment seconds button -->\\n    <td *ngIf=\\\"showSeconds\\\">\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canIncrementSeconds || !isEditable\\\"\\n         (click)=\\\"changeSeconds(secondsStep)\\\">\\n        <span class=\\\"bs-chevron bs-chevron-up\\\"></span>\\n      </a>\\n    </td>\\n    <!-- space between -->\\n    <td *ngIf=\\\"showMeridian\\\">&nbsp;&nbsp;&nbsp;</td>\\n    <!-- meridian placeholder-->\\n    <td *ngIf=\\\"showMeridian\\\"></td>\\n  </tr>\\n  <tr>\\n    <!-- hours -->\\n    <td class=\\\"form-group mb-3\\\" [class.has-error]=\\\"invalidHours\\\">\\n      <input type=\\\"text\\\" [class.is-invalid]=\\\"invalidHours\\\"\\n             class=\\\"form-control text-center bs-timepicker-field\\\"\\n             [placeholder]=\\\"hoursPlaceholder\\\"\\n             maxlength=\\\"2\\\"\\n             [readonly]=\\\"readonlyInput\\\"\\n             [disabled]=\\\"disabled\\\"\\n             [value]=\\\"hours\\\"\\n             (wheel)=\\\"prevDef($event);changeHours(hourStep * wheelSign($event), 'wheel')\\\"\\n             (keydown.ArrowUp)=\\\"changeHours(hourStep, 'key')\\\"\\n             (keydown.ArrowDown)=\\\"changeHours(-hourStep, 'key')\\\"\\n             (change)=\\\"updateHours($event.target)\\\" [attr.aria-label]=\\\"labelHours\\\"></td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showMinutes\\\">&nbsp;:&nbsp;</td>\\n    <!-- minutes -->\\n    <td class=\\\"form-group mb-3\\\" *ngIf=\\\"showMinutes\\\" [class.has-error]=\\\"invalidMinutes\\\">\\n      <input type=\\\"text\\\" [class.is-invalid]=\\\"invalidMinutes\\\"\\n             class=\\\"form-control text-center bs-timepicker-field\\\"\\n             [placeholder]=\\\"minutesPlaceholder\\\"\\n             maxlength=\\\"2\\\"\\n             [readonly]=\\\"readonlyInput\\\"\\n             [disabled]=\\\"disabled\\\"\\n             [value]=\\\"minutes\\\"\\n             (wheel)=\\\"prevDef($event);changeMinutes(minuteStep * wheelSign($event), 'wheel')\\\"\\n             (keydown.ArrowUp)=\\\"changeMinutes(minuteStep, 'key')\\\"\\n             (keydown.ArrowDown)=\\\"changeMinutes(-minuteStep, 'key')\\\"\\n             (change)=\\\"updateMinutes($event.target)\\\" [attr.aria-label]=\\\"labelMinutes\\\">\\n    </td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showSeconds\\\">&nbsp;:&nbsp;</td>\\n    <!-- seconds -->\\n    <td class=\\\"form-group mb-3\\\" *ngIf=\\\"showSeconds\\\" [class.has-error]=\\\"invalidSeconds\\\">\\n      <input type=\\\"text\\\" [class.is-invalid]=\\\"invalidSeconds\\\"\\n             class=\\\"form-control text-center bs-timepicker-field\\\"\\n             [placeholder]=\\\"secondsPlaceholder\\\"\\n             maxlength=\\\"2\\\"\\n             [readonly]=\\\"readonlyInput\\\"\\n             [disabled]=\\\"disabled\\\"\\n             [value]=\\\"seconds\\\"\\n             (wheel)=\\\"prevDef($event);changeSeconds(secondsStep * wheelSign($event), 'wheel')\\\"\\n             (keydown.ArrowUp)=\\\"changeSeconds(secondsStep, 'key')\\\"\\n             (keydown.ArrowDown)=\\\"changeSeconds(-secondsStep, 'key')\\\"\\n             (change)=\\\"updateSeconds($event.target)\\\" [attr.aria-label]=\\\"labelSeconds\\\">\\n    </td>\\n    <!-- space between -->\\n    <td *ngIf=\\\"showMeridian\\\">&nbsp;&nbsp;&nbsp;</td>\\n    <!-- meridian -->\\n    <td *ngIf=\\\"showMeridian\\\">\\n      <button type=\\\"button\\\" class=\\\"btn btn-default text-center\\\"\\n              [disabled]=\\\"!isEditable || !canToggleMeridian\\\"\\n              [class.disabled]=\\\"!isEditable || !canToggleMeridian\\\"\\n              (click)=\\\"toggleMeridian()\\\"\\n      >{{ meridian }}\\n      </button>\\n    </td>\\n  </tr>\\n  <tr class=\\\"text-center\\\" [hidden]=\\\"!showSpinners\\\">\\n    <!-- decrement hours button-->\\n    <td>\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canDecrementHours || !isEditable\\\"\\n         (click)=\\\"changeHours(-hourStep)\\\">\\n        <span class=\\\"bs-chevron bs-chevron-down\\\"></span>\\n      </a>\\n    </td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showMinutes\\\">&nbsp;&nbsp;&nbsp;</td>\\n    <!-- decrement minutes button-->\\n    <td *ngIf=\\\"showMinutes\\\">\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canDecrementMinutes || !isEditable\\\"\\n         (click)=\\\"changeMinutes(-minuteStep)\\\">\\n        <span class=\\\"bs-chevron bs-chevron-down\\\"></span>\\n      </a>\\n    </td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showSeconds\\\">&nbsp;</td>\\n    <!-- decrement seconds button-->\\n    <td *ngIf=\\\"showSeconds\\\">\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canDecrementSeconds || !isEditable\\\"\\n         (click)=\\\"changeSeconds(-secondsStep)\\\">\\n        <span class=\\\"bs-chevron bs-chevron-down\\\"></span>\\n      </a>\\n    </td>\\n    <!-- space between -->\\n    <td *ngIf=\\\"showMeridian\\\">&nbsp;&nbsp;&nbsp;</td>\\n    <!-- meridian placeholder-->\\n    <td *ngIf=\\\"showMeridian\\\"></td>\\n  </tr>\\n  </tbody>\\n</table>\\n\"\n    }]\n  }], function () {\n    return [{\n      type: TimepickerConfig\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: TimepickerStore\n    }, {\n      type: TimepickerActions\n    }];\n  }, {\n    hourStep: [{\n      type: Input\n    }],\n    minuteStep: [{\n      type: Input\n    }],\n    secondsStep: [{\n      type: Input\n    }],\n    readonlyInput: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    mousewheel: [{\n      type: Input\n    }],\n    arrowkeys: [{\n      type: Input\n    }],\n    showSpinners: [{\n      type: Input\n    }],\n    showMeridian: [{\n      type: Input\n    }],\n    showMinutes: [{\n      type: Input\n    }],\n    showSeconds: [{\n      type: Input\n    }],\n    meridians: [{\n      type: Input\n    }],\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    hoursPlaceholder: [{\n      type: Input\n    }],\n    minutesPlaceholder: [{\n      type: Input\n    }],\n    secondsPlaceholder: [{\n      type: Input\n    }],\n    isValid: [{\n      type: Output\n    }],\n    meridianChange: [{\n      type: Output\n    }]\n  });\n})();\n\nclass TimepickerModule {\n  static forRoot() {\n    return {\n      ngModule: TimepickerModule,\n      providers: [TimepickerActions, TimepickerStore]\n    };\n  }\n\n}\n\nTimepickerModule.ɵfac = function TimepickerModule_Factory(t) {\n  return new (t || TimepickerModule)();\n};\n\nTimepickerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: TimepickerModule\n});\nTimepickerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [TimepickerStore],\n  imports: [[CommonModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TimepickerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [TimepickerComponent],\n      exports: [TimepickerComponent],\n      providers: [TimepickerStore]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { TimepickerActions, TimepickerComponent, TimepickerConfig, TimepickerModule, TimepickerStore };", "map": {"version": 3, "sources": ["D:/Office Projects/brac-lms/Brac.LMS.View.Admin/node_modules/ngx-bootstrap/timepicker/fesm2015/ngx-bootstrap-timepicker.mjs"], "names": ["i0", "Injectable", "forwardRef", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "NgModule", "NG_VALUE_ACCESSOR", "BehaviorSubject", "MiniStore", "MiniState", "i4", "CommonModule", "dex", "hoursPerDay", "hoursPerDayHalf", "minutesPerHour", "seconds<PERSON><PERSON><PERSON><PERSON><PERSON>", "isValidDate", "value", "Date", "isNaN", "getHours", "isValidLimit", "controls", "newDate", "min", "max", "toNumber", "NaN", "parseInt", "isNumber", "parseHours", "isPM", "hour", "parseMinutes", "minute", "parseSeconds", "seconds", "parseTime", "changeTime", "diff", "createDate", "minutes", "getMinutes", "getSeconds", "setTime", "opts", "hours", "newValue", "getFullYear", "getMonth", "getDate", "getMilliseconds", "setFullYear", "setMonth", "setDate", "padNumber", "_value", "toString", "length", "isHourInputValid", "isMinuteInputValid", "isSecondInputValid", "isInputLimitValid", "isOneOfDatesEmpty", "isInputValid", "canChangeValue", "state", "event", "readonlyInput", "disabled", "source", "mousewheel", "arrowkeys", "canChangeHours", "step", "canIncrementHours", "canDecrementHours", "canChangeMinutes", "canIncrementMinutes", "canDecrementMinutes", "canChangeSeconds", "canIncrementSeconds", "canDecrementSeconds", "getControlsValue", "hourStep", "minuteStep", "secondsStep", "showSpinners", "showMeridian", "showSeconds", "meridians", "timepickerControls", "res", "canToggleMeridian", "_newHour", "_newMinutes", "_newSeconds", "TimepickerConfig", "constructor", "allowEmptyTime", "showMinutes", "hoursPlaceholder", "minutesPlaceholder", "secondsPlaceholder", "ariaLabelHours", "ariaLabelMinutes", "ariaLabelSeconds", "ɵfac", "ɵprov", "type", "args", "providedIn", "TimepickerActions", "writeValue", "WRITE_VALUE", "payload", "changeHours", "CHANGE_HOURS", "changeMinutes", "CHANGE_MINUTES", "changeSeconds", "CHANGE_SECONDS", "SET_TIME_UNIT", "updateControls", "UPDATE_CONTROLS", "initialState", "config", "timepickerReducer", "action", "Object", "assign", "_newTime", "_newControlsState", "_newState", "TimepickerStore", "_dispatcher", "TIMEPICKER_CONTROL_VALUE_ACCESSOR", "provide", "useExisting", "TimepickerComponent", "multi", "_config", "_cd", "_store", "_timepickerActions", "<PERSON><PERSON><PERSON><PERSON>", "meridianChange", "meridian", "invalidHours", "invalidMinutes", "invalidSeconds", "labelHours", "labelMinutes", "labelSeconds", "onChange", "Function", "prototype", "onTouched", "timepickerSub", "select", "subscribe", "_renderTime", "dispatch", "controlsState", "isTimepickerInputValid", "isOneOfDatesIsEmpty", "emit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isSpinnersVisible", "isEditable", "resetValidation", "prevDef", "$event", "preventDefault", "wheelSign", "Math", "sign", "deltaY", "ngOnChanges", "updateHours", "target", "_updateTime", "updateMinutes", "updateSeconds", "_seconds", "_minutes", "toggleMeridian", "_hoursPerDayHalf", "obj", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnDestroy", "_a", "unsubscribe", "_hours", "getUTCSeconds", "ChangeDetectorRef", "ɵcmp", "NgIf", "selector", "changeDetection", "OnPush", "providers", "styles", "encapsulation", "None", "template", "TimepickerModule", "forRoot", "ngModule", "ɵmod", "ɵinj", "imports", "declarations", "exports"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,UAArB,EAAiCC,YAAjC,EAA+CC,SAA/C,EAA0DC,uBAA1D,EAAmFC,iBAAnF,EAAsGC,KAAtG,EAA6GC,MAA7G,EAAqHC,QAArH,QAAqI,eAArI;AACA,SAASC,iBAAT,QAAkC,gBAAlC;AACA,SAASC,eAAT,QAAgC,MAAhC;AACA,SAASC,SAAT,EAAoBC,SAApB,QAAqC,yBAArC;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;;;;AA+TmGf,IAAAA,EAkb+hC,wB;AAlb/hCA,IAAAA,EAkbyjC,0B;AAlbzjCA,IAAAA,EAkb2kC,e;;;;;;iBAlb3kCA,E;;AAAAA,IAAAA,EAkb6nC,wB;AAlb7nCA,IAAAA,EAkb+pC,0B;AAlb/pCA,IAAAA,EAkb4vC;AAlb5vCA,MAAAA,EAkb4vC;AAAA,sBAlb5vCA,EAkb4vC;AAAA,aAAU,yCAAV;AAAA,M;AAlb5vCA,IAAAA,EAkb0yC,wB;AAlb1yCA,IAAAA,EAkb01C,e;AAlb11CA,IAAAA,EAkbo2C,e;;;;mBAlbp2CA,E;AAAAA,IAAAA,EAkbyrC,a;AAlbzrCA,IAAAA,EAkbyrC,2E;;;;;;AAlbzrCA,IAAAA,EAkbq4C,wB;AAlbr4CA,IAAAA,EAkb+5C,kB;AAlb/5CA,IAAAA,EAkbq6C,e;;;;;;iBAlbr6CA,E;;AAAAA,IAAAA,EAkbu9C,wB;AAlbv9CA,IAAAA,EAkby/C,0B;AAlbz/CA,IAAAA,EAkbslD;AAlbtlDA,MAAAA,EAkbslD;AAAA,sBAlbtlDA,EAkbslD;AAAA,aAAU,0CAAV;AAAA,M;AAlbtlDA,IAAAA,EAkbuoD,wB;AAlbvoDA,IAAAA,EAkb+rD,e;AAlb/rDA,IAAAA,EAkbysD,e;;;;mBAlbzsDA,E;AAAAA,IAAAA,EAkbmhD,a;AAlbnhDA,IAAAA,EAkbmhD,2E;;;;;;AAlbnhDA,IAAAA,EAkbgvD,wB;AAlbhvDA,IAAAA,EAkb2wD,0B;AAlb3wDA,IAAAA,EAkb6xD,e;;;;;;AAlb7xDA,IAAAA,EAkb00D,mB;;;;;;AAlb10DA,IAAAA,EAkbsnF,wB;AAlbtnFA,IAAAA,EAkbgpF,uB;AAlbhpFA,IAAAA,EAkb6pF,e;;;;;;iBAlb7pFA,E;;AAAAA,IAAAA,EAkb8rF,2B;AAlb9rFA,IAAAA,EAkb+xF,8B;AAlb/xFA,IAAAA,EAkbinG;AAlbjnGA,MAAAA,EAkbinG;AAAA,sBAlbjnGA,EAkbinG;AAAU,6BAAV;AAAA,aAA0B,2CAA2B,yBAA3B,EAA8C,OAA9C,CAA1B;AAAA;AAlbjnGA,MAAAA,EAkbinG;AAAA,sBAlbjnGA,EAkbinG;AAAA,aAAqH,0CAA0B,KAA1B,CAArH;AAAA;AAlbjnGA,MAAAA,EAkbinG;AAAA,sBAlbjnGA,EAkbinG;AAAA,aAA4L,2CAA2B,KAA3B,CAA5L;AAAA;AAlbjnGA,MAAAA,EAkbinG;AAAA,sBAlbjnGA,EAkbinG;AAAA,aAAyP,oCAAzP;AAAA,M;AAlbjnGA,IAAAA,EAkb+xF,e;AAlb/xFA,IAAAA,EAkbk7G,e;;;;mBAlbl7GA,E;AAAAA,IAAAA,EAkbkvF,gD;AAlblvFA,IAAAA,EAkbozF,a;AAlbpzFA,IAAAA,EAkbozF,iD;AAlbpzFA,IAAAA,EAkb66F,6I;AAlb76FA,IAAAA,EAkby4G,+C;;;;;;AAlbz4GA,IAAAA,EAkbm9G,wB;AAlbn9GA,IAAAA,EAkb6+G,uB;AAlb7+GA,IAAAA,EAkb0/G,e;;;;;;iBAlb1/GA,E;;AAAAA,IAAAA,EAkb2hH,2B;AAlb3hHA,IAAAA,EAkb4nH,8B;AAlb5nHA,IAAAA,EAkb88H;AAlb98HA,MAAAA,EAkb88H;AAAA,sBAlb98HA,EAkb88H;AAAU,6BAAV;AAAA,aAA0B,4CAA4B,yBAA5B,EAA+C,OAA/C,CAA1B;AAAA;AAlb98HA,MAAAA,EAkb88H;AAAA,sBAlb98HA,EAkb88H;AAAA,aAAsH,2CAA2B,KAA3B,CAAtH;AAAA;AAlb98HA,MAAAA,EAkb88H;AAAA,sBAlb98HA,EAkb88H;AAAA,aAA8L,4CAA4B,KAA5B,CAA9L;AAAA;AAlb98HA,MAAAA,EAkb88H;AAAA,sBAlb98HA,EAkb88H;AAAA,aAA4P,oCAA5P;AAAA,M;AAlb98HA,IAAAA,EAkb4nH,e;AAlb5nHA,IAAAA,EAkbkxI,e;;;;mBAlblxIA,E;AAAAA,IAAAA,EAkb+kH,gD;AAlb/kHA,IAAAA,EAkbipH,a;AAlbjpHA,IAAAA,EAkbipH,iD;AAlbjpHA,IAAAA,EAkb0wH,6I;AAlb1wHA,IAAAA,EAkbyuI,+C;;;;;;AAlbzuIA,IAAAA,EAkbyzI,wB;AAlbzzIA,IAAAA,EAkbo1I,0B;AAlbp1IA,IAAAA,EAkbs2I,e;;;;;;iBAlbt2IA,E;;AAAAA,IAAAA,EAkbw4I,wB;AAlbx4IA,IAAAA,EAkb26I,+B;AAlb36IA,IAAAA,EAkb8nJ;AAlb9nJA,MAAAA,EAkb8nJ;AAAA,sBAlb9nJA,EAkb8nJ;AAAA,aAAU,wBAAV;AAAA,M;AAlb9nJA,IAAAA,EAkbmqJ,U;AAlbnqJA,IAAAA,EAkbyrJ,e;AAlbzrJA,IAAAA,EAkbwsJ,e;;;;oBAlbxsJA,E;AAAAA,IAAAA,EAkbwjJ,a;AAlbxjJA,IAAAA,EAkbwjJ,2E;AAlbxjJA,IAAAA,EAkbw/I,0E;AAlbx/IA,IAAAA,EAkbmqJ,a;AAlbnqJA,IAAAA,EAkbmqJ,8C;;;;;;AAlbnqJA,IAAAA,EAkbkjK,wB;AAlbljKA,IAAAA,EAkb4kK,0B;AAlb5kKA,IAAAA,EAkb8lK,e;;;;;;iBAlb9lKA,E;;AAAAA,IAAAA,EAkb+oK,wB;AAlb/oKA,IAAAA,EAkbirK,0B;AAlbjrKA,IAAAA,EAkb8wK;AAlb9wKA,MAAAA,EAkb8wK;AAAA,sBAlb9wKA,EAkb8wK;AAAA,aAAU,0CAAV;AAAA,M;AAlb9wKA,IAAAA,EAkb+zK,wB;AAlb/zKA,IAAAA,EAkby3K,e;AAlbz3KA,IAAAA,EAkbm4K,e;;;;oBAlbn4KA,E;AAAAA,IAAAA,EAkb2sK,a;AAlb3sKA,IAAAA,EAkb2sK,6E;;;;;;AAlb3sKA,IAAAA,EAkbo6K,wB;AAlbp6KA,IAAAA,EAkb87K,kB;AAlb97KA,IAAAA,EAkbo8K,e;;;;;;iBAlbp8KA,E;;AAAAA,IAAAA,EAkbq/K,wB;AAlbr/KA,IAAAA,EAkbuhL,0B;AAlbvhLA,IAAAA,EAkbonL;AAlbpnLA,MAAAA,EAkbonL;AAAA,sBAlbpnLA,EAkbonL;AAAA,aAAU,2CAAV;AAAA,M;AAlbpnLA,IAAAA,EAkbsqL,wB;AAlbtqLA,IAAAA,EAkbguL,e;AAlbhuLA,IAAAA,EAkb0uL,e;;;;oBAlb1uLA,E;AAAAA,IAAAA,EAkbijL,a;AAlbjjLA,IAAAA,EAkbijL,6E;;;;;;AAlbjjLA,IAAAA,EAkbixL,wB;AAlbjxLA,IAAAA,EAkb4yL,0B;AAlb5yLA,IAAAA,EAkb8zL,e;;;;;;AAlb9zLA,IAAAA,EAkb22L,mB;;;;AA/uB98L,MAAMgB,GAAG,GAAG,EAAZ;AACA,MAAMC,WAAW,GAAG,EAApB;AACA,MAAMC,eAAe,GAAG,EAAxB;AACA,MAAMC,cAAc,GAAG,EAAvB;AACA,MAAMC,gBAAgB,GAAG,EAAzB;;AACA,SAASC,WAAT,CAAqBC,KAArB,EAA4B;AACxB,MAAI,CAACA,KAAL,EAAY;AACR,WAAO,KAAP;AACH;;AACD,MAAIA,KAAK,YAAYC,IAAjB,IAAyBC,KAAK,CAACF,KAAK,CAACG,QAAN,EAAD,CAAlC,EAAsD;AAClD,WAAO,KAAP;AACH;;AACD,MAAI,OAAOH,KAAP,KAAiB,QAArB,EAA+B;AAC3B,WAAOD,WAAW,CAAC,IAAIE,IAAJ,CAASD,KAAT,CAAD,CAAlB;AACH;;AACD,SAAO,IAAP;AACH;;AACD,SAASI,YAAT,CAAsBC,QAAtB,EAAgCC,OAAhC,EAAyC;AACrC,MAAID,QAAQ,CAACE,GAAT,IAAgBD,OAAO,GAAGD,QAAQ,CAACE,GAAvC,EAA4C;AACxC,WAAO,KAAP;AACH;;AACD,MAAIF,QAAQ,CAACG,GAAT,IAAgBF,OAAO,GAAGD,QAAQ,CAACG,GAAvC,EAA4C;AACxC,WAAO,KAAP;AACH;;AACD,SAAO,IAAP;AACH;;AACD,SAASC,QAAT,CAAkBT,KAAlB,EAAyB;AACrB,MAAI,OAAOA,KAAP,KAAiB,WAArB,EAAkC;AAC9B,WAAOU,GAAP;AACH;;AACD,MAAI,OAAOV,KAAP,KAAiB,QAArB,EAA+B;AAC3B,WAAOA,KAAP;AACH;;AACD,SAAOW,QAAQ,CAACX,KAAD,EAAQN,GAAR,CAAf;AACH;;AACD,SAASkB,QAAT,CAAkBZ,KAAlB,EAAyB;AACrB,SAAO,CAACE,KAAK,CAACO,QAAQ,CAACT,KAAD,CAAT,CAAb;AACH;;AACD,SAASa,UAAT,CAAoBb,KAApB,EAA2Bc,IAAI,GAAG,KAAlC,EAAyC;AACrC,QAAMC,IAAI,GAAGN,QAAQ,CAACT,KAAD,CAArB;;AACA,MAAIE,KAAK,CAACa,IAAD,CAAL,IACAA,IAAI,GAAG,CADP,IAEAA,IAAI,IAAID,IAAI,GAAGlB,eAAH,GAAqBD,WAA7B,CAFR,EAEmD;AAC/C,WAAOe,GAAP;AACH;;AACD,SAAOK,IAAP;AACH;;AACD,SAASC,YAAT,CAAsBhB,KAAtB,EAA6B;AACzB,QAAMiB,MAAM,GAAGR,QAAQ,CAACT,KAAD,CAAvB;;AACA,MAAIE,KAAK,CAACe,MAAD,CAAL,IAAiBA,MAAM,GAAG,CAA1B,IAA+BA,MAAM,GAAGpB,cAA5C,EAA4D;AACxD,WAAOa,GAAP;AACH;;AACD,SAAOO,MAAP;AACH;;AACD,SAASC,YAAT,CAAsBlB,KAAtB,EAA6B;AACzB,QAAMmB,OAAO,GAAGV,QAAQ,CAACT,KAAD,CAAxB;;AACA,MAAIE,KAAK,CAACiB,OAAD,CAAL,IAAkBA,OAAO,GAAG,CAA5B,IAAiCA,OAAO,GAAGrB,gBAA/C,EAAiE;AAC7D,WAAOY,GAAP;AACH;;AACD,SAAOS,OAAP;AACH;;AACD,SAASC,SAAT,CAAmBpB,KAAnB,EAA0B;AACtB,MAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;AAC3B,WAAO,IAAIC,IAAJ,CAASD,KAAT,CAAP;AACH;;AACD,SAAOA,KAAP;AACH;;AACD,SAASqB,UAAT,CAAoBrB,KAApB,EAA2BsB,IAA3B,EAAiC;AAC7B,MAAI,CAACtB,KAAL,EAAY;AACR,WAAOqB,UAAU,CAACE,UAAU,CAAC,IAAItB,IAAJ,EAAD,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,CAAX,EAAkCqB,IAAlC,CAAjB;AACH;;AACD,MAAI,CAACA,IAAL,EAAW;AACP,WAAOtB,KAAP;AACH;;AACD,MAAIe,IAAI,GAAGf,KAAK,CAACG,QAAN,EAAX;AACA,MAAIqB,OAAO,GAAGxB,KAAK,CAACyB,UAAN,EAAd;AACA,MAAIN,OAAO,GAAGnB,KAAK,CAAC0B,UAAN,EAAd;;AACA,MAAIJ,IAAI,CAACP,IAAT,EAAe;AACXA,IAAAA,IAAI,GAAGA,IAAI,GAAGN,QAAQ,CAACa,IAAI,CAACP,IAAN,CAAtB;AACH;;AACD,MAAIO,IAAI,CAACL,MAAT,EAAiB;AACbO,IAAAA,OAAO,GAAGA,OAAO,GAAGf,QAAQ,CAACa,IAAI,CAACL,MAAN,CAA5B;AACH;;AACD,MAAIK,IAAI,CAACH,OAAT,EAAkB;AACdA,IAAAA,OAAO,GAAGA,OAAO,GAAGV,QAAQ,CAACa,IAAI,CAACH,OAAN,CAA5B;AACH;;AACD,SAAOI,UAAU,CAACvB,KAAD,EAAQe,IAAR,EAAcS,OAAd,EAAuBL,OAAvB,CAAjB;AACH;;AACD,SAASQ,OAAT,CAAiB3B,KAAjB,EAAwB4B,IAAxB,EAA8B;AAC1B,MAAIb,IAAI,GAAGF,UAAU,CAACe,IAAI,CAACb,IAAN,CAArB;AACA,QAAME,MAAM,GAAGD,YAAY,CAACY,IAAI,CAACX,MAAN,CAA3B;AACA,QAAME,OAAO,GAAGD,YAAY,CAACU,IAAI,CAACT,OAAN,CAAZ,IAA8B,CAA9C;;AACA,MAAIS,IAAI,CAACd,IAAL,IAAaC,IAAI,KAAK,EAA1B,EAA8B;AAC1BA,IAAAA,IAAI,IAAInB,eAAR;AACH;;AACD,MAAI,CAACI,KAAL,EAAY;AACR,QAAI,CAACE,KAAK,CAACa,IAAD,CAAN,IAAgB,CAACb,KAAK,CAACe,MAAD,CAA1B,EAAoC;AAChC,aAAOM,UAAU,CAAC,IAAItB,IAAJ,EAAD,EAAac,IAAb,EAAmBE,MAAnB,EAA2BE,OAA3B,CAAjB;AACH;;AACD,WAAOnB,KAAP;AACH;;AACD,MAAIE,KAAK,CAACa,IAAD,CAAL,IAAeb,KAAK,CAACe,MAAD,CAAxB,EAAkC;AAC9B,WAAOjB,KAAP;AACH;;AACD,SAAOuB,UAAU,CAACvB,KAAD,EAAQe,IAAR,EAAcE,MAAd,EAAsBE,OAAtB,CAAjB;AACH;;AACD,SAASI,UAAT,CAAoBvB,KAApB,EAA2B6B,KAA3B,EAAkCL,OAAlC,EAA2CL,OAA3C,EAAoD;AAChD,QAAMW,QAAQ,GAAG,IAAI7B,IAAJ,CAASD,KAAK,CAAC+B,WAAN,EAAT,EAA8B/B,KAAK,CAACgC,QAAN,EAA9B,EAAgDhC,KAAK,CAACiC,OAAN,EAAhD,EAAiEJ,KAAjE,EAAwEL,OAAxE,EAAiFL,OAAjF,EAA0FnB,KAAK,CAACkC,eAAN,EAA1F,CAAjB,CADgD,CAEhD;;AACAJ,EAAAA,QAAQ,CAACK,WAAT,CAAqBnC,KAAK,CAAC+B,WAAN,EAArB;AACAD,EAAAA,QAAQ,CAACM,QAAT,CAAkBpC,KAAK,CAACgC,QAAN,EAAlB;AACAF,EAAAA,QAAQ,CAACO,OAAT,CAAiBrC,KAAK,CAACiC,OAAN,EAAjB;AACA,SAAOH,QAAP;AACH;;AACD,SAASQ,SAAT,CAAmBtC,KAAnB,EAA0B;AACtB,QAAMuC,MAAM,GAAGvC,KAAK,CAACwC,QAAN,EAAf;;AACA,MAAID,MAAM,CAACE,MAAP,GAAgB,CAApB,EAAuB;AACnB,WAAOF,MAAP;AACH;;AACD,SAAQ,IAAGA,MAAO,EAAlB;AACH;;AACD,SAASG,gBAAT,CAA0Bb,KAA1B,EAAiCf,IAAjC,EAAuC;AACnC,SAAO,CAACZ,KAAK,CAACW,UAAU,CAACgB,KAAD,EAAQf,IAAR,CAAX,CAAb;AACH;;AACD,SAAS6B,kBAAT,CAA4BnB,OAA5B,EAAqC;AACjC,SAAO,CAACtB,KAAK,CAACc,YAAY,CAACQ,OAAD,CAAb,CAAb;AACH;;AACD,SAASoB,kBAAT,CAA4BzB,OAA5B,EAAqC;AACjC,SAAO,CAACjB,KAAK,CAACgB,YAAY,CAACC,OAAD,CAAb,CAAb;AACH;;AACD,SAAS0B,iBAAT,CAA2BvB,IAA3B,EAAiCd,GAAjC,EAAsCD,GAAtC,EAA2C;AACvC,QAAMD,OAAO,GAAGqB,OAAO,CAAC,IAAI1B,IAAJ,EAAD,EAAaqB,IAAb,CAAvB;;AACA,MAAI,CAAChB,OAAL,EAAc;AACV,WAAO,KAAP;AACH;;AACD,MAAIE,GAAG,IAAIF,OAAO,GAAGE,GAArB,EAA0B;AACtB,WAAO,KAAP;AACH;;AACD,MAAID,GAAG,IAAID,OAAO,GAAGC,GAArB,EAA0B;AACtB,WAAO,KAAP;AACH;;AACD,SAAO,IAAP;AACH;;AACD,SAASuC,iBAAT,CAA2BjB,KAA3B,EAAkCL,OAAlC,EAA2CL,OAA3C,EAAoD;AAChD,SAAOU,KAAK,CAACY,MAAN,KAAiB,CAAjB,IAAsBjB,OAAO,CAACiB,MAAR,KAAmB,CAAzC,IAA8CtB,OAAO,CAACsB,MAAR,KAAmB,CAAxE;AACH;;AACD,SAASM,YAAT,CAAsBlB,KAAtB,EAA6BL,OAAO,GAAG,GAAvC,EAA4CL,OAAO,GAAG,GAAtD,EAA2DL,IAA3D,EAAiE;AAC7D,SAAO4B,gBAAgB,CAACb,KAAD,EAAQf,IAAR,CAAhB,IACA6B,kBAAkB,CAACnB,OAAD,CADlB,IAEAoB,kBAAkB,CAACzB,OAAD,CAFzB;AAGH;;AAED,SAAS6B,cAAT,CAAwBC,KAAxB,EAA+BC,KAA/B,EAAsC;AAClC,MAAID,KAAK,CAACE,aAAN,IAAuBF,KAAK,CAACG,QAAjC,EAA2C;AACvC,WAAO,KAAP;AACH;;AACD,MAAIF,KAAJ,EAAW;AACP,QAAIA,KAAK,CAACG,MAAN,KAAiB,OAAjB,IAA4B,CAACJ,KAAK,CAACK,UAAvC,EAAmD;AAC/C,aAAO,KAAP;AACH;;AACD,QAAIJ,KAAK,CAACG,MAAN,KAAiB,KAAjB,IAA0B,CAACJ,KAAK,CAACM,SAArC,EAAgD;AAC5C,aAAO,KAAP;AACH;AACJ;;AACD,SAAO,IAAP;AACH;;AACD,SAASC,cAAT,CAAwBN,KAAxB,EAA+B7C,QAA/B,EAAyC;AACrC,MAAI,CAAC6C,KAAK,CAACO,IAAX,EAAiB;AACb,WAAO,KAAP;AACH;;AACD,MAAIP,KAAK,CAACO,IAAN,GAAa,CAAb,IAAkB,CAACpD,QAAQ,CAACqD,iBAAhC,EAAmD;AAC/C,WAAO,KAAP;AACH;;AACD,MAAIR,KAAK,CAACO,IAAN,GAAa,CAAb,IAAkB,CAACpD,QAAQ,CAACsD,iBAAhC,EAAmD;AAC/C,WAAO,KAAP;AACH;;AACD,SAAO,IAAP;AACH;;AACD,SAASC,gBAAT,CAA0BV,KAA1B,EAAiC7C,QAAjC,EAA2C;AACvC,MAAI,CAAC6C,KAAK,CAACO,IAAX,EAAiB;AACb,WAAO,KAAP;AACH;;AACD,MAAIP,KAAK,CAACO,IAAN,GAAa,CAAb,IAAkB,CAACpD,QAAQ,CAACwD,mBAAhC,EAAqD;AACjD,WAAO,KAAP;AACH;;AACD,MAAIX,KAAK,CAACO,IAAN,GAAa,CAAb,IAAkB,CAACpD,QAAQ,CAACyD,mBAAhC,EAAqD;AACjD,WAAO,KAAP;AACH;;AACD,SAAO,IAAP;AACH;;AACD,SAASC,gBAAT,CAA0Bb,KAA1B,EAAiC7C,QAAjC,EAA2C;AACvC,MAAI,CAAC6C,KAAK,CAACO,IAAX,EAAiB;AACb,WAAO,KAAP;AACH;;AACD,MAAIP,KAAK,CAACO,IAAN,GAAa,CAAb,IAAkB,CAACpD,QAAQ,CAAC2D,mBAAhC,EAAqD;AACjD,WAAO,KAAP;AACH;;AACD,MAAId,KAAK,CAACO,IAAN,GAAa,CAAb,IAAkB,CAACpD,QAAQ,CAAC4D,mBAAhC,EAAqD;AACjD,WAAO,KAAP;AACH;;AACD,SAAO,IAAP;AACH;;AACD,SAASC,gBAAT,CAA0BjB,KAA1B,EAAiC;AAC7B,QAAM;AAAEkB,IAAAA,QAAF;AAAYC,IAAAA,UAAZ;AAAwBC,IAAAA,WAAxB;AAAqClB,IAAAA,aAArC;AAAoDC,IAAAA,QAApD;AAA8DE,IAAAA,UAA9D;AAA0EC,IAAAA,SAA1E;AAAqFe,IAAAA,YAArF;AAAmGC,IAAAA,YAAnG;AAAiHC,IAAAA,WAAjH;AAA8HC,IAAAA,SAA9H;AAAyIlE,IAAAA,GAAzI;AAA8IC,IAAAA;AAA9I,MAAsJyC,KAA5J;AACA,SAAO;AACHkB,IAAAA,QADG;AAEHC,IAAAA,UAFG;AAGHC,IAAAA,WAHG;AAIHlB,IAAAA,aAJG;AAKHC,IAAAA,QALG;AAMHE,IAAAA,UANG;AAOHC,IAAAA,SAPG;AAQHe,IAAAA,YARG;AASHC,IAAAA,YATG;AAUHC,IAAAA,WAVG;AAWHC,IAAAA,SAXG;AAYHlE,IAAAA,GAZG;AAaHC,IAAAA;AAbG,GAAP;AAeH;;AACD,SAASkE,kBAAT,CAA4B1E,KAA5B,EAAmCiD,KAAnC,EAA0C;AACtC,QAAMtD,WAAW,GAAG,EAApB;AACA,QAAMC,eAAe,GAAG,EAAxB;AACA,QAAM;AAAEW,IAAAA,GAAF;AAAOC,IAAAA,GAAP;AAAY2D,IAAAA,QAAZ;AAAsBC,IAAAA,UAAtB;AAAkCC,IAAAA,WAAlC;AAA+CG,IAAAA;AAA/C,MAA+DvB,KAArE;AACA,QAAM0B,GAAG,GAAG;AACRjB,IAAAA,iBAAiB,EAAE,IADX;AAERG,IAAAA,mBAAmB,EAAE,IAFb;AAGRG,IAAAA,mBAAmB,EAAE,IAHb;AAIRL,IAAAA,iBAAiB,EAAE,IAJX;AAKRG,IAAAA,mBAAmB,EAAE,IALb;AAMRG,IAAAA,mBAAmB,EAAE,IANb;AAORW,IAAAA,iBAAiB,EAAE;AAPX,GAAZ;;AASA,MAAI,CAAC5E,KAAL,EAAY;AACR,WAAO2E,GAAP;AACH,GAfqC,CAgBtC;;;AACA,MAAInE,GAAJ,EAAS;AACL,UAAMqE,QAAQ,GAAGxD,UAAU,CAACrB,KAAD,EAAQ;AAAEe,MAAAA,IAAI,EAAEoD;AAAR,KAAR,CAA3B;;AACAQ,IAAAA,GAAG,CAACjB,iBAAJ,GAAwBlD,GAAG,GAAGqE,QAAN,IAAmB7E,KAAK,CAACG,QAAN,KAAmBgE,QAApB,GAAgCxE,WAA1E;;AACA,QAAI,CAACgF,GAAG,CAACjB,iBAAT,EAA4B;AACxB,YAAMoB,WAAW,GAAGzD,UAAU,CAACrB,KAAD,EAAQ;AAAEiB,QAAAA,MAAM,EAAEmD;AAAV,OAAR,CAA9B;;AACAO,MAAAA,GAAG,CAACd,mBAAJ,GAA0BW,WAAW,GAC/BhE,GAAG,GAAGsE,WADyB,GAE/BtE,GAAG,IAAIsE,WAFb;AAGH;;AACD,QAAI,CAACH,GAAG,CAACd,mBAAT,EAA8B;AAC1B,YAAMkB,WAAW,GAAG1D,UAAU,CAACrB,KAAD,EAAQ;AAAEmB,QAAAA,OAAO,EAAEkD;AAAX,OAAR,CAA9B;;AACAM,MAAAA,GAAG,CAACX,mBAAJ,GAA0BxD,GAAG,IAAIuE,WAAjC;AACH;;AACD,QAAI/E,KAAK,CAACG,QAAN,KAAmBP,eAAvB,EAAwC;AACpC+E,MAAAA,GAAG,CAACC,iBAAJ,GAAwBvD,UAAU,CAACrB,KAAD,EAAQ;AAAEe,QAAAA,IAAI,EAAEnB;AAAR,OAAR,CAAV,GAA+CY,GAAvE;AACH;AACJ;;AACD,MAAID,GAAJ,EAAS;AACL,UAAMsE,QAAQ,GAAGxD,UAAU,CAACrB,KAAD,EAAQ;AAAEe,MAAAA,IAAI,EAAE,CAACoD;AAAT,KAAR,CAA3B;;AACAQ,IAAAA,GAAG,CAAChB,iBAAJ,GAAwBpD,GAAG,GAAGsE,QAA9B;;AACA,QAAI,CAACF,GAAG,CAAChB,iBAAT,EAA4B;AACxB,YAAMmB,WAAW,GAAGzD,UAAU,CAACrB,KAAD,EAAQ;AAAEiB,QAAAA,MAAM,EAAE,CAACmD;AAAX,OAAR,CAA9B;;AACAO,MAAAA,GAAG,CAACb,mBAAJ,GAA0BU,WAAW,GAC/BjE,GAAG,GAAGuE,WADyB,GAE/BvE,GAAG,IAAIuE,WAFb;AAGH;;AACD,QAAI,CAACH,GAAG,CAACb,mBAAT,EAA8B;AAC1B,YAAMiB,WAAW,GAAG1D,UAAU,CAACrB,KAAD,EAAQ;AAAEmB,QAAAA,OAAO,EAAE,CAACkD;AAAZ,OAAR,CAA9B;;AACAM,MAAAA,GAAG,CAACV,mBAAJ,GAA0B1D,GAAG,IAAIwE,WAAjC;AACH;;AACD,QAAI/E,KAAK,CAACG,QAAN,MAAoBP,eAAxB,EAAyC;AACrC+E,MAAAA,GAAG,CAACC,iBAAJ,GAAwBvD,UAAU,CAACrB,KAAD,EAAQ;AAAEe,QAAAA,IAAI,EAAE,CAACnB;AAAT,OAAR,CAAV,GAAgDW,GAAxE;AACH;AACJ;;AACD,SAAOoE,GAAP;AACH;AAED;;;AACA,MAAMK,gBAAN,CAAuB;AACnBC,EAAAA,WAAW,GAAG;AACV;AACA,SAAKd,QAAL,GAAgB,CAAhB;AACA;;AACA,SAAKC,UAAL,GAAkB,CAAlB;AACA;;AACA,SAAKC,WAAL,GAAmB,EAAnB;AACA;;AACA,SAAKE,YAAL,GAAoB,IAApB;AACA;;AACA,SAAKE,SAAL,GAAiB,CAAC,IAAD,EAAO,IAAP,CAAjB;AACA;;AACA,SAAKtB,aAAL,GAAqB,KAArB;AACA;;AACA,SAAKC,QAAL,GAAgB,KAAhB;AACA;;AACA,SAAK8B,cAAL,GAAsB,KAAtB;AACA;;AACA,SAAK5B,UAAL,GAAkB,IAAlB;AACA;;AACA,SAAKC,SAAL,GAAiB,IAAjB;AACA;;AACA,SAAKe,YAAL,GAAoB,IAApB;AACA;;AACA,SAAKE,WAAL,GAAmB,KAAnB;AACA;;AACA,SAAKW,WAAL,GAAmB,IAAnB;AACA;;AACA,SAAKC,gBAAL,GAAwB,IAAxB;AACA;;AACA,SAAKC,kBAAL,GAA0B,IAA1B;AACA;;AACA,SAAKC,kBAAL,GAA0B,IAA1B;AACA;;AACA,SAAKC,cAAL,GAAsB,OAAtB;AACA;;AACA,SAAKC,gBAAL,GAAwB,SAAxB;AACA;;AACA,SAAKC,gBAAL,GAAwB,SAAxB;AACH;;AAxCkB;;AA0CvBT,gBAAgB,CAACU,IAAjB;AAAA,mBAA6GV,gBAA7G;AAAA;;AACAA,gBAAgB,CAACW,KAAjB,kBADmGjH,EACnG;AAAA,SAAiHsG,gBAAjH;AAAA,WAAiHA,gBAAjH;AAAA,cAA+I;AAA/I;;AACA;AAAA,qDAFmGtG,EAEnG,mBAA2FsG,gBAA3F,EAAyH,CAAC;AAC9GY,IAAAA,IAAI,EAAEjH,UADwG;AAE9GkH,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,UAAU,EAAE;AADb,KAAD;AAFwG,GAAD,CAAzH;AAAA;;AAOA,MAAMC,iBAAN,CAAwB;AACpBC,EAAAA,UAAU,CAAChG,KAAD,EAAQ;AACd,WAAO;AACH4F,MAAAA,IAAI,EAAEG,iBAAiB,CAACE,WADrB;AAEHC,MAAAA,OAAO,EAAElG;AAFN,KAAP;AAIH;;AACDmG,EAAAA,WAAW,CAACjD,KAAD,EAAQ;AACf,WAAO;AACH0C,MAAAA,IAAI,EAAEG,iBAAiB,CAACK,YADrB;AAEHF,MAAAA,OAAO,EAAEhD;AAFN,KAAP;AAIH;;AACDmD,EAAAA,aAAa,CAACnD,KAAD,EAAQ;AACjB,WAAO;AACH0C,MAAAA,IAAI,EAAEG,iBAAiB,CAACO,cADrB;AAEHJ,MAAAA,OAAO,EAAEhD;AAFN,KAAP;AAIH;;AACDqD,EAAAA,aAAa,CAACrD,KAAD,EAAQ;AACjB,WAAO;AACH0C,MAAAA,IAAI,EAAEG,iBAAiB,CAACS,cADrB;AAEHN,MAAAA,OAAO,EAAEhD;AAFN,KAAP;AAIH;;AACDvB,EAAAA,OAAO,CAAC3B,KAAD,EAAQ;AACX,WAAO;AACH4F,MAAAA,IAAI,EAAEG,iBAAiB,CAACU,aADrB;AAEHP,MAAAA,OAAO,EAAElG;AAFN,KAAP;AAIH;;AACD0G,EAAAA,cAAc,CAAC1G,KAAD,EAAQ;AAClB,WAAO;AACH4F,MAAAA,IAAI,EAAEG,iBAAiB,CAACY,eADrB;AAEHT,MAAAA,OAAO,EAAElG;AAFN,KAAP;AAIH;;AApCmB;;AAsCxB+F,iBAAiB,CAACE,WAAlB,GAAgC,wCAAhC;AACAF,iBAAiB,CAACK,YAAlB,GAAiC,2BAAjC;AACAL,iBAAiB,CAACO,cAAlB,GAAmC,6BAAnC;AACAP,iBAAiB,CAACS,cAAlB,GAAmC,6BAAnC;AACAT,iBAAiB,CAACU,aAAlB,GAAkC,4BAAlC;AACAV,iBAAiB,CAACY,eAAlB,GAAoC,8BAApC;;AACAZ,iBAAiB,CAACL,IAAlB;AAAA,mBAA8GK,iBAA9G;AAAA;;AACAA,iBAAiB,CAACJ,KAAlB,kBAtDmGjH,EAsDnG;AAAA,SAAkHqH,iBAAlH;AAAA,WAAkHA,iBAAlH;AAAA,cAAiJ;AAAjJ;;AACA;AAAA,qDAvDmGrH,EAuDnG,mBAA2FqH,iBAA3F,EAA0H,CAAC;AAC/GH,IAAAA,IAAI,EAAEjH,UADyG;AAE/GkH,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFyG,GAAD,CAA1H;AAAA;;AAKA,MAAMc,YAAY,GAAG;AACjB5G,EAAAA,KAAK,EAAE,KAAK,CADK;AAEjB6G,EAAAA,MAAM,EAAE,IAAI7B,gBAAJ,EAFS;AAGjB3E,EAAAA,QAAQ,EAAE;AACNqD,IAAAA,iBAAiB,EAAE,IADb;AAENG,IAAAA,mBAAmB,EAAE,IAFf;AAGNG,IAAAA,mBAAmB,EAAE,IAHf;AAINL,IAAAA,iBAAiB,EAAE,IAJb;AAKNG,IAAAA,mBAAmB,EAAE,IALf;AAMNG,IAAAA,mBAAmB,EAAE,IANf;AAONW,IAAAA,iBAAiB,EAAE;AAPb;AAHO,CAArB;;AAaA,SAASkC,iBAAT,CAA2B7D,KAAK,GAAG2D,YAAnC,EAAiDG,MAAjD,EAAyD;AACrD,UAAQA,MAAM,CAACnB,IAAf;AACI,SAAKG,iBAAiB,CAACE,WAAvB;AAAoC;AAChC,eAAOe,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBhE,KAAlB,EAAyB;AAAEjD,UAAAA,KAAK,EAAE+G,MAAM,CAACb;AAAhB,SAAzB,CAAP;AACH;;AACD,SAAKH,iBAAiB,CAACK,YAAvB;AAAqC;AACjC,YAAI,CAACpD,cAAc,CAACC,KAAK,CAAC4D,MAAP,EAAeE,MAAM,CAACb,OAAtB,CAAf,IACA,CAAC1C,cAAc,CAACuD,MAAM,CAACb,OAAR,EAAiBjD,KAAK,CAAC5C,QAAvB,CADnB,EACqD;AACjD,iBAAO4C,KAAP;AACH;;AACD,cAAMiE,QAAQ,GAAG7F,UAAU,CAAC4B,KAAK,CAACjD,KAAP,EAAc;AAAEe,UAAAA,IAAI,EAAEgG,MAAM,CAACb,OAAP,CAAezC;AAAvB,SAAd,CAA3B;;AACA,YAAI,CAACR,KAAK,CAAC4D,MAAN,CAAarG,GAAb,IAAoByC,KAAK,CAAC4D,MAAN,CAAatG,GAAlC,KAA0C,CAACH,YAAY,CAAC6C,KAAK,CAAC4D,MAAP,EAAeK,QAAf,CAA3D,EAAqF;AACjF,iBAAOjE,KAAP;AACH;;AACD,eAAO+D,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBhE,KAAlB,EAAyB;AAAEjD,UAAAA,KAAK,EAAEkH;AAAT,SAAzB,CAAP;AACH;;AACD,SAAKnB,iBAAiB,CAACO,cAAvB;AAAuC;AACnC,YAAI,CAACtD,cAAc,CAACC,KAAK,CAAC4D,MAAP,EAAeE,MAAM,CAACb,OAAtB,CAAf,IACA,CAACtC,gBAAgB,CAACmD,MAAM,CAACb,OAAR,EAAiBjD,KAAK,CAAC5C,QAAvB,CADrB,EACuD;AACnD,iBAAO4C,KAAP;AACH;;AACD,cAAMiE,QAAQ,GAAG7F,UAAU,CAAC4B,KAAK,CAACjD,KAAP,EAAc;AAAEiB,UAAAA,MAAM,EAAE8F,MAAM,CAACb,OAAP,CAAezC;AAAzB,SAAd,CAA3B;;AACA,YAAI,CAACR,KAAK,CAAC4D,MAAN,CAAarG,GAAb,IAAoByC,KAAK,CAAC4D,MAAN,CAAatG,GAAlC,KAA0C,CAACH,YAAY,CAAC6C,KAAK,CAAC4D,MAAP,EAAeK,QAAf,CAA3D,EAAqF;AACjF,iBAAOjE,KAAP;AACH;;AACD,eAAO+D,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBhE,KAAlB,EAAyB;AAAEjD,UAAAA,KAAK,EAAEkH;AAAT,SAAzB,CAAP;AACH;;AACD,SAAKnB,iBAAiB,CAACS,cAAvB;AAAuC;AACnC,YAAI,CAACxD,cAAc,CAACC,KAAK,CAAC4D,MAAP,EAAeE,MAAM,CAACb,OAAtB,CAAf,IACA,CAACnC,gBAAgB,CAACgD,MAAM,CAACb,OAAR,EAAiBjD,KAAK,CAAC5C,QAAvB,CADrB,EACuD;AACnD,iBAAO4C,KAAP;AACH;;AACD,cAAMiE,QAAQ,GAAG7F,UAAU,CAAC4B,KAAK,CAACjD,KAAP,EAAc;AACrCmB,UAAAA,OAAO,EAAE4F,MAAM,CAACb,OAAP,CAAezC;AADa,SAAd,CAA3B;;AAGA,YAAI,CAACR,KAAK,CAAC4D,MAAN,CAAarG,GAAb,IAAoByC,KAAK,CAAC4D,MAAN,CAAatG,GAAlC,KAA0C,CAACH,YAAY,CAAC6C,KAAK,CAAC4D,MAAP,EAAeK,QAAf,CAA3D,EAAqF;AACjF,iBAAOjE,KAAP;AACH;;AACD,eAAO+D,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBhE,KAAlB,EAAyB;AAAEjD,UAAAA,KAAK,EAAEkH;AAAT,SAAzB,CAAP;AACH;;AACD,SAAKnB,iBAAiB,CAACU,aAAvB;AAAsC;AAClC,YAAI,CAACzD,cAAc,CAACC,KAAK,CAAC4D,MAAP,CAAnB,EAAmC;AAC/B,iBAAO5D,KAAP;AACH;;AACD,cAAMiE,QAAQ,GAAGvF,OAAO,CAACsB,KAAK,CAACjD,KAAP,EAAc+G,MAAM,CAACb,OAArB,CAAxB;;AACA,eAAOc,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBhE,KAAlB,EAAyB;AAAEjD,UAAAA,KAAK,EAAEkH;AAAT,SAAzB,CAAP;AACH;;AACD,SAAKnB,iBAAiB,CAACY,eAAvB;AAAwC;AACpC,cAAMQ,iBAAiB,GAAGzC,kBAAkB,CAACzB,KAAK,CAACjD,KAAP,EAAc+G,MAAM,CAACb,OAArB,CAA5C;;AACA,cAAMkB,SAAS,GAAG;AACdpH,UAAAA,KAAK,EAAEiD,KAAK,CAACjD,KADC;AAEd6G,UAAAA,MAAM,EAAEE,MAAM,CAACb,OAFD;AAGd7F,UAAAA,QAAQ,EAAE8G;AAHI,SAAlB;;AAKA,YAAIlE,KAAK,CAAC4D,MAAN,CAAatC,YAAb,KAA8B6C,SAAS,CAACP,MAAV,CAAiBtC,YAAnD,EAAiE;AAC7D,cAAItB,KAAK,CAACjD,KAAV,EAAiB;AACboH,YAAAA,SAAS,CAACpH,KAAV,GAAkB,IAAIC,IAAJ,CAASgD,KAAK,CAACjD,KAAf,CAAlB;AACH;AACJ;;AACD,eAAOgH,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBhE,KAAlB,EAAyBmE,SAAzB,CAAP;AACH;;AACD;AACI,aAAOnE,KAAP;AA7DR;AA+DH;;AAED,MAAMoE,eAAN,SAA8B/H,SAA9B,CAAwC;AACpC2F,EAAAA,WAAW,GAAG;AACV,UAAMqC,WAAW,GAAG,IAAIjI,eAAJ,CAAoB;AACpCuG,MAAAA,IAAI,EAAE;AAD8B,KAApB,CAApB;;AAGA,UAAM3C,KAAK,GAAG,IAAI1D,SAAJ,CAAcqH,YAAd,EAA4BU,WAA5B,EAAyCR,iBAAzC,CAAd;AACA,UAAMQ,WAAN,EAAmBR,iBAAnB,EAAsC7D,KAAtC;AACH;;AAPmC;;AASxCoE,eAAe,CAAC3B,IAAhB;AAAA,mBAA4G2B,eAA5G;AAAA;;AACAA,eAAe,CAAC1B,KAAhB,kBArJmGjH,EAqJnG;AAAA,SAAgH2I,eAAhH;AAAA,WAAgHA,eAAhH;AAAA,cAA6I;AAA7I;;AACA;AAAA,qDAtJmG3I,EAsJnG,mBAA2F2I,eAA3F,EAAwH,CAAC;AAC7GzB,IAAAA,IAAI,EAAEjH,UADuG;AAE7GkH,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFuG,GAAD,CAAxH,EAG4B,YAAY;AAAE,WAAO,EAAP;AAAY,GAHtD;AAAA;;AAKA,MAAMyB,iCAAiC,GAAG;AACtCC,EAAAA,OAAO,EAAEpI,iBAD6B;AAEtCqI,EAAAA,WAAW,EAAE7I,UAAU,CAAC,MAAM8I,mBAAP,CAFe;AAGtCC,EAAAA,KAAK,EAAE;AAH+B,CAA1C;;AAKA,MAAMD,mBAAN,CAA0B;AACtBzC,EAAAA,WAAW,CAAC2C,OAAD,EAAUC,GAAV,EAAeC,MAAf,EAAuBC,kBAAvB,EAA2C;AAClD,SAAKF,GAAL,GAAWA,GAAX;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKC,kBAAL,GAA0BA,kBAA1B;AACA;;AACA,SAAK5D,QAAL,GAAgB,CAAhB;AACA;;AACA,SAAKC,UAAL,GAAkB,CAAlB;AACA;;AACA,SAAKC,WAAL,GAAmB,EAAnB;AACA;;AACA,SAAKlB,aAAL,GAAqB,KAArB;AACA;;AACA,SAAKC,QAAL,GAAgB,KAAhB;AACA;;AACA,SAAKE,UAAL,GAAkB,IAAlB;AACA;;AACA,SAAKC,SAAL,GAAiB,IAAjB;AACA;;AACA,SAAKe,YAAL,GAAoB,IAApB;AACA;;AACA,SAAKC,YAAL,GAAoB,IAApB;AACA;;AACA,SAAKY,WAAL,GAAmB,IAAnB;AACA;;AACA,SAAKX,WAAL,GAAmB,KAAnB;AACA;;AACA,SAAKC,SAAL,GAAiB,CAAC,IAAD,EAAO,IAAP,CAAjB;AACA;;AACA,SAAKW,gBAAL,GAAwB,IAAxB;AACA;;AACA,SAAKC,kBAAL,GAA0B,IAA1B;AACA;;AACA,SAAKC,kBAAL,GAA0B,IAA1B;AACA;;AACA,SAAK0C,OAAL,GAAe,IAAInJ,YAAJ,EAAf;AACA;;AACA,SAAKoJ,cAAL,GAAsB,IAAIpJ,YAAJ,EAAtB,CArCkD,CAsClD;;AACA,SAAKgD,KAAL,GAAa,EAAb;AACA,SAAKL,OAAL,GAAe,EAAf;AACA,SAAKL,OAAL,GAAe,EAAf;AACA,SAAK+G,QAAL,GAAgB,EAAhB,CA1CkD,CA2ClD;;AACA,SAAKC,YAAL,GAAoB,KAApB;AACA,SAAKC,cAAL,GAAsB,KAAtB;AACA,SAAKC,cAAL,GAAsB,KAAtB,CA9CkD,CA+ClD;;AACA,SAAKC,UAAL,GAAkB,OAAlB;AACA,SAAKC,YAAL,GAAoB,SAApB;AACA,SAAKC,YAAL,GAAoB,SAApB,CAlDkD,CAmDlD;;AACA,SAAK9E,iBAAL,GAAyB,IAAzB;AACA,SAAKG,mBAAL,GAA2B,IAA3B;AACA,SAAKG,mBAAL,GAA2B,IAA3B;AACA,SAAKL,iBAAL,GAAyB,IAAzB;AACA,SAAKG,mBAAL,GAA2B,IAA3B;AACA,SAAKG,mBAAL,GAA2B,IAA3B;AACA,SAAKW,iBAAL,GAAyB,IAAzB,CA1DkD,CA2DlD;;AACA,SAAK6D,QAAL,GAAgBC,QAAQ,CAACC,SAAzB,CA5DkD,CA6DlD;;AACA,SAAKC,SAAL,GAAiBF,QAAQ,CAACC,SAA1B;AACA,SAAK9B,MAAL,GAAce,OAAd;AACAZ,IAAAA,MAAM,CAACC,MAAP,CAAc,IAAd,EAAoB,KAAKJ,MAAzB;AACA,SAAKgC,aAAL,GAAqBf,MAAM,CAACgB,MAAP,CAAc7F,KAAK,IAAIA,KAAK,CAACjD,KAA7B,EAChB+I,SADgB,CACL/I,KAAD,IAAW;AACtB;AACA,WAAKgJ,WAAL,CAAiBhJ,KAAjB;;AACA,WAAKyI,QAAL,CAAczI,KAAd;;AACA,WAAK8H,MAAL,CAAYmB,QAAZ,CAAqB,KAAKlB,kBAAL,CAAwBrB,cAAxB,CAAuCxC,gBAAgB,CAAC,IAAD,CAAvD,CAArB;AACH,KANoB,CAArB;;AAOA4D,IAAAA,MAAM,CAACgB,MAAP,CAAc7F,KAAK,IAAIA,KAAK,CAAC5C,QAA7B,EACK0I,SADL,CACgBG,aAAD,IAAmB;AAC9B,YAAMC,sBAAsB,GAAGpG,YAAY,CAAC,KAAKlB,KAAN,EAAa,KAAKL,OAAlB,EAA2B,KAAKL,OAAhC,EAAyC,KAAKL,IAAL,EAAzC,CAA3C;AACA,YAAMkH,OAAO,GAAG,KAAKnB,MAAL,CAAY3B,cAAZ,GACZ,KAAKkE,mBAAL,MAA8BD,sBADlB,GAEVA,sBAFN;AAGA,WAAKnB,OAAL,CAAaqB,IAAb,CAAkBrB,OAAlB;AACAhB,MAAAA,MAAM,CAACC,MAAP,CAAc,IAAd,EAAoBiC,aAApB;;AACArB,MAAAA,GAAG,CAACyB,YAAJ;AACH,KATD;AAUH;AACD;;;AACqB,MAAjBC,iBAAiB,GAAG;AACpB,WAAO,KAAKjF,YAAL,IAAqB,CAAC,KAAKnB,aAAlC;AACH;;AACa,MAAVqG,UAAU,GAAG;AACb,WAAO,EAAE,KAAKrG,aAAL,IAAsB,KAAKC,QAA7B,CAAP;AACH;;AACDqG,EAAAA,eAAe,GAAG;AACd,SAAKtB,YAAL,GAAoB,KAApB;AACA,SAAKC,cAAL,GAAsB,KAAtB;AACA,SAAKC,cAAL,GAAsB,KAAtB;AACH;;AACDvH,EAAAA,IAAI,GAAG;AACH,WAAO,KAAKyD,YAAL,IAAqB,KAAK2D,QAAL,KAAkB,KAAKzD,SAAL,CAAe,CAAf,CAA9C;AACH;;AACDiF,EAAAA,OAAO,CAACC,MAAD,EAAS;AACZA,IAAAA,MAAM,CAACC,cAAP;AACH;;AACDC,EAAAA,SAAS,CAACF,MAAD,EAAS;AACd,WAAOG,IAAI,CAACC,IAAL,CAAUJ,MAAM,CAACK,MAAP,IAAiB,CAA3B,IAAgC,CAAC,CAAxC;AACH;;AACDC,EAAAA,WAAW,GAAG;AACV,SAAKnC,MAAL,CAAYmB,QAAZ,CAAqB,KAAKlB,kBAAL,CAAwBrB,cAAxB,CAAuCxC,gBAAgB,CAAC,IAAD,CAAvD,CAArB;AACH;;AACDiC,EAAAA,WAAW,CAAC1C,IAAD,EAAOJ,MAAM,GAAG,EAAhB,EAAoB;AAC3B,SAAKoG,eAAL;;AACA,SAAK3B,MAAL,CAAYmB,QAAZ,CAAqB,KAAKlB,kBAAL,CAAwB5B,WAAxB,CAAoC;AAAE1C,MAAAA,IAAF;AAAQJ,MAAAA;AAAR,KAApC,CAArB;AACH;;AACDgD,EAAAA,aAAa,CAAC5C,IAAD,EAAOJ,MAAM,GAAG,EAAhB,EAAoB;AAC7B,SAAKoG,eAAL;;AACA,SAAK3B,MAAL,CAAYmB,QAAZ,CAAqB,KAAKlB,kBAAL,CAAwB1B,aAAxB,CAAsC;AAAE5C,MAAAA,IAAF;AAAQJ,MAAAA;AAAR,KAAtC,CAArB;AACH;;AACDkD,EAAAA,aAAa,CAAC9C,IAAD,EAAOJ,MAAM,GAAG,EAAhB,EAAoB;AAC7B,SAAKoG,eAAL;;AACA,SAAK3B,MAAL,CAAYmB,QAAZ,CAAqB,KAAKlB,kBAAL,CAAwBxB,aAAxB,CAAsC;AAAE9C,MAAAA,IAAF;AAAQJ,MAAAA;AAAR,KAAtC,CAArB;AACH;;AACD6G,EAAAA,WAAW,CAACC,MAAD,EAAS;AAChB,SAAKV,eAAL;AACA,SAAK5H,KAAL,GAAasI,MAAM,CAACnK,KAApB;AACA,UAAMmJ,sBAAsB,GAAGzG,gBAAgB,CAAC,KAAKb,KAAN,EAAa,KAAKf,IAAL,EAAb,CAAhB,IAA6C,KAAKV,YAAL,EAA5E;AACA,UAAM4H,OAAO,GAAG,KAAKnB,MAAL,CAAY3B,cAAZ,GACZ,KAAKkE,mBAAL,MAA8BD,sBADlB,GAEVA,sBAFN;;AAGA,QAAI,CAACnB,OAAL,EAAc;AACV,WAAKG,YAAL,GAAoB,IAApB;AACA,WAAKH,OAAL,CAAaqB,IAAb,CAAkB,KAAlB;AACA,WAAKZ,QAAL,CAAc,IAAd;AACA;AACH;;AACD,SAAK2B,WAAL;AACH;;AACDC,EAAAA,aAAa,CAACF,MAAD,EAAS;AAClB,SAAKV,eAAL;AACA,SAAKjI,OAAL,GAAe2I,MAAM,CAACnK,KAAtB;AACA,UAAMmJ,sBAAsB,GAAGxG,kBAAkB,CAAC,KAAKnB,OAAN,CAAlB,IAAoC,KAAKpB,YAAL,EAAnE;AACA,UAAM4H,OAAO,GAAG,KAAKnB,MAAL,CAAY3B,cAAZ,GACZ,KAAKkE,mBAAL,MAA8BD,sBADlB,GAEVA,sBAFN;;AAGA,QAAI,CAACnB,OAAL,EAAc;AACV,WAAKI,cAAL,GAAsB,IAAtB;AACA,WAAKJ,OAAL,CAAaqB,IAAb,CAAkB,KAAlB;AACA,WAAKZ,QAAL,CAAc,IAAd;AACA;AACH;;AACD,SAAK2B,WAAL;AACH;;AACDE,EAAAA,aAAa,CAACH,MAAD,EAAS;AAClB,SAAKV,eAAL;AACA,SAAKtI,OAAL,GAAegJ,MAAM,CAACnK,KAAtB;AACA,UAAMmJ,sBAAsB,GAAGvG,kBAAkB,CAAC,KAAKzB,OAAN,CAAlB,IAAoC,KAAKf,YAAL,EAAnE;AACA,UAAM4H,OAAO,GAAG,KAAKnB,MAAL,CAAY3B,cAAZ,GACZ,KAAKkE,mBAAL,MAA8BD,sBADlB,GAEVA,sBAFN;;AAGA,QAAI,CAACnB,OAAL,EAAc;AACV,WAAKK,cAAL,GAAsB,IAAtB;AACA,WAAKL,OAAL,CAAaqB,IAAb,CAAkB,KAAlB;AACA,WAAKZ,QAAL,CAAc,IAAd;AACA;AACH;;AACD,SAAK2B,WAAL;AACH;;AACDhK,EAAAA,YAAY,GAAG;AACX,WAAOyC,iBAAiB,CAAC;AACrB9B,MAAAA,IAAI,EAAE,KAAKc,KADU;AAErBZ,MAAAA,MAAM,EAAE,KAAKO,OAFQ;AAGrBL,MAAAA,OAAO,EAAE,KAAKA,OAHO;AAIrBL,MAAAA,IAAI,EAAE,KAAKA,IAAL;AAJe,KAAD,EAKrB,KAAKN,GALgB,EAKX,KAAKD,GALM,CAAxB;AAMH;;AACD6I,EAAAA,mBAAmB,GAAG;AAClB,WAAOtG,iBAAiB,CAAC,KAAKjB,KAAN,EAAa,KAAKL,OAAlB,EAA2B,KAAKL,OAAhC,CAAxB;AACH;;AACDiJ,EAAAA,WAAW,GAAG;AACV,UAAMG,QAAQ,GAAG,KAAK/F,WAAL,GAAmB,KAAKrD,OAAxB,GAAkC,KAAK,CAAxD;;AACA,UAAMqJ,QAAQ,GAAG,KAAKrF,WAAL,GAAmB,KAAK3D,OAAxB,GAAkC,KAAK,CAAxD;;AACA,UAAM2H,sBAAsB,GAAGpG,YAAY,CAAC,KAAKlB,KAAN,EAAa2I,QAAb,EAAuBD,QAAvB,EAAiC,KAAKzJ,IAAL,EAAjC,CAA3C;AACA,UAAMkH,OAAO,GAAG,KAAKnB,MAAL,CAAY3B,cAAZ,GACZ,KAAKkE,mBAAL,MAA8BD,sBADlB,GAEVA,sBAFN;;AAGA,QAAI,CAACnB,OAAL,EAAc;AACV,WAAKA,OAAL,CAAaqB,IAAb,CAAkB,KAAlB;AACA,WAAKZ,QAAL,CAAc,IAAd;AACA;AACH;;AACD,SAAKX,MAAL,CAAYmB,QAAZ,CAAqB,KAAKlB,kBAAL,CAAwBpG,OAAxB,CAAgC;AACjDZ,MAAAA,IAAI,EAAE,KAAKc,KADsC;AAEjDZ,MAAAA,MAAM,EAAE,KAAKO,OAFoC;AAGjDL,MAAAA,OAAO,EAAE,KAAKA,OAHmC;AAIjDL,MAAAA,IAAI,EAAE,KAAKA,IAAL;AAJ2C,KAAhC,CAArB;AAMH;;AACD2J,EAAAA,cAAc,GAAG;AACb,QAAI,CAAC,KAAKlG,YAAN,IAAsB,CAAC,KAAKiF,UAAhC,EAA4C;AACxC;AACH;;AACD,UAAMkB,gBAAgB,GAAG,EAAzB;;AACA,SAAK5C,MAAL,CAAYmB,QAAZ,CAAqB,KAAKlB,kBAAL,CAAwB5B,WAAxB,CAAoC;AACrD1C,MAAAA,IAAI,EAAEiH,gBAD+C;AAErDrH,MAAAA,MAAM,EAAE;AAF6C,KAApC,CAArB;AAIH;AACD;AACJ;AACA;;;AACI2C,EAAAA,UAAU,CAAC2E,GAAD,EAAM;AACZ,QAAI5K,WAAW,CAAC4K,GAAD,CAAf,EAAsB;AAClB,WAAKlB,eAAL;;AACA,WAAK3B,MAAL,CAAYmB,QAAZ,CAAqB,KAAKlB,kBAAL,CAAwB/B,UAAxB,CAAmC5E,SAAS,CAACuJ,GAAD,CAA5C,CAArB;AACH,KAHD,MAIK,IAAIA,GAAG,IAAI,IAAX,EAAiB;AAClB,WAAK7C,MAAL,CAAYmB,QAAZ,CAAqB,KAAKlB,kBAAL,CAAwB/B,UAAxB,EAArB;AACH;AACJ;AACD;AACJ;AACA;AACI;;;AACA4E,EAAAA,gBAAgB,CAACC,EAAD,EAAK;AACjB,SAAKpC,QAAL,GAAgBoC,EAAhB;AACH;AACD;AACJ;AACA;;;AACIC,EAAAA,iBAAiB,CAACD,EAAD,EAAK;AAClB,SAAKjC,SAAL,GAAiBiC,EAAjB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIE,EAAAA,gBAAgB,CAACC,UAAD,EAAa;AACzB,SAAK5H,QAAL,GAAgB4H,UAAhB;;AACA,SAAKnD,GAAL,CAASyB,YAAT;AACH;;AACD2B,EAAAA,WAAW,GAAG;AACV,QAAIC,EAAJ;;AACA,KAACA,EAAE,GAAG,KAAKrC,aAAX,MAA8B,IAA9B,IAAsCqC,EAAE,KAAK,KAAK,CAAlD,GAAsD,KAAK,CAA3D,GAA+DA,EAAE,CAACC,WAAH,EAA/D;AACH;;AACDnC,EAAAA,WAAW,CAAChJ,KAAD,EAAQ;AACf,QAAI,CAACA,KAAD,IAAU,CAACD,WAAW,CAACC,KAAD,CAA1B,EAAmC;AAC/B,WAAK6B,KAAL,GAAa,EAAb;AACA,WAAKL,OAAL,GAAe,EAAf;AACA,WAAKL,OAAL,GAAe,EAAf;AACA,WAAK+G,QAAL,GAAgB,KAAKzD,SAAL,CAAe,CAAf,CAAhB;AACA,WAAKwD,cAAL,CAAoBoB,IAApB,CAAyB,KAAKnB,QAA9B;AACA;AACH;;AACD,UAAM3F,MAAM,GAAGnB,SAAS,CAACpB,KAAD,CAAxB;;AACA,QAAI,CAACuC,MAAL,EAAa;AACT;AACH;;AACD,UAAMmI,gBAAgB,GAAG,EAAzB;;AACA,QAAIU,MAAM,GAAG7I,MAAM,CAACpC,QAAP,EAAb;;AACA,QAAI,KAAKoE,YAAT,EAAuB;AACnB,WAAK2D,QAAL,GAAgB,KAAKzD,SAAL,CAAe2G,MAAM,IAAIV,gBAAV,GAA6B,CAA7B,GAAiC,CAAhD,CAAhB;AACA,WAAKzC,cAAL,CAAoBoB,IAApB,CAAyB,KAAKnB,QAA9B;AACAkD,MAAAA,MAAM,GAAGA,MAAM,GAAGV,gBAAlB,CAHmB,CAInB;;AACA,UAAIU,MAAM,KAAK,CAAf,EAAkB;AACdA,QAAAA,MAAM,GAAGV,gBAAT;AACH;AACJ;;AACD,SAAK7I,KAAL,GAAaS,SAAS,CAAC8I,MAAD,CAAtB;AACA,SAAK5J,OAAL,GAAec,SAAS,CAACC,MAAM,CAACd,UAAP,EAAD,CAAxB;AACA,SAAKN,OAAL,GAAemB,SAAS,CAACC,MAAM,CAAC8I,aAAP,EAAD,CAAxB;AACH;;AA/QqB;;AAiR1B3D,mBAAmB,CAAChC,IAApB;AAAA,mBAAgHgC,mBAAhH,EAjbmGhJ,EAibnG,mBAAqJsG,gBAArJ,GAjbmGtG,EAibnG,mBAAkLA,EAAE,CAAC4M,iBAArL,GAjbmG5M,EAibnG,mBAAmN2I,eAAnN,GAjbmG3I,EAibnG,mBAA+OqH,iBAA/O;AAAA;;AACA2B,mBAAmB,CAAC6D,IAApB,kBAlbmG7M,EAkbnG;AAAA,QAAoGgJ,mBAApG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAlbmGhJ,EAkbnG,oBAA6rB,CAAC6I,iCAAD,EAAoCF,eAApC,CAA7rB,GAlbmG3I,EAkbnG;AAAA;AAAA;AAAA;AAAA;AAAA;AAlbmGA,MAAAA,EAkb8rB,2BAAjyB;AAlbmGA,MAAAA,EAkbysB,2BAA5yB;AAlbmGA,MAAAA,EAkbotB,2BAAvzB;AAlbmGA,MAAAA,EAkbmzB,wBAAt5B;AAlbmGA,MAAAA,EAkb+zB,0BAAl6B;AAlbmGA,MAAAA,EAkb05B;AAAA,eAAU,6BAAV;AAAA,QAA7/B;AAlbmGA,MAAAA,EAkbo8B,wBAAviC;AAlbmGA,MAAAA,EAkbo/B,eAAvlC;AAlbmGA,MAAAA,EAkb8/B,eAAjmC;AAlbmGA,MAAAA,EAkb+hC,gEAAloC;AAlbmGA,MAAAA,EAkb6nC,gEAAhuC;AAlbmGA,MAAAA,EAkbq4C,gEAAx+C;AAlbmGA,MAAAA,EAkbu9C,gEAA1jD;AAlbmGA,MAAAA,EAkbgvD,kEAAn1D;AAlbmGA,MAAAA,EAkb00D,kEAA76D;AAlbmGA,MAAAA,EAkb82D,eAAj9D;AAlbmGA,MAAAA,EAkbu3D,yBAA19D;AAlbmGA,MAAAA,EAkbq5D,4BAAx/D;AAlbmGA,MAAAA,EAkb89D,+BAAjkE;AAlbmGA,MAAAA,EAkb0yE;AAAU,2BAAV;AAAA,eAA0B,+BAAuB,qBAAvB,EAA0C,OAA1C,CAA1B;AAAA;AAAA,eAAiH,8BAAsB,KAAtB,CAAjH;AAAA;AAAA,eAAoL,+BAAuB,KAAvB,CAApL;AAAA;AAAA,eAA6O,8BAA7O;AAAA,QAA74E;AAlbmGA,MAAAA,EAkb89D,eAAjkE;AAlbmGA,MAAAA,EAkbqlF,eAAxrF;AAlbmGA,MAAAA,EAkbsnF,kEAAztF;AAlbmGA,MAAAA,EAkb8rF,kEAAjyF;AAlbmGA,MAAAA,EAkbm9G,kEAAtjH;AAlbmGA,MAAAA,EAkb2hH,kEAA9nH;AAlbmGA,MAAAA,EAkbyzI,kEAA55I;AAlbmGA,MAAAA,EAkbw4I,kEAA3+I;AAlbmGA,MAAAA,EAkbitJ,eAApzJ;AAlbmGA,MAAAA,EAkb0tJ,4BAA7zJ;AAlbmGA,MAAAA,EAkbyzJ,yBAA55J;AAlbmGA,MAAAA,EAkbq0J,2BAAx6J;AAlbmGA,MAAAA,EAkbg6J;AAAA,eAAU,8BAAV;AAAA,QAAngK;AAlbmGA,MAAAA,EAkb68J,yBAAhjK;AAlbmGA,MAAAA,EAkbugK,eAA1mK;AAlbmGA,MAAAA,EAkbihK,eAApnK;AAlbmGA,MAAAA,EAkbkjK,kEAArpK;AAlbmGA,MAAAA,EAkb+oK,kEAAlvK;AAlbmGA,MAAAA,EAkbo6K,kEAAvgL;AAlbmGA,MAAAA,EAkbq/K,kEAAxlL;AAlbmGA,MAAAA,EAkbixL,kEAAp3L;AAlbmGA,MAAAA,EAkb22L,kEAA98L;AAlbmGA,MAAAA,EAkb+4L,eAAl/L;AAlbmGA,MAAAA,EAkbw5L,eAA3/L;AAlbmGA,MAAAA,EAkbk6L,eAArgM;AAAA;;AAAA;AAlbmGA,MAAAA,EAkb8uB,aAAj1B;AAlbmGA,MAAAA,EAkb8uB,wCAAj1B;AAlbmGA,MAAAA,EAkby1B,aAA57B;AAlbmGA,MAAAA,EAkby1B,mEAA57B;AAlbmGA,MAAAA,EAkboiC,aAAvoC;AAlbmGA,MAAAA,EAkboiC,oCAAvoC;AAlbmGA,MAAAA,EAkbkoC,aAAruC;AAlbmGA,MAAAA,EAkbkoC,oCAAruC;AAlbmGA,MAAAA,EAkb04C,aAA7+C;AAlbmGA,MAAAA,EAkb04C,oCAA7+C;AAlbmGA,MAAAA,EAkb49C,aAA/jD;AAlbmGA,MAAAA,EAkb49C,oCAA/jD;AAlbmGA,MAAAA,EAkbqvD,aAAx1D;AAlbmGA,MAAAA,EAkbqvD,qCAAx1D;AAlbmGA,MAAAA,EAkb+0D,aAAl7D;AAlbmGA,MAAAA,EAkb+0D,qCAAl7D;AAlbmGA,MAAAA,EAkbm7D,aAAthE;AAlbmGA,MAAAA,EAkbm7D,2CAAthE;AAlbmGA,MAAAA,EAkbm/D,aAAtlE;AAlbmGA,MAAAA,EAkbm/D,4CAAtlE;AAlbmGA,MAAAA,EAkb0mE,6HAA7sE;AAlbmGA,MAAAA,EAkbojF,0CAAvpF;AAlbmGA,MAAAA,EAkb2nF,aAA9tF;AAlbmGA,MAAAA,EAkb2nF,oCAA9tF;AAlbmGA,MAAAA,EAkb6tF,aAAh0F;AAlbmGA,MAAAA,EAkb6tF,oCAAh0F;AAlbmGA,MAAAA,EAkbw9G,aAA3jH;AAlbmGA,MAAAA,EAkbw9G,oCAA3jH;AAlbmGA,MAAAA,EAkb0jH,aAA7pH;AAlbmGA,MAAAA,EAkb0jH,oCAA7pH;AAlbmGA,MAAAA,EAkb8zI,aAAj6I;AAlbmGA,MAAAA,EAkb8zI,qCAAj6I;AAlbmGA,MAAAA,EAkb64I,aAAh/I;AAlbmGA,MAAAA,EAkb64I,qCAAh/I;AAlbmGA,MAAAA,EAkbovJ,aAAv1J;AAlbmGA,MAAAA,EAkbovJ,wCAAv1J;AAlbmGA,MAAAA,EAkb+1J,aAAl8J;AAlbmGA,MAAAA,EAkb+1J,mEAAl8J;AAlbmGA,MAAAA,EAkbujK,aAA1pK;AAlbmGA,MAAAA,EAkbujK,oCAA1pK;AAlbmGA,MAAAA,EAkbopK,aAAvvK;AAlbmGA,MAAAA,EAkbopK,oCAAvvK;AAlbmGA,MAAAA,EAkby6K,aAA5gL;AAlbmGA,MAAAA,EAkby6K,oCAA5gL;AAlbmGA,MAAAA,EAkb0/K,aAA7lL;AAlbmGA,MAAAA,EAkb0/K,oCAA7lL;AAlbmGA,MAAAA,EAkbsxL,aAAz3L;AAlbmGA,MAAAA,EAkbsxL,qCAAz3L;AAlbmGA,MAAAA,EAkbg3L,aAAn9L;AAlbmGA,MAAAA,EAkbg3L,qCAAn9L;AAAA;AAAA;AAAA,eAAg0Mc,EAAE,CAACgM,IAAn0M;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA,qDAnbmG9M,EAmbnG,mBAA2FgJ,mBAA3F,EAA4H,CAAC;AACjH9B,IAAAA,IAAI,EAAE9G,SAD2G;AAEjH+G,IAAAA,IAAI,EAAE,CAAC;AAAE4F,MAAAA,QAAQ,EAAE,YAAZ;AAA0BC,MAAAA,eAAe,EAAE3M,uBAAuB,CAAC4M,MAAnE;AAA2EC,MAAAA,SAAS,EAAE,CAACrE,iCAAD,EAAoCF,eAApC,CAAtF;AAA4IwE,MAAAA,MAAM,EAAE,CAAE;AACzK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GA1BuK,CAApJ;AA0BbC,MAAAA,aAAa,EAAE9M,iBAAiB,CAAC+M,IA1BpB;AA0B0BC,MAAAA,QAAQ,EAAE;AA1BpC,KAAD;AAF2G,GAAD,CAA5H,EA6B4B,YAAY;AAAE,WAAO,CAAC;AAAEpG,MAAAA,IAAI,EAAEZ;AAAR,KAAD,EAA6B;AAAEY,MAAAA,IAAI,EAAElH,EAAE,CAAC4M;AAAX,KAA7B,EAA6D;AAAE1F,MAAAA,IAAI,EAAEyB;AAAR,KAA7D,EAAwF;AAAEzB,MAAAA,IAAI,EAAEG;AAAR,KAAxF,CAAP;AAA8H,GA7BxK,EA6B0L;AAAE5B,IAAAA,QAAQ,EAAE,CAAC;AACvLyB,MAAAA,IAAI,EAAE3G;AADiL,KAAD,CAAZ;AAE1KmF,IAAAA,UAAU,EAAE,CAAC;AACbwB,MAAAA,IAAI,EAAE3G;AADO,KAAD,CAF8J;AAI1KoF,IAAAA,WAAW,EAAE,CAAC;AACduB,MAAAA,IAAI,EAAE3G;AADQ,KAAD,CAJ6J;AAM1KkE,IAAAA,aAAa,EAAE,CAAC;AAChByC,MAAAA,IAAI,EAAE3G;AADU,KAAD,CAN2J;AAQ1KmE,IAAAA,QAAQ,EAAE,CAAC;AACXwC,MAAAA,IAAI,EAAE3G;AADK,KAAD,CARgK;AAU1KqE,IAAAA,UAAU,EAAE,CAAC;AACbsC,MAAAA,IAAI,EAAE3G;AADO,KAAD,CAV8J;AAY1KsE,IAAAA,SAAS,EAAE,CAAC;AACZqC,MAAAA,IAAI,EAAE3G;AADM,KAAD,CAZ+J;AAc1KqF,IAAAA,YAAY,EAAE,CAAC;AACfsB,MAAAA,IAAI,EAAE3G;AADS,KAAD,CAd4J;AAgB1KsF,IAAAA,YAAY,EAAE,CAAC;AACfqB,MAAAA,IAAI,EAAE3G;AADS,KAAD,CAhB4J;AAkB1KkG,IAAAA,WAAW,EAAE,CAAC;AACdS,MAAAA,IAAI,EAAE3G;AADQ,KAAD,CAlB6J;AAoB1KuF,IAAAA,WAAW,EAAE,CAAC;AACdoB,MAAAA,IAAI,EAAE3G;AADQ,KAAD,CApB6J;AAsB1KwF,IAAAA,SAAS,EAAE,CAAC;AACZmB,MAAAA,IAAI,EAAE3G;AADM,KAAD,CAtB+J;AAwB1KsB,IAAAA,GAAG,EAAE,CAAC;AACNqF,MAAAA,IAAI,EAAE3G;AADA,KAAD,CAxBqK;AA0B1KuB,IAAAA,GAAG,EAAE,CAAC;AACNoF,MAAAA,IAAI,EAAE3G;AADA,KAAD,CA1BqK;AA4B1KmG,IAAAA,gBAAgB,EAAE,CAAC;AACnBQ,MAAAA,IAAI,EAAE3G;AADa,KAAD,CA5BwJ;AA8B1KoG,IAAAA,kBAAkB,EAAE,CAAC;AACrBO,MAAAA,IAAI,EAAE3G;AADe,KAAD,CA9BsJ;AAgC1KqG,IAAAA,kBAAkB,EAAE,CAAC;AACrBM,MAAAA,IAAI,EAAE3G;AADe,KAAD,CAhCsJ;AAkC1K+I,IAAAA,OAAO,EAAE,CAAC;AACVpC,MAAAA,IAAI,EAAE1G;AADI,KAAD,CAlCiK;AAoC1K+I,IAAAA,cAAc,EAAE,CAAC;AACjBrC,MAAAA,IAAI,EAAE1G;AADW,KAAD;AApC0J,GA7B1L;AAAA;;AAqEA,MAAM+M,gBAAN,CAAuB;AACL,SAAPC,OAAO,GAAG;AACb,WAAO;AACHC,MAAAA,QAAQ,EAAEF,gBADP;AAEHL,MAAAA,SAAS,EAAE,CAAC7F,iBAAD,EAAoBsB,eAApB;AAFR,KAAP;AAIH;;AANkB;;AAQvB4E,gBAAgB,CAACvG,IAAjB;AAAA,mBAA6GuG,gBAA7G;AAAA;;AACAA,gBAAgB,CAACG,IAAjB,kBAjgBmG1N,EAigBnG;AAAA,QAA8GuN;AAA9G;AACAA,gBAAgB,CAACI,IAAjB,kBAlgBmG3N,EAkgBnG;AAAA,aAA2I,CAAC2I,eAAD,CAA3I;AAAA,YAAwK,CAAC5H,YAAD,CAAxK;AAAA;;AACA;AAAA,qDAngBmGf,EAmgBnG,mBAA2FuN,gBAA3F,EAAyH,CAAC;AAC9GrG,IAAAA,IAAI,EAAEzG,QADwG;AAE9G0G,IAAAA,IAAI,EAAE,CAAC;AACCyG,MAAAA,OAAO,EAAE,CAAC7M,YAAD,CADV;AAEC8M,MAAAA,YAAY,EAAE,CAAC7E,mBAAD,CAFf;AAGC8E,MAAAA,OAAO,EAAE,CAAC9E,mBAAD,CAHV;AAICkE,MAAAA,SAAS,EAAE,CAACvE,eAAD;AAJZ,KAAD;AAFwG,GAAD,CAAzH;AAAA;AAUA;AACA;AACA;;;AAEA,SAAStB,iBAAT,EAA4B2B,mBAA5B,EAAiD1C,gBAAjD,EAAmEiH,gBAAnE,EAAqF5E,eAArF", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { BehaviorSubject } from 'rxjs';\nimport { MiniStore, MiniState } from 'ngx-bootstrap/mini-ngrx';\nimport * as i4 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nconst dex = 10;\nconst hoursPerDay = 24;\nconst hoursPerDayHalf = 12;\nconst minutesPerHour = 60;\nconst secondsPerMinute = 60;\nfunction isValidDate(value) {\n    if (!value) {\n        return false;\n    }\n    if (value instanceof Date && isNaN(value.getHours())) {\n        return false;\n    }\n    if (typeof value === 'string') {\n        return isValidDate(new Date(value));\n    }\n    return true;\n}\nfunction isValidLimit(controls, newDate) {\n    if (controls.min && newDate < controls.min) {\n        return false;\n    }\n    if (controls.max && newDate > controls.max) {\n        return false;\n    }\n    return true;\n}\nfunction toNumber(value) {\n    if (typeof value === 'undefined') {\n        return NaN;\n    }\n    if (typeof value === 'number') {\n        return value;\n    }\n    return parseInt(value, dex);\n}\nfunction isNumber(value) {\n    return !isNaN(toNumber(value));\n}\nfunction parseHours(value, isPM = false) {\n    const hour = toNumber(value);\n    if (isNaN(hour) ||\n        hour < 0 ||\n        hour > (isPM ? hoursPerDayHalf : hoursPerDay)) {\n        return NaN;\n    }\n    return hour;\n}\nfunction parseMinutes(value) {\n    const minute = toNumber(value);\n    if (isNaN(minute) || minute < 0 || minute > minutesPerHour) {\n        return NaN;\n    }\n    return minute;\n}\nfunction parseSeconds(value) {\n    const seconds = toNumber(value);\n    if (isNaN(seconds) || seconds < 0 || seconds > secondsPerMinute) {\n        return NaN;\n    }\n    return seconds;\n}\nfunction parseTime(value) {\n    if (typeof value === 'string') {\n        return new Date(value);\n    }\n    return value;\n}\nfunction changeTime(value, diff) {\n    if (!value) {\n        return changeTime(createDate(new Date(), 0, 0, 0), diff);\n    }\n    if (!diff) {\n        return value;\n    }\n    let hour = value.getHours();\n    let minutes = value.getMinutes();\n    let seconds = value.getSeconds();\n    if (diff.hour) {\n        hour = hour + toNumber(diff.hour);\n    }\n    if (diff.minute) {\n        minutes = minutes + toNumber(diff.minute);\n    }\n    if (diff.seconds) {\n        seconds = seconds + toNumber(diff.seconds);\n    }\n    return createDate(value, hour, minutes, seconds);\n}\nfunction setTime(value, opts) {\n    let hour = parseHours(opts.hour);\n    const minute = parseMinutes(opts.minute);\n    const seconds = parseSeconds(opts.seconds) || 0;\n    if (opts.isPM && hour !== 12) {\n        hour += hoursPerDayHalf;\n    }\n    if (!value) {\n        if (!isNaN(hour) && !isNaN(minute)) {\n            return createDate(new Date(), hour, minute, seconds);\n        }\n        return value;\n    }\n    if (isNaN(hour) || isNaN(minute)) {\n        return value;\n    }\n    return createDate(value, hour, minute, seconds);\n}\nfunction createDate(value, hours, minutes, seconds) {\n    const newValue = new Date(value.getFullYear(), value.getMonth(), value.getDate(), hours, minutes, seconds, value.getMilliseconds());\n    // #3139 ensure date part remains unchanged\n    newValue.setFullYear(value.getFullYear());\n    newValue.setMonth(value.getMonth());\n    newValue.setDate(value.getDate());\n    return newValue;\n}\nfunction padNumber(value) {\n    const _value = value.toString();\n    if (_value.length > 1) {\n        return _value;\n    }\n    return `0${_value}`;\n}\nfunction isHourInputValid(hours, isPM) {\n    return !isNaN(parseHours(hours, isPM));\n}\nfunction isMinuteInputValid(minutes) {\n    return !isNaN(parseMinutes(minutes));\n}\nfunction isSecondInputValid(seconds) {\n    return !isNaN(parseSeconds(seconds));\n}\nfunction isInputLimitValid(diff, max, min) {\n    const newDate = setTime(new Date(), diff);\n    if (!newDate) {\n        return false;\n    }\n    if (max && newDate > max) {\n        return false;\n    }\n    if (min && newDate < min) {\n        return false;\n    }\n    return true;\n}\nfunction isOneOfDatesEmpty(hours, minutes, seconds) {\n    return hours.length === 0 || minutes.length === 0 || seconds.length === 0;\n}\nfunction isInputValid(hours, minutes = '0', seconds = '0', isPM) {\n    return isHourInputValid(hours, isPM)\n        && isMinuteInputValid(minutes)\n        && isSecondInputValid(seconds);\n}\n\nfunction canChangeValue(state, event) {\n    if (state.readonlyInput || state.disabled) {\n        return false;\n    }\n    if (event) {\n        if (event.source === 'wheel' && !state.mousewheel) {\n            return false;\n        }\n        if (event.source === 'key' && !state.arrowkeys) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction canChangeHours(event, controls) {\n    if (!event.step) {\n        return false;\n    }\n    if (event.step > 0 && !controls.canIncrementHours) {\n        return false;\n    }\n    if (event.step < 0 && !controls.canDecrementHours) {\n        return false;\n    }\n    return true;\n}\nfunction canChangeMinutes(event, controls) {\n    if (!event.step) {\n        return false;\n    }\n    if (event.step > 0 && !controls.canIncrementMinutes) {\n        return false;\n    }\n    if (event.step < 0 && !controls.canDecrementMinutes) {\n        return false;\n    }\n    return true;\n}\nfunction canChangeSeconds(event, controls) {\n    if (!event.step) {\n        return false;\n    }\n    if (event.step > 0 && !controls.canIncrementSeconds) {\n        return false;\n    }\n    if (event.step < 0 && !controls.canDecrementSeconds) {\n        return false;\n    }\n    return true;\n}\nfunction getControlsValue(state) {\n    const { hourStep, minuteStep, secondsStep, readonlyInput, disabled, mousewheel, arrowkeys, showSpinners, showMeridian, showSeconds, meridians, min, max } = state;\n    return {\n        hourStep,\n        minuteStep,\n        secondsStep,\n        readonlyInput,\n        disabled,\n        mousewheel,\n        arrowkeys,\n        showSpinners,\n        showMeridian,\n        showSeconds,\n        meridians,\n        min,\n        max\n    };\n}\nfunction timepickerControls(value, state) {\n    const hoursPerDay = 24;\n    const hoursPerDayHalf = 12;\n    const { min, max, hourStep, minuteStep, secondsStep, showSeconds } = state;\n    const res = {\n        canIncrementHours: true,\n        canIncrementMinutes: true,\n        canIncrementSeconds: true,\n        canDecrementHours: true,\n        canDecrementMinutes: true,\n        canDecrementSeconds: true,\n        canToggleMeridian: true\n    };\n    if (!value) {\n        return res;\n    }\n    // compare dates\n    if (max) {\n        const _newHour = changeTime(value, { hour: hourStep });\n        res.canIncrementHours = max > _newHour && (value.getHours() + hourStep) < hoursPerDay;\n        if (!res.canIncrementHours) {\n            const _newMinutes = changeTime(value, { minute: minuteStep });\n            res.canIncrementMinutes = showSeconds\n                ? max > _newMinutes\n                : max >= _newMinutes;\n        }\n        if (!res.canIncrementMinutes) {\n            const _newSeconds = changeTime(value, { seconds: secondsStep });\n            res.canIncrementSeconds = max >= _newSeconds;\n        }\n        if (value.getHours() < hoursPerDayHalf) {\n            res.canToggleMeridian = changeTime(value, { hour: hoursPerDayHalf }) < max;\n        }\n    }\n    if (min) {\n        const _newHour = changeTime(value, { hour: -hourStep });\n        res.canDecrementHours = min < _newHour;\n        if (!res.canDecrementHours) {\n            const _newMinutes = changeTime(value, { minute: -minuteStep });\n            res.canDecrementMinutes = showSeconds\n                ? min < _newMinutes\n                : min <= _newMinutes;\n        }\n        if (!res.canDecrementMinutes) {\n            const _newSeconds = changeTime(value, { seconds: -secondsStep });\n            res.canDecrementSeconds = min <= _newSeconds;\n        }\n        if (value.getHours() >= hoursPerDayHalf) {\n            res.canToggleMeridian = changeTime(value, { hour: -hoursPerDayHalf }) > min;\n        }\n    }\n    return res;\n}\n\n/** Provides default configuration values for timepicker */\nclass TimepickerConfig {\n    constructor() {\n        /** hours change step */\n        this.hourStep = 1;\n        /** minutes change step */\n        this.minuteStep = 5;\n        /** seconds changes step */\n        this.secondsStep = 10;\n        /** if true works in 12H mode and displays AM/PM. If false works in 24H mode and hides AM/PM */\n        this.showMeridian = true;\n        /** meridian labels based on locale */\n        this.meridians = ['AM', 'PM'];\n        /** if true hours and minutes fields will be readonly */\n        this.readonlyInput = false;\n        /** if true hours and minutes fields will be disabled */\n        this.disabled = false;\n        /** if true emptyTime is not marked as invalid */\n        this.allowEmptyTime = false;\n        /** if true scroll inside hours and minutes inputs will change time */\n        this.mousewheel = true;\n        /** if true the values of hours and minutes can be changed using the up/down arrow keys on the keyboard */\n        this.arrowkeys = true;\n        /** if true spinner arrows above and below the inputs will be shown */\n        this.showSpinners = true;\n        /** show seconds in timepicker */\n        this.showSeconds = false;\n        /** show minutes in timepicker */\n        this.showMinutes = true;\n        /** placeholder for hours field in timepicker */\n        this.hoursPlaceholder = 'HH';\n        /** placeholder for minutes field in timepicker */\n        this.minutesPlaceholder = 'MM';\n        /** placeholder for seconds field in timepicker */\n        this.secondsPlaceholder = 'SS';\n        /** hours aria label */\n        this.ariaLabelHours = 'hours';\n        /** minutes aria label */\n        this.ariaLabelMinutes = 'minutes';\n        /** seconds aria label */\n        this.ariaLabelSeconds = 'seconds';\n    }\n}\nTimepickerConfig.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: TimepickerConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nTimepickerConfig.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: TimepickerConfig, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: TimepickerConfig, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }] });\n\nclass TimepickerActions {\n    writeValue(value) {\n        return {\n            type: TimepickerActions.WRITE_VALUE,\n            payload: value\n        };\n    }\n    changeHours(event) {\n        return {\n            type: TimepickerActions.CHANGE_HOURS,\n            payload: event\n        };\n    }\n    changeMinutes(event) {\n        return {\n            type: TimepickerActions.CHANGE_MINUTES,\n            payload: event\n        };\n    }\n    changeSeconds(event) {\n        return {\n            type: TimepickerActions.CHANGE_SECONDS,\n            payload: event\n        };\n    }\n    setTime(value) {\n        return {\n            type: TimepickerActions.SET_TIME_UNIT,\n            payload: value\n        };\n    }\n    updateControls(value) {\n        return {\n            type: TimepickerActions.UPDATE_CONTROLS,\n            payload: value\n        };\n    }\n}\nTimepickerActions.WRITE_VALUE = '[timepicker] write value from ng model';\nTimepickerActions.CHANGE_HOURS = '[timepicker] change hours';\nTimepickerActions.CHANGE_MINUTES = '[timepicker] change minutes';\nTimepickerActions.CHANGE_SECONDS = '[timepicker] change seconds';\nTimepickerActions.SET_TIME_UNIT = '[timepicker] set time unit';\nTimepickerActions.UPDATE_CONTROLS = '[timepicker] update controls';\nTimepickerActions.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: TimepickerActions, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nTimepickerActions.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: TimepickerActions, providedIn: 'platform' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: TimepickerActions, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'platform' }]\n        }] });\n\nconst initialState = {\n    value: void 0,\n    config: new TimepickerConfig(),\n    controls: {\n        canIncrementHours: true,\n        canIncrementMinutes: true,\n        canIncrementSeconds: true,\n        canDecrementHours: true,\n        canDecrementMinutes: true,\n        canDecrementSeconds: true,\n        canToggleMeridian: true\n    }\n};\nfunction timepickerReducer(state = initialState, action) {\n    switch (action.type) {\n        case TimepickerActions.WRITE_VALUE: {\n            return Object.assign({}, state, { value: action.payload });\n        }\n        case TimepickerActions.CHANGE_HOURS: {\n            if (!canChangeValue(state.config, action.payload) ||\n                !canChangeHours(action.payload, state.controls)) {\n                return state;\n            }\n            const _newTime = changeTime(state.value, { hour: action.payload.step });\n            if ((state.config.max || state.config.min) && !isValidLimit(state.config, _newTime)) {\n                return state;\n            }\n            return Object.assign({}, state, { value: _newTime });\n        }\n        case TimepickerActions.CHANGE_MINUTES: {\n            if (!canChangeValue(state.config, action.payload) ||\n                !canChangeMinutes(action.payload, state.controls)) {\n                return state;\n            }\n            const _newTime = changeTime(state.value, { minute: action.payload.step });\n            if ((state.config.max || state.config.min) && !isValidLimit(state.config, _newTime)) {\n                return state;\n            }\n            return Object.assign({}, state, { value: _newTime });\n        }\n        case TimepickerActions.CHANGE_SECONDS: {\n            if (!canChangeValue(state.config, action.payload) ||\n                !canChangeSeconds(action.payload, state.controls)) {\n                return state;\n            }\n            const _newTime = changeTime(state.value, {\n                seconds: action.payload.step\n            });\n            if ((state.config.max || state.config.min) && !isValidLimit(state.config, _newTime)) {\n                return state;\n            }\n            return Object.assign({}, state, { value: _newTime });\n        }\n        case TimepickerActions.SET_TIME_UNIT: {\n            if (!canChangeValue(state.config)) {\n                return state;\n            }\n            const _newTime = setTime(state.value, action.payload);\n            return Object.assign({}, state, { value: _newTime });\n        }\n        case TimepickerActions.UPDATE_CONTROLS: {\n            const _newControlsState = timepickerControls(state.value, action.payload);\n            const _newState = {\n                value: state.value,\n                config: action.payload,\n                controls: _newControlsState\n            };\n            if (state.config.showMeridian !== _newState.config.showMeridian) {\n                if (state.value) {\n                    _newState.value = new Date(state.value);\n                }\n            }\n            return Object.assign({}, state, _newState);\n        }\n        default:\n            return state;\n    }\n}\n\nclass TimepickerStore extends MiniStore {\n    constructor() {\n        const _dispatcher = new BehaviorSubject({\n            type: '[mini-ngrx] dispatcher init'\n        });\n        const state = new MiniState(initialState, _dispatcher, timepickerReducer);\n        super(_dispatcher, timepickerReducer, state);\n    }\n}\nTimepickerStore.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: TimepickerStore, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nTimepickerStore.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: TimepickerStore, providedIn: 'platform' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: TimepickerStore, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'platform' }]\n        }], ctorParameters: function () { return []; } });\n\nconst TIMEPICKER_CONTROL_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => TimepickerComponent),\n    multi: true\n};\nclass TimepickerComponent {\n    constructor(_config, _cd, _store, _timepickerActions) {\n        this._cd = _cd;\n        this._store = _store;\n        this._timepickerActions = _timepickerActions;\n        /** hours change step */\n        this.hourStep = 1;\n        /** minutes change step */\n        this.minuteStep = 5;\n        /** seconds change step */\n        this.secondsStep = 10;\n        /** if true hours and minutes fields will be readonly */\n        this.readonlyInput = false;\n        /** if true hours and minutes fields will be disabled */\n        this.disabled = false;\n        /** if true scroll inside hours and minutes inputs will change time */\n        this.mousewheel = true;\n        /** if true the values of hours and minutes can be changed using the up/down arrow keys on the keyboard */\n        this.arrowkeys = true;\n        /** if true spinner arrows above and below the inputs will be shown */\n        this.showSpinners = true;\n        /** if true meridian button will be shown */\n        this.showMeridian = true;\n        /** show minutes in timepicker */\n        this.showMinutes = true;\n        /** show seconds in timepicker */\n        this.showSeconds = false;\n        /** meridian labels based on locale */\n        this.meridians = ['AM', 'PM'];\n        /** placeholder for hours field in timepicker */\n        this.hoursPlaceholder = 'HH';\n        /** placeholder for minutes field in timepicker */\n        this.minutesPlaceholder = 'MM';\n        /** placeholder for seconds field in timepicker */\n        this.secondsPlaceholder = 'SS';\n        /** emits true if value is a valid date */\n        this.isValid = new EventEmitter();\n        /** emits value of meridian*/\n        this.meridianChange = new EventEmitter();\n        // ui variables\n        this.hours = '';\n        this.minutes = '';\n        this.seconds = '';\n        this.meridian = '';\n        // min\\max validation for input fields\n        this.invalidHours = false;\n        this.invalidMinutes = false;\n        this.invalidSeconds = false;\n        // aria-label variables\n        this.labelHours = 'hours';\n        this.labelMinutes = 'minutes';\n        this.labelSeconds = 'seconds';\n        // time picker controls state\n        this.canIncrementHours = true;\n        this.canIncrementMinutes = true;\n        this.canIncrementSeconds = true;\n        this.canDecrementHours = true;\n        this.canDecrementMinutes = true;\n        this.canDecrementSeconds = true;\n        this.canToggleMeridian = true;\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        this.onChange = Function.prototype;\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        this.onTouched = Function.prototype;\n        this.config = _config;\n        Object.assign(this, this.config);\n        this.timepickerSub = _store.select(state => state.value)\n            .subscribe((value) => {\n            // update UI values if date changed\n            this._renderTime(value);\n            this.onChange(value);\n            this._store.dispatch(this._timepickerActions.updateControls(getControlsValue(this)));\n        });\n        _store.select(state => state.controls)\n            .subscribe((controlsState) => {\n            const isTimepickerInputValid = isInputValid(this.hours, this.minutes, this.seconds, this.isPM());\n            const isValid = this.config.allowEmptyTime ?\n                this.isOneOfDatesIsEmpty() || isTimepickerInputValid\n                : isTimepickerInputValid;\n            this.isValid.emit(isValid);\n            Object.assign(this, controlsState);\n            _cd.markForCheck();\n        });\n    }\n    /** @deprecated - please use `isEditable` instead */\n    get isSpinnersVisible() {\n        return this.showSpinners && !this.readonlyInput;\n    }\n    get isEditable() {\n        return !(this.readonlyInput || this.disabled);\n    }\n    resetValidation() {\n        this.invalidHours = false;\n        this.invalidMinutes = false;\n        this.invalidSeconds = false;\n    }\n    isPM() {\n        return this.showMeridian && this.meridian === this.meridians[1];\n    }\n    prevDef($event) {\n        $event.preventDefault();\n    }\n    wheelSign($event) {\n        return Math.sign($event.deltaY || 0) * -1;\n    }\n    ngOnChanges() {\n        this._store.dispatch(this._timepickerActions.updateControls(getControlsValue(this)));\n    }\n    changeHours(step, source = '') {\n        this.resetValidation();\n        this._store.dispatch(this._timepickerActions.changeHours({ step, source }));\n    }\n    changeMinutes(step, source = '') {\n        this.resetValidation();\n        this._store.dispatch(this._timepickerActions.changeMinutes({ step, source }));\n    }\n    changeSeconds(step, source = '') {\n        this.resetValidation();\n        this._store.dispatch(this._timepickerActions.changeSeconds({ step, source }));\n    }\n    updateHours(target) {\n        this.resetValidation();\n        this.hours = target.value;\n        const isTimepickerInputValid = isHourInputValid(this.hours, this.isPM()) && this.isValidLimit();\n        const isValid = this.config.allowEmptyTime ?\n            this.isOneOfDatesIsEmpty() || isTimepickerInputValid\n            : isTimepickerInputValid;\n        if (!isValid) {\n            this.invalidHours = true;\n            this.isValid.emit(false);\n            this.onChange(null);\n            return;\n        }\n        this._updateTime();\n    }\n    updateMinutes(target) {\n        this.resetValidation();\n        this.minutes = target.value;\n        const isTimepickerInputValid = isMinuteInputValid(this.minutes) && this.isValidLimit();\n        const isValid = this.config.allowEmptyTime ?\n            this.isOneOfDatesIsEmpty() || isTimepickerInputValid\n            : isTimepickerInputValid;\n        if (!isValid) {\n            this.invalidMinutes = true;\n            this.isValid.emit(false);\n            this.onChange(null);\n            return;\n        }\n        this._updateTime();\n    }\n    updateSeconds(target) {\n        this.resetValidation();\n        this.seconds = target.value;\n        const isTimepickerInputValid = isSecondInputValid(this.seconds) && this.isValidLimit();\n        const isValid = this.config.allowEmptyTime ?\n            this.isOneOfDatesIsEmpty() || isTimepickerInputValid\n            : isTimepickerInputValid;\n        if (!isValid) {\n            this.invalidSeconds = true;\n            this.isValid.emit(false);\n            this.onChange(null);\n            return;\n        }\n        this._updateTime();\n    }\n    isValidLimit() {\n        return isInputLimitValid({\n            hour: this.hours,\n            minute: this.minutes,\n            seconds: this.seconds,\n            isPM: this.isPM()\n        }, this.max, this.min);\n    }\n    isOneOfDatesIsEmpty() {\n        return isOneOfDatesEmpty(this.hours, this.minutes, this.seconds);\n    }\n    _updateTime() {\n        const _seconds = this.showSeconds ? this.seconds : void 0;\n        const _minutes = this.showMinutes ? this.minutes : void 0;\n        const isTimepickerInputValid = isInputValid(this.hours, _minutes, _seconds, this.isPM());\n        const isValid = this.config.allowEmptyTime ?\n            this.isOneOfDatesIsEmpty() || isTimepickerInputValid\n            : isTimepickerInputValid;\n        if (!isValid) {\n            this.isValid.emit(false);\n            this.onChange(null);\n            return;\n        }\n        this._store.dispatch(this._timepickerActions.setTime({\n            hour: this.hours,\n            minute: this.minutes,\n            seconds: this.seconds,\n            isPM: this.isPM()\n        }));\n    }\n    toggleMeridian() {\n        if (!this.showMeridian || !this.isEditable) {\n            return;\n        }\n        const _hoursPerDayHalf = 12;\n        this._store.dispatch(this._timepickerActions.changeHours({\n            step: _hoursPerDayHalf,\n            source: ''\n        }));\n    }\n    /**\n     * Write a new value to the element.\n     */\n    writeValue(obj) {\n        if (isValidDate(obj)) {\n            this.resetValidation();\n            this._store.dispatch(this._timepickerActions.writeValue(parseTime(obj)));\n        }\n        else if (obj == null) {\n            this._store.dispatch(this._timepickerActions.writeValue());\n        }\n    }\n    /**\n     * Set the function to be called when the control receives a change event.\n     */\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    registerOnChange(fn) {\n        this.onChange = fn;\n    }\n    /**\n     * Set the function to be called when the control receives a touch event.\n     */\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    /**\n     * This function is called when the control status changes to or from \"disabled\".\n     * Depending on the value, it will enable or disable the appropriate DOM element.\n     *\n     * @param isDisabled\n     */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this._cd.markForCheck();\n    }\n    ngOnDestroy() {\n        var _a;\n        (_a = this.timepickerSub) === null || _a === void 0 ? void 0 : _a.unsubscribe();\n    }\n    _renderTime(value) {\n        if (!value || !isValidDate(value)) {\n            this.hours = '';\n            this.minutes = '';\n            this.seconds = '';\n            this.meridian = this.meridians[0];\n            this.meridianChange.emit(this.meridian);\n            return;\n        }\n        const _value = parseTime(value);\n        if (!_value) {\n            return;\n        }\n        const _hoursPerDayHalf = 12;\n        let _hours = _value.getHours();\n        if (this.showMeridian) {\n            this.meridian = this.meridians[_hours >= _hoursPerDayHalf ? 1 : 0];\n            this.meridianChange.emit(this.meridian);\n            _hours = _hours % _hoursPerDayHalf;\n            // should be 12 PM, not 00 PM\n            if (_hours === 0) {\n                _hours = _hoursPerDayHalf;\n            }\n        }\n        this.hours = padNumber(_hours);\n        this.minutes = padNumber(_value.getMinutes());\n        this.seconds = padNumber(_value.getUTCSeconds());\n    }\n}\nTimepickerComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: TimepickerComponent, deps: [{ token: TimepickerConfig }, { token: i0.ChangeDetectorRef }, { token: TimepickerStore }, { token: TimepickerActions }], target: i0.ɵɵFactoryTarget.Component });\nTimepickerComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.1.1\", type: TimepickerComponent, selector: \"timepicker\", inputs: { hourStep: \"hourStep\", minuteStep: \"minuteStep\", secondsStep: \"secondsStep\", readonlyInput: \"readonlyInput\", disabled: \"disabled\", mousewheel: \"mousewheel\", arrowkeys: \"arrowkeys\", showSpinners: \"showSpinners\", showMeridian: \"showMeridian\", showMinutes: \"showMinutes\", showSeconds: \"showSeconds\", meridians: \"meridians\", min: \"min\", max: \"max\", hoursPlaceholder: \"hoursPlaceholder\", minutesPlaceholder: \"minutesPlaceholder\", secondsPlaceholder: \"secondsPlaceholder\" }, outputs: { isValid: \"isValid\", meridianChange: \"meridianChange\" }, providers: [TIMEPICKER_CONTROL_VALUE_ACCESSOR, TimepickerStore], usesOnChanges: true, ngImport: i0, template: \"<table>\\n  <tbody>\\n  <tr class=\\\"text-center\\\" [hidden]=\\\"!showSpinners\\\">\\n    <!-- increment hours button-->\\n    <td>\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canIncrementHours || !isEditable\\\"\\n         (click)=\\\"changeHours(hourStep)\\\"\\n      ><span class=\\\"bs-chevron bs-chevron-up\\\"></span></a>\\n    </td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showMinutes\\\">&nbsp;&nbsp;&nbsp;</td>\\n    <!-- increment minutes button -->\\n    <td *ngIf=\\\"showMinutes\\\">\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canIncrementMinutes || !isEditable\\\"\\n         (click)=\\\"changeMinutes(minuteStep)\\\"\\n      ><span class=\\\"bs-chevron bs-chevron-up\\\"></span></a>\\n    </td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showSeconds\\\">&nbsp;</td>\\n    <!-- increment seconds button -->\\n    <td *ngIf=\\\"showSeconds\\\">\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canIncrementSeconds || !isEditable\\\"\\n         (click)=\\\"changeSeconds(secondsStep)\\\">\\n        <span class=\\\"bs-chevron bs-chevron-up\\\"></span>\\n      </a>\\n    </td>\\n    <!-- space between -->\\n    <td *ngIf=\\\"showMeridian\\\">&nbsp;&nbsp;&nbsp;</td>\\n    <!-- meridian placeholder-->\\n    <td *ngIf=\\\"showMeridian\\\"></td>\\n  </tr>\\n  <tr>\\n    <!-- hours -->\\n    <td class=\\\"form-group mb-3\\\" [class.has-error]=\\\"invalidHours\\\">\\n      <input type=\\\"text\\\" [class.is-invalid]=\\\"invalidHours\\\"\\n             class=\\\"form-control text-center bs-timepicker-field\\\"\\n             [placeholder]=\\\"hoursPlaceholder\\\"\\n             maxlength=\\\"2\\\"\\n             [readonly]=\\\"readonlyInput\\\"\\n             [disabled]=\\\"disabled\\\"\\n             [value]=\\\"hours\\\"\\n             (wheel)=\\\"prevDef($event);changeHours(hourStep * wheelSign($event), 'wheel')\\\"\\n             (keydown.ArrowUp)=\\\"changeHours(hourStep, 'key')\\\"\\n             (keydown.ArrowDown)=\\\"changeHours(-hourStep, 'key')\\\"\\n             (change)=\\\"updateHours($event.target)\\\" [attr.aria-label]=\\\"labelHours\\\"></td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showMinutes\\\">&nbsp;:&nbsp;</td>\\n    <!-- minutes -->\\n    <td class=\\\"form-group mb-3\\\" *ngIf=\\\"showMinutes\\\" [class.has-error]=\\\"invalidMinutes\\\">\\n      <input type=\\\"text\\\" [class.is-invalid]=\\\"invalidMinutes\\\"\\n             class=\\\"form-control text-center bs-timepicker-field\\\"\\n             [placeholder]=\\\"minutesPlaceholder\\\"\\n             maxlength=\\\"2\\\"\\n             [readonly]=\\\"readonlyInput\\\"\\n             [disabled]=\\\"disabled\\\"\\n             [value]=\\\"minutes\\\"\\n             (wheel)=\\\"prevDef($event);changeMinutes(minuteStep * wheelSign($event), 'wheel')\\\"\\n             (keydown.ArrowUp)=\\\"changeMinutes(minuteStep, 'key')\\\"\\n             (keydown.ArrowDown)=\\\"changeMinutes(-minuteStep, 'key')\\\"\\n             (change)=\\\"updateMinutes($event.target)\\\" [attr.aria-label]=\\\"labelMinutes\\\">\\n    </td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showSeconds\\\">&nbsp;:&nbsp;</td>\\n    <!-- seconds -->\\n    <td class=\\\"form-group mb-3\\\" *ngIf=\\\"showSeconds\\\" [class.has-error]=\\\"invalidSeconds\\\">\\n      <input type=\\\"text\\\" [class.is-invalid]=\\\"invalidSeconds\\\"\\n             class=\\\"form-control text-center bs-timepicker-field\\\"\\n             [placeholder]=\\\"secondsPlaceholder\\\"\\n             maxlength=\\\"2\\\"\\n             [readonly]=\\\"readonlyInput\\\"\\n             [disabled]=\\\"disabled\\\"\\n             [value]=\\\"seconds\\\"\\n             (wheel)=\\\"prevDef($event);changeSeconds(secondsStep * wheelSign($event), 'wheel')\\\"\\n             (keydown.ArrowUp)=\\\"changeSeconds(secondsStep, 'key')\\\"\\n             (keydown.ArrowDown)=\\\"changeSeconds(-secondsStep, 'key')\\\"\\n             (change)=\\\"updateSeconds($event.target)\\\" [attr.aria-label]=\\\"labelSeconds\\\">\\n    </td>\\n    <!-- space between -->\\n    <td *ngIf=\\\"showMeridian\\\">&nbsp;&nbsp;&nbsp;</td>\\n    <!-- meridian -->\\n    <td *ngIf=\\\"showMeridian\\\">\\n      <button type=\\\"button\\\" class=\\\"btn btn-default text-center\\\"\\n              [disabled]=\\\"!isEditable || !canToggleMeridian\\\"\\n              [class.disabled]=\\\"!isEditable || !canToggleMeridian\\\"\\n              (click)=\\\"toggleMeridian()\\\"\\n      >{{ meridian }}\\n      </button>\\n    </td>\\n  </tr>\\n  <tr class=\\\"text-center\\\" [hidden]=\\\"!showSpinners\\\">\\n    <!-- decrement hours button-->\\n    <td>\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canDecrementHours || !isEditable\\\"\\n         (click)=\\\"changeHours(-hourStep)\\\">\\n        <span class=\\\"bs-chevron bs-chevron-down\\\"></span>\\n      </a>\\n    </td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showMinutes\\\">&nbsp;&nbsp;&nbsp;</td>\\n    <!-- decrement minutes button-->\\n    <td *ngIf=\\\"showMinutes\\\">\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canDecrementMinutes || !isEditable\\\"\\n         (click)=\\\"changeMinutes(-minuteStep)\\\">\\n        <span class=\\\"bs-chevron bs-chevron-down\\\"></span>\\n      </a>\\n    </td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showSeconds\\\">&nbsp;</td>\\n    <!-- decrement seconds button-->\\n    <td *ngIf=\\\"showSeconds\\\">\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canDecrementSeconds || !isEditable\\\"\\n         (click)=\\\"changeSeconds(-secondsStep)\\\">\\n        <span class=\\\"bs-chevron bs-chevron-down\\\"></span>\\n      </a>\\n    </td>\\n    <!-- space between -->\\n    <td *ngIf=\\\"showMeridian\\\">&nbsp;&nbsp;&nbsp;</td>\\n    <!-- meridian placeholder-->\\n    <td *ngIf=\\\"showMeridian\\\"></td>\\n  </tr>\\n  </tbody>\\n</table>\\n\", styles: [\".bs-chevron{border-style:solid;display:block;width:9px;height:9px;position:relative;border-width:3px 0px 0 3px}.bs-chevron-up{transform:rotate(45deg);top:2px}.bs-chevron-down{transform:rotate(-135deg);top:-2px}.bs-timepicker-field{width:65px;padding:.375rem .55rem}\\n\"], directives: [{ type: i4.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: TimepickerComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'timepicker', changeDetection: ChangeDetectionStrategy.OnPush, providers: [TIMEPICKER_CONTROL_VALUE_ACCESSOR, TimepickerStore], styles: [`\n    .bs-chevron {\n      border-style: solid;\n      display: block;\n      width: 9px;\n      height: 9px;\n      position: relative;\n      border-width: 3px 0px 0 3px;\n    }\n\n    .bs-chevron-up {\n      -webkit-transform: rotate(45deg);\n      transform: rotate(45deg);\n      top: 2px;\n    }\n\n    .bs-chevron-down {\n      -webkit-transform: rotate(-135deg);\n      transform: rotate(-135deg);\n      top: -2px;\n    }\n\n    .bs-timepicker-field {\n      width: 65px;\n      padding: .375rem .55rem;\n    }\n  `], encapsulation: ViewEncapsulation.None, template: \"<table>\\n  <tbody>\\n  <tr class=\\\"text-center\\\" [hidden]=\\\"!showSpinners\\\">\\n    <!-- increment hours button-->\\n    <td>\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canIncrementHours || !isEditable\\\"\\n         (click)=\\\"changeHours(hourStep)\\\"\\n      ><span class=\\\"bs-chevron bs-chevron-up\\\"></span></a>\\n    </td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showMinutes\\\">&nbsp;&nbsp;&nbsp;</td>\\n    <!-- increment minutes button -->\\n    <td *ngIf=\\\"showMinutes\\\">\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canIncrementMinutes || !isEditable\\\"\\n         (click)=\\\"changeMinutes(minuteStep)\\\"\\n      ><span class=\\\"bs-chevron bs-chevron-up\\\"></span></a>\\n    </td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showSeconds\\\">&nbsp;</td>\\n    <!-- increment seconds button -->\\n    <td *ngIf=\\\"showSeconds\\\">\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canIncrementSeconds || !isEditable\\\"\\n         (click)=\\\"changeSeconds(secondsStep)\\\">\\n        <span class=\\\"bs-chevron bs-chevron-up\\\"></span>\\n      </a>\\n    </td>\\n    <!-- space between -->\\n    <td *ngIf=\\\"showMeridian\\\">&nbsp;&nbsp;&nbsp;</td>\\n    <!-- meridian placeholder-->\\n    <td *ngIf=\\\"showMeridian\\\"></td>\\n  </tr>\\n  <tr>\\n    <!-- hours -->\\n    <td class=\\\"form-group mb-3\\\" [class.has-error]=\\\"invalidHours\\\">\\n      <input type=\\\"text\\\" [class.is-invalid]=\\\"invalidHours\\\"\\n             class=\\\"form-control text-center bs-timepicker-field\\\"\\n             [placeholder]=\\\"hoursPlaceholder\\\"\\n             maxlength=\\\"2\\\"\\n             [readonly]=\\\"readonlyInput\\\"\\n             [disabled]=\\\"disabled\\\"\\n             [value]=\\\"hours\\\"\\n             (wheel)=\\\"prevDef($event);changeHours(hourStep * wheelSign($event), 'wheel')\\\"\\n             (keydown.ArrowUp)=\\\"changeHours(hourStep, 'key')\\\"\\n             (keydown.ArrowDown)=\\\"changeHours(-hourStep, 'key')\\\"\\n             (change)=\\\"updateHours($event.target)\\\" [attr.aria-label]=\\\"labelHours\\\"></td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showMinutes\\\">&nbsp;:&nbsp;</td>\\n    <!-- minutes -->\\n    <td class=\\\"form-group mb-3\\\" *ngIf=\\\"showMinutes\\\" [class.has-error]=\\\"invalidMinutes\\\">\\n      <input type=\\\"text\\\" [class.is-invalid]=\\\"invalidMinutes\\\"\\n             class=\\\"form-control text-center bs-timepicker-field\\\"\\n             [placeholder]=\\\"minutesPlaceholder\\\"\\n             maxlength=\\\"2\\\"\\n             [readonly]=\\\"readonlyInput\\\"\\n             [disabled]=\\\"disabled\\\"\\n             [value]=\\\"minutes\\\"\\n             (wheel)=\\\"prevDef($event);changeMinutes(minuteStep * wheelSign($event), 'wheel')\\\"\\n             (keydown.ArrowUp)=\\\"changeMinutes(minuteStep, 'key')\\\"\\n             (keydown.ArrowDown)=\\\"changeMinutes(-minuteStep, 'key')\\\"\\n             (change)=\\\"updateMinutes($event.target)\\\" [attr.aria-label]=\\\"labelMinutes\\\">\\n    </td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showSeconds\\\">&nbsp;:&nbsp;</td>\\n    <!-- seconds -->\\n    <td class=\\\"form-group mb-3\\\" *ngIf=\\\"showSeconds\\\" [class.has-error]=\\\"invalidSeconds\\\">\\n      <input type=\\\"text\\\" [class.is-invalid]=\\\"invalidSeconds\\\"\\n             class=\\\"form-control text-center bs-timepicker-field\\\"\\n             [placeholder]=\\\"secondsPlaceholder\\\"\\n             maxlength=\\\"2\\\"\\n             [readonly]=\\\"readonlyInput\\\"\\n             [disabled]=\\\"disabled\\\"\\n             [value]=\\\"seconds\\\"\\n             (wheel)=\\\"prevDef($event);changeSeconds(secondsStep * wheelSign($event), 'wheel')\\\"\\n             (keydown.ArrowUp)=\\\"changeSeconds(secondsStep, 'key')\\\"\\n             (keydown.ArrowDown)=\\\"changeSeconds(-secondsStep, 'key')\\\"\\n             (change)=\\\"updateSeconds($event.target)\\\" [attr.aria-label]=\\\"labelSeconds\\\">\\n    </td>\\n    <!-- space between -->\\n    <td *ngIf=\\\"showMeridian\\\">&nbsp;&nbsp;&nbsp;</td>\\n    <!-- meridian -->\\n    <td *ngIf=\\\"showMeridian\\\">\\n      <button type=\\\"button\\\" class=\\\"btn btn-default text-center\\\"\\n              [disabled]=\\\"!isEditable || !canToggleMeridian\\\"\\n              [class.disabled]=\\\"!isEditable || !canToggleMeridian\\\"\\n              (click)=\\\"toggleMeridian()\\\"\\n      >{{ meridian }}\\n      </button>\\n    </td>\\n  </tr>\\n  <tr class=\\\"text-center\\\" [hidden]=\\\"!showSpinners\\\">\\n    <!-- decrement hours button-->\\n    <td>\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canDecrementHours || !isEditable\\\"\\n         (click)=\\\"changeHours(-hourStep)\\\">\\n        <span class=\\\"bs-chevron bs-chevron-down\\\"></span>\\n      </a>\\n    </td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showMinutes\\\">&nbsp;&nbsp;&nbsp;</td>\\n    <!-- decrement minutes button-->\\n    <td *ngIf=\\\"showMinutes\\\">\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canDecrementMinutes || !isEditable\\\"\\n         (click)=\\\"changeMinutes(-minuteStep)\\\">\\n        <span class=\\\"bs-chevron bs-chevron-down\\\"></span>\\n      </a>\\n    </td>\\n    <!-- divider -->\\n    <td *ngIf=\\\"showSeconds\\\">&nbsp;</td>\\n    <!-- decrement seconds button-->\\n    <td *ngIf=\\\"showSeconds\\\">\\n      <a class=\\\"btn btn-link\\\" [class.disabled]=\\\"!canDecrementSeconds || !isEditable\\\"\\n         (click)=\\\"changeSeconds(-secondsStep)\\\">\\n        <span class=\\\"bs-chevron bs-chevron-down\\\"></span>\\n      </a>\\n    </td>\\n    <!-- space between -->\\n    <td *ngIf=\\\"showMeridian\\\">&nbsp;&nbsp;&nbsp;</td>\\n    <!-- meridian placeholder-->\\n    <td *ngIf=\\\"showMeridian\\\"></td>\\n  </tr>\\n  </tbody>\\n</table>\\n\" }]\n        }], ctorParameters: function () { return [{ type: TimepickerConfig }, { type: i0.ChangeDetectorRef }, { type: TimepickerStore }, { type: TimepickerActions }]; }, propDecorators: { hourStep: [{\n                type: Input\n            }], minuteStep: [{\n                type: Input\n            }], secondsStep: [{\n                type: Input\n            }], readonlyInput: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], mousewheel: [{\n                type: Input\n            }], arrowkeys: [{\n                type: Input\n            }], showSpinners: [{\n                type: Input\n            }], showMeridian: [{\n                type: Input\n            }], showMinutes: [{\n                type: Input\n            }], showSeconds: [{\n                type: Input\n            }], meridians: [{\n                type: Input\n            }], min: [{\n                type: Input\n            }], max: [{\n                type: Input\n            }], hoursPlaceholder: [{\n                type: Input\n            }], minutesPlaceholder: [{\n                type: Input\n            }], secondsPlaceholder: [{\n                type: Input\n            }], isValid: [{\n                type: Output\n            }], meridianChange: [{\n                type: Output\n            }] } });\n\nclass TimepickerModule {\n    static forRoot() {\n        return {\n            ngModule: TimepickerModule,\n            providers: [TimepickerActions, TimepickerStore]\n        };\n    }\n}\nTimepickerModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: TimepickerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nTimepickerModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: TimepickerModule, declarations: [TimepickerComponent], imports: [CommonModule], exports: [TimepickerComponent] });\nTimepickerModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: TimepickerModule, providers: [TimepickerStore], imports: [[CommonModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: TimepickerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    declarations: [TimepickerComponent],\n                    exports: [TimepickerComponent],\n                    providers: [TimepickerStore]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TimepickerActions, TimepickerComponent, TimepickerConfig, TimepickerModule, TimepickerStore };\n"]}, "metadata": {}, "sourceType": "module"}