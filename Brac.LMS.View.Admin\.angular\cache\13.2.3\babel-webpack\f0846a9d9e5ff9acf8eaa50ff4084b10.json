{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport * as Highcharts from 'highcharts';\nimport { Page } from '../_models/page';\nimport { environment } from 'src/environments/environment';\nimport { ResponseStatus } from '../_models/enum';\nimport { NgbTooltipConfig } from '@ng-bootstrap/ng-bootstrap';\nimport { BlockUI } from 'ng-block-ui';\nimport * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../_services/common.service\";\nimport * as i2 from \"../app.component\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"ngx-toastr\";\nimport * as i7 from \"../_services/authentication.service\";\nimport * as i8 from \"ng-block-ui\";\nimport * as i9 from \"ngx-bootstrap/datepicker\";\nimport * as i10 from \"@ng-select/ng-select\";\nimport * as i11 from \"ngx-bootstrap/tooltip\";\nimport * as i12 from \"@angular/common\";\nimport * as i13 from \"highcharts-angular\";\n\nfunction AdminViewComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵelementStart(1, \"div\", 3);\n    i0.ɵɵelementStart(2, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function AdminViewComponent_div_38_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return ctx_r8.goToPage(\"/trainee-list\");\n    });\n    i0.ɵɵelementStart(3, \"div\", 0);\n    i0.ɵɵelementStart(4, \"div\", 26);\n    i0.ɵɵelement(5, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 28);\n    i0.ɵɵelementStart(7, \"h5\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"Trainees\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function AdminViewComponent_div_38_Template_div_click_11_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return ctx_r10.goToPage(\"/course\");\n    });\n    i0.ɵɵelementStart(12, \"div\", 0);\n    i0.ɵɵelementStart(13, \"div\", 26);\n    i0.ɵɵelement(14, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 28);\n    i0.ɵɵelementStart(16, \"h5\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19, \"Courses\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 30);\n    i0.ɵɵlistener(\"click\", function AdminViewComponent_div_38_Template_div_click_20_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return ctx_r11.goToPage(\"/e-library\");\n    });\n    i0.ɵɵelementStart(21, \"div\", 0);\n    i0.ɵɵelementStart(22, \"div\", 26);\n    i0.ɵɵelement(23, \"i\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 28);\n    i0.ɵɵelementStart(25, \"h5\");\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28, \"Library Items\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 3);\n    i0.ɵɵelementStart(30, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function AdminViewComponent_div_38_Template_div_click_30_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return ctx_r12.goToPage(\"/open-material\");\n    });\n    i0.ɵɵelementStart(31, \"div\", 0);\n    i0.ɵɵelementStart(32, \"div\", 26);\n    i0.ɵɵelement(33, \"i\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 28);\n    i0.ɵɵelementStart(35, \"h5\");\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38, \"Learning Hour Items\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function AdminViewComponent_div_38_Template_div_click_39_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return ctx_r13.goToPage(\"/evaluation-test-list\");\n    });\n    i0.ɵɵelementStart(40, \"div\", 0);\n    i0.ɵɵelementStart(41, \"div\", 26);\n    i0.ɵɵelement(42, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 28);\n    i0.ɵɵelementStart(44, \"h5\");\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"span\");\n    i0.ɵɵtext(47, \"Evaluation\\u00A0Tests\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 30);\n    i0.ɵɵlistener(\"click\", function AdminViewComponent_div_38_Template_div_click_48_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return ctx_r14.goToPage(\"/forum-posts\");\n    });\n    i0.ɵɵelementStart(49, \"div\", 0);\n    i0.ɵɵelementStart(50, \"div\", 26);\n    i0.ɵɵelement(51, \"i\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 28);\n    i0.ɵɵelementStart(53, \"h5\");\n    i0.ɵɵtext(54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"span\");\n    i0.ɵɵtext(56, \"Forum Posts (Active)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.counter.Trainees);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.counter.Courses);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.counter.LibraryItems);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r1.counter.LearningHourItems);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.counter.EvaluationTests);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.counter.ForumPosts);\n  }\n}\n\nfunction AdminViewComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelementStart(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"h4\", 37);\n    i0.ɵɵtext(3, \"No course with enrollments found.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AdminViewComponent_highcharts_chart_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"highcharts-chart\", 38);\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"Highcharts\", ctx_r3.Highcharts)(\"options\", ctx_r3.enrollChartOptions);\n  }\n}\n\nfunction AdminViewComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelementStart(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"h4\", 37);\n    i0.ɵɵtext(3, \"No enrollments were obtained.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AdminViewComponent_highcharts_chart_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"highcharts-chart\", 39);\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"Highcharts\", ctx_r5.Highcharts)(\"options\", ctx_r5.courseChartOptions);\n  }\n}\n\nfunction AdminViewComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelementStart(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"h4\", 37);\n    i0.ɵɵtext(3, \"No certificates were obtained.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AdminViewComponent_highcharts_chart_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"highcharts-chart\", 38);\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"Highcharts\", ctx_r7.Highcharts)(\"options\", ctx_r7.certificateChartOptions);\n  }\n}\n\nexport class AdminViewComponent {\n  constructor(_service, appComponent, renderer, el, config, router, formBuilder, toastr, authService) {\n    this._service = _service;\n    this.appComponent = appComponent;\n    this.renderer = renderer;\n    this.el = el;\n    this.router = router;\n    this.formBuilder = formBuilder;\n    this.toastr = toastr;\n    this.authService = authService;\n    this.limit = 5;\n    this.page = new Page();\n    this.baseUrl = environment.baseUrl;\n    this.traineeProgressList = {\n      list: [],\n      total: 0\n    };\n    this.traineePerformance = {\n      list: [],\n      total: 0\n    };\n    this.belatedTraineeList = {\n      list: [],\n      total: 0\n    };\n    this.certificateList = {\n      list: [],\n      total: 0\n    };\n    this.courseProgressList = {\n      list: [],\n      total: 0\n    };\n    this.enrollmentList = {\n      list: [],\n      month: null,\n      new: null,\n      total: null,\n      progrss: null\n    };\n    this.statusList = [{\n      Id: true,\n      Name: \"Published\"\n    }, {\n      Id: false,\n      Name: \"UnPublished\"\n    }];\n    this.Highcharts = Highcharts;\n    this.chart1Options = {\n      chart: {\n        type: 'bar'\n      },\n      title: {\n        text: 'Obtained Certificates In Last 6 Months'\n      },\n      legend: {\n        layout: 'vertical',\n        align: 'left',\n        verticalAlign: 'top',\n        x: 250,\n        y: 100,\n        floating: true,\n        borderWidth: 1\n      },\n      //  backgroundColor: (\n      //     (Highcharts.theme && Highcharts.theme.legendBackgroundColor) ||\n      //       '#FFFFFF'), shadow: true\n      //  },\n      xAxis: {\n        categories: ['Bangla', 'English', 'Word', 'Powerpoint', 'Excel'],\n        title: {\n          text: null\n        }\n      },\n      yAxis: {\n        min: 0,\n        title: {\n          text: 'Certificate Count',\n          align: 'high'\n        },\n        labels: {\n          overflow: 'justify'\n        }\n      },\n      plotOptions: {\n        bar: {\n          dataLabels: {\n            enabled: true\n          }\n        }\n      },\n      credits: {\n        enabled: false\n      },\n      series: [{\n        name: 'Month Feb',\n        data: [17, 31, 65, 23, 2]\n      }, {\n        name: 'Month March',\n        data: [145, 16, 97, 40, 63]\n      }, {\n        name: 'Month April',\n        data: [9, 4, 5, 2, 15]\n      }]\n    };\n    this.page.pageNumber = 0;\n    this.page.size = 3; //    config.placement = 'right';\n    //  config.triggers = 'click';\n  }\n\n  ngOnInit() {\n    this.filterForm = this.formBuilder.group({\n      dates: [[]],\n      statusId: [null],\n      isForCertificate: [true],\n      isForEnrolledCourse: [true],\n      isForEnrolledProgress: [true]\n    });\n    this.getCounts(); // this.getBelatedTraineeList();\n\n    if (this.filterForm.value.isForCertificate) this.getCertificateList();\n    if (this.filterForm.value.isForEnrolledCourse) this.getCourseProgressList();\n    if (this.filterForm.value.isForEnrolledProgress) this.getEnrollmentList();\n  }\n\n  ngAfterViewInit() {\n    const elements = this.el.nativeElement.querySelectorAll('.point-series');\n    elements.forEach(element => {\n      const color = element.getAttribute('data-color');\n      this.renderer.setStyle(element, 'color', color);\n    });\n  }\n\n  goToPage(url) {\n    this.router.navigate([url]);\n  }\n\n  handleSelectClick(selectElement) {\n    this.appComponent.handleSelectClick(selectElement);\n  }\n\n  getCounts() {\n    this._service.get('dashboard/get-count-for-admin').subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.counter = res.Data;\n    }, err => {\n      this.toastr.warning(err.Messaage || err, 'Warning!', {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  getTraineeProgressList() {\n    const obj = {\n      name: this.filterForm.value.name ? this.filterForm.value.name.trim() : this.filterForm.value.name,\n      categoryId: this.filterForm.value.categoryId,\n      size: this.page.size,\n      pageNumber: this.page.pageNumber\n    };\n\n    this._service.get('dashboard/get-trainee-progress', this.page).subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.traineeProgressList.list = res.Data;\n    }, err => {\n      this.toastr.warning(err.Messaage || err, 'Warning!', {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  getTraineePerformanceList() {\n    this._service.get('dashboard/get-top-trainee-performance/' + this.limit).subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.traineePerformance.list = [];\n      res.Data.forEach(element => {\n        this.traineePerformance.list.push({\n          name: element.Course,\n          y: Number(element.EnrolledCount),\n          exam: Number(element.Examcount)\n        });\n      });\n      this.traineePerformanceChartOptions = {\n        chart: {\n          plotBorderWidth: null,\n          plotShadow: false\n        },\n        title: {\n          text: 'Top ' + this.limit + ' Course Progress'\n        },\n        tooltip: {\n          pointFormat: '{series.name}: <b>{point.y:f}</b>'\n        },\n        credits: {\n          enabled: false\n        },\n        plotOptions: {\n          pie: {\n            allowPointSelect: true,\n            cursor: 'pointer',\n            shadow: false,\n            dataLabels: {\n              enabled: false\n            },\n            showInLegend: true\n          }\n        },\n        series: [{\n          type: 'pie',\n          name: 'Enrollments',\n          data: this.traineePerformance.list // data: [\n          //    {\n          //       name: this.traineePerformance.list.map(x => x.Name),\n          //       y: this.traineePerformance.list.map(x=>x.Certificates)\n          //    },\n          //    {\n          //       name: this.traineePerformance.list.map(x => x.Name),\n          //       y: this.traineePerformance.list.map(x=>x.Certificates)\n          //    },\n          //    {\n          //       name: this.traineePerformance.list.map(x => x.Name),\n          //       y: this.traineePerformance.list.map(x=>x.Certificates)\n          //    },\n          //    {\n          //       name: this.traineePerformance.list.map(x => x.Name),\n          //       y: this.traineePerformance.list.map(x=>x.Certificates)\n          //    },\n          //    {\n          //       name: this.traineePerformance.list.map(x => x.Name),\n          //       y: this.traineePerformance.list.map(x=>x.Certificates)\n          //    },\n          //    // {\n          //    //    name: 'Mr John Doe',\n          //    //    y: 10,\n          //    //    sliced: true,\n          //    //    selected: true\n          //    // },\n          //    // ['Mr Saiful Alam', 10],\n          // ]\n\n        }]\n      };\n    }, err => {\n      this.toastr.warning(err.Messaage || err, 'Warning!', {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  getBelatedTraineeList() {\n    this._service.get('dashboard/get-belated-trainee-list', this.page).subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.belatedTraineeList.list = res.Data.Records;\n      this.belatedTraineeList.total = res.Data.Total;\n    }, err => {\n      this.toastr.warning(err.Messaage || err, 'Warning!', {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  getCertificateList() {\n    const obj = {\n      size: 5,\n      startDate: this.filterForm.value.isForCertificate != null ? this.filterForm.value.dates.length === 2 ? moment(this.filterForm.value.dates[0]).format('DD-MMM-YYYY') : null : null,\n      endDate: this.filterForm.value.isForCertificate != null ? this.filterForm.value.dates.length === 2 ? moment(this.filterForm.value.dates[1]).format('DD-MMM-YYYY') : null : null,\n      status: this.filterForm.value.statusId,\n      pageNumber: 6\n    };\n    this.certificateList.list = [];\n    let titles = [];\n\n    this._service.get('dashboard/get-month-wise-certificate-gains/', obj).subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      res.Data.Records.forEach(element => {\n        this.certificateList.list.push({\n          name: element.Month,\n          data: element.certificates\n        });\n      });\n      res.Data.Course.forEach(element => {\n        // titles.push(element.Name)\n        titles.push({\n          Name: element.Name,\n          ShortName: element.ShortName\n        });\n      });\n      this.certificateList.total = res.Total; // console.log(moment(this.filterForm.value.dates[0]).format('DD-MMM-YYYY'))\n\n      this.certificateChartOptions = {\n        chart: {\n          type: 'bar'\n        },\n        title: {\n          text: this.filterForm.value.dates.length !== 2 ? 'Obtained Certificates In Last 6 Months ' : 'Obtained Certificates from ' + moment(this.filterForm.value.dates[0]).format('DD-MMM-YYYY') + ' - ' + moment(this.filterForm.value.dates[1]).format('DD-MMM-YYYY')\n        },\n        xAxis: {\n          categories: titles.map(x => ({\n            shortTitle: x.ShortName != null ? x.ShortName : x.Name,\n            title: x.Name\n          })),\n          labels: {\n            formatter: function () {\n              // Display short name on x-axis labels\n              return this.value.shortTitle;\n            }\n          },\n          crosshair: true\n        },\n        yAxis: {\n          min: 0,\n          title: {\n            text: 'Number of certificates ',\n            align: 'middle'\n          }\n        },\n        credits: {\n          enabled: false\n        },\n        legend: {\n          reversed: true\n        },\n        plotOptions: {\n          series: {\n            stacking: 'normal'\n          }\n        },\n        tooltip: {\n          formatter: function () {\n            // return this.points.reduce(function (s, point) {\n            //    return s + '<br/><b style=\"color: ' + point.series.color + '\">' + point.series.name + ': </b>' + point.y\n            // }, '<b>' + this.x.title + '</b>');\n            return this.points.reduce(function (s, point) {\n              return s + '<br/><b class=\"point-series\" data-color=\"' + point.series.color + '\">' + point.series.name + ': </b>' + point.y;\n            }, '<b>' + this.x.title + '</b>');\n          },\n          shared: true,\n          useHTML: true\n        },\n        series: this.certificateList.list\n      };\n    }, err => {\n      this.toastr.warning(err.Messaage || err, 'Warning!', {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  getEnrollmentList() {\n    const obj = {\n      size: 5,\n      startDate: this.filterForm.value.dates.length === 2 ? moment(this.filterForm.value.dates[0]).format('DD-MMM-YYYY') : null,\n      endDate: this.filterForm.value.dates.length === 2 ? moment(this.filterForm.value.dates[1]).format('DD-MMM-YYYY') : null,\n      status: this.filterForm.value.statusId,\n      pageNumber: 6\n    };\n    this.enrollmentList.list = [];\n    let titles = [];\n\n    this._service.get('dashboard/get-month-wise-enrollment/', obj).subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      res.Data.Records.forEach(element => {\n        this.enrollmentList.list.push({\n          month: element.Month,\n          total: element.Total,\n          new: element.New,\n          progress: element.Progress\n        });\n      }); // res.Data.Course.forEach(element => {\n      //    titles.push(element.month)\n      // });\n\n      this.enrollmentList.total = res.Total; // this.enrollmentList.list = [];\n\n      this.enrollChartOptions = {\n        chart: {\n          type: 'spline'\n        },\n        title: {\n          text: 'Enrollment Progress'\n        },\n        xAxis: {\n          categories: res.Data.Records.map(x => x.Month),\n          crosshair: true\n        },\n        yAxis: {\n          title: {\n            text: 'Count'\n          },\n          labels: {\n            formatter: function () {\n              return Highcharts.numberFormat(this.value, 0, '', ',');\n            }\n          }\n        },\n        tooltip: {\n          crosshairs: true,\n          shared: true,\n          formatter: function () {\n            return this.x + '</br> Total: ' + Highcharts.numberFormat(this.points[0].y, 0, '', ',') + '</br> New: ' + (this.points.length > 1 ? Highcharts.numberFormat(this.points[1].y, 0, '', ',') : 0) + '</br> Progress: ' + (this.points.length > 2 ? Highcharts.numberFormat(this.points[2].y, 0, '', ',') : 0);\n          } // pointFormat:'{point.y:,.0f}'\n\n        },\n        plotOptions: {\n          spline: {\n            marker: {\n              enabled: true,\n              radius: 4,\n              lineColor: '#1111',\n              lineWidth: 1\n            }\n          }\n        },\n        // series: this.enrollmentList.list,\n        series: [{\n          name: 'All',\n          color: '#FFA500',\n          data: this.enrollmentList.list.map(function (item) {\n            return {\n              y: item.total\n            };\n          }),\n          pointPadding: 0.3,\n          connectNulls: true\n        }, {\n          name: 'New',\n          color: '#0000FF',\n          data: this.enrollmentList.list.map(function (item) {\n            return {\n              y: item.new\n            };\n          }),\n          pointPadding: 0.4,\n          connectNulls: true\n        }, {\n          name: 'Progress',\n          color: '#808080',\n          data: this.enrollmentList.list.map(function (item) {\n            return {\n              y: item.progress\n            };\n          }),\n          pointPadding: 0.4,\n          connectNulls: true\n        }]\n      };\n    }, err => {\n      this.toastr.warning(err.Messaage || err, 'Warning!', {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  onChangeFilter() {\n    if (this.filterForm.value.isForCertificate) this.getCertificateList();\n    if (this.filterForm.value.isForEnrolledCourse) this.getCourseProgressList();\n    if (this.filterForm.value.isForEnrolledProgress) this.getEnrollmentList();\n  }\n\n  getCourseProgressList() {\n    const obj = {\n      limit: this.limit,\n      startDate: this.filterForm.value.isForEnrolledCourse != null ? this.filterForm.value.dates.length === 2 ? moment(this.filterForm.value.dates[0]).format('DD-MMM-YYYY') : null : null,\n      endDate: this.filterForm.value.isForEnrolledCourse != null ? this.filterForm.value.dates.length === 2 ? moment(this.filterForm.value.dates[1]).format('DD-MMM-YYYY') : null : null,\n      status: this.filterForm.value.statusId\n    };\n\n    this._service.get('dashboard/get-top-enrollment-courses/', obj).subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.courseChartOptions = {\n        chart: {\n          type: 'column'\n        },\n        title: {\n          text: 'Top ' + this.limit + ' Enrolled Courses'\n        },\n        xAxis: {\n          categories: res.Data.map(x => ({\n            shortTitle: x.CourseShortTitle != null ? x.CourseShortTitle : x.CourseTitle,\n            title: x.CourseTitle\n          })),\n          labels: {\n            formatter: function () {\n              // Display short name on x-axis labels\n              return this.value.shortTitle;\n            }\n          },\n          // categories: res.Data.map(x =>x.CourseShortTitle),\n          title: {\n            text: 'Courses'\n          }\n        },\n        yAxis: [{\n          min: 0,\n          title: {\n            text: 'Enrollments'\n          }\n        }],\n        legend: {\n          shadow: false\n        },\n        credits: {\n          enabled: false\n        },\n        tooltip: {\n          // headerFormat: '<strong style=\"font-size:10px\">{point.key}</strong><table>',\n          // pointFormat: '<tr><th style=\"color:{series.color};padding:0\">{series.name}: </th>' +\n          //    '<td style=\"padding:0\"><b>{point.y} </b></td></tr>' +\n          //    '<tr><th style=\"padding:0\">Avg. Progress: </th>' +\n          //    '<td style=\"padding:0\"><b>{point.avg} </b></td></tr>',\n          // footerFormat: '</table>',\n          formatter: function () {\n            return this.points.reduce(function (s, point) {\n              return s + '<br/><b style=\"color: ' + point.series.color + '\">' + point.series.name + ': </b>' + point.y;\n            }, '<b>' + this.x.title + '</b>');\n          },\n          shared: true,\n          useHTML: true\n        },\n        plotOptions: {\n          column: {\n            grouping: false,\n            shadow: false,\n            borderWidth: 0\n          }\n        },\n        series: [{\n          name: 'Enrollments',\n          color: '#056cb5',\n          // data: res.Data.map(function (item) { return { y: item.NoOfEnrollments, avg: item.AvgProgress } }),\n          data: res.Data.map(function (item) {\n            return {\n              y: item.NoOfEnrollments,\n              avg: item.Progress / item.CountMutiPercant * 100\n            };\n          }),\n          pointPadding: 0.3\n        }, {\n          name: 'Completed',\n          color: '#04245c',\n          data: res.Data.map(function (item) {\n            return {\n              y: item.NoOfTraineesCompleted,\n              avg: item.Progress / item.CountMutiPercant * 100\n            };\n          }),\n          pointPadding: 0.4\n        }]\n      };\n    }, err => {\n      this.toastr.warning(err.Messaage || err, 'Warning!', {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n}\n\nAdminViewComponent.ɵfac = function AdminViewComponent_Factory(t) {\n  return new (t || AdminViewComponent)(i0.ɵɵdirectiveInject(i1.CommonService), i0.ɵɵdirectiveInject(i2.AppComponent), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.NgbTooltipConfig), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.ToastrService), i0.ɵɵdirectiveInject(i7.AuthenticationService));\n};\n\nAdminViewComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AdminViewComponent,\n  selectors: [[\"app-dashboard-admin\"]],\n  features: [i0.ɵɵProvidersFeature([NgbTooltipConfig])],\n  decls: 51,\n  vars: 12,\n  consts: [[1, \"row\"], [1, \"col-12\"], [1, \"card\", \"table-card\"], [1, \"row-table\"], [\"autocomplete\", \"off\", 1, \"col-12\", \"custom-inline-form\", 3, \"formGroup\"], [1, \"mb-3\", \"col-lg-3\", \"col-12\"], [1, \"col-form-label\", \"col-form-label-sm\", \"text-right\"], [\"type\", \"text\", \"formControlName\", \"dates\", \"bsDaterangepicker\", \"\", \"placeholder\", \"Select a date range\", 1, \"form-control\", \"form-control-sm\", 3, \"bsConfig\"], [1, \"mb-2\", \"col-lg-2\", \"col-12\"], [\"formControlName\", \"statusId\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Select Published/UnPublished/All\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\"], [\"selectElement\", \"\"], [1, \"mb-2\", \"col-lg-2\", \"col-3\"], [\"type\", \"checkbox\", \"formControlName\", \"isForCertificate\", \"placeholder\", \"Select a date range\"], [\"type\", \"checkbox\", \"formControlName\", \"isForEnrolledCourse\"], [\"type\", \"checkbox\", \"formControlName\", \"isForEnrolledProgress\"], [1, \"mb-1\", \"col-lg-1\", \"col-12\"], [\"tooltip\", \"Filter\", 1, \"btn\", \"btn-theme\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"feather\", \"icon-search\", \"text-white\"], [1, \"col-lg-12\", \"col-12\"], [\"class\", \"card table-card\", 4, \"ngIf\"], [\"class\", \"card\", 4, \"ngIf\"], [\"class\", \"admin.width-100-pc-block\", 3, \"Highcharts\", \"options\", 4, \"ngIf\"], [1, \"col-lg-12\", \"col-12\", \"dac-style-2\"], [1, \"col-md-6\"], [\"class\", \"dac-style-1\", 3, \"Highcharts\", \"options\", 4, \"ngIf\"], [1, \"col-sm-4\", \"card-block-big\", \"br\", \"cursor-div\", 3, \"click\"], [1, \"col-sm-4\"], [1, \"fas\", \"fa-users\", \"fa-3x\"], [1, \"col-sm-8\", \"text-center\"], [1, \"fas\", \"fa-book-open\", \"fa-3x\"], [1, \"col-sm-4\", \"card-block-big\", \"cursor-div\", 3, \"click\"], [1, \"fas\", \"fa-book-reader\", \"fa-3x\"], [1, \"fas\", \"fa-file-alt\", \"fa-3x\"], [1, \"fas\", \"fa-question-circle\", \"fa-3x\"], [1, \"fas\", \"fa-comments\", \"fa-3x\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\", \"mb-0\"], [1, \"admin.width-100-pc-block\", 3, \"Highcharts\", \"options\"], [1, \"dac-style-1\", 3, \"Highcharts\", \"options\"]],\n  template: function AdminViewComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r15 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 0);\n      i0.ɵɵelementStart(4, \"div\", 2);\n      i0.ɵɵelementStart(5, \"div\", 3);\n      i0.ɵɵelementStart(6, \"form\", 4);\n      i0.ɵɵelementStart(7, \"div\", 0);\n      i0.ɵɵelementStart(8, \"div\", 5);\n      i0.ɵɵelementStart(9, \"label\", 6);\n      i0.ɵɵtext(10, \" Date Range \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(11, \"input\", 7);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(12, \"div\", 8);\n      i0.ɵɵelementStart(13, \"label\", 6);\n      i0.ɵɵtext(14, \" Status \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"ng-select\", 9, 10);\n      i0.ɵɵlistener(\"click\", function AdminViewComponent_Template_ng_select_click_15_listener() {\n        i0.ɵɵrestoreView(_r15);\n\n        const _r0 = i0.ɵɵreference(16);\n\n        return ctx.handleSelectClick(_r0);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(17, \"div\", 11);\n      i0.ɵɵelementStart(18, \"label\", 6);\n      i0.ɵɵtext(19, \" Certificate \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(20, \"br\");\n      i0.ɵɵelement(21, \"input\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(22, \"div\", 11);\n      i0.ɵɵelementStart(23, \"label\", 6);\n      i0.ɵɵtext(24, \" Enrolled\\u00A0Course \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(25, \"br\");\n      i0.ɵɵelement(26, \"input\", 13);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(27, \"div\", 11);\n      i0.ɵɵelementStart(28, \"label\", 6);\n      i0.ɵɵtext(29, \" Enrolled\\u00A0/\\u00A0Progress \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(30, \"br\");\n      i0.ɵɵelement(31, \"input\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(32, \"div\", 15);\n      i0.ɵɵelementStart(33, \"button\", 16);\n      i0.ɵɵlistener(\"click\", function AdminViewComponent_Template_button_click_33_listener() {\n        return ctx.onChangeFilter();\n      });\n      i0.ɵɵelement(34, \"i\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(35, \"div\", 18);\n      i0.ɵɵelementStart(36, \"div\", 0);\n      i0.ɵɵelementStart(37, \"div\", 1);\n      i0.ɵɵtemplate(38, AdminViewComponent_div_38_Template, 57, 6, \"div\", 19);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(39, \"div\", 18);\n      i0.ɵɵelementStart(40, \"div\", 0);\n      i0.ɵɵtemplate(41, AdminViewComponent_div_41_Template, 4, 0, \"div\", 20);\n      i0.ɵɵtemplate(42, AdminViewComponent_highcharts_chart_42_Template, 1, 2, \"highcharts-chart\", 21);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(43, \"div\", 22);\n      i0.ɵɵelementStart(44, \"div\", 0);\n      i0.ɵɵelementStart(45, \"div\", 23);\n      i0.ɵɵtemplate(46, AdminViewComponent_div_46_Template, 4, 0, \"div\", 20);\n      i0.ɵɵtemplate(47, AdminViewComponent_highcharts_chart_47_Template, 1, 2, \"highcharts-chart\", 24);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(48, \"div\", 23);\n      i0.ɵɵtemplate(49, AdminViewComponent_div_49_Template, 4, 0, \"div\", 20);\n      i0.ɵɵtemplate(50, AdminViewComponent_highcharts_chart_50_Template, 1, 2, \"highcharts-chart\", 21);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"bsConfig\", ctx.bsConfig);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"clearable\", true)(\"clearOnBackspace\", true)(\"items\", ctx.statusList);\n      i0.ɵɵadvance(23);\n      i0.ɵɵproperty(\"ngIf\", ctx.counter);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", !ctx.courseChartOptions);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.enrollmentList.list.length > 0);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", ctx.enrollmentList.list.length === 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.courseChartOptions);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.certificateList.list.length === 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.certificateList.list.length > 0);\n    }\n  },\n  directives: [i8.BlockUIComponent, i5.ɵNgNoValidate, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.DefaultValueAccessor, i9.BsDaterangepickerInputDirective, i5.NgControlStatus, i5.FormControlName, i9.BsDaterangepickerDirective, i10.NgSelectComponent, i5.CheckboxControlValueAccessor, i11.TooltipDirective, i12.NgIf, i13.HighchartsChartComponent],\n  styles: [\".table-card .row-table:first-child{border-bottom:1px solid #ddd}.table-card .row-table{display:flex;align-items:center;table-layout:fixed;height:100%;width:100%;margin:0}.table-card .row-table>[class*=col-]{display:table-cell;float:none;table-layout:fixed;vertical-align:middle}.table-card .row-table .br{border-right:1px solid #0072c4}.card-block-big{padding:2em}.table-card .row-table h5{display:block;margin-bottom:.3em;margin-right:0;font-size:13px;font-weight:500}.table-card .row-table span{text-transform:uppercase;font-size:13px;font-weight:500}.table-card .row-table i{color:#042b71}.cursor-div:hover{background-color:#e1f1fd}.dac-style-1{width:100%;display:block}.dac-style-2{margin-top:2%}.point-series{color:#000}\\n\"],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], AdminViewComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}