﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AngleSharp" version="0.16.1" targetFramework="net472" />
  <package id="AngleSharp.Css" version="0.16.3" targetFramework="net472" />
  <package id="Antlr" version="3.5.0.2" targetFramework="net472" />
  <package id="Azure.Core" version="1.30.0" targetFramework="net472" />
  <package id="bootstrap" version="5.1.2" targetFramework="net472" />
  <package id="BouncyCastle" version="1.8.9" targetFramework="net472" />
  <package id="Dapper" version="1.60.6" targetFramework="net472" />
  <package id="EntityFramework" version="6.4.4" targetFramework="net472" />
  <package id="jQuery" version="3.6.0" targetFramework="net472" />
  <package id="jQuery.Validation" version="1.19.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.Cors" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.Identity.Core" version="2.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.Identity.EntityFramework" version="2.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.Identity.Owin" version="2.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.Razor" version="3.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Cors" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.HelpPage" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Owin" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.7" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="3.6.0" targetFramework="net472" />
  <package id="Microsoft.Graph" version="5.4.0" targetFramework="net472" />
  <package id="Microsoft.Graph.Core" version="3.0.4" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Abstractions" version="6.27.0" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.JsonWebTokens" version="6.27.0" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Logging" version="6.27.0" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Protocols" version="6.27.0" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Protocols.OpenIdConnect" version="6.27.0" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Tokens" version="6.27.0" targetFramework="net472" />
  <package id="Microsoft.jQuery.Unobtrusive.Validation" version="3.2.12" targetFramework="net472" />
  <package id="Microsoft.Kiota.Abstractions" version="1.1.0" targetFramework="net472" />
  <package id="Microsoft.Kiota.Authentication.Azure" version="1.0.2" targetFramework="net472" />
  <package id="Microsoft.Kiota.Http.HttpClientLibrary" version="1.0.1" targetFramework="net472" />
  <package id="Microsoft.Kiota.Serialization.Form" version="1.0.1" targetFramework="net472" />
  <package id="Microsoft.Kiota.Serialization.Json" version="1.0.3" targetFramework="net472" />
  <package id="Microsoft.Kiota.Serialization.Text" version="1.0.1" targetFramework="net472" />
  <package id="Microsoft.NETCore.Platforms" version="1.1.0" targetFramework="net472" />
  <package id="Microsoft.Owin" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Cors" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security.Cookies" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security.Facebook" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security.Google" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security.MicrosoftAccount" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security.OAuth" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security.Twitter" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net472" />
  <package id="Modernizr" version="2.8.3" targetFramework="net472" />
  <package id="NETStandard.Library" version="2.0.3" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net472" />
  <package id="Owin" version="1.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Diagnostics.DiagnosticSource" version="6.0.0" targetFramework="net472" />
  <package id="System.IdentityModel.Tokens.Jwt" version="6.27.0" targetFramework="net472" />
  <package id="System.IO" version="4.3.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Memory.Data" version="1.0.2" targetFramework="net472" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net472" />
  <package id="System.Net.Http.WinHttpHandler" version="7.0.0" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Claims" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net472" />
  <package id="System.Text.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Text.Encoding.CodePages" version="5.0.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="6.0.0" targetFramework="net472" />
  <package id="System.Text.Json" version="6.0.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
  <package id="Tavis.UriTemplates" version="2.0.0" targetFramework="net472" />
  <package id="WebActivatorEx" version="2.2.0" targetFramework="net472" />
  <package id="WebApiThrottle" version="1.5.4" targetFramework="net472" />
  <package id="WebGrease" version="1.6.0" targetFramework="net472" />
</packages>