{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let TruncatePipe = /*#__PURE__*/(() => {\n  class TruncatePipe {\n    transform(value, length) {\n      // let limit = args.length > 0 ? parseInt(args[0], 10) : 10;\n      // let trail = args.length > 1 ? args[1] : '...';\n      let limit = length ? length : 10;\n      let trail = '...';\n      return value.length > limit ? value.substring(0, limit) + trail : value;\n    }\n\n  }\n\n  TruncatePipe.ɵfac = function TruncatePipe_Factory(t) {\n    return new (t || TruncatePipe)();\n  };\n\n  TruncatePipe.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n    name: \"truncate\",\n    type: TruncatePipe,\n    pure: true\n  });\n  return TruncatePipe;\n})();", "map": null, "metadata": {}, "sourceType": "module"}