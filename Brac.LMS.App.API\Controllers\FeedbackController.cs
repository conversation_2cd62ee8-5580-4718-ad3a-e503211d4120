﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [RoutePrefix("api/feedback")]
    public class FeedbackController : ApplicationController
    {
        private readonly IFeedbackService _service;

        public FeedbackController()
        {
            _service = new FeedbackService();
        }

        [Authorize(Roles = "Admin"), HttpPost, Route("course/questions/create-or-update")]
        public async Task<IHttpActionResult> CourseFeedbackQuestionCreateOrUpdate(FeedbackQuestionModel model)
        {
            var _nservice = new FeedbackService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.FeedbackQuestionCreateOrUpdate(model, LMS.Models.EvaluationType.Course, User.Identity));
        }

        [Authorize(Roles = "Admin"), HttpPost, Route("learning-hour/questions/create-or-update")]
        public async Task<IHttpActionResult> LearningHourFeedbackQuestionCreateOrUpdate(FeedbackQuestionModel model)
        {
            var _nservice = new FeedbackService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.FeedbackQuestionCreateOrUpdate(model, LMS.Models.EvaluationType.LearningHour, User.Identity));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("course/questions/list")]
        public async Task<IHttpActionResult> GetCourseFeedbackQuestionList(QuestionGroup? group, QuestionType? qType, int size, int pageNumber)
        {
            return Ok(await _service.GetFeedbackQuestionList(EvaluationType.Course, group, qType, size, pageNumber));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("learning-hour/questions/list")]
        public async Task<IHttpActionResult> GetLearningHourFeedbackQuestionList(QuestionGroup? group, QuestionType? qType, int size, int pageNumber)
        {
            return Ok(await _service.GetFeedbackQuestionList(EvaluationType.LearningHour, group, qType, size, pageNumber));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("questions/get/{id}")]
        public async Task<IHttpActionResult> GetFeedbackQuestionById(long id)
        {
            return Ok(await _service.GetFeedbackQuestionById(id));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("questions/delete/{id}")]
        public async Task<IHttpActionResult> DeleteFeedbackQuestionById(long id)
        {
            var _nservice = new FeedbackService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.DeleteFeedbackQuestionById(id));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("question-group/dropdown-list")]
        public IHttpActionResult GetQuestionGroups()
        {
            return Ok(Enum.GetNames(typeof(QuestionGroup)));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("question-type/dropdown-list")]
        public IHttpActionResult GetQuestionTypes()
        {
            return Ok(Enum.GetNames(typeof(QuestionType)));
        }

        [AllowAnonymous, HttpGet, Route("download-trainee-course-feedbacks-in-excel")]
        public async Task<HttpResponseMessage> GetTraineeCourseFeedbacks(Guid courseId, int timeZoneOffset, ReportType reportType)
        {
            byte[] byteArray = reportType == ReportType.Excel ? await _service.GetTraineeCourseFeedbacks(courseId, timeZoneOffset) : await _service.GetTraineeCourseFeedbacksPdf(courseId, timeZoneOffset);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.
            //response.Content.Headers.Add("x-filename", "Student_List.xls");
            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }

        [AllowAnonymous, HttpGet, Route("download-trainee-learning-hour-feedbacks-in-excel")]
        public async Task<HttpResponseMessage> GetTraineeLearningHourFeedbacks(Guid examId, ReportType reportType, int timeZoneOffset)
        {
            byte[] byteArray = reportType == ReportType.Excel ? await _service.GetTraineeLearningHourFeedbacks(examId, timeZoneOffset) : await _service.GetTraineeLearningHourFeedbacksPdf(examId);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.
            //response.Content.Headers.Add("x-filename", "Student_List.xls");
            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }

        #region Trainee Panel APIs

        [Authorize(Roles = "Trainee"), HttpPost, Route("course/save-or-update")]
        public async Task<IHttpActionResult> CourseFeedbackSaveOrUpdate(CourseFeedbackModel model)
        {
            var _nservice = new FeedbackService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.CourseFeedbackSaveOrUpdate(model, CurrentUser));
        }

        [Authorize(Roles = "Trainee"), HttpPost, Route("learning-hour/save-or-update")]
        public async Task<IHttpActionResult> LearningHourFeedbackSaveOrUpdate(LearningHourFeedbackModel model)
        {
            var _nservice = new FeedbackService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.LearningHourFeedbackSaveOrUpdate(model, CurrentUser));
        }
        #endregion
    }
}
