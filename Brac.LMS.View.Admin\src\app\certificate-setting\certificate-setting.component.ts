import {
  Component,
  TemplateRef,
  ViewChild,
  ElementRef,
  ViewEncapsulation,
  OnInit,
} from "@angular/core";
import { BsModalService, BsModalRef } from "ngx-bootstrap/modal";
import { ColumnMode } from "@swimlane/ngx-datatable";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { CommonService } from "../_services/common.service";
import { ToastrService } from "ngx-toastr";
import { BlockUI, NgBlockUI } from "ng-block-ui";
import { environment } from "../../environments/environment";
import { ResponseStatus } from "../_models/enum";
//import { Page } from '../_models/page';
import { AppComponent } from "../app.component";

@Component({
  selector: "app-certificate-setting",
  templateUrl: "./certificate-setting.component.html",
  encapsulation: ViewEncapsulation.None,
})
export class CertificateSettingComponent implements OnInit {
  @ViewChild("versionModal") versionModal: TemplateRef<any>;

  entryForm: FormGroup;
  submitted = false;
  @BlockUI() blockUI: NgBlockUI;
  btnSaveText = "Save";

  modalConfig: any = { class: "gray modal-md", backdrop: "static" };
  modalRef: BsModalRef;

  // Version management
  versionModalRef: BsModalRef;
  createNewVersion: boolean = false;
  changeReason: string = "";
  currentVersion: number = 1;

  timeStamp;
  templateImageURL: any;
  person1SignURL: any;
  person2SignURL: any;
  person3SignURL: any;
  public message: string;

  templateImageFile: any = null;
  person1SignFile: any = null;
  person2SignFile: any = null;
  person3SignFile: any = null;

  // page = new Page();

  rows = [];
  courseList = [];
  templateList = [];
  loadingIndicator = false;
  ColumnMode = ColumnMode;

  scrollBarHorizontal = window.innerWidth < 1200;

  constructor(
    private appComponent: AppComponent,
    private modalService: BsModalService,
    public formBuilder: FormBuilder,
    private _service: CommonService,
    private toastr: ToastrService,
    private router: Router
  ) {
    window.onresize = () => {
      this.scrollBarHorizontal = window.innerWidth < 1200;
    };
  }
  handleSelectClick(selectElement) {
    this.appComponent.handleSelectClick(selectElement);
  }

  ngOnInit() {
    this.entryForm = this.formBuilder.group({
      id: [null],
      course: [null, [Validators.required]],
      template: [null, [Validators.required]],
      courseDescription: [
        null,
        [Validators.required, Validators.maxLength(500)],
      ],
      designation1: [null, [Validators.maxLength(250)]],
      designation2: [null, [Validators.maxLength(250)]],
      designation3: [null, [Validators.maxLength(250)]],
      person1Name: [null, [Validators.maxLength(250)]],
      person2Name: [null, [Validators.maxLength(250)]],
      person3Name: [null, [Validators.maxLength(250)]],
      templateImagePath: [null],
      person1SignPath: [null],
      person2SignPath: [null],
      person3SignPath: [null],
    });
    this.getCourseList();
    this.getTemplateList();
  }

  get f() {
    return this.entryForm.controls;
  }

  getCourseList() {
    this._service.get("course/dropdown-list").subscribe(
      (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.courseList = res.Data;
      },
      () => {}
    );
  }

  getTemplateList() {
    this._service.get("certificate-configuration/get-templates").subscribe(
      (res) => {
        this.templateList = res;
      },
      () => {}
    );
  }

  // onCourseChange(event) {
  //   this.getItem(event.Id)
  // }

  getItem(id) {
    this.blockUI.start("Getting data...");
    this._service.get("certificate-configuration/get/" + id).subscribe(
      (res) => {
        this.blockUI.stop();
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }

        this.entryForm.reset({
          course: this.entryForm.value.course,
        });
        this.templateImageURL = null;
        this.person1SignURL = null;
        this.person2SignURL = null;
        this.person3SignURL = null;
        this.person1SignFile = null;
        this.person2SignFile = null;
        this.person3SignFile = null;
        console.log("res.Data", res.Data);
        if (res.Data) {
          const timeNow = new Date();
          this.timeStamp = timeNow.getTime();

          this.btnSaveText = "Update";
          this.currentVersion = res.Data.Version || 1;
          this.entryForm.controls["id"].setValue(res.Data.Id);
          this.entryForm.controls["template"].setValue(res.Data.Template);
          this.entryForm.controls["courseDescription"].setValue(
            res.Data.CourseDescription
          );
          this.entryForm.controls["designation1"].setValue(
            res.Data.Designation1
          );
          this.entryForm.controls["designation2"].setValue(
            res.Data.Designation2
          );
          this.entryForm.controls["designation3"].setValue(
            res.Data.Designation3
          );

          this.entryForm.controls["person1Name"].setValue(res.Data.Person1Name);
          this.entryForm.controls["person2Name"].setValue(res.Data.Person2Name);
          this.entryForm.controls["person3Name"].setValue(res.Data.Person3Name);

          // this.entryForm.controls['Person1SignPath'].setValue(res.Record.Person1SignPath);
          // this.entryForm.controls['WrittenSignPath'].setValue(res.Record.WrittenSignPath);
          // this.entryForm.controls['Person2SignPath'].setValue(res.Record.Person2SignPath);
          if (res.Data.TemplatePath)
            this.templateImageURL = environment.baseUrl + res.Data.TemplatePath;
          console.log("this.templateImageURL", this.templateImageURL);
          if (res.Data.Person1SignPath)
            this.person1SignURL =
              environment.baseUrl +
              res.Data.Person1SignPath +
              "?v=" +
              this.timeStamp;
          console.log("this.person1SignURL", this.person1SignURL);
          if (res.Data.Person2SignPath)
            this.person2SignURL =
              environment.baseUrl +
              res.Data.Person2SignPath +
              "?v=" +
              this.timeStamp;

          if (res.Data.Person3SignPath)
            this.person3SignURL =
              environment.baseUrl +
              res.Data.Person3SignPath +
              "?v=" +
              this.timeStamp;
        }
      },
      (err) => {
        this.blockUI.stop();
        this.toastr.error(err.message || err, "Error!", { timeOut: 2000 });
      }
    );
  }

  templatePreview(files) {
    if (files.length === 0) return;

    var mimeType = files[0].type;
    if (mimeType.match(/image\/*/) == null) {
      this.message = "Only images are supported.";
      return;
    }

    var reader = new FileReader();
    this.templateImageFile = files[0];
    reader.readAsDataURL(files[0]);
    reader.onload = (_event) => {
      this.templateImageURL = reader.result;
    };
  }

  person1SignPreview(files) {
    if (files.length === 0) return;

    var mimeType = files[0].type;
    if (mimeType.match(/image\/*/) == null) {
      this.message = "Only images are supported.";
      return;
    }

    var reader = new FileReader();
    this.person1SignFile = files[0];
    reader.readAsDataURL(files[0]);
    reader.onload = (_event) => {
      this.person1SignURL = reader.result;
    };
  }

  person2SignPreview(files) {
    if (files.length === 0) return;

    var mimeType = files[0].type;
    if (mimeType.match(/image\/*/) == null) {
      this.message = "Only images are supported.";
      return;
    }

    var reader = new FileReader();
    this.person2SignFile = files[0];
    reader.readAsDataURL(files[0]);
    reader.onload = (_event) => {
      this.person2SignURL = reader.result;
    };
  }

  person3SignPreview(files) {
    if (files.length === 0) return;

    var mimeType = files[0].type;
    if (mimeType.match(/image\/*/) == null) {
      this.message = "Only images are supported.";
      return;
    }

    var reader = new FileReader();
    this.person3SignFile = files[0];
    reader.readAsDataURL(files[0]);
    reader.onload = (_event) => {
      this.person3SignURL = reader.result;
    };
  }

  removeImage(imageNumber: number) {
    switch (imageNumber) {
      case 0:
        this.templateImageFile = null;
        this.templateImageURL = null;
        break;
      case 1:
        this.person1SignFile = null;
        this.person1SignURL = null;
        break;
      case 2:
        this.person2SignFile = null;
        this.person2SignURL = null;
        break;
      case 3:
        this.person3SignFile = null;
        this.person3SignURL = null;
        break;
    }
  }

  onFormSubmit() {
    this.submitted = true;
    if (this.templateImageURL) {
      this.entryForm.get("templateImagePath").setValidators([]);
      this.entryForm.get("templateImagePath").updateValueAndValidity();
    }
    if (!this.person1SignURL && !this.person2SignURL && !this.person3SignURL) {
      this.toastr.warning(
        "Minimum one signatory's signature must be uploaded",
        "Warning!"
      );
      return;
    }

    if (this.person1SignURL) {
      this.entryForm.get("person1SignPath").setValidators([]);
      this.entryForm.get("person1SignPath").updateValueAndValidity();
    }

    if (this.person2SignURL) {
      this.entryForm.get("person2SignPath").setValidators([]);
      this.entryForm.get("person2SignPath").updateValueAndValidity();
    }

    if (this.person3SignURL) {
      this.entryForm.get("person3SignPath").setValidators([]);
      this.entryForm.get("person3SignPath").updateValueAndValidity();
    }

    if (this.entryForm.invalid) {
      return;
    }

    // Show version choice dialog for existing configurations
    if (this.btnSaveText === "Update") {
      this.showVersionDialog();
    } else {
      // For new configurations, save directly
      this.saveConfiguration();
    }
  }

  showVersionDialog() {
    // Reset version dialog state
    this.createNewVersion = false;
    this.changeReason = "";
    // Open the version modal
    this.versionModalRef = this.modalService.show(
      this.versionModal,
      this.modalConfig
    );
  }

  saveConfiguration() {
    this.blockUI.start("Saving...");

    const obj = {
      Id: this.entryForm.value.id ? this.entryForm.value.id : 0,
      CourseId: this.entryForm.value.course,
      Template: this.entryForm.value.template,
      CourseDescription: this.entryForm.value.courseDescription.trim(),
      Designation1: this.entryForm.value.designation1
        ? this.entryForm.value.designation1.trim()
        : null,
      Designation2: this.entryForm.value.designation2
        ? this.entryForm.value.designation2.trim()
        : null,
      Designation3: this.entryForm.value.designation3
        ? this.entryForm.value.designation3.trim()
        : null,
      Person1Name: this.entryForm.value.person1Name
        ? this.entryForm.value.person1Name.trim()
        : null,
      Person2Name: this.entryForm.value.person2Name
        ? this.entryForm.value.person2Name.trim()
        : null,
      Person3Name: this.entryForm.value.person3Name
        ? this.entryForm.value.person3Name.trim()
        : null,
      // Add version fields
      CreateNewVersion: this.createNewVersion,
      ChangeReason: this.changeReason,
    };

    const certificateFormdata = new FormData();
    certificateFormdata.append("Model", JSON.stringify(obj));
    if (this.templateImageFile)
      certificateFormdata.append("TemplatePath", this.templateImageFile);
    if (this.person1SignFile)
      certificateFormdata.append("Person1Sign", this.person1SignFile);
    if (this.person2SignFile)
      certificateFormdata.append("Person2Sign", this.person2SignFile);
    if (this.person3SignFile)
      certificateFormdata.append("Person3Sign", this.person3SignFile);

    this._service
      .post("certificate-configuration/create-or-update", certificateFormdata)
      .subscribe(
        (res) => {
          this.blockUI.stop();
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }

          // Enhanced success message
          const message = res.Data?.IsNewVersion
            ? `Successfully created version v${res.Data.Version}!`
            : res.Message || "Successfully saved!";

          this.toastr.success(message, "Success!", { timeOut: 3000 });

          // Update current version if new version was created
          if (res.Data?.IsNewVersion) {
            this.currentVersion = res.Data.Version;
          }

          // Hide version modal if it was open
          if (this.versionModalRef) {
            this.versionModalRef.hide();
          }
        },
        (err) => {
          this.blockUI.stop();
          this.toastr.error(err.message || err, "Error!", { timeOut: 2000 });
        }
      );
  }

  modalHide() {
    this.entryForm.reset();
    this.modalRef.hide();
    this.submitted = false;
    this.btnSaveText = "Save";
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.modalConfig);
  }

  openVersionModal(template: TemplateRef<any>) {
    this.versionModalRef = this.modalService.show(template, this.modalConfig);
  }

  closeVersionModal() {
    if (this.versionModalRef) {
      this.versionModalRef.hide();
    }
    this.createNewVersion = false;
    this.changeReason = "";
  }

  onVersionChoiceChange(createNew: boolean) {
    this.createNewVersion = createNew;
    if (!createNew) {
      this.changeReason = "";
    }
  }

  confirmSave() {
    // Validate change reason if creating new version
    if (
      this.createNewVersion &&
      (!this.changeReason || this.changeReason.trim().length === 0)
    ) {
      this.toastr.warning(
        "Please provide a reason for creating new version",
        "Warning!"
      );
      return;
    }

    if (this.createNewVersion && this.changeReason.trim().length > 50) {
      this.toastr.warning(
        "Change reason cannot exceed 50 characters",
        "Warning!"
      );
      return;
    }

    this.saveConfiguration();
  }
}
