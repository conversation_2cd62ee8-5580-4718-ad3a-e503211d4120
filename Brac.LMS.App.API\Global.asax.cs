﻿using Brac.LMS.App.API.Controllers;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Http;
using System.Web.Mvc;
using System.Web.Optimization;
using System.Web.Routing;

namespace Brac.LMS.App.API
{
    public class WebApiApplication : System.Web.HttpApplication
    {
        protected void Application_Start()
        {
            AreaRegistration.RegisterAllAreas();
            GlobalConfiguration.Configure(WebApiConfig.Register);
            FilterConfig.RegisterGlobalFilters(GlobalFilters.Filters);
            RouteConfig.RegisterRoutes(RouteTable.Routes);
            BundleConfig.RegisterBundles(BundleTable.Bundles);
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            MvcHandler.DisableMvcResponseHeader=true;
        }

        protected void Application_BeginRequest(object sender, EventArgs e)
        {
            var app = sender as HttpApplication;
            if (app != null && app.Context != null)
            {
                app.Context.Response.Headers.Remove("Server");
                app.Context.Response.Headers.Remove("X-AspNet-Version");
            }
            if (HttpContext.Current.Request.Cookies.AllKeys.Contains(".Brac.LMS.Cookie"))
            {
                Request.Headers.Add("Authorization", $"Bearer {HttpContext.Current.Request.Cookies.Get(".Brac.LMS.Cookie").Value}");
            }
        }

        protected void Application_Error(object sender, EventArgs e)
        {
            var exception = Server.GetLastError();

            Response.Clear();
            Server.ClearError();

            var routeData = new RouteData();
            routeData.Values["controller"] = "ErrorPage";
            routeData.Values["action"] = "Oops";

            if (exception is HttpException httpException && httpException.GetHttpCode() == 404)
            {
                Response.StatusCode = 404;
            }

            IController errorController = new ErrorPageController();
            errorController.Execute(new RequestContext(new HttpContextWrapper(Context), routeData));
        }

    }
}
