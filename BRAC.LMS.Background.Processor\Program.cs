﻿using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Newtonsoft.Json;
using SendEmailApp;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Diagnostics;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace BRAC.LMS.Background.Processor
{
    class Program
    {
        private static string _logPath = "";
        private static int _delayBetweenEmails = 0;
        private static string _webUrl = "";
        private static ApplicationDbContext _context = null;

        static void Main(string[] args)
        {
            try
            {
                if (ConfigurationManager.AppSettings["DelayBetweenEmailsMilliseconds"] != null && !string.IsNullOrEmpty(ConfigurationManager.AppSettings["DelayBetweenEmailsMilliseconds"]))
                {
                    _delayBetweenEmails = int.Parse(ConfigurationManager.AppSettings["DelayBetweenEmailsMilliseconds"]);
                }
                if (ConfigurationManager.AppSettings["WebUrl"] != null && !string.IsNullOrEmpty(ConfigurationManager.AppSettings["WebUrl"]))
                {
                    _webUrl = Convert.ToString(ConfigurationManager.AppSettings["WebUrl"]);
                }
                if (ConfigurationManager.AppSettings["LogPath"] != null && !string.IsNullOrEmpty(ConfigurationManager.AppSettings["LogPath"]))
                {
                    _logPath = ConfigurationManager.AppSettings["LogPath"] + "/";
                    if (!System.IO.Directory.Exists(_logPath))
                    {
                        System.IO.Directory.CreateDirectory(_logPath);
                    }
                }

                Sync().Wait();
            }
            catch (Exception ex)
            {
                WriteLog(ex.Message);
            }
        }

        private static async Task Sync()
        {
            try
            {
                if (IsProcessRunning()) return;

                _context = new ApplicationDbContext();
                WriteLog("Delete NoProgrss30 Notification: Started");
                await DeleteNoProgrss30();
                WriteLog("Delete NoProgrss30 Notification: Completed");

                WriteLog("Check_NoProgress30: Started");
                await Check_NoProgress30();
                WriteLog("Check_NoProgress30: Completed");


                WriteLog("Check_LastWeekReminder: Started");
                await Check_LastWeekReminder();
                WriteLog("Check_LastWeekReminder: Completed");


                WriteLog("Check_LastDayReminder: Started");
                await Check_LastDayReminder();
                WriteLog("Check_LastDayReminder: Completed");


                WriteLog("Check_FailureToComplete: Started");
                await Check_FailureToComplete();
                WriteLog("Check_FailureToComplete: Completed");


                WriteLog("Check_OneWeekRemainCertificateExpiry: Started");
                await Check_OneWeekRemainCertificateExpiry();
                WriteLog("Check_OneWeekRemainCertificateExpiry: Completed");


                WriteLog("Check_CertificateExpiry: Started");
                await Check_CertificateExpiry();
                WriteLog("Check_CertificateExpiry: Completed");


                WriteLog("Check_PendingNotification: Started");
                await Check_PendingNotification();
                WriteLog("Check_PendingNotification: Completed");


                WriteLog("Send_Pending_Emails: Started");
                await Send_Pending_Emails();
                WriteLog("Send_Pending_Emails: Completed");


                WriteLog("ExpireCertificates: Started");
                await ExpireCertificates();
                WriteLog("ExpireCertificates: Completed");


                WriteLog("DeleteOldNotification: Started");
                await DeleteOldNotification();
                WriteLog("DeleteOldNotification: Completed");

                WriteLog("Delete Inactive And Unpublish Notification: Started");
                await DeleteNotifications();
                WriteLog("Delete Inactive And Unpublish Notification: Completed");
               


            }
            catch (Exception ex)
            {
                WriteLog("Fatal Error: " + ex.Message);
            }
            finally
            {
                _context?.Dispose();
            }
        }

        private static async Task Send_Pending_Emails()
        {
            Dictionary<Guid, int> sentMail = new Dictionary<Guid, int>();
            string message = "", subject = "";
            try
            {
                var emails = await _context.NotificationEmails.Where(x => x.AttemptLimit > 0 && (x.SendStatus == SendStatus.Pending || x.SendStatus == SendStatus.Failed)).Take(300).ToListAsync();
                int sent = 0, failed = 0;
                Guid refId;
                var em = new MailSendService();
                string traineeName;
                do
                {
                    foreach (var item in emails)
                    {
                        try
                        {
                            Guid.TryParse(item.Notification.Payload, out refId);
                            traineeName = item.Notification.TargetTrainee != null ? item.Notification.TargetTrainee.Name : "";
                            switch (item.Notification.NotificationType)
                            {
                                case NotificationType.CourseEnrolmentByAdmin:
                                    message = $"<h4>Dear {traineeName},</h4><p>Greetings from BRAC Bank eLearning!</p> <p>The admin has enrolled you in a new course <span style='color:#0070c0;font-size: 14.0pt;'>{await _context.Courses.Where(x => x.Id == refId).Select(x => x.Title).FirstOrDefaultAsync()}</span>. Please login or click on the following link to see the course details.</p><p>Course’s web link:<br/> <a href='{_webUrl + "course-details/" + item.Notification.Payload}'>{_webUrl + "course-details/" + item.Notification.Payload}</a></p><p>Please use the latest version of “Microsoft Edge” for better visibility, using “Windows Explorer” may not work properly.</p><h4>Regards</h4><h4>BRAC Bank eLearning</h4>";
                                    subject = item.Notification.Title;
                                    break;
                                case NotificationType.NoProgress30:
                                    message = $"<h4>Dear {traineeName},</h4> <p>Greetings from BRAC Bank eLearning!</p><p>You have no progress in this course for the last 30 days. Please, continue your study on <span style='color:#0070c0;font-size: 14.0pt;'>{await _context.Courses.Where(x => x.Id == refId).Select(x => x.Title).FirstOrDefaultAsync()}</span> and complete the course ASAP. Please login or click on the following link to study the course content.</p><p>Course’s web link:<br/> <a href='{_webUrl + "course-details/" + item.Notification.Payload}'>{_webUrl + "course-details/" + item.Notification.Payload}</a></p><p>Please use the latest version of “Microsoft Edge” for better visibility, using “Windows Explorer” may not work properly.</p><h4>Regards</h4><h4>BRAC Bank eLearning</h4>";
                                    subject = item.Notification.Title;
                                    break;
                                case NotificationType.LastDayReminder:
                                    var data = await _context.CourseExams.Where(x => x.Id == refId)
                                        .GroupJoin(_context.CourseEnrollments.Where(x => x.TraineeId == item.Notification.TargetTraineeId), x => x.CourseId, y => y.CourseId, (x, y) => new { Exam = x, Enrolments = y })
                                        .SelectMany(x => x.Enrolments.DefaultIfEmpty(), (y, z) => new { y.Exam, Enrolment = z })
                                        .Select(x => new { x.Exam.Course.Title, x.Exam.EndDate, x.Enrolment.ExpireDate }).FirstOrDefaultAsync();
                                    var expiredDate = data.ExpireDate.HasValue ? data.ExpireDate.Value.AddDays(1).AddTicks(-1) : default(DateTime?);
                                    var endDate = data.EndDate.HasValue && expiredDate.HasValue ? (data.EndDate > expiredDate ? data.EndDate.Value : expiredDate.Value) : (data.EndDate.HasValue ? data.EndDate.Value : expiredDate.Value);

                                    message = $"<h4>Dear {traineeName},</h4> <p>Greetings from BRAC Bank eLearning!</p><p>You have not enough time to take the Certification Exam on \"<span style='color:#0070c0;font-size: 14.0pt;'>{data.Title}</span>\" . You will no longer be able to participate in the certification exam for this course after {endDate:dd MMM, yyyy}. So, get the certificate by participating in the test early. Please login or click on the following link to take the certification exam.</p><p>Exam’s web link:<br/> <a href='{_webUrl + "course -details/" + item.Notification.Payload}'>{_webUrl + "course-certificate-test//" + item.Notification.Payload}</a></p><p>Please use the latest version of “Microsoft Edge” for better visibility, using “Windows Explorer” may not work properly.</p><h4>Regards</h4><h4>BRAC Bank eLearning</h4>";
                                    subject = item.Notification.Title;
                                    break;
                                case NotificationType.FailureToComplete:
                                    message = $"<h4>Dear {traineeName},</h4><p>Greetings from BRAC Bank eLearning!</p> <p>You failed to complete the course \"<span style='color:#0070c0;font-size: 14.0pt;'>{await _context.Courses.Where(x => x.Id == refId).Select(x => x.Title).FirstOrDefaultAsync()}</span>\" within the time allotted to you. Contact the appropriate authority for further instructions.</p><h4>Regards</h4><h4>BRAC Bank eLearning</h4>";
                                    subject = item.Notification.Title;
                                    break;
                                case NotificationType.SuccessfullCompletion:
                                    message = $"<h4>Dear {traineeName},</h4><p>Greetings from BRAC Bank eLearning!</p> <p>You have successfully completed thes course \"<span style='color:#0070c0;font-size: 14.0pt;'>{await _context.Courses.Where(x => x.Id == refId).Select(x => x.Title).FirstOrDefaultAsync()}</span>\". Congratulations to you.</p><h4>Regards</h4><h4>BRAC Bank eLearning</h4>";
                                    subject = item.Notification.Title;
                                    break;
                            }
                            try
                            {
                                item.AttemptLimit--;
                                item.LastTryOn = DateTime.UtcNow;
                                em.SendMail(item.Notification.TargetTrainee.Email, null, null, subject, message);
                                await Task.Delay(_delayBetweenEmails);
                                item.SendStatus = SendStatus.Sent;
                                _context.Entry(item).State = EntityState.Deleted;
                                sent++;
                            }
                            catch (Exception ex)
                            {
                                item.SendStatus = SendStatus.Failed;
                                WriteLog("Email Sending Failed: " + item.Notification.NotificationType.ToString() + " to Trainee: " + item.Notification.TargetTrainee.PIN + " => Send to:" + item.Notification.TargetTrainee.Email + " | Error: " + ex.Message);
                                failed++;
                            }
                            await _context.SaveChangesAsync();
                        }
                        catch (Exception ex)
                        {
                            WriteLog("Email Generation Error For: " + item.Notification.NotificationType.ToString() + " Trainee: " + item.Notification.TargetTrainee.PIN + " => " + ex.Message);
                        }
                    }

                    WriteLog("Email Sending Success: " + sent + " Failed: " + failed);
                    emails = await _context.NotificationEmails.Where(x => x.AttemptLimit > 0 && (x.SendStatus == SendStatus.Pending || x.SendStatus == SendStatus.Failed)).Take(300).ToListAsync();
                } while (emails.Any());
            }
            catch (Exception ex)
            {
                WriteLog("Send_Pending_Emails: " + ex.Message);
            }
        }


        private static async Task Check_NoProgress30()
        {
            Notification notification = null;
            Dictionary<Notification, List<string>> fcmNotification = new Dictionary<Notification, List<string>>();
            try
            {
                var notiEvent = await _context.NotificationEvents.FirstOrDefaultAsync(x => x.Type == NotificationType.NoProgress30);
                //if (notiEvent == null || notiEvent.LastChecked == DateTime.Today) return;
                if (notiEvent == null) return;

                var date = DateTime.Today.AddDays(-30);
                var data = await _context.TraineeCourseActivities.Where(x => x.LastStudyDate <= date && x.Course.Active && x.Course.Published).Select(x => new { x.TraineeId, x.CourseId, x.Course.Title, x.Trainee.PhoneNo }).ToListAsync();
                SMSSender smsSender = notiEvent?.SMS ?? false ? new SMSSender() : null;

                //foreach (var item in ToDeletedata)
                //{
                //    var dataNotification = await _context.Notifications.Where(x => x.Payload == item.CourseId.ToString()).ToListAsync();
                //    foreach (var subItem in dataNotification)
                //    {
                //        _context.Entry(subItem).State = EntityState.Deleted;
                //    }
                //}
                foreach (var item in data)
                {
                    if(_context.Courses.Where(x=>x.Id.ToString()==item.CourseId.ToString() && x.Published==true).Count()>0)
                    {
                        notification = new Notification()
                        {
                            Id = Guid.NewGuid(),
                            CreatedOn = DateTime.UtcNow,
                            NotificationType = NotificationType.NoProgress30,
                            TargetUserType = UserType.Trainee,
                            TargetTraineeId = item.TraineeId,
                            Title = "No Progress in 30 Days",
                            Details = $"You have no progress in course \"{item.Title}\" for the last 30 days. Please, continue your study on this course.",
                            Payload = item.CourseId.ToString(),
                            NavigateTo = Navigation.CourseDetails
                        };

                        if (notiEvent.InApp)
                        {
                            _context.Notifications.Add(notification);

                            fcmNotification.Add(notification, await _context.TraineeDevices.Where(x => x.TraineeId == item.TraineeId).Select(x => x.Token).ToListAsync());
                        }
                        if (notiEvent.Email) _context.NotificationEmails.Add(new NotificationEmail { Id = Guid.NewGuid(), NotificationId = notification.Id });
                        if (notiEvent.SMS)
                        {
                            if (!string.IsNullOrEmpty(item.PhoneNo) && item.PhoneNo.Length >= 10) await smsSender.SendAsync(item.PhoneNo, notification.Details, Brac.LMS.Common.SMS.Enums.SMSEventType.NoProgressReminder);
                        }
                    }
                    
                }

                notiEvent.LastChecked = DateTime.Today;
                _context.Entry(notiEvent).State = EntityState.Modified;

                await _context.SaveChangesAsync();

                WriteLog($"No Progress in 30 Days notification sent: {data.Count}");

                foreach (var key in fcmNotification.Keys)
                {
                    await new FirebaseMessagingClient().SendNotifications(fcmNotification[key].ToArray(), key.Title, key.Details, new
                    {
                        NavigateTo = key.NavigateTo.ToString(),
                        key.Payload,
                        key.Id,
                        NotificationType = key.NotificationType.ToString()
                    });
                }

            }
            catch (Exception ex)
            {
                WriteLog("Check_NoProgress30: " + ex.Message);
            }
        }

        private static async Task Check_LastWeekReminder()
        {
            Notification notification = null;
            Dictionary<Notification, List<string>> fcmNotification = new Dictionary<Notification, List<string>>();
            try
            {
                var notiEvent = await _context.NotificationEvents.FirstOrDefaultAsync(x => x.Type == NotificationType.LastWeekReminder);
                if (notiEvent == null || notiEvent.LastChecked == DateTime.Today) return;

                var date = DateTime.Today.AddDays(7);
                var data = await _context.CourseEnrollments
                    .GroupJoin(_context.TraineeCourseActivities, x => new { x.CourseId, x.TraineeId }, y => new { y.CourseId, y.TraineeId }, (x, y) => new
                    {
                        Enrollment = x,
                        Activities = y
                    }).SelectMany(x => x.Activities.DefaultIfEmpty(), (y, z) => new { y.Enrollment, Activity = z })
                    .GroupJoin(_context.CourseExams, x => x.Enrollment.CourseId, y => y.CourseId, (x, y) => new
                    {
                        x.Enrollment,
                        x.Activity,
                        Exams = y
                    }).SelectMany(x => x.Exams.DefaultIfEmpty(), (y, z) => new { y.Enrollment, y.Activity, Exam = z })
                    .Where(x => x.Enrollment.ExpireDate != null && x.Enrollment.ExpireDate == date && (x.Activity == null || x.Activity.Progress < 100) || (x.Exam != null && x.Exam.EndDate != null && x.Exam.EndDate == date && (x.Activity == null || x.Activity.Progress < 100)))
                    .Select(x => new { x.Enrollment.TraineeId, x.Enrollment.CourseId, x.Enrollment.Course.Title, x.Enrollment.Trainee.PhoneNo }).ToListAsync();

                SMSSender smsSender = notiEvent?.SMS ?? false ? new SMSSender() : null;
                foreach (var item in data)
                {
                    notification = new Notification()
                    {
                        Id = Guid.NewGuid(),
                        CreatedOn = DateTime.UtcNow,
                        NotificationType = NotificationType.LastWeekReminder,
                        TargetUserType = UserType.Trainee,
                        TargetTraineeId = item.TraineeId,
                        Title = "Last Week Reminder",
                        Details = $"Last week reminder for the unfinished course \"{item.Title}\"! ",
                        Payload = item.CourseId.ToString(),
                        NavigateTo = Navigation.CourseDetails
                    };

                    if (notiEvent.InApp)
                    {
                        _context.Notifications.Add(notification);
                        fcmNotification.Add(notification, await _context.TraineeDevices.Where(x => x.TraineeId == item.TraineeId).Select(x => x.Token).ToListAsync());
                    }
                    if (notiEvent.Email) _context.NotificationEmails.Add(new NotificationEmail { Id = Guid.NewGuid(), NotificationId = notification.Id });
                    if (notiEvent.SMS)
                    {
                        if (!string.IsNullOrEmpty(item.PhoneNo) && item.PhoneNo.Length >= 10) await smsSender.SendAsync(item.PhoneNo, notification.Details, Brac.LMS.Common.SMS.Enums.SMSEventType.LastWeekReminder);
                    }
                }

                notiEvent.LastChecked = DateTime.Today;
                _context.Entry(notiEvent).State = EntityState.Modified;
                await _context.SaveChangesAsync();

                WriteLog($"Last week reminder for the unfinished course notification sent: {data.Count}");

                foreach (var key in fcmNotification.Keys)
                {
                    await new FirebaseMessagingClient().SendNotifications(fcmNotification[key].ToArray(), key.Title, key.Details, new
                    {
                        NavigateTo = key.NavigateTo.ToString(),
                        key.Payload,
                        key.Id,
                        NotificationType = key.NotificationType.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                WriteLog("Check_LastWeekReminder: " + ex.Message);
            }
        }

        private static async Task Check_LastDayReminder()
        {
            Notification notification = null;
            Dictionary<Notification, List<string>> fcmNotification = new Dictionary<Notification, List<string>>();
            try
            {
                var notiEvent = await _context.NotificationEvents.FirstOrDefaultAsync(x => x.Type == NotificationType.LastDayReminder);
                if (notiEvent == null || notiEvent.LastChecked == DateTime.Today) return;

                var date = DateTime.UtcNow.AddDays(1);
                var data = await _context.CourseEnrollments
                    .GroupJoin(_context.TraineeCourseActivities, x => new { x.CourseId, x.TraineeId }, y => new { y.CourseId, y.TraineeId }, (x, y) => new
                    {
                        Enrollment = x,
                        Activities = y
                    }).SelectMany(x => x.Activities.DefaultIfEmpty(), (y, z) => new { y.Enrollment, Activity = z })
                    .GroupJoin(_context.CourseExams, x => x.Enrollment.CourseId, y => y.CourseId, (x, y) => new
                    {
                        x.Enrollment,
                        x.Activity,
                        Exams = y
                    }).SelectMany(x => x.Exams.DefaultIfEmpty(), (y, z) => new { y.Enrollment, y.Activity, Exam = z })
                    .Where(x => x.Enrollment.ExpireDate != null && x.Enrollment.ExpireDate == date && (x.Activity == null || x.Activity.Progress < 100) || (x.Exam != null && x.Exam.EndDate != null && x.Exam.EndDate == date && (x.Activity == null || x.Activity.Progress < 100)))
                    .Select(x => new { x.Enrollment.TraineeId, x.Enrollment.CourseId, x.Enrollment.Course.Title, x.Enrollment.Trainee.PhoneNo }).ToListAsync();

                SMSSender smsSender = notiEvent?.SMS ?? false ? new SMSSender() : null;
                foreach (var item in data)
                {
                    new Notification()
                    {
                        Id = Guid.NewGuid(),
                        CreatedOn = DateTime.UtcNow,
                        NotificationType = NotificationType.LastDayReminder,
                        TargetUserType = UserType.Trainee,
                        TargetTraineeId = item.TraineeId,
                        Title = "Last Day Reminder",
                        Details = $"Last day reminder for the unfinished course \"{item.Title}\"! ",
                        Payload = item.CourseId.ToString(),
                        NavigateTo = Navigation.CourseDetails
                    };

                    if (notiEvent.InApp)
                    {
                        _context.Notifications.Add(notification);
                        fcmNotification.Add(notification, await _context.TraineeDevices.Where(x => x.TraineeId == item.TraineeId).Select(x => x.Token).ToListAsync());
                    }
                    if (notiEvent.Email) _context.NotificationEmails.Add(new NotificationEmail { Id = Guid.NewGuid(), NotificationId = notification.Id });
                    if (notiEvent.SMS)
                    {
                        if (!string.IsNullOrEmpty(item.PhoneNo) && item.PhoneNo.Length >= 10) await smsSender.SendAsync(item.PhoneNo, notification.Details, Brac.LMS.Common.SMS.Enums.SMSEventType.LastDayReminder);
                    }
                }

                notiEvent.LastChecked = DateTime.Today;
                _context.Entry(notiEvent).State = EntityState.Modified;

                await _context.SaveChangesAsync();

                WriteLog($"Last day reminder for the unfinished course notification sent: {data.Count}");

                foreach (var key in fcmNotification.Keys)
                {
                    await new FirebaseMessagingClient().SendNotifications(fcmNotification[key].ToArray(), key.Title, key.Details, new
                    {
                        NavigateTo = key.NavigateTo.ToString(),
                        key.Payload,
                        key.Id,
                        NotificationType = key.NotificationType.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                WriteLog("Check_LastDayReminder: " + ex.Message);
            }
        }


        private static async Task Check_FailureToComplete()
        {
            Notification notification = null;
            Dictionary<Notification, List<string>> fcmNotification = new Dictionary<Notification, List<string>>();
            try
            {
                var notiEvent = await _context.NotificationEvents.FirstOrDefaultAsync(x => x.Type == NotificationType.FailureToComplete);
                if (notiEvent == null || notiEvent.LastChecked == DateTime.Today) return;

                var date = DateTime.UtcNow.AddDays(-1);
                var data = await _context.CourseEnrollments
                    .GroupJoin(_context.TraineeCourseActivities, x => new { x.CourseId, x.TraineeId }, y => new { y.CourseId, y.TraineeId }, (x, y) => new
                    {
                        Enrollment = x,
                        Activities = y
                    }).SelectMany(x => x.Activities.DefaultIfEmpty(), (y, z) => new { y.Enrollment, Activity = z })
                    .GroupJoin(_context.CourseExams, x => x.Enrollment.CourseId, y => y.CourseId, (x, y) => new
                    {
                        x.Enrollment,
                        x.Activity,
                        Exams = y
                    }).SelectMany(x => x.Exams.DefaultIfEmpty(), (y, z) => new { y.Enrollment, y.Activity, Exam = z })
                    .Where(x => x.Enrollment.ExpireDate != null && x.Enrollment.ExpireDate == date && (x.Activity == null || x.Activity.Progress < 100) || (x.Exam != null && x.Exam.EndDate != null && x.Exam.EndDate == date && (x.Activity == null || x.Activity.Progress < 100)))
                    .Select(x => new { x.Enrollment.TraineeId, x.Enrollment.CourseId, x.Enrollment.Course.Title, x.Enrollment.Trainee.PhoneNo }).ToListAsync();

                SMSSender smsSender = notiEvent?.SMS ?? false ? new SMSSender() : null;
                foreach (var item in data)
                {
                    notification = new Notification()
                    {
                        Id = Guid.NewGuid(),
                        CreatedOn = DateTime.UtcNow,
                        NotificationType = NotificationType.FailureToComplete,
                        TargetUserType = UserType.Trainee,
                        TargetTraineeId = item.TraineeId,
                        Title = "Failed to Complete",
                        Details = $"You have failed to complete the course \"{item.Title}\" in time. Contact the appropriate authority for further instructions.",
                        Payload = item.CourseId.ToString(),
                        NavigateTo = Navigation.CourseDetails
                    };

                    if (notiEvent.InApp)
                    {
                        _context.Notifications.Add(notification);
                        fcmNotification.Add(notification, await _context.TraineeDevices.Where(x => x.TraineeId == item.TraineeId).Select(x => x.Token).ToListAsync());
                    }
                    if (notiEvent.Email) _context.NotificationEmails.Add(new NotificationEmail { Id = Guid.NewGuid(), NotificationId = notification.Id });
                    if (notiEvent.SMS)
                    {
                        if (!string.IsNullOrEmpty(item.PhoneNo) && item.PhoneNo.Length >= 10) await smsSender.SendAsync(item.PhoneNo, notification.Details, Brac.LMS.Common.SMS.Enums.SMSEventType.FailedToCompleteReminder);
                    }
                }

                notiEvent.LastChecked = DateTime.Today;
                _context.Entry(notiEvent).State = EntityState.Modified;

                await _context.SaveChangesAsync();

                WriteLog($"Failed to complete the course notification sent: {data.Count}");

                foreach (var key in fcmNotification.Keys)
                {
                    await new FirebaseMessagingClient().SendNotifications(fcmNotification[key].ToArray(), key.Title, key.Details, new
                    {
                        NavigateTo = key.NavigateTo.ToString(),
                        key.Payload,
                        key.Id,
                        NotificationType = key.NotificationType.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                WriteLog("Check_FailureToComplete: " + ex.Message);
            }
        }

        private static async Task Check_OneWeekRemainCertificateExpiry()
        {
            Notification notification = null;
            Dictionary<Notification, List<string>> fcmNotification = new Dictionary<Notification, List<string>>();
            try
            {
                var notiEvent = await _context.NotificationEvents.FirstOrDefaultAsync(x => x.Type == NotificationType.OneWeekRemain);
                if (notiEvent == null || notiEvent.LastChecked == DateTime.Today) return;

                var date = DateTime.Today.AddDays(7);
                var data = await _context.TraineeCertificates.Where(x => !x.Expired && x.ExpiryDate != null && x.ExpiryDate == date && x.Course.Active && x.Course.Published).Select(x => new { x.Id, x.TraineeId, x.CourseId, x.Course.Title, x.Trainee.PhoneNo }).ToListAsync();

                SMSSender smsSender = notiEvent?.SMS ?? false ? new SMSSender() : null;
                foreach (var item in data)
                {
                    notification = new Notification()
                    {
                        Id = Guid.NewGuid(),
                        CreatedOn = DateTime.UtcNow,
                        NotificationType = NotificationType.OneWeekRemain,
                        TargetUserType = UserType.Trainee,
                        TargetTraineeId = item.TraineeId,
                        Title = "One week remains of a recurring certification",
                        Details = $"One week remains of a recurring certification of course \"{item.Title}\". After that, your certificate will be expired and you must re-study the course",
                        Payload = item.CourseId.ToString(),
                        NavigateTo = Navigation.CourseDetails
                    };

                    if (notiEvent.InApp)
                    {
                        _context.Notifications.Add(notification);
                        fcmNotification.Add(notification, await _context.TraineeDevices.Where(x => x.TraineeId == item.TraineeId).Select(x => x.Token).ToListAsync());
                    }
                    if (notiEvent.Email) _context.NotificationEmails.Add(new NotificationEmail { Id = Guid.NewGuid(), NotificationId = notification.Id });
                    if (notiEvent.SMS)
                    {
                        if (!string.IsNullOrEmpty(item.PhoneNo) && item.PhoneNo.Length >= 10) await smsSender.SendAsync(item.PhoneNo, notification.Details, Brac.LMS.Common.SMS.Enums.SMSEventType.OneWeekRemain);
                    }
                }

                notiEvent.LastChecked = DateTime.Today;
                _context.Entry(notiEvent).State = EntityState.Modified;

                await _context.SaveChangesAsync();

                WriteLog($"One week remains of a recurring certification notification sent: {data.Count}");

                foreach (var key in fcmNotification.Keys)
                {
                    await new FirebaseMessagingClient().SendNotifications(fcmNotification[key].ToArray(), key.Title, key.Details, new
                    {
                        NavigateTo = key.NavigateTo.ToString(),
                        key.Payload,
                        key.Id,
                        NotificationType = key.NotificationType.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                WriteLog("Check_OneWeekRemainCertificateExpiry: " + ex.Message);
            }
        }

        private static async Task Check_CertificateExpiry()
        {
            Notification notification = null;
            Dictionary<Notification, List<string>> fcmNotification = new Dictionary<Notification, List<string>>();
            try
            {
                var notiEvent = await _context.NotificationEvents.FirstOrDefaultAsync(x => x.Type == NotificationType.OneWeekRemain);
                if (notiEvent == null || notiEvent.LastChecked == DateTime.Today) return;

                var date = DateTime.Today.AddDays(-1);
                var data = await _context.TraineeCertificates.Where(x => !x.Expired && x.ExpiryDate != null && x.ExpiryDate == date && x.Course.Published && x.Course.Active).Select(x => new { x.Id, x.TraineeId, x.CourseId, x.Course.Title, x.Trainee.PhoneNo }).ToListAsync();

                SMSSender smsSender = notiEvent?.SMS ?? false ? new SMSSender() : null;
                foreach (var item in data)
                {
                    notification = new Notification()
                    {
                        Id = Guid.NewGuid(),
                        CreatedOn = DateTime.UtcNow,
                        NotificationType = NotificationType.OneWeekRemain,
                        TargetUserType = UserType.Trainee,
                        TargetTraineeId = item.TraineeId,
                        Title = "Certificate Expired",
                        Details = $"Your certificate on \"{item.Title}\" has been expired. You must re-study the course again",
                        Payload = item.CourseId.ToString(),
                        NavigateTo = Navigation.CourseDetails
                    };

                    if (notiEvent.InApp)
                    {
                        _context.Notifications.Add(notification);
                        fcmNotification.Add(notification, await _context.TraineeDevices.Where(x => x.TraineeId == item.TraineeId).Select(x => x.Token).ToListAsync());
                    }
                    if (notiEvent.Email) _context.NotificationEmails.Add(new NotificationEmail { Id = Guid.NewGuid(), NotificationId = notification.Id });
                    if (notiEvent.SMS)
                    {
                        if (!string.IsNullOrEmpty(item.PhoneNo) && item.PhoneNo.Length >= 10) await smsSender.SendAsync(item.PhoneNo, notification.Details, Brac.LMS.Common.SMS.Enums.SMSEventType.CertificateExpiryReminder);
                    }
                }

                notiEvent.LastChecked = DateTime.Today;
                _context.Entry(notiEvent).State = EntityState.Modified;

                await _context.SaveChangesAsync();

                WriteLog($"Certificate expired notification sent: {data.Count}");

                foreach (var key in fcmNotification.Keys)
                {
                    await new FirebaseMessagingClient().SendNotifications(fcmNotification[key].ToArray(), key.Title, key.Details, new
                    {
                        NavigateTo = key.NavigateTo.ToString(),
                        key.Payload,
                        key.Id,
                        NotificationType = key.NotificationType.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                WriteLog("Check_CertificateExpiry: " + ex.Message);
            }
        }

        private static async Task ExpireCertificates()
        {
            try
            {
                var date = DateTime.Today;
                var data = await _context.TraineeCertificates.Where(x => x.ExpiryDate != null && x.ExpiryDate < date && !x.Expired && x.Course.Active && x.Course.Published).ToListAsync();
                if (!data.Any()) return;

                List<TraineeMockTestAttempt> mockAttempts = null;
                List<TraineeExamAttempt> examAttempts = null;
                List<TraineeMockTest> traineeMockTests = null;
                List<TraineeExam> traineeExams = null;
                List<TraineeCourseActivity> activities = null;
                foreach (var item in data)
                {
                    mockAttempts = await _context.TraineeMockTestAttempts.Where(x => x.TraineeId == item.TraineeId && x.Exam.CourseId == item.CourseId).ToListAsync();
                    foreach (var mockAttempt in mockAttempts)
                    {
                        _context.Entry(mockAttempt).State = EntityState.Deleted;
                    }
                    examAttempts = await _context.TraineeExamAttempts.Where(x => x.TraineeId == item.TraineeId && x.Exam.CourseId == item.CourseId).ToListAsync();
                    foreach (var examAttempt in examAttempts)
                    {
                        _context.Entry(examAttempt).State = EntityState.Deleted;
                    }
                    traineeMockTests = await _context.TraineeMockTests.Where(x => x.TraineeId == item.TraineeId && x.Exam.CourseId == item.CourseId).ToListAsync();
                    foreach (var traineeMockTest in traineeMockTests)
                    {
                        traineeMockTest.MCQMockTestAnswers.Clear();
                        _context.Entry(traineeMockTest).State = EntityState.Deleted;
                    }
                    traineeExams = await _context.TraineeExams.Where(x => x.TraineeId == item.TraineeId && x.Exam.CourseId == item.CourseId).ToListAsync();
                    foreach (var traineeExam in traineeExams)
                    {
                        traineeExam.MCQAnswers.Clear();
                        traineeExam.TrueFalseAnswers.Clear();
                        traineeExam.FIGAnswers.Clear();
                        traineeExam.MatchingAnswers.Clear();
                        traineeExam.WrittenAnswers.Clear();
                        _context.Entry(traineeExam).State = EntityState.Deleted;
                    }
                    activities = await _context.TraineeCourseActivities.Where(x => x.TraineeId == item.TraineeId && x.CourseId == item.CourseId).ToListAsync();
                    foreach (var activity in activities)
                    {
                        activity.FirstStudyDate = null;
                        activity.LastStudyDate = null;
                        activity.NoOfContentsStudied = 0;
                        activity.Progress = 0;
                        activity.CertificateAchieved = false;

                        if (activity.MaterialStudies != null)
                            activity.MaterialStudies.Clear();
                        _context.Entry(activity).State = EntityState.Modified;
                    }
                    item.Expired = true;
                    _context.Entry(item).State = EntityState.Modified;
                }

                await _context.SaveChangesAsync();

                WriteLog($"Certificate expired : {data.Count}");
            }
            catch (Exception ex)
            {
                WriteLog("ExpireCertificates: " + ex.Message);
            }
        }

        private static async Task Check_PendingNotification()
        {
            Notification notification = null;
            NotificationEvent notiEvent = null;
            SMSSender smsSender = new SMSSender();
            List<List<string>> tokens = null;
            Dictionary<Notification, List<List<string>>> notificationTokens = new Dictionary<Notification, List<List<string>>>(); ;
            var fcmClient = new FirebaseMessagingClient();
            try
            {
                var data = await _context.PendingNotifications.ToListAsync();

                Guid refGuidId;
                long refLongId;
                foreach (var item in data)
                {
                    notiEvent = await _context.NotificationEvents.FirstOrDefaultAsync(x => x.Type == item.NotificationType);
                    if (notiEvent == null) return;

                    tokens = new List<List<string>> { new List<string>() };

                    Guid.TryParse(item.Payload, out refGuidId);
                    long.TryParse(item.Payload, out refLongId);

                    switch (notiEvent.Type)
                    {
                        case NotificationType.NewAvailableCourse:
                            var trainees = await _context.Trainees.Where(x => x.Active)
                                .GroupJoin(_context.CourseEnrollments, x => new { TraineeId = x.Id, CourseId = refGuidId }, y => new { y.TraineeId, y.CourseId }, (x, y) => new
                                {
                                    Trainee = x,
                                    Enrolments = y
                                }).Where(x => !x.Enrolments.Any())
                                .Select(x => new { x.Trainee.Id, x.Trainee.PhoneNo, Tokens = x.Trainee.Devices.Select(y => y.Token).ToList() }).ToListAsync();
                            foreach (var trainee in trainees)
                            {
                                notification = new Notification()
                                {
                                    Id = Guid.NewGuid(),
                                    CreatedOn = item.CreatedOn,
                                    NotificationType = item.NotificationType,
                                    TargetUserType = UserType.Trainee,
                                    TargetTraineeId = trainee.Id,
                                    Title = item.Title,
                                    Details = item.Details,
                                    Payload = item.Payload,
                                    NavigateTo = item.NavigateTo
                                };

                                if (notiEvent.InApp)
                                {
                                    _context.Notifications.Add(notification);
                                    if (!tokens.Any()) continue;
                                    if (tokens.Last().Count + trainee.Tokens.Count() <= 20) tokens.Last().AddRange(trainee.Tokens);
                                    else tokens.Add(trainee.Tokens);
                                }
                                if (notiEvent.Email) _context.NotificationEmails.Add(new NotificationEmail { Id = Guid.NewGuid(), NotificationId = notification.Id });
                                if (notiEvent.SMS)
                                {
                                    if (!string.IsNullOrEmpty(trainee.PhoneNo) && trainee.PhoneNo.Length >= 10) await smsSender.SendAsync(trainee.PhoneNo, notification.Details, Brac.LMS.Common.SMS.Enums.SMSEventType.PendingNotification);
                                }
                            }

                            _context.Entry(item).State = EntityState.Deleted;
                            await _context.SaveChangesAsync();

                            WriteLog($"New course available notification sent: {trainees.Count}");

                            foreach (var notiToken in tokens)
                            {
                                await fcmClient.SendNotifications(notiToken.ToArray(), notification.Title, notification.Details, new
                                {
                                    NavigateTo = notification.NavigateTo.ToString(),
                                    notification.Payload,
                                    notification.Id
                                });
                            }

                            break;
                        case NotificationType.ForumComment:

                            var commentTopic = await _context.ForumPosts.Where(x => x.Id == refLongId).Select(x => new { x.Topic.Id, x.Topic.CreatorId, x.CreatedDate }).FirstOrDefaultAsync();

                            var topicRelatedUserIds = await _context.ForumPosts.Where(x => x.TopicId == commentTopic.Id && x.CreatorId != commentTopic.CreatorId && x.CreatorId != item.SourceId && x.CreatedDate < commentTopic.CreatedDate).Select(x => x.CreatorId).Distinct().ToListAsync();

                            var topicRelatedTrainees = await _context.Trainees.Where(x => x.Active && topicRelatedUserIds.Contains(x.UserId))
                                .Select(x => new { x.Id, Tokens = x.Devices.Select(y => y.Token).ToList() }).ToListAsync();

                            foreach (var trainee in topicRelatedTrainees)
                            {
                                notification = new Notification()
                                {
                                    Id = Guid.NewGuid(),
                                    CreatedOn = item.CreatedOn,
                                    NotificationType = item.NotificationType,
                                    TargetUserType = UserType.Trainee,
                                    TargetTraineeId = trainee.Id,
                                    Title = item.Title,
                                    Details = item.Details,
                                    NavigateTo = Navigation.ForumComment,
                                    Payload = JsonConvert.SerializeObject(new
                                    {
                                        TopicId = commentTopic.Id,
                                        PostId = refLongId
                                    })
                                };

                                _context.Notifications.Add(notification);
                                if (!tokens.Any()) continue;

                                if (tokens.Last().Count + trainee.Tokens.Count() <= 20) tokens.Last().AddRange(trainee.Tokens);
                                else tokens.Add(trainee.Tokens);
                            }

                            _context.Entry(item).State = EntityState.Deleted;
                            await _context.SaveChangesAsync();

                            WriteLog($"New comment on post notification sent: {topicRelatedTrainees.Count}");

                            foreach (var notiToken in tokens)
                            {
                                if (notiToken.Any())
                                    await fcmClient.SendNotifications(notiToken.ToArray(), notification.Title, notification.Details, new
                                    {
                                        NavigateTo = notification.NavigateTo.ToString(),
                                        notification.Payload,
                                        notification.Id
                                    });
                            }

                            break;
                        case NotificationType.ForumReply:

                            var replyPost = await _context.ForumPosts.Where(x => x.Id == refLongId).Select(x => new { x.ParentPost.Id, x.ParentPost.CreatorId, x.TopicId, x.CreatedDate }).FirstOrDefaultAsync();

                            var replyRelatedUserIds = await _context.ForumPosts.Where(x => x.ParentPostId == replyPost.Id && x.CreatorId != replyPost.CreatorId && x.CreatorId != item.SourceId && x.CreatedDate < replyPost.CreatedDate).Select(x => x.CreatorId).Distinct().ToListAsync();

                            var replyRelatedTrainees = await _context.Trainees.Where(x => x.Active && replyRelatedUserIds.Contains(x.UserId))
                                .Select(x => new { x.Id, Tokens = x.Devices.Select(y => y.Token).ToList() }).ToListAsync();

                            foreach (var trainee in replyRelatedTrainees)
                            {
                                notification = new Notification()
                                {
                                    Id = Guid.NewGuid(),
                                    CreatedOn = item.CreatedOn,
                                    NotificationType = item.NotificationType,
                                    TargetUserType = UserType.Trainee,
                                    TargetTraineeId = trainee.Id,
                                    Title = item.Title,
                                    Details = item.Details,
                                    Payload = JsonConvert.SerializeObject(new
                                    {
                                        TopicId = replyPost.TopicId,
                                        PostId = replyPost.Id,
                                        ReplyId = refLongId
                                    }),
                                    NavigateTo = item.NavigateTo,
                                };

                                _context.Notifications.Add(notification);
                                if (!tokens.Any()) continue;

                                if (tokens.Last().Count + trainee.Tokens.Count() <= 20) tokens.Last().AddRange(trainee.Tokens);
                                else tokens.Add(trainee.Tokens);
                            }

                            _context.Entry(item).State = EntityState.Deleted;
                            await _context.SaveChangesAsync();

                            WriteLog($"New reply to a comment notification sent: {replyRelatedTrainees.Count}");

                            foreach (var notiToken in tokens)
                            {
                                await fcmClient.SendNotifications(notiToken.ToArray(), notification.Title, notification.Details, new
                                {
                                    NavigateTo = notification.NavigateTo.ToString(),
                                    notification.Payload,
                                    notification.Id
                                });
                            }

                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog("Check_PendingNotification: " + ex.Message);
            }
        }

        private static async Task DeleteOldNotification()
        {
            try
            {
                var notiEvent = await _context.NotificationEvents.FirstOrDefaultAsync(x => x.Type == NotificationType.DeleteOldNotification);
                if (notiEvent == null || notiEvent.LastChecked == DateTime.Today) return;

                var dayPrevMonth = DateTime.Today.AddMonths(-1);
                var data = await _context.Notifications.Where(x => x.CreatedOn < dayPrevMonth).ToListAsync();

                foreach (var notification in data)
                {
                    _context.Entry(notification).State = EntityState.Deleted;
                }

                notiEvent.LastChecked = DateTime.Today;
                _context.Entry(notiEvent).State = EntityState.Modified;

                await _context.SaveChangesAsync();

                WriteLog($"Notifications deleted before {dayPrevMonth.ToString("dd MMM, yyyy")}: {data.Count}");
            }
            catch (Exception ex)
            {
                WriteLog("DeleteOldNotification: " + ex.Message);
            }
        }
        private static async Task<bool> DeleteNotifications(List<Guid> traineeCourseActivities=null)
        {
            try
            {
               var courses= await _context.Courses.Where(x=>!x.Active || !x.Published).Select(x=>x.Id.ToString()).ToListAsync();
               var notifications= await _context.Notifications.Where(x=>courses.Contains(x.Payload)).ToListAsync();
                if (notifications.Any())
                {
                    _context.Notifications.RemoveRange(notifications);
                    _context.SaveChanges();
                    return true;
                }
                else
                    return false;
            }
            catch (Exception ex)
            {

                return false;
            }
        }
        public static async Task DeleteNoProgrss30()
        {
            var courseIds = _context.Courses.Where(x => x.Published == false).Select(s => s.Id.ToString()).ToList();
            var noProgressNotifications = _context.Notifications.Where(x => x.NotificationType == NotificationType.NoProgress30 && courseIds.Contains(x.Payload)).AsQueryable();
            _context.Notifications.RemoveRange(noProgressNotifications);
            await _context.SaveChangesAsync();
        }
        private static void WriteLog(string msg)
        {
            System.IO.StreamWriter file = null;
            try
            {
                if (msg == null) return;
                Console.WriteLine(DateTime.Now.ToString("dd MMM yy hh:mm:ss tt") + "\t" + msg);
                file = new System.IO.StreamWriter(_logPath + DateTime.Now.ToString("ddMMMyyyy") + ".txt", true);
                file.WriteLine("\n" + DateTime.Now.ToString("dd MMM yy hh:mm:ss tt") + "\t" + msg);
                file.Flush();

            }
            catch (Exception)
            {

            }
            finally
            {
                if (file != null)
                {
                    try
                    {
                        file.Close();
                        file.Dispose();
                    }
                    catch { }

                }
            }
        }

        public static bool IsProcessRunning()
        {
            string name = Process.GetCurrentProcess().ProcessName;
            int id = Process.GetCurrentProcess().Id;
            foreach (Process clsProcess in Process.GetProcesses())
            {
                if (clsProcess.ProcessName.Contains(name) && clsProcess.Id != id)
                {
                    Console.WriteLine(clsProcess.MainModule.FileName);
                    return true;
                }
            }
            return false;
        }
        
    }
}
