﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Microsoft.AspNet.Identity.Owin;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using WebApiThrottle;

namespace Brac.LMS.App.API.Controllers
{
    [RoutePrefix("api/forum")]
    public class ForumController : ApplicationController
    {
        private readonly IForumService _service;

        public ForumController()
        {
            _service = new ForumService();
        }
        public ApplicationUserManager UserManager
        {
            get
            {
                return Request.GetOwinContext().GetUserManager<ApplicationUserManager>();
            }
        }


        [Authorize(Roles = "Trainee"), EnableThrottling(PerDay = 50)]

        [HttpPost, Route("topic/create-or-update")]
        public async Task<IHttpActionResult> TopicCreateOrUpdate(ForumTopicModel model)
        {
            return Ok(await _service.TopicCreateOrUpdate(model, CurrentUser));
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("get-topics")]
        public async Task<IHttpActionResult> GetMyTopics(string text, int size, int pageNumber)
        {
            return Ok(await _service.GetTopics(text, size, pageNumber, CurrentUser));
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("get-my-topic-titles/{limit}")]
        public async Task<IHttpActionResult> GetMyTopicTitles(int limit)
        {
            return Ok(await _service.GetMyTopicTitles(limit, CurrentUser));
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("{id}/get-my-topic-titles/{limit}")]
        public async Task<IHttpActionResult> GetMyTopicTitles(int limit, Guid id)
        {
            return Ok(await _service.GetMyTopicTitles(limit, CurrentUser, id));
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("get-my-topics")]
        public async Task<IHttpActionResult> GetMyTopics(int size, int pageNumber)
        {
            return Ok(await _service.GetMyTopics(size, pageNumber, CurrentUser));
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("get-popular-topic-titles/{limit}")]
        public async Task<IHttpActionResult> GetPopularTopicTitles(int limit)
        {
            return Ok(await _service.GetPopularTopicTitles(limit, CurrentUser));
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("get-popular-topics")]
        public async Task<IHttpActionResult> GetPopularTopics(int size, int pageNumber)
        {
            return Ok(await _service.GetPopularTopics(size, pageNumber, CurrentUser));
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("get-my-popular-topic-titles/{limit}")]
        public async Task<IHttpActionResult> GetMyPopularTopicTitles(int limit)
        {
            return Ok(await _service.GetMyPopularTopicTitles(limit, CurrentUser));
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("{id}/get-my-popular-topic-titles/{limit}")]
        public async Task<IHttpActionResult> GetMyPopularTopicTitles(int limit, Guid id)
        {
            return Ok(await _service.GetMyPopularTopicTitles(limit, CurrentUser, id));
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("get-my-popular-topics")]
        public async Task<IHttpActionResult> GetMyPopularTopics(int size, int pageNumber)
        {
            return Ok(await _service.GetMyPopularTopics(size, pageNumber, CurrentUser));
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("get-topic/{id}")]
        public async Task<IHttpActionResult> GetTopicById(Guid id)
        {
            return Ok(await _service.GetTopicById(id));
        }

        [Authorize(Roles = "Trainee,Admin"), HttpGet, Route("get-topic-details/{id}")]
        public async Task<IHttpActionResult> GetTopicDetails(Guid id)
        {
            return Ok(await _service.GetTopicDetails(id));
        }

        [Authorize(Roles = "Trainee,Admin"), HttpGet, Route("topic/get-posts")]
        public async Task<IHttpActionResult> GetTopicPosts(Guid id, int size, int pageNumber)
        {
            return Ok(await _service.GetTopicPosts(id, size, pageNumber, CurrentUser));
        }

        [Authorize(Roles = "Trainee"), HttpPost, Route("post/save-or-update")]
        public async Task<IHttpActionResult> PostSaveOrUpdate(ForumPostModel model)
        {
            var _nservice = new ForumService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.PostSaveOrUpdate(model, CurrentUser));
        }

        [Authorize(Roles = "Trainee"), HttpPost, Route("reply/save-or-update")]
        public async Task<IHttpActionResult> ReplySaveOrUpdate(ForumReplyModel model)
        {
            var _nservice = new ForumService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.ReplySaveOrUpdate(model, CurrentUser));
        }

        [Authorize(Roles = "Trainee"), HttpPost, Route("post/{id}/like-or-unlike")]
        public async Task<IHttpActionResult> PostLikeOrUnlike(long id)
        {
            var _nservice = new ForumService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.PostLikeOrUnlike(id, CurrentUser));
        }

        [Authorize(Roles = "Trainee"), HttpPost, Route("my-favorite-categories/save-or-modify")]
        public async Task<IHttpActionResult> AddOrModifyMyFavoriteCategories(List<long> categoryIds)
        {
            var _nservice = new ForumService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.AddOrModifyMyFavoriteCategories(categoryIds, CurrentUser));
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("my-favorite-categories/get")]
        public async Task<IHttpActionResult> GetMyFavoriteCategories()
        {
            return Ok(await _service.GetMyFavoriteCategories(CurrentUser));
        }

        #region Admin Panel APIs

        [Authorize(Roles = "Admin"), HttpGet, Route("topic/list")]
        public async Task<IHttpActionResult> GetTopicList(string text, string categoryIds, ForumTopicStatus? status, int size, int pageNumber)
        {
            return Ok(await _service.GetTopicList(text, categoryIds, status, size, pageNumber));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("topic/approve/{id}")]
        public async Task<IHttpActionResult> ApproveTopic(Guid id)
        {
            return Ok(await _service.ChangeTopicStatus(id, ForumTopicStatus.Open, CurrentUser));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("topic/close/{id}")]
        public async Task<IHttpActionResult> CloseTopic(Guid id)
        {
            return Ok(await _service.ChangeTopicStatus(id, ForumTopicStatus.Closed, CurrentUser));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("topic/delete/{id}")]
        public async Task<IHttpActionResult> DeleteTopic(Guid id)
        {
            return Ok(await _service.DeleteTopic(id));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("comment-or-reply/delete/{id}")]
        public async Task<IHttpActionResult> DeleteCommentOrReply(long id)
        {
            return Ok(await _service.DeleteCommentOrReply(id));
        }
        #endregion
    }
}
