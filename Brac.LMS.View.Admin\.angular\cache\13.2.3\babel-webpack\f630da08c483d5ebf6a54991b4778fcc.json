{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../theme/shared/components/card/card.component\";\nexport let SamplePageComponent = /*#__PURE__*/(() => {\n  class SamplePageComponent {\n    constructor() {}\n\n    ngOnInit() {}\n\n  }\n\n  SamplePageComponent.ɵfac = function SamplePageComponent_Factory(t) {\n    return new (t || SamplePageComponent)();\n  };\n\n  SamplePageComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SamplePageComponent,\n    selectors: [[\"app-sample-page\"]],\n    decls: 5,\n    vars: 0,\n    consts: [[1, \"row\"], [1, \"col-sm-12\"], [\"cardTitle\", \"Hello Card\"]],\n    template: function SamplePageComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵelementStart(2, \"app-card\", 2);\n        i0.ɵɵelementStart(3, \"p\");\n        i0.ɵɵtext(4, \"\\\"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\\\"\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n      }\n    },\n    directives: [i1.CardComponent],\n    styles: [\"\"]\n  });\n  return SamplePageComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}