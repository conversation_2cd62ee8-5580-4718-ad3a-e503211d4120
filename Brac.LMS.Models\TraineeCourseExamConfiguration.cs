﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class TraineeCourseExamConfiguration : AuditableEntity
    {
        public Guid TraineeId { get; set; }
        public virtual Trainee Trainee { get; set; }


        public Guid ExamId { get; set; }
        public virtual CourseExam CourseExam { get; set; }

        public int Quota { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }

    }
}
