﻿using Brac.LMS.App.ViewModels;
using Brac.LMS.DB;
using Brac.LMS.Models;
using System;
using System.Data.Entity;
using System.Linq;
using System.Security.Principal;
using System.Threading.Tasks;
using System.Transactions;

namespace Brac.LMS.App.Services
{
    public class UserGroupService : IUserGroupService
    {
        private readonly ApplicationDbContext _context;
        public UserGroupService()
        {
            _context = new ApplicationDbContext();
        }

        public async Task<APIResponse> UserGroupCreateOrUpdate(UserGroupModel model, IIdentity identity, ApplicationRoleManager roleManager)
        {
            bool isEdit = true;
            try
            {
                if (await _context.UserGroups.AnyAsync(x => x.Id != model.Id && x.Name == model.Name))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Already exists: " + model.Name
                    };


                UserGroup item;
                if (model.Id.HasValue && model.Id != 0)
                {
                    item = await _context.UserGroups.FindAsync(model.Id);
                    if (item == null) return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "User group not found"
                    };
                }
                else
                {
                    item = new UserGroup();
                    isEdit = false;
                }

                item.Name = model.Name;
                item.Description = model.Description;
                item.NoOfRoles = model.Roles.Count;

                var roles = await roleManager.Roles.Where(x => model.Roles.Contains(x.Name)).Select(x => x.Id).ToListAsync();
                if (item.Roles != null)
                    item.Roles.Clear();
                item.Roles = roles.Select(x => new UserGroupRole { RoleId = x }).ToList();
                item.SetAuditTrailEntity(identity);

                using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    try
                    {
                        if (!isEdit)
                        {
                            _context.UserGroups.Add(item);
                        }
                        else
                        {
                            _context.Entry(item).State = EntityState.Modified;
                        }
                        await _context.SaveChangesAsync();
                        scope.Complete();
                    }
                    catch (Exception ex)
                    {
                        scope.Dispose();
                        return new APIResponse
                        {
                            Status = ResponseStatus.Error,
                            Message = ex.Message
                        };

                    }

                }
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Message = "Successfully " + (isEdit ? "Updated" : "Saved"),
                        item.Id
                    }
                };

            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }

        }

        public async Task<APIResponse> GetUserGroupList(int size, int pageNumber)
        {
            try
            {
                var data = await _context.UserGroups.OrderByDescending(x => x.Name)
                    .Select(x => new { x.Id, x.Name, x.Description, x.NoOfRoles, Roles = x.Roles.Select(y => y.Role.NormalizeName).ToList() }).Skip(pageNumber * size).Take(size).ToListAsync();
                var count = await _context.UserGroups.CountAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Records = data,
                        Total = count
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }

        }

        public async Task<APIResponse> GetUserGroupById(long id)
        {
            try
            {
                var data = await _context.UserGroups.Where(t => t.Id == id)
                .Select(t => new
                {
                    t.Id,
                    t.Name,
                    t.Description,
                    Roles = t.Roles.Select(x => x.Role.Name).ToList()
                }).FirstOrDefaultAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Record = data }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetUserGroupDropDownList()
        {
            try
            {
                var data = await _context.UserGroups.OrderBy(o => o.Name)
                .Select(t => new
                {
                    t.Id,
                    t.Name
                }).ToListAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetAllIdentityRoles(string currentUserId, ApplicationUserManager userManager, ApplicationRoleManager roleManager)
        {
            try
            {
                var userRoles = await userManager.GetRolesAsync(currentUserId);
                var data = await roleManager.Roles.Where(x => userRoles.Contains(x.Name) && !x.Restricted).OrderBy(o => o.NormalizeName)
                .Select(t => new { Id = t.Name, Name = t.NormalizeName }).OrderBy(x => x.Name).ToListAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetIdentityRolesByGroupId(long id)
        {
            try
            {
                var data = await _context.UserGroups.Where(w => w.Id == id)
                .SelectMany(t => t.Roles.Select(x => new { Id = x.Role.Name, Name = x.Role.NormalizeName })).OrderBy(x => x.Id).ToListAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }
    }

    public interface IUserGroupService
    {
        Task<APIResponse> UserGroupCreateOrUpdate(UserGroupModel model, IIdentity identity, ApplicationRoleManager roleManager);
        Task<APIResponse> GetUserGroupList(int size, int pageNumber);
        Task<APIResponse> GetUserGroupById(long id);
        Task<APIResponse> GetUserGroupDropDownList();
        Task<APIResponse> GetAllIdentityRoles(string currentUserId, ApplicationUserManager userManager, ApplicationRoleManager roleManager);
        Task<APIResponse> GetIdentityRolesByGroupId(long id);
    }
}
