﻿using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using Microsoft.AspNet.Identity.Owin;
using Microsoft.Owin.Security;
using Brac.LMS.DB;
using Brac.LMS.ViewModels;
using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using System.Security.Claims;
using Microsoft.Owin.Security.OAuth;
using System;
using Brac.LMS.Models;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using System.Data.Entity;
using System.Linq;
using System.Net.Http.Headers;
using Microsoft.Owin.Security.Cookies;
using Microsoft.Owin;
using Newtonsoft.Json;
using Microsoft.AspNet.Identity.EntityFramework;
using Dapper;
using System.Security.Cryptography;
using Microsoft.AspNet.Identity;
using Brac.LMS.Common;
using System.Net;
using System.Web.Http.Cors;
using System.Data.Entity.Validation;
using Microsoft.Graph.Models;

namespace Brac.LMS.App.API.Controllers

{

    [RoutePrefix("api/account")]

    public class AccountController : ApplicationController
    {
        private ApplicationUserManager _userManager;
        private readonly IAccountService _service;
        private readonly ApplicationDbContext _context;

        private readonly AuditLogHelper _auditLogHelper;
        private readonly AuditLog audit;
        private  ApplicationUser _appUser;
        //public CourseService()
        //{
        //    _context = new ApplicationDbContext();
        //    _auditLogHelper = new AuditLogHelper();
        //    audit = _auditLogHelper.auditLog;
        //}
        //public AccountController(string controller, string method, ApplicationUser applicationUser)
        //{
        //    _auditLogHelper = new AuditLogHelper(controller, method);
        //    audit = _auditLogHelper.auditLog;
        //    _appUser = applicationUser;
        //    audit.CreatedBy = _appUser.Id;
        //    _context = new ApplicationDbContext();
        //}




        public AccountController()
        {
            _service = new AccountService();
            _context = new ApplicationDbContext();
            _auditLogHelper = new AuditLogHelper();
            audit = _auditLogHelper.auditLog;
        }

        public ApplicationUserManager UserManager
        {
            get
            {
                return _userManager ?? Request.GetOwinContext().GetUserManager<ApplicationUserManager>();
            }
            private set
            {
                _userManager = value;
            }
        }
        public ApplicationRoleManager RoleManager => Request.GetOwinContext().Get<ApplicationRoleManager>();




        [AllowAnonymous, HttpGet, Route("get-trainee-profile")]
        public IHttpActionResult GetTraineeProfile()
        {
            return Ok(CurrentUser != null && User.IsInRole("Trainee") || User.IsInRole("Guest") ? new
            {
                FullName = CurrentUser.FirstName + (!string.IsNullOrEmpty(CurrentUser.LastName) ? " " + CurrentUser.LastName : ""),
                Position = CurrentUser.Trainee != null ? CurrentUser.Trainee.Position : "",
                CurrentUser.Gender,
                CurrentUser.ImagePath,
                CurrentUser.Id,
                CurrentUser.UserName,
                Roles = CurrentUser.UserRoles
            } : null);
        }

        [AllowAnonymous, HttpGet, Route("get-admin-profile")]
        public IHttpActionResult GetAdminProfile()
        {

            return Ok(CurrentUser != null && User.IsInRole("Admin") ? new
            {
                FullName = CurrentUser.FirstName + (!string.IsNullOrEmpty(CurrentUser.LastName) ? " " + CurrentUser.LastName : ""),
                Position = CurrentUser.Trainee != null ? CurrentUser.Trainee.Position : "",
                CurrentUser.Gender,
                CurrentUser.ImagePath,
                CurrentUser.Id,
                Roles = CurrentUser.UserRoles
            } : null);
        }

        [Authorize(Roles = "Admin, Trainee"), HttpGet, Route("get-user-list")]
        public async Task<IHttpActionResult> GetUserList(string text, int size, int pageNumber)
        {
            return Ok(await _service.GetUserList(text, size, pageNumber, User.Identity));
        }


        [Authorize(Roles = "Admin, Trainee"), HttpGet, Route("get-user/{id}")]
        public async Task<IHttpActionResult> GetUserById(string id)
        {
            return Ok(await _service.GetUserById(id, UserManager));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-admin-user/{id}")]
        public async Task<IHttpActionResult> GetAdminUserById(string id)
        {
            return Ok(await _service.GetAdminUserById(id, UserManager));
        }


        [Authorize(Roles = "Admin, Trainee"), HttpGet, Route("reset-password/{id}")]
        public async Task<IHttpActionResult> ResetPassword(string id)
        {
            return Ok(await _service.ResetPassword(id, UserManager));
        }


        [AllowAnonymous, HttpGet, Route("forget-password")]
        public async Task<IHttpActionResult> ForgetPassword(string email)
        {
            return Ok(await _service.ForgetPassword(email, UserManager));
        }


        [AllowAnonymous, HttpPost, Route("set-new-password")]
        public async Task<IHttpActionResult> SetNewPassword(ForgetPasswordModel model)
        {
            return Ok(await _service.SetNewPassword(model, UserManager));
        }


        [Authorize(Roles = "Admin, Trainee"), HttpPost, Route("create-or-update-user")]
        public async Task<IHttpActionResult> CreateOrUpdateUser(AccountModel model)
        {
            return Ok(await _service.CreateOrUpdateUser(model, UserManager));
        }


        [Authorize(Roles = "Admin"), HttpPost, Route("create-or-update-admin-user")]
        public async Task<IHttpActionResult> CreateOrUpdateAdminUser(AdminAccountModel model)
        {
            return Ok(await _service.CreateOrUpdateAdminUser(model, UserManager));
        }


        [Authorize(Roles = "Admin"), HttpPost, Route("change-password")]
        public async Task<IHttpActionResult> ChangePassword(PasswordChangeModel model)
        {
            return Ok(await _service.ChangePassword(model, UserManager, User.Identity));
        }


        [Authorize(Roles = "Admin, Trainee"), HttpPost, Route("update-profile")]
        public async Task<IHttpActionResult> UpdateProfile(UserProfileModel model)
        {
            return Ok(await _service.UpdateProfile(model, User.Identity, UserManager));
        }


        [Authorize(Roles = "Admin, Trainee, Guest"), HttpPost, Route("update-photo")]
        public async Task<IHttpActionResult> UpdatePhoto()
        {
            return Ok(await _service.UpdatePhoto(User.Identity, UserManager));
        }


        [Authorize(Roles = "Admin, Trainee"), HttpGet, Route("get-profile")]
        public async Task<IHttpActionResult> GetProfile()
        {
            return Ok(await _service.GetProfile(User.Identity, UserManager));
        }


        [AllowAnonymous, HttpPost, Route("create-role")]
        public async Task<IHttpActionResult> CreateRole(string name, string normalizeName, bool restricted)
        {
            return Ok(await _service.CreateRole(name, normalizeName, restricted, RoleManager));
        }

        [AllowAnonymous, HttpPost, Route("external-login")]
        public async Task<IHttpActionResult> ExternalLogin(LoginModel model)
        {
            audit.Controller = this.ControllerContext.ControllerDescriptor.ControllerName;
            audit.Method = this.ActionContext.ActionDescriptor.ActionName;
            try
            {
                model.Password = Uri.UnescapeDataString(model.Password);
                ApplicationUser user = await UserManager.FindByNameAsync(model.Username);
                if (user == null)
                {
                    var traineeUserId = _context.Trainees.FirstOrDefault(x => x.PIN == model.Username)?.UserId;
                    user = await UserManager.FindByIdAsync(traineeUserId);
                }
                bool passwordVarified = false;
                if (UserManager.PasswordHasher.VerifyHashedPassword(user.PasswordHash, model.Password) == PasswordVerificationResult.Success)
                    passwordVarified = true;
                if (user.PasswordHash == Cipher.EncryptSHA256Str(model.Password))
                    passwordVarified = true;
                if (user != null && await UserManager.IsLockedOutAsync(user.Id))
                {
                    throw new Exception($"You account has been locked out. Please try after {GetTimeDifference(DateTime.UtcNow.ToKindLocal(), user.LockoutEndDateUtc.ToKindLocal().Value)} or contact to System Administrator");
                }

                if (user == null || user.LoginType == LoginType.External)
                {
                    var pin = model.LoginType == LoginTypeModel.Default ?
                        VerifyHRMUser(model.Username, model.Password) : VerifyADUser(model.Username, model.Password);

                    if (pin == null)
                    {
                        if (user != null) await UserManager.AccessFailedAsync(user.Id);
                        throw new Exception("The user name or password is incorrect.");
                    }

                    user = await UserManager.FindByNameAsync(pin);
                    if (user == null)
                    {
                        throw new Exception("No information found about you on this system. Please contact with System Administrator.");
                    }
                }
                //else if (UserManager.PasswordHasher.VerifyHashedPassword(user.PasswordHash, model.Password) != PasswordVerificationResult.Success)
                else if (!passwordVarified)
                {
                    await UserManager.AccessFailedAsync(user.Id);
                    throw new Exception("The user name or password is incorrect.");
                }

                if (!await UserManager.IsInRoleAsync(user.Id, "Trainee") && !await UserManager.IsInRoleAsync(user.Id, "Guest"))
                {
                    await UserManager.AccessFailedAsync(user.Id);
                    throw new Exception("The user name or password is incorrect");
                }

                if (!user.Active)
                {
                    await UserManager.AccessFailedAsync(user.Id);
                    throw new Exception("This user has been de-activated by the System Administrator. Please contact with the System Administrator");
                }

                await UserManager.ResetAccessFailedCountAsync(user.Id);

                var dbContext = Request.GetOwinContext().Get<ApplicationDbContext>();
                var userGroupName = "";

                if (user.UserGroupId.HasValue)
                {
                    userGroupName = await dbContext.UserGroups.Where(x => x.Id == user.UserGroupId.Value).Select(x => x.Name).FirstOrDefaultAsync();
                }

                user.LastLogOn = DateTime.UtcNow.ToKindLocal();
                await UserManager.UpdateAsync(user);

                var position = await dbContext.Trainees.Where(x => x.UserId == user.Id).Select(x => x.Position).FirstOrDefaultAsync();

                ClaimsIdentity oAuthIdentity = await user.GenerateUserIdentityAsync(UserManager,
                   OAuthDefaults.AuthenticationType);

                var roles = await UserManager.GetRolesAsync(user.Id);

                AuthenticationProperties properties = CreateProperties(user, roles, userGroupName, position);
                AuthenticationTicket ticket = new AuthenticationTicket(oAuthIdentity, properties);

                properties.IssuedUtc = DateTime.UtcNow;
                properties.ExpiresUtc = DateTime.UtcNow.AddYears(1);
                AuthenticationTicket ticket2 = new AuthenticationTicket(oAuthIdentity, properties);
                var accessToken = Startup.OAuthOptions.AccessTokenFormat.Protect(ticket2);

                audit.CreatedBy = user.Id;
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);
                return Ok(new APIResponse
                {
                    Status = ViewModels.ResponseStatus.Success,
                    Message = "Successfully logged in",
                    Data = new
                    {
                        access_token = accessToken,
                        token_type = "Bearer",
                        issued = ticket.Properties.IssuedUtc.ToString(),
                        expires = ticket.Properties.ExpiresUtc.ToString(),
                        //user.Id,
                        //user.UserName,
                        user.Email,
                        Name = user.FirstName,
                        PIN= user.UserName,
                        Position = position,
                        user.ImagePath,
                        Gender = user.Gender.ToString(),
                        Roles = roles
                    }
                });
            }
            catch (Exception ex)
            {
                return Ok(new APIResponse
                {
                    Status = ViewModels.ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                });
            }
        }

        [AllowAnonymous, HttpPost, Route("trainee/login")]
        public async Task<HttpResponseMessage> TraineeLogin(LoginModel model)
        {
            audit.Controller = this.ControllerContext.ControllerDescriptor.ControllerName;
            audit.Method = this.ActionContext.ActionDescriptor.ActionName;
            var resp = new HttpResponseMessage();
            try
            {
                model.Password = Uri.UnescapeDataString(model.Password);
                bool passwordVarified = false;
                ApplicationUser user = await UserManager.FindByNameAsync(model.Username);
                _appUser = user;

                if (user == null)
                {
                    var traineeUserId = _context.Trainees.FirstOrDefault(x => x.PIN == model.Username)?.UserId;
                    user = await UserManager.FindByIdAsync(traineeUserId);
                    if (user == null) throw new Exception("No information found about you on this system. Please contact with System Administrator.");
                }
                if (!user.Active)
                {
                    throw new Exception("Sorry! Your account has been expired or deactivated.");
                }
                if (await UserManager.IsLockedOutAsync(user.Id))
                {
                    throw new Exception($"You account has been locked out. Please try after {GetTimeDifference(DateTime.UtcNow.ToKindLocal(), user.LockoutEndDateUtc.ToKindLocal().Value)} or contact to System Administrator");
                }

                if (UserManager.PasswordHasher.VerifyHashedPassword(user.PasswordHash, model.Password) == PasswordVerificationResult.Success)
                    passwordVarified = true;
                if(user.PasswordHash== Cipher.EncryptSHA256Str(model.Password))
                    passwordVarified = true;
                if (user.LoginType == LoginType.External)
                {
                    var pin = model.LoginType == LoginTypeModel.Default ?
                        VerifyHRMUser(model.Username, model.Password) : VerifyADUser(model.Username, model.Password);

                    if (pin == null)
                    {
                        await UserManager.AccessFailedAsync(user.Id);
                        throw new Exception("The user name or password is incorrect.");
                    }

                    user = await UserManager.Users.FirstOrDefaultAsync(x => x.UserName == pin);
                    if (user == null)
                    {
                        throw new Exception("No information found about you on this system. Please contact with System Administrator.");
                    }
                }
                else if (user.LoginType == LoginType.Regular && !passwordVarified)
                {
                    await UserManager.AccessFailedAsync(user.Id);
                    throw new Exception("The password is incorrect for regular login.");
                }

                else if (!passwordVarified)
                {
                    await UserManager.AccessFailedAsync(user.Id);
                    throw new Exception("The password is incorrect.");
                }

                if (user.LastPasswordChanged.AddMonths(3) < DateTime.UtcNow.ToKindLocal() )
                {
                    throw new Exception("Your password is expired. Please change password using Forgot Password menu.");
                }

                bool trainee_or_guest_or_admin = await UserManager.IsInRoleAsync(user.Id, "Trainee") || await UserManager.IsInRoleAsync(user.Id, "Admin") || await UserManager.IsInRoleAsync(user.Id, "Guest");
                //bool not_trainee_admin = !await UserManager.IsInRoleAsync(user.Id, "Trainee") && !await UserManager.IsInRoleAsync(user.Id, "Admin");


                if (!trainee_or_guest_or_admin)
                {
                    await UserManager.AccessFailedAsync(user.Id);
                    throw new Exception("The user is incorrect");
                }

                if (!user.Active)
                {
                    await UserManager.AccessFailedAsync(user.Id);
                    throw new Exception("This user has been de-activated by the System Administrator. Please contact with the System Administrator");
                }

                await UserManager.ResetAccessFailedCountAsync(user.Id);

                ClaimsIdentity cookiesIdentity = await user.GenerateUserIdentityAsync(UserManager,
                    CookieAuthenticationDefaults.AuthenticationType);

                var roles = await UserManager.GetRolesAsync(user.Id);

                AuthenticationProperties properties = new AuthenticationProperties
                {
                    IssuedUtc = DateTime.UtcNow,
                    ExpiresUtc = DateTime.UtcNow.AddHours(3),
                    IsPersistent = true
                };
                AuthenticationTicket ticket = new AuthenticationTicket(cookiesIdentity, properties);
                var accessToken = Startup.OAuthOptions.AccessTokenFormat.Protect(ticket);

                var principal = new ClaimsPrincipal(cookiesIdentity);
                HttpContext.Current.GetOwinContext().Authentication.SignIn(properties, cookiesIdentity);

                var cookie = new CookieHeaderValue(".Brac.LMS.Cookie", accessToken);
                cookie.Expires = ticket.Properties.ExpiresUtc;
                cookie.Domain = Request.RequestUri.Host;
                cookie.HttpOnly = true;
                cookie.Secure = true;
                cookie.Path = "/";
                resp.StatusCode = System.Net.HttpStatusCode.OK;
                resp.Headers.AddCookies(new CookieHeaderValue[] { cookie });
                resp.Content = new StringContent(JsonConvert.SerializeObject(new { Expires = cookie.Expires.Value.DateTime }));

                user.LastLogOn = DateTime.UtcNow.ToKindLocal();
                await UserManager.UpdateAsync(user);

                audit.CreatedBy = user.Id;
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);
            }
            catch (Exception ex)
            {
                resp.StatusCode = System.Net.HttpStatusCode.BadRequest;
                resp.Content = new StringContent(ex.Message);
                audit.CreatedBy = _appUser!=null? _appUser.Id:"Failed Login User";
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
            }
            return resp;
        }
        [AllowAnonymous, HttpPost, Route("ghoori-trainee/login")]
        public async Task<HttpResponseMessage> GhooriTraineeLogin(LoginModel model)
        {
            audit.Controller = this.ControllerContext.ControllerDescriptor.ControllerName;
            audit.Method = this.ActionContext.ActionDescriptor.ActionName;
            var resp = new HttpResponseMessage();
            try
            {
                model.Password = Uri.UnescapeDataString(model.Password);
                bool passwordVarified = false;
                ApplicationUser user = await UserManager.FindByNameAsync(model.Username);
                _appUser = user;

                if (user == null)
                {
                    var traineeUserId = _context.Trainees.FirstOrDefault(x => x.PIN == model.Username)?.UserId;
                    user = await UserManager.FindByIdAsync(traineeUserId);
                }
                if (user != null && await UserManager.IsLockedOutAsync(user.Id))
                {
                    throw new Exception($"You account has been locked out. Please try after {GetTimeDifference(DateTime.UtcNow.ToKindLocal(), user.LockoutEndDateUtc.ToKindLocal().Value)} or contact to System Administrator");
                }
                if (UserManager.PasswordHasher.VerifyHashedPassword(user.PasswordHash, model.Password) == PasswordVerificationResult.Success)
                    passwordVarified = true;
                if (user.PasswordHash == Cipher.EncryptSHA256Str(model.Password))
                    passwordVarified = true;
                if (user == null || user.LoginType == LoginType.External)
                {
                    var pin = model.LoginType == LoginTypeModel.Default ?
                        VerifyHRMUser(model.Username, model.Password) : VerifyADUser(model.Username, model.Password);

                    if (pin == null)
                    {
                        if (user != null) await UserManager.AccessFailedAsync(user.Id);
                        throw new Exception("The user name or password is incorrect.");
                    }

                    user = await UserManager.Users.FirstOrDefaultAsync(x => x.UserName == pin);
                    if (user == null)
                    {
                        throw new Exception("No information found about you on this system. Please contact with System Administrator.");
                    }
                }
                else if (user.LoginType == LoginType.Regular && !passwordVarified)
                {
                    await UserManager.AccessFailedAsync(user.Id);
                    throw new Exception("The password is incorrect for regular login.");
                }

                else if (!passwordVarified)
                {
                    await UserManager.AccessFailedAsync(user.Id);
                    throw new Exception("The password is incorrect.");
                }
                bool trainee_or_guest_or_admin = await UserManager.IsInRoleAsync(user.Id, "Trainee") || await UserManager.IsInRoleAsync(user.Id, "Admin") || await UserManager.IsInRoleAsync(user.Id, "Guest");
                //bool not_trainee_admin = !await UserManager.IsInRoleAsync(user.Id, "Trainee") && !await UserManager.IsInRoleAsync(user.Id, "Admin");


                if (!trainee_or_guest_or_admin)
                {
                    await UserManager.AccessFailedAsync(user.Id);
                    throw new Exception("The user is incorrect");
                }

                if (!user.Active)
                {
                    await UserManager.AccessFailedAsync(user.Id);
                    throw new Exception("This user has been de-activated by the System Administrator. Please contact with the System Administrator");
                }

                await UserManager.ResetAccessFailedCountAsync(user.Id);

                ClaimsIdentity cookiesIdentity = await user.GenerateUserIdentityAsync(UserManager,
                    CookieAuthenticationDefaults.AuthenticationType);

                var roles = await UserManager.GetRolesAsync(user.Id);

                AuthenticationProperties properties = new AuthenticationProperties
                {
                    IssuedUtc = DateTime.UtcNow,
                    ExpiresUtc = DateTime.UtcNow.AddHours(3),
                    IsPersistent = true
                };
                AuthenticationTicket ticket = new AuthenticationTicket(cookiesIdentity, properties);
                var accessToken = Startup.OAuthOptions.AccessTokenFormat.Protect(ticket);

                var principal = new ClaimsPrincipal(cookiesIdentity);
                HttpContext.Current.GetOwinContext().Authentication.SignIn(properties, cookiesIdentity);

                var cookie = new CookieHeaderValue(".Brac.LMS.Cookie", accessToken);
                cookie.Expires = ticket.Properties.ExpiresUtc;
                cookie.Domain = Request.RequestUri.Host;
                cookie.HttpOnly = true;
                cookie.Secure = true;
                cookie.Path = "/";
                resp.StatusCode = System.Net.HttpStatusCode.OK;
                resp.Headers.AddCookies(new CookieHeaderValue[] { cookie });
                resp.Content = new StringContent(JsonConvert.SerializeObject(new { Expires= ticket.Properties.ExpiresUtc , AccessToken = accessToken }));

                audit.CreatedBy = user.Id;
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);
            }
            catch (Exception ex)
            {
                resp.StatusCode = System.Net.HttpStatusCode.BadRequest;
                resp.Content = new StringContent(ex.Message);

                audit.CreatedBy = _appUser != null ? _appUser.Id : "Failed Login User";
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
            }
            return resp;
        }

        [AllowAnonymous, HttpPost, Route("admin/login")]
        public async Task<HttpResponseMessage> AdminLogin(LoginModel model)
        {
            audit.Controller = this.ControllerContext.ControllerDescriptor.ControllerName;
            audit.Method = this.ActionContext.ActionDescriptor.ActionName;
            var resp = new HttpResponseMessage();
            try
            {
                model.Password = Uri.UnescapeDataString(model.Password);
                ApplicationUser user = await UserManager.FindByNameAsync(model.Username);
                if (user != null && await UserManager.IsLockedOutAsync(user.Id))
                {
                    throw new UnauthorizedAccessException($"You account has been locked out. Please try after {GetTimeDifference(DateTime.UtcNow.ToKindLocal(), user.LockoutEndDateUtc.ToKindLocal().Value)} or contact to System Administrator");
                }
                if (!user.Active)
                {
                    await UserManager.AccessFailedAsync(user.Id);
                    throw new UnauthorizedAccessException("Sorry! Your account has been expired or deactivated. Please contact with the System Administrator.");
                }
                
                if (user.LoginType == LoginType.External)
                {
                    var pin = model.LoginType == LoginTypeModel.Default ?
                        VerifyHRMUser(model.Username, model.Password) : 
                        VerifyADUser(model.Username, model.Password);

                    if (pin == null)
                    {
                        await UserManager.AccessFailedAsync(user.Id);
                        throw new Exception("The user name or password is incorrect.");
                    }
                    user = await UserManager.FindByNameAsync(pin);
                    if (user == null)
                    {
                        throw new Exception("No information found about you on this system. Please contact with System Administrator.");
                    }
                }
                else if (UserManager.PasswordHasher.VerifyHashedPassword(user.PasswordHash, model.Password) != PasswordVerificationResult.Success)
                {
                    await UserManager.AccessFailedAsync(user.Id);
                    throw new UnauthorizedAccessException("The user name or password is incorrect.");
                }

                if (!await UserManager.IsInRoleAsync(user.Id, "Admin"))
                {
                    await UserManager.AccessFailedAsync(user.Id);
                    throw new UnauthorizedAccessException("You don't have permission on this site!");
                }

                await UserManager.ResetAccessFailedCountAsync(user.Id);

                //var dbContext = Request.GetOwinContext().Get<ApplicationDbContext>();
                //var userGroupName = "";

                //if (user.UserGroupId.HasValue)
                //{
                //    //userGroupName = await dbContext.UserGroups.Where(x => x.Id == user.UserGroupId.Value).Select(x => x.Name).FirstOrDefaultAsync();
                //}

                user.LastLogOn = DateTime.UtcNow.ToKindLocal();
                await UserManager.UpdateAsync(user);

                ClaimsIdentity cookiesIdentity = await user.GenerateUserIdentityAsync(UserManager,
                    CookieAuthenticationDefaults.AuthenticationType);

                AuthenticationProperties properties = new AuthenticationProperties
                {
                    IssuedUtc = DateTime.UtcNow,
                    ExpiresUtc = DateTime.UtcNow.AddHours(3),
                    IsPersistent = true
                };
                AuthenticationTicket ticket = new AuthenticationTicket(cookiesIdentity, properties);
                var accessToken = Startup.OAuthOptions.AccessTokenFormat.Protect(ticket);

                HttpContext.Current.GetOwinContext().Authentication.SignIn(properties, cookiesIdentity);

                var cookie = new CookieHeaderValue(".Brac.LMS.Cookie", accessToken)
                {
                    Expires = ticket.Properties.ExpiresUtc,
                    Domain = Request.RequestUri.Host,
                    HttpOnly = true,
                    Secure = true,
                    Path = "/"
                };
                resp.StatusCode = System.Net.HttpStatusCode.OK;
                resp.Headers.AddCookies(new CookieHeaderValue[] { cookie });
                resp.Content = new StringContent(JsonConvert.SerializeObject(new
                {
                    Expires = cookie.Expires.Value.DateTime,
                    FullName = user.FirstName + (!string.IsNullOrEmpty(user.LastName) ? " " + user.LastName : ""),
                    Position = "",
                    user.Gender,
                    user.ImagePath,
                    user.Id,
                    Roles = await UserManager.GetRolesAsync(user.Id)
                }));
                audit.CreatedBy = user.Id;
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);
            }
            catch (InvalidOperationException ex)
            {
                resp.StatusCode = System.Net.HttpStatusCode.BadRequest;
                resp.Content = new StringContent(ex.Message);
            }
            catch (UnauthorizedAccessException ex)
            {
                resp.StatusCode = System.Net.HttpStatusCode.Unauthorized;
                resp.Content = new StringContent(ex.Message);
            }
            catch (Exception ex)
            {
                resp.StatusCode = System.Net.HttpStatusCode.BadRequest;
                resp.Content = new StringContent(ex.Message);
            }
            audit.CreatedBy =  string.IsNullOrEmpty(User.Identity.Name) ? model.Username : User.Identity.Name;
            audit.StatusCode = resp.StatusCode.ToString();
            await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(resp), _context);
            return resp;
        }

        [AllowAnonymous, HttpPost, Route("logout")]
        public async Task<HttpResponseMessage> Logout()
        {
            audit.Controller = this.ControllerContext.ControllerDescriptor.ControllerName;
            audit.Method = this.ActionContext.ActionDescriptor.ActionName;
            audit.CreatedBy = User.Identity.Name;
            var resp = new HttpResponseMessage();
            try
            {
                if (HttpContext.Current.Request.Cookies.AllKeys.Contains(".Brac.LMS.Cookie"))
                {
                    var cookie = new CookieHeaderValue(".Brac.LMS.Cookie", "");
                    cookie.Expires = DateTime.UtcNow.AddDays(-1);
                    cookie.Domain = Request.RequestUri.Host;
                    cookie.HttpOnly = true;
                    cookie.Secure = true;
                    cookie.Path = "/";
                    resp.StatusCode = System.Net.HttpStatusCode.OK;
                    resp.Headers.AddCookies(new CookieHeaderValue[] { cookie });
                    resp.Content = new StringContent(JsonConvert.SerializeObject(new { Expires = cookie.Expires.Value.DateTime }));
                    await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(cookie), _context);
                }
            }
            catch (Exception ex)
            {
                resp.StatusCode = System.Net.HttpStatusCode.Unauthorized;
                resp.ReasonPhrase = ex.Message;
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
            }
            return resp;
        }

        [AllowAnonymous, HttpPost, Route("request-for-otp/{pin}")]
        public async Task<IHttpActionResult> RequestForOtp(string pin)
        {
            return Ok(await _service.RequestForOtp(pin));
        }
        [AllowAnonymous, HttpPost, Route("request-for-otp-app/{pin}")]
        public async Task<IHttpActionResult> RequestForOtpApp(string pin)
        {
            return Ok(await _service.RequestForOtpApp(pin));
        }
        [AllowAnonymous, HttpGet, Route("validate-otp")]
        public async Task<IHttpActionResult> ValidateOtp(string pin, string otp)
        {
            return Ok(await _service.ValidateOtp(pin, otp));
        }

        [AllowAnonymous, HttpPost, Route("validate-otp-app")]
        public async Task<IHttpActionResult> ValidateOtpApp(PasswordResetModel model)
        {
            return Ok(await _service.ValidateOtp(model.Pin, model.Otp));
        }

        [AllowAnonymous, HttpPost, Route("reset-password-by-otp")]
        public async Task<IHttpActionResult> ResetPasswordByOtp(PasswordResetModel model)
        {
            return Ok(await _service.ResetPasswordByOtp(model, UserManager));
        }

        private string VerifyADUser(string username, string password)
        {
            string pin = null;
            try
            {
                string ldapPath = System.Configuration.ConfigurationManager.AppSettings["LDAPPath"];
                string ldapDn = System.Configuration.ConfigurationManager.AppSettings["LDAPDn"];
                string uidProperty = System.Configuration.ConfigurationManager.AppSettings["UidProperty"];
                string pinProperty = System.Configuration.ConfigurationManager.AppSettings["PinProperty"];


                var root = new System.DirectoryServices.DirectoryEntry(ldapPath, string.Format("{0}\\{1}", ldapDn, username), password, System.DirectoryServices.AuthenticationTypes.SecureSocketsLayer | System.DirectoryServices.AuthenticationTypes.Secure);

                var ds = new System.DirectoryServices.DirectorySearcher(root);

                // set options
                ds.Filter = string.Format("({0}={1})", uidProperty, username);
                // do we find anyone by that name??
                var result = ds.FindOne();

                if (result != null && result.Properties[pinProperty].Count > 0)
                    pin = result.Properties[pinProperty][0].ToString();

                return pin;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        private string VerifyHRMUser(string username, string password)
        {
            string pin = null;
            bool active = false;
            System.Data.SqlClient.SqlConnection con = null;
            System.Data.SqlClient.SqlDataReader reader;

            try
            {
                con = new System.Data.SqlClient.SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["BBLHrmConnection"].ConnectionString);
                var command = new System.Data.SqlClient.SqlCommand("SELECT TOP 1 UserId, IsActive FROM Hr_Mob_UsersInfo WHERE UserId=@username AND Password=@password", con);
                command.Parameters.Add("@username", System.Data.SqlDbType.NVarChar);
                command.Parameters.Add("@password", System.Data.SqlDbType.NVarChar);
                command.Parameters["@username"].Value = username;
                command.Parameters["@password"].Value = Cipher.EncryptSHA256Str(password);

                con.Open();
                reader = command.ExecuteReader();
                if (reader.HasRows)
                {
                    while (reader.Read())
                    {
                        pin = reader.GetString(0);
                        active = reader.GetInt64(1) == 1;
                    }
                    if (!active) throw new Exception("This user has been de-activated by the System Administrator. Please contact with the System Administrator");
                }
                else
                {
                    throw new Exception("The user name or password is incorrect.");
                }
                reader.Close();
            }
            catch (Exception ex)
            {
                throw (ex);
            }
            finally
            {
                if (con != null && con.State != System.Data.ConnectionState.Closed) con.Close();
            }
            return pin;
        }

        //private string VerifyUserFromBBLHRM(string username, string password)
        //{
        //    string pin = null;
        //    try
        //    {
        //        password = ;
        //        string ldapDn = System.Configuration.ConfigurationManager.AppSettings["LDAPDn"];
        //        string uidProperty = System.Configuration.ConfigurationManager.AppSettings["UidProperty"];
        //        string pinProperty = System.Configuration.ConfigurationManager.AppSettings["PinProperty"];

        //        using (System.Data.IDbConnection db = new System.Data.SqlClient.SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["BBLHRMDbConnection"].ConnectionString))
        //        {

        //            pin = await db.QueryAsync<string>("SELECT UserId FROM Hr_Mob_UsersInfo WHERE UserId=@username AND Password=@password");
        //        }


        //        var root = new System.DirectoryServices.DirectoryEntry(ldapPath, string.Format("{0}\\{1}", ldapDn, username), password, System.DirectoryServices.AuthenticationTypes.SecureSocketsLayer | System.DirectoryServices.AuthenticationTypes.Secure);

        //        var ds = new System.DirectoryServices.DirectorySearcher(root);

        //        // set options
        //        ds.Filter = string.Format("({0}={1})", uidProperty, username);
        //        // do we find anyone by that name??
        //        var result = ds.FindOne();

        //        if (result != null && result.Properties[pinProperty].Count > 0)
        //            pin = result.Properties[pinProperty][0].ToString();

        //        return pin;
        //    }
        //    catch (Exception ex)
        //    {
        //        throw new Exception("AD Authentication: " + ex.Message);
        //    }
        //}

        protected override void Dispose(bool disposing)
        {
            //if (disposing && _userManager != null)
            //{
            //    _userManager.Dispose();
            //    _userManager = null;
            //}

            base.Dispose(disposing);
        }

        #region Helpers

        private AuthenticationProperties CreateProperties(ApplicationUser user, IList<string> roles, string userGroupName, string position)
        {

            IDictionary<string, string> data = new Dictionary<string, string>
            {
                {
                    "Id",user.Id
                },
                {
                    "UserName", user.UserName
                },
                {
                    "Email", user.Email
                },
                {
                    "FirstName", user.FirstName ?? ""
                },
                {
                    "LastName", user.LastName ?? ""
                },
                {
                    "Position", position ?? ""
                },
                {
                    "ImagePath", user.ImagePath ?? ""
                },
                {
                    "UserType", user.UserType.ToString()
                },
                {
                    "Gender", user.Gender.ToString()
                },
                //{
                //    "Roles", JsonConvert.SerializeObject(roles)
                //},
                {
                    "UserGroup", userGroupName ?? "Super Admin"
                }
            };
            return new AuthenticationProperties(data);
        }

        private string GetTimeDifference(DateTime start, DateTime end)
        {
            var diff = end.Subtract(start);
            return string.Format("{0}m {1}s", diff.Minutes, diff.Seconds);
        }
        //private IAuthenticationManager Authentication
        //{
        //    get { return Request.GetOwinContext().Authentication; }
        //}

        //private IHttpActionResult GetErrorResult(IdentityResult result)
        //{
        //    if (result == null)
        //    {
        //        return InternalServerError();
        //    }

        //    if (!result.Succeeded)
        //    {
        //        if (result.Errors != null)
        //        {
        //            foreach (string error in result.Errors)
        //            {
        //                ModelState.AddModelError("", error);
        //            }
        //        }

        //        if (ModelState.IsValid)
        //        {
        //            // No ModelState errors are available to send, so just return an empty BadRequest.
        //            return BadRequest();
        //        }

        //        return BadRequest(ModelState);
        //    }

        //    return null;
        //}

        //private class ExternalLoginData
        //{
        //    public string LoginProvider { get; set; }
        //    public string ProviderKey { get; set; }
        //    public string UserName { get; set; }

        //    public IList<Claim> GetClaims()
        //    {
        //        IList<Claim> claims = new List<Claim>();
        //        claims.Add(new Claim(ClaimTypes.NameIdentifier, ProviderKey, null, LoginProvider));

        //        if (UserName != null)
        //        {
        //            claims.Add(new Claim(ClaimTypes.Name, UserName, null, LoginProvider));
        //        }

        //        return claims;
        //    }

        //    public static ExternalLoginData FromIdentity(ClaimsIdentity identity)
        //    {
        //        if (identity == null)
        //        {
        //            return null;
        //        }

        //        Claim providerKeyClaim = identity.FindFirst(ClaimTypes.NameIdentifier);

        //        if (providerKeyClaim == null || String.IsNullOrEmpty(providerKeyClaim.Issuer)
        //            || String.IsNullOrEmpty(providerKeyClaim.Value))
        //        {
        //            return null;
        //        }

        //        if (providerKeyClaim.Issuer == ClaimsIdentity.DefaultIssuer)
        //        {
        //            return null;
        //        }

        //        return new ExternalLoginData
        //        {
        //            LoginProvider = providerKeyClaim.Issuer,
        //            ProviderKey = providerKeyClaim.Value,
        //            UserName = identity.FindFirstValue(ClaimTypes.Name)
        //        };
        //    }
        //}

        //private static class RandomOAuthStateGenerator
        //{
        //    private static RandomNumberGenerator _random = new RNGCryptoServiceProvider();

        //    public static string Generate(int strengthInBits)
        //    {
        //        const int bitsPerByte = 8;

        //        if (strengthInBits % bitsPerByte != 0)
        //        {
        //            throw new ArgumentException("strengthInBits must be evenly divisible by 8.", "strengthInBits");
        //        }

        //        int strengthInBytes = strengthInBits / bitsPerByte;

        //        byte[] data = new byte[strengthInBytes];
        //        _random.GetBytes(data);
        //        return HttpServerUtility.UrlTokenEncode(data);
        //    }
        //}

        #endregion
    }
}