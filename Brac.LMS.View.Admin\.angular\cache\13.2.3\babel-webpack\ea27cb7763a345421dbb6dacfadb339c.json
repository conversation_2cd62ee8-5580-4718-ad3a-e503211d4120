{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nexport function fromEventPattern(addHandler, removeHandler, resultSelector) {\n  if (resultSelector) {\n    return fromEventPattern(addHand<PERSON>, removeHandler).pipe(mapOneOrManyArgs(resultSelector));\n  }\n\n  return new Observable(subscriber => {\n    const handler = (...e) => subscriber.next(e.length === 1 ? e[0] : e);\n\n    const retValue = addHandler(handler);\n    return isFunction(removeHandler) ? () => removeHandler(handler, retValue) : undefined;\n  });\n} //# sourceMappingURL=fromEventPattern.js.map", "map": null, "metadata": {}, "sourceType": "module"}