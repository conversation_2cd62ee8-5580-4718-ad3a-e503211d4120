﻿using Brac.LMS.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class GhooriCertificate : AuditableEntity
    {
        [Required, StringLength(250)]
        public string CourseName { get; set; }

        [Required, StringLength(250)]
        public string TraineePIN { get; set; }

        [Required, StringLength(250)]
        public string TraineeName { get; set; }

        public DateTime? DateOfCertification { get; set; }

        [Required, StringLength(250)]
        public string CertficationType { get; set; }

        [Required, StringLength(250)]
        public string CertficationPath { get; set; }

    }
}
