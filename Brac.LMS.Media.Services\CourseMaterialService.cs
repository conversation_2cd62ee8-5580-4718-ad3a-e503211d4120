﻿using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Media.ViewModels;
using Brac.LMS.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Hosting;
using WMPLib;

namespace Brac.LMS.Media.Services
{
    public class CourseMaterialService : ICourseMaterialService
    {
        private readonly ApplicationDbContext _context;
        public CourseMaterialService()
        {
            _context = new ApplicationDbContext();
        }

        public async Task<APIResponse> MaterialCreateOrUpdate(CourseMaterialModel model, IIdentity identity)
        {
            CourseMaterial item = null;
            bool isEdit = true;
            try
            {
                if (await _context.CourseMaterials.AnyAsync(x => x.Id != model.Id && x.CourseId == model.CourseId && x.Title == model.Title))
                    throw new Exception("Already exists in this course: " + model.Title);

                if (model.Id != Guid.Empty)
                {
                    item = await _context.CourseMaterials.FindAsync(model.Id);
                    if (item == null) throw new Exception("Material not found");
                }
                else
                {
                    item = new CourseMaterial();
                    isEdit = false;

                    if (HttpContext.Current.Request.Files.Count == 0) throw new Exception("No file supplied");
                }

                if (HttpContext.Current.Request.Files.Count > 0)
                {
                    switch (model.MaterialType)
                    {
                        case MaterialType.Document:
                            item = SaveDocumentFile("DOC" + Utility.RandomString(4, false) + DateTime.Now.ToString("yyyMMddHHmmss"), "/Files/CourseMaterial/Documents/", HttpContext.Current.Request.Files[0], item);
                            break;
                        case MaterialType.Video:
                            item = SaveVideoFile("VID" + Utility.RandomString(4, false) + DateTime.Now.ToString("yyyMMddHHmmss"), "/Files/CourseMaterial/Videos/", HttpContext.Current.Request.Files[0], item);
                            break;
                    }
                }

                //item.YoutubeID = model.YoutubeID;
                item.CourseId = model.CourseId;
                item.MaterialType = model.MaterialType;
                item.Title = model.Title;

                item.SetAuditTrailEntity(identity);

                if (!isEdit)
                {
                    item.Id = Guid.NewGuid();
                    _context.CourseMaterials.Add(item);
                }
                else
                {
                    _context.Entry(item).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                    }
                };
              
            }
            catch (Exception ex)
            {
                return new APIResponse { Status = ResponseStatus.Error, Message = ex.Message };
            }
        }

        private CourseMaterial SaveVideoFile(string fileName, string partialPath, HttpPostedFile hpf, CourseMaterial item)
        {
            try
            {
                string serverPath = HostingEnvironment.MapPath("~"), extension, filePath;

                if (!Directory.Exists(serverPath + partialPath)) Directory.CreateDirectory(serverPath + partialPath);

                if (!string.IsNullOrEmpty(item.FilePath) && File.Exists(serverPath + item.FilePath))
                {
                    File.Delete(serverPath + item.FilePath);
                }

                string[] mediaExtensions = { ".AVI", ".MP4", ".DIVX", ".WMV" };

                extension = Path.GetExtension(hpf.FileName);
                if (mediaExtensions.Contains(extension.ToUpper()))
                {
                    if (hpf.ContentLength > 524288000)
                    {
                        throw new Exception("File size exceeded. Max file size is 500MB. Your selected file size is " + hpf.ContentLength / (1024 * 1024) + ".");
                    }
                    filePath = partialPath + fileName + extension;

                    if (File.Exists(serverPath + filePath))
                        File.Delete(serverPath + filePath);

                    hpf.SaveAs(serverPath + filePath);

                    WindowsMediaPlayer wmp = new WindowsMediaPlayer();
                    IWMPMedia mediainfo = wmp.newMedia(serverPath + filePath);

                    //var info = new FFmpegMediaInfo(serverPath + filePath, Generator.FFMPEG_EXE_PATH, Generator.FFPROBE_EXE_PATH);

                    item.FileSizeKb = (long)(hpf.ContentLength / 1024);
                    item.VideoDurationSecond = (int)mediainfo.duration;

                    item.FilePath = filePath;
                    return item;
                }
                else throw new Exception("Only AVI, MP4, DIVX or WMV video files are allowed to upload.");
            }
            catch (Exception ex)
            {
                throw new Exception("SaveVideoFile Error: " + ex.Message);
            }
        }

        private CourseMaterial SaveDocumentFile(string fileName, string partialPath, HttpPostedFile hpf, CourseMaterial item)
        {
            try
            {
                string serverPath = HostingEnvironment.MapPath("~"), extension, filePath;

                if (!Directory.Exists(serverPath + partialPath)) Directory.CreateDirectory(serverPath + partialPath);

                if (!string.IsNullOrEmpty(item.FilePath) && File.Exists(serverPath + item.FilePath))
                {
                    File.Delete(serverPath + item.FilePath);
                }

                string[] fileExtensions = { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".jpg", ".jpeg", ".png" };

                extension = Path.GetExtension(hpf.FileName);
                if (fileExtensions.Contains(extension.ToLower()))
                {
                    if (hpf.ContentLength > 102400000)
                    {
                        throw new Exception("File size exceeded. Max file size is 100MB. Your selected file size is " + hpf.ContentLength / 1000 + ".");
                    }
                    filePath = partialPath + fileName + extension;

                    if (File.Exists(serverPath + filePath))
                        File.Delete(serverPath + filePath);

                    hpf.SaveAs(serverPath + filePath);
                    item.FilePath = filePath;
                    item.FileSizeKb = (long)(hpf.ContentLength / 1024);

                    return item;
                }
                else throw new Exception("Only PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, JPG, JPEG or PNG files are allowed to upload.");
            }
            catch (Exception ex)
            {
                throw new Exception("SaveDocumentFile Error: " + ex.Message);
            }
        }

        public async Task<APIResponse> GetRandomVideo()
        {
            try
            {
                var data = await _context.CourseMaterials.Where(t => t.MaterialType == MaterialType.Video)
                .Select(t => new
                {
                    t.Id,
                    t.Title,
                    t.FilePath,
                    t.S3Path
                }).FirstOrDefaultAsync();
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Record = data
                    }
                };
              
             
            }
            catch (Exception ex)
            {
                return new APIResponse { Status = ResponseStatus.Error, Message = ex.Message };
            }
        }
    }

    public interface ICourseMaterialService
    {
        Task<APIResponse> MaterialCreateOrUpdate(CourseMaterialModel model, IIdentity identity);
        Task<APIResponse> GetRandomVideo();
    }
}
