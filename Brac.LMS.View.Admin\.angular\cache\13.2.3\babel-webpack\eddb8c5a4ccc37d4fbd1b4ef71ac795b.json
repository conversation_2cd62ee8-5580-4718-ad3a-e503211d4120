{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Kazakh [kk]\n//! authors : <PERSON><PERSON><PERSON> : https://github.com/nurlan\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var suffixes = {\n    0: '-ші',\n    1: '-ші',\n    2: '-ші',\n    3: '-ші',\n    4: '-ші',\n    5: '-ші',\n    6: '-шы',\n    7: '-ші',\n    8: '-ші',\n    9: '-шы',\n    10: '-шы',\n    20: '-шы',\n    30: '-шы',\n    40: '-шы',\n    50: '-ші',\n    60: '-шы',\n    70: '-ші',\n    80: '-ші',\n    90: '-шы',\n    100: '-ші'\n  };\n  var kk = moment.defineLocale('kk', {\n    months: 'қаңтар_ақпан_наурыз_сәуір_мамыр_маусым_шілде_тамыз_қыркүйек_қазан_қараша_желтоқсан'.split('_'),\n    monthsShort: 'қаң_ақп_нау_сәу_мам_мау_шіл_там_қыр_қаз_қар_жел'.split('_'),\n    weekdays: 'жексенбі_дүйсенбі_сейсенбі_сәрсенбі_бейсенбі_жұма_сенбі'.split('_'),\n    weekdaysShort: 'жек_дүй_сей_сәр_бей_жұм_сен'.split('_'),\n    weekdaysMin: 'жк_дй_сй_ср_бй_жм_сн'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Бүгін сағат] LT',\n      nextDay: '[Ертең сағат] LT',\n      nextWeek: 'dddd [сағат] LT',\n      lastDay: '[Кеше сағат] LT',\n      lastWeek: '[Өткен аптаның] dddd [сағат] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s ішінде',\n      past: '%s бұрын',\n      s: 'бірнеше секунд',\n      ss: '%d секунд',\n      m: 'бір минут',\n      mm: '%d минут',\n      h: 'бір сағат',\n      hh: '%d сағат',\n      d: 'бір күн',\n      dd: '%d күн',\n      M: 'бір ай',\n      MM: '%d ай',\n      y: 'бір жыл',\n      yy: '%d жыл'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-(ші|шы)/,\n    ordinal: function (number) {\n      var a = number % 10,\n          b = number >= 100 ? 100 : null;\n      return number + (suffixes[number] || suffixes[a] || suffixes[b]);\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n\n    }\n  });\n  return kk;\n});", "map": null, "metadata": {}, "sourceType": "script"}