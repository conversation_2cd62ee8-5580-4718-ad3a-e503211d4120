{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { EvalTestProgressReportComponent } from './evaluation-test-progress-report.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: EvalTestProgressReportComponent\n}];\nexport let EvalTestProgressReportRoutingModule = /*#__PURE__*/(() => {\n  class EvalTestProgressReportRoutingModule {}\n\n  EvalTestProgressReportRoutingModule.ɵfac = function EvalTestProgressReportRoutingModule_Factory(t) {\n    return new (t || EvalTestProgressReportRoutingModule)();\n  };\n\n  EvalTestProgressReportRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: EvalTestProgressReportRoutingModule\n  });\n  EvalTestProgressReportRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return EvalTestProgressReportRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}