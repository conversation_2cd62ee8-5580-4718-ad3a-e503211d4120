{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ForumPostDetailsComponent } from './forum-post-details.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ForumPostDetailsComponent\n}];\nexport let ForumPostDetailsRoutingModule = /*#__PURE__*/(() => {\n  class ForumPostDetailsRoutingModule {}\n\n  ForumPostDetailsRoutingModule.ɵfac = function ForumPostDetailsRoutingModule_Factory(t) {\n    return new (t || ForumPostDetailsRoutingModule)();\n  };\n\n  ForumPostDetailsRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ForumPostDetailsRoutingModule\n  });\n  ForumPostDetailsRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return ForumPostDetailsRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}