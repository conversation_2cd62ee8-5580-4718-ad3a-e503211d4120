﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class TraineeEvaluationActivity : NumberAuditableEntity
    {
        public Guid TraineeId { get; set; }
        public virtual Trainee Trainee { get; set; }
        public string Title { get; set; }
        public int NoOfContents { get; set; }
        public int NoOfContentsCompleted { get; set; }
        public DateTime? FirstStudyDate { get; set; }
        public DateTime? LastStudyDate { get; set; }
        public bool? VideoCompleted { get; set; }
        public bool? DocumentCompleted { get; set; }
        public bool? ExamCompleted { get; set; }
        public int Progress { get; set; }
        public DateTime? ExamCompletedDate { get; set; }
    }

}
