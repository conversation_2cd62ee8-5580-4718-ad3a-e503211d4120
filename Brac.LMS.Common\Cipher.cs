﻿using System;
using System.Text;
using System.Security.Cryptography;
using System.IO;

namespace Brac.LMS.Common
{
    public static class Cipher
    {
        /// <summary>
        /// Encrypt a string.
        /// </summary>
        /// <param name="plainText">String to be encrypted</param>
        /// <param name="password">Password</param>
        public static string EncryptSHA256Str(string val)
        {
            //byte[] passwordBytes = null;
            byte[] bytesEncrypted;
            try
            {
                string key = "IB";
                if (val == null)
                {
                    return null;
                }

                if (key == null)
                {
                    key = string.Empty;
                }

                // Get the bytes of the string
                var bytesToBeEncrypted = Encoding.UTF8.GetBytes(val);


                // Hash the password with SHA256

                bytesEncrypted = Encrypt(bytesToBeEncrypted, SHA256.Create().ComputeHash(Encoding.UTF8.GetBytes(key)));

                return Convert.ToBase64String(bytesEncrypted);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private static byte[] Encrypt(byte[] bytesToBeEncrypted, byte[] passwordBytes)
        {
            byte[] encryptedBytes = null;

            // Set your salt here, change it to meet your flavor:
            // The salt bytes must be at least 8 bytes.
            var saltBytes = new byte[] { 1, 2, 3, 4, 5, 6, 7, 8 };

            using (MemoryStream ms = new MemoryStream())
            {
                using (RijndaelManaged AES = new RijndaelManaged())
                {
                    var key = new Rfc2898DeriveBytes(passwordBytes, saltBytes, 1000);

                    AES.KeySize = 256;
                    AES.BlockSize = 128;
                    AES.Key = key.GetBytes(AES.KeySize / 8);
                    AES.IV = key.GetBytes(AES.BlockSize / 8);

                    AES.Mode = CipherMode.CBC;

                    using (var cs = new CryptoStream(ms, AES.CreateEncryptor(), CryptoStreamMode.Write))
                    {
                        cs.Write(bytesToBeEncrypted, 0, bytesToBeEncrypted.Length);
                        cs.Close();
                    }

                    encryptedBytes = ms.ToArray();
                }
            }

            return encryptedBytes;
        }
    }
}
