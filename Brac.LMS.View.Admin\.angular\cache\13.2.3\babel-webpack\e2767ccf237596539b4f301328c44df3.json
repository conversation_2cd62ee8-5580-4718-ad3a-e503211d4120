{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { ChangePasswordRoutes } from './change-password.routing';\nimport { SharedModule } from 'src/app/theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let ChangePasswordModule = /*#__PURE__*/(() => {\n  class ChangePasswordModule {}\n\n  ChangePasswordModule.ɵfac = function ChangePasswordModule_Factory(t) {\n    return new (t || ChangePasswordModule)();\n  };\n\n  ChangePasswordModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ChangePasswordModule\n  });\n  ChangePasswordModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, RouterModule.forChild(ChangePasswordRoutes), SharedModule]]\n  });\n  return ChangePasswordModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}