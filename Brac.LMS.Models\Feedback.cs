﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class FeedbackQuestion : NumberAuditableEntity
    {
        public EvaluationType EvaluationType { get; set; }
        public QuestionGroup QuestionGroup { get; set; }

        [Required]
        public string Question { get; set; }

        public QuestionType QuestionType { get; set; }
        public string Options { get; set; }
    }

    public class CourseFeedback : AuditableEntity
    {
        public Guid CourseId { get; set; }
        public virtual Course Course { get; set; }

        public Guid TraineeId { get; set; }
        public virtual Trainee Trainee { get; set; }

        public decimal Rating { get; set; }
        public string Comment { get; set; }

        public virtual ICollection<CourseFeedbackAnswer> Feedbacks { get; set; }
    }

    public class CourseFeedbackAnswer : NumberEntityField
    {
        public Guid FeedbackId { get; set; }
        public virtual CourseFeedback Feedback { get; set; }

        public long QuestionId { get; set; }
        public virtual FeedbackQuestion Question { get; set; }

        public string Answer { get; set; }
    }

    public class LearningHourFeedback : NumberAuditableEntity
    {
        public Guid ExamId { get; set; }
        public virtual EvaluationExam Exam { get; set; }

        public Guid TraineeId { get; set; }
        public virtual Trainee Trainee { get; set; }

        public long QuestionId { get; set; }
        public virtual FeedbackQuestion Question { get; set; }

        public string Answer { get; set; }
    }
}
