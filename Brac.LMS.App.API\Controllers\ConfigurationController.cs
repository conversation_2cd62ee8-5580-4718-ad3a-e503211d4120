﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Web.Http;
using Tavis.UriTemplates;

namespace Brac.LMS.App.API.Controllers
{
    [Authorize(Roles = "Admin"), RoutePrefix("api/configuration")]
    public class ConfigurationController : ApplicationController
    {
        private readonly IConfigurationService _service;

        public ConfigurationController()
        {
            _service = new ConfigurationService();
        }

        [HttpPost, Route("create-or-update")]
        public async Task<IHttpActionResult> ConfigurationCreateOrUpdate()
        {
            try
            {
                var model = Newtonsoft.Json.JsonConvert.DeserializeObject<ConfigurationModel>(System.Web.HttpContext.Current.Request.Form["Model"]);
                var _nservice = new ConfigurationService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
                return Ok(await _nservice.ConfigurationCreateOrUpdate(model, User.Identity));
            }
            catch (Exception ex)
            {
                return Ok(new { Success = false, ex.Message });
            }
        }

        [HttpGet, Route("get")]
        public async Task<IHttpActionResult> GetConfiguration()
        {
            return Ok(await _service.GetConfiguration());
        }

        [AllowAnonymous, HttpGet, Route("get-institute-info")]
        public async Task<IHttpActionResult> GetInstitueInfo()
        {
            return Ok(await _service.GetInstitueInfo());
        }

        [HttpGet, Route("get-exam-data")]
        public async Task<IHttpActionResult> GetExamData()
        {
            return Ok(await _service.GetExamData());
        }
        [AllowAnonymous, HttpGet, Route("download-document-file")]
        public HttpResponseMessage DownloadDocumentFile(string type)
        {
            var partialPath = _service.DocOrInfoPath(type);
            var fullPath = System.Web.Hosting.HostingEnvironment.MapPath("~") + partialPath;
            if (!File.Exists(fullPath))
            {
                return Request.CreateResponse(HttpStatusCode.NotFound);
            }
            else
            {
                HttpResponseMessage result = Request.CreateResponse(HttpStatusCode.OK);
                result.Content = new StreamContent(File.OpenRead(fullPath));
                result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
                return result;
            }
        }
        [AllowAnonymous, HttpGet, Route("doc-or-info-file-available")]
        public async Task<IHttpActionResult> DocOrInfoPathAvailable()
        {
            return Ok(await _service.DocOrInfoPathAvailable());
        }
    }
}
