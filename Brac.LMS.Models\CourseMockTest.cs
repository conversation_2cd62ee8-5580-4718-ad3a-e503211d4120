﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class CourseMockTest : AuditableEntity
    {
        public Guid CourseId { get; set; }
        public virtual Course Course { get; set; }

        public string ExamName { get; set; }
        public string ExamInstructions { get; set; }
        public int Quota { get; set; }
        public int Marks { get; set; }
        public int DurationMnt { get; set; }
        public int MCQNo { get; set; }

        public virtual ICollection<TraineeMockTestAttempt> TraineeAttempts { get; set; }
        public virtual ICollection<MockTestMCQQuestion> Questions { get; set; }
    }

    public class MockTestMCQQuestion : NumberAuditableEntity
    {
        [Required]
        public string Question { get; set; }

        [Required]
        public string Option1 { get; set; }

        [Required]
        public string Option2 { get; set; }

        [Required]
        public string Option3 { get; set; }

        [Required]
        public string Option4 { get; set; }

        [Required]
        public string Answers { get; set; }

        public float Mark { get; set; }

        public Guid ExamId { get; set; }
        public virtual CourseMockTest Exam { get; set; }

        [NotMapped]
        public bool Updated { get; set; }
    }



}
