﻿using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Hosting;

namespace Brac.LMS.App.Services
{
    public class ConfigurationService : IConfigurationService
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditLogHelper _auditLogHelper;
        private readonly AuditLog audit;
        private readonly ApplicationUser _appUser;
        public ConfigurationService()
        {
            _context = new ApplicationDbContext();
        }
        public ConfigurationService(string controller, string method, ApplicationUser applicationUser)
        {
            _auditLogHelper = new AuditLogHelper(controller, method);
            audit = _auditLogHelper.auditLog;
            _appUser = applicationUser;
            audit.CreatedBy = _appUser.Id;
            _context = new ApplicationDbContext();
        }

        public async Task<APIResponse> ConfigurationCreateOrUpdate(ConfigurationModel model, IIdentity identity)
        {
            Configuration item = null;
            bool isEdit = true;
            try
            {

                item = await _context.Configurations.FirstOrDefaultAsync();

                if (item == null)
                {
                    item = new Configuration();
                    isEdit = false;
                }

                item.Name = model.Name;
                item.Address = model.Address;
                item.ContactNo = model.ContactNo;
                item.Address = model.Address;
                item.InstituteEmail = model.InstituteEmail;
                item.ExamInstruction = model.ExamInstruction;
                item.InstituteWebsite = model.InstituteWebsite;
                item.MCQMark = model.MCQMark;
                item.TrueFalseMark = model.TrueFalseMark;
                item.FIGMark = model.FIGMark;
                item.MatchingMark = model.MatchingMark;
                item.WrittenMark = model.WrittenMark;
                item.AndoridAppLink = model.AndoridAppLink;
                item.iOSAppLink = model.iOSAppLink;
                item.LastERPSyncDate = model.LastERPSyncDate;

                if (HttpContext.Current.Request.Files.Count > 0)
                    for (int i = 0; i < HttpContext.Current.Request.Files.Count; i++)
                    {
                        if (HttpContext.Current.Request.Files.AllKeys[i] == "Logo")
                            item.LogoPath = Utility.SaveImage("company_logo_" + Utility.RandomString(3, false), "/Images/Configuration/", HttpContext.Current.Request.Files[i], item.LogoPath, 325, 65);
                        else if(HttpContext.Current.Request.Files.AllKeys[i] == "Documentation")
                            SaveDocumentFile("DOC" + Utility.RandomString(4, false) + DateTime.Now.ToString("yyyMMddHHmmss"), "/Files/Configuration/Documents/", HttpContext.Current.Request.Files[i], item, "Documentation");
                        else if (HttpContext.Current.Request.Files.AllKeys[i] == "Info")
                            SaveDocumentFile("DOC" + Utility.RandomString(4, false) + DateTime.Now.ToString("yyyMMddHHmmss"), "/Files/Configuration/Infos/", HttpContext.Current.Request.Files[i], item, "Info");
                    }

                item.SetAuditTrailEntity(identity);

                if (!isEdit)
                {
                    _context.Configurations.Add(item);
                }
                else
                {
                    _context.Entry(item).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                };
            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())
                };
            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);

                return new APIResponse { Status = ResponseStatus.Error, Message = ex.InnerException?.Message ?? ex.Message };
            }
        }

        public async Task<APIResponse> GetConfiguration()
        {
            try
            {
                var data = await _context.Configurations
                .Select(t => new
                {
                    t.Name,
                    t.Address,
                    t.ContactNo,
                    t.InstituteEmail,
                    t.InstituteWebsite,
                    t.AndoridAppLink,
                    t.iOSAppLink,
                    t.ExamInstruction,
                    t.MatchingMark,
                    t.MCQMark,
                    t.TrueFalseMark,
                    t.FIGMark,
                    t.WrittenMark,
                    t.LogoPath,
                    t.LastERPSyncDate,
                    t.DocumentationPath,
                    t.InfoPath
                }).FirstOrDefaultAsync();

                return new APIResponse { Status = ResponseStatus.Success, Data = data };
            }
            catch (Exception ex)
            {
                return new APIResponse { Status = ResponseStatus.Error, Message = ex.Message };
            }
        }

        public async Task<APIResponse> GetExamData()
        {
            try
            {
                var data = await _context.Configurations
                .Select(t => new { t.ExamInstruction, t.MCQMark, t.TrueFalseMark, t.FIGMark, t.MatchingMark, t.WrittenMark }).FirstOrDefaultAsync();
                return new APIResponse { Status = ResponseStatus.Success, Data = data };
            }
            catch (Exception ex)
            {
                return new APIResponse { Status = ResponseStatus.Error, Message = ex.Message };
            }
        }

        public async Task<APIResponse> GetInstitueInfo()
        {
            try
            {
                var data = await _context.Configurations
                .Select(t => new
                {
                    t.Name,
                    t.Address,
                    t.ContactNo,
                    t.InstituteEmail,
                    t.InstituteWebsite,
                    t.LogoPath,
                    t.AndoridAppLink,
                    t.iOSAppLink,
                    t.DocumentationPath
                }).FirstOrDefaultAsync();

                return new APIResponse { Status = ResponseStatus.Success, Data = data };
            }
            catch (Exception ex)
            {
                return new APIResponse { Status = ResponseStatus.Error, Message = ex.Message };
            }
        }
        public string DocOrInfoPath(string type)
        {
            try
            {
                string data =type== "document"?  _context.Configurations
                .Select(t => t.DocumentationPath).FirstOrDefault(): _context.Configurations
                .Select(t => t.InfoPath).FirstOrDefault();
                return data;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        public async Task<APIResponse> DocOrInfoPathAvailable()
        {
            try
            {
                var data = await _context.Configurations.Select(t => new { t.InfoPath, t.DocumentationPath }).FirstOrDefaultAsync();
                return new APIResponse { Status = ResponseStatus.Success, Data = data };
            }
            catch (Exception ex)
            {
                return new APIResponse { Status = ResponseStatus.Error, Message = ex.Message };
            }
        }
        private Configuration SaveDocumentFile(string fileName, string partialPath, HttpPostedFile hpf, Configuration item,string type)
        {
            try
            {
                string serverPath = HostingEnvironment.MapPath("~"), extension, filePath;

                if (!Directory.Exists(serverPath + partialPath)) Directory.CreateDirectory(serverPath + partialPath);

                if (!string.IsNullOrEmpty(item.DocumentationPath) && File.Exists(serverPath + item.DocumentationPath) && type == "Documentation")
                {
                    File.Delete(serverPath + item.DocumentationPath);
                }
                if (!string.IsNullOrEmpty(item.InfoPath) && File.Exists(serverPath + item.InfoPath) && type == "Info")
                {
                    File.Delete(serverPath + item.InfoPath);
                }
                string[] fileExtensions = { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".jpg", ".jpeg", ".png" };

                extension = Path.GetExtension(hpf.FileName);
                if (fileExtensions.Contains(extension.ToLower()))
                {
                    filePath = partialPath + fileName + extension;

                    if (File.Exists(serverPath + filePath))
                        File.Delete(serverPath + filePath);

                    hpf.SaveAs(serverPath + filePath);
                    if(type== "Documentation")
                    item.DocumentationPath = filePath;
                    if (type == "Info")
                    item.InfoPath = filePath;
                    return item;
                }
                else throw new Exception("Only PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, JPG, JPEG or PNG files are allowed to upload.");
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                throw new Exception("SaveDocumentFile Error: " + ex.Message);
            }
        }

    }

    public interface IConfigurationService
    {
        Task<APIResponse> ConfigurationCreateOrUpdate(ConfigurationModel model, IIdentity identity);
        Task<APIResponse> GetConfiguration();
        Task<APIResponse> GetExamData();
        Task<APIResponse> GetInstitueInfo();
        string DocOrInfoPath(string type);
        Task<APIResponse> DocOrInfoPathAvailable();
    }
}
