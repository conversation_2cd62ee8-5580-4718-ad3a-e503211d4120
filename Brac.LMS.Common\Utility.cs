﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Common
{
    public enum ColumnType
    {
        Int, Long, Double, DateTime, String, PhoneNumber, Email, Enum
    }

    public static class Utility
    {
        public static TimeZoneInfo TimeZoneInfo
        {
            get
            {
                return TimeZoneInfo.FindSystemTimeZoneById(System.Configuration.ConfigurationManager.AppSettings["TimeZoneName"] != null ? System.Configuration.ConfigurationManager.AppSettings["TimeZoneName"].ToString() : "Central Asia Standard Time");
            }
        }
        //public static DateTime CurrentDateTime()
        //{
        //    return TimeZoneInfo.ConvertTime(DateTime.Now, TimeZoneInfo);
        //}

        public static DateTime ToLocalTime(DateTime date)
        {
            return TimeZoneInfo.ConvertTime(date, TimeZoneInfo);
        }

        public static DateTime UnspecifiedToUTC(DateTime date)
        {
            var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById("Central Asia Standard Time");
            var dateTimeUnspec = DateTime.SpecifyKind(date, DateTimeKind.Unspecified);
            return TimeZoneInfo.ConvertTimeToUtc(dateTimeUnspec, timeZoneInfo);
        }

        public static DateTime UnspecifiedUTCToLocal(DateTime date)
        {
            var dateTimeUnspec = DateTime.SpecifyKind(date, DateTimeKind.Utc);
            return TimeZoneInfo.ConvertTime(dateTimeUnspec, TimeZoneInfo);
        }

        public static DateTime UTCToLocal(DateTime date)
        {
            return TimeZoneInfo.ConvertTime(date, TimeZoneInfo);
        }

        public static DateTime UTCToLocal(DateTime dt, int timezoneOffset)
        {
            DateTime newDate = dt - new TimeSpan(timezoneOffset / 60, timezoneOffset % 60, 0);
            return DateTime.SpecifyKind(newDate, DateTimeKind.Local);
        }

        public static string GenerateCode(string lastCode, string prefix, int padLeft)
        {
            string newCode;
            if (!string.IsNullOrEmpty(lastCode))
            {
                int onlyLastPart = int.Parse(lastCode.Replace(prefix, ""));
                onlyLastPart++;
                newCode = prefix + onlyLastPart.ToString().PadLeft(padLeft, '0');
            }
            else
            {
                newCode = prefix + 1.ToString().PadLeft(padLeft, '0');
            }
            return newCode;
        }

        public static int RandomNumber(int min, int max)
        {
            Random random = new Random();
            return random.Next(min, max);
        }

        public static byte[] GetBytesFromImage(string imagePath)
        {
            if (File.Exists(imagePath))
            {
                return File.ReadAllBytes(imagePath);
            }
            throw new Exception("File not exist in: " + imagePath);
        }

        public static string RandomString(int size, bool lowerCase)
        {
            StringBuilder builder = new StringBuilder();
            Random random = new Random();
            char ch;
            for (int i = 0; i < size; i++)
            {
                ch = Convert.ToChar(Convert.ToInt32(Math.Floor(26 * random.NextDouble() + 65)));
                builder.Append(ch);
            }
            if (lowerCase)
                return builder.ToString().ToLower();
            return builder.ToString();
        }

        public static string RandomString(int length)
        {
            Random random = new Random();
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            return new string(Enumerable.Repeat(chars, length).Select(s => s[random.Next(s.Length)]).ToArray());
        }

        public static string RandomStringWithDigitAndLetter(int size)
        {
            Random random = new Random();
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var stringChars = new char[size];
            stringChars = Enumerable.Repeat(chars, size)
              .Select(s => s[random.Next(s.Length)]).ToArray();
            return new String(stringChars);
        }

        public static bool IsValidEmail(string emailaddress)
        {
            try
            {
                var m = new System.Net.Mail.MailAddress(emailaddress);

                return true;
            }
            catch (FormatException)
            {
                return false;
            }
        }

        public static string Convert_StringvalueToHexvalue(string stringvalue, System.Text.Encoding encoding)
        {
            Byte[] stringBytes = encoding.GetBytes(stringvalue);
            StringBuilder sbBytes = new StringBuilder(stringBytes.Length * 2);
            foreach (byte b in stringBytes)
            {
                sbBytes.AppendFormat("{0:X2}", b);
            }
            return sbBytes.ToString();
        }

        public static string Convert_HexvalueToStringvalue(string hexvalue, Encoding encoding)
        {
            int CharsLength = hexvalue.Length;
            byte[] bytesarray = new byte[CharsLength / 2];
            for (int i = 0; i < CharsLength; i += 2)
            {
                bytesarray[i / 2] = Convert.ToByte(hexvalue.Substring(i, 2), 16);
            }
            return encoding.GetString(bytesarray);
        }

        public static bool VarifyHexvalue(string hexvalue, Encoding encoding, string stringvalue)
        {
            int CharsLength = hexvalue.Length;
            byte[] bytesarray = new byte[CharsLength / 2];
            for (int i = 0; i < CharsLength; i += 2)
            {
                bytesarray[i / 2] = Convert.ToByte(hexvalue.Substring(i, 2), 16);
            }
            var plaintext = encoding.GetString(bytesarray);
            return plaintext == stringvalue;
        }
        private static Image ScaleImageWithRatio(System.Web.HttpPostedFile imgFile, int maxWidth, int maxHeight)
        {
            Image image = Image.FromStream(imgFile.InputStream, true, true);
            var ratioX = (double)maxWidth / image.Width;
            var ratioY = (double)maxHeight / image.Height;
            var ratio = Math.Min(ratioX, ratioY);

            var newWidth = (int)(image.Width * ratio);
            var newHeight = (int)(image.Height * ratio);

            var newImage = new Bitmap(newWidth, newHeight);

            using (var graphics = Graphics.FromImage(newImage))
                graphics.DrawImage(image, 0, 0, newWidth, newHeight);

            return newImage;
        }

        private static Image ScaleImage(System.Web.HttpPostedFile imgFile, int width, int height)
        {
            Image image = Image.FromStream(imgFile.InputStream, true, true);

            var newImage = new Bitmap(width, height);

            using (var graphics = Graphics.FromImage(newImage))
                graphics.DrawImage(image, 0, 0, width, height);

            return newImage;
        }

        public static string SaveImage(string imageName, string partialPath, System.Web.HttpPostedFile hpf, string oldPath, int? width = null, int? height = null, bool maintainRatio = false)
        {
            try
            {
                string serverPath = System.Web.Hosting.HostingEnvironment.MapPath("~"), extension, imagePath;

                if (!Directory.Exists(serverPath + partialPath)) Directory.CreateDirectory(serverPath + partialPath);
                if (!string.IsNullOrEmpty(oldPath) && File.Exists(serverPath + oldPath)) File.Delete(serverPath + oldPath);

                extension = Path.GetExtension(hpf.FileName);
                if (new[] { ".jpg", ".jpeg", ".png" }.Contains(extension.ToLower()))
                {
                    if (hpf.ContentType == "image/jpeg" || hpf.ContentType == "image/png")
                    {
                        if (hpf.ContentLength > 102400000)
                        {
                            throw new Exception("File size exceeded. Max file size is 100MB. Your selected file size is " + hpf.ContentLength / 1000 + ".");
                        }
                        imagePath = partialPath + imageName + extension;

                        if (File.Exists(serverPath + imagePath))
                            File.Delete(serverPath + imagePath);

                        if (width.HasValue && height.HasValue)
                        {
                            var bitmap = maintainRatio ? ScaleImageWithRatio(hpf, width.Value, height.Value) : ScaleImage(hpf, width.Value, height.Value);
                            //var imgEncoder = GetEncoder(hpf.ContentType == "image/jpeg"? System.Drawing.Imaging.ImageFormat.Jpeg : System.Drawing.Imaging.ImageFormat.Png);

                            //var encoderParameters = new System.Drawing.Imaging.EncoderParameters(1);

                            //encoderParameters.Param[0] = new System.Drawing.Imaging.EncoderParameter(System.Drawing.Imaging.Encoder.Quality, 40);
                            bitmap.Save(serverPath + imagePath, bitmap.RawFormat);
                        }
                        else
                            hpf.SaveAs(serverPath + imagePath);

                        return imagePath;
                    }
                    else
                        throw new Exception("Only jpeg or png images are allowed to upload. Your selected file format is " + hpf.ContentType + ".");
                }
                else throw new Exception("Only jpeg or png images are allowed to upload.");
            }
            catch (Exception ex)
            {
                throw new Exception("SaveImage Error: " + ex.Message);
            }
        }

        public static string RemoveImage(string oldPath)
        {
            try
            {
                string serverPath = System.Web.Hosting.HostingEnvironment.MapPath("~");

                if (!string.IsNullOrEmpty(oldPath) && File.Exists(serverPath + oldPath)) File.Delete(serverPath + oldPath);
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception("SaveImage Error: " + ex.Message);
            }
        }
        public static DateTime CreateDateTime(DateTime date, string time)
        {
            int year = date.Year;
            int month = date.Month;
            int day = date.Day;
            int hour = Convert.ToInt32(time.Split(':')[0]);
            int minutes = Convert.ToInt32(time.Split(' ')[0].Split(':')[1]);
            String format = time.Split(' ')[1];

            DateTime dateTime = new DateTime(year,
                                  month,
                                  day,
                                  (format.ToUpperInvariant() == "PM" && hour < 12) ?
                                      hour + 12 : hour,
                                  minutes,
                                  00);

            return dateTime;
        }

    }
}
