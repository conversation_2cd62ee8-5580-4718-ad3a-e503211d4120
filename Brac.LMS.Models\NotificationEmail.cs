﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public enum SendStatus { Pending, Sent, Failed, Hold }

    public class NotificationEmail : Entity<PERSON>ield
    {
        public Guid NotificationId { get; set; }
        public virtual Notification Notification { get; set; }

        public SendStatus SendStatus { get; set; }
        public int AttemptLimit { get; set; } = 3;
        public DateTime? LastTryOn { get; set; }
        public DateTime? SentOn { get; set; }
    }
}
