﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Microsoft.AspNet.Identity.Owin;
using Microsoft.Graph.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [Authorize(Roles = "Admin"), RoutePrefix("api/open-material")]
    public class OpenMaterialController : ApplicationController
    {
        private readonly IOpenMaterialService _service;

        public OpenMaterialController()
        {
            _service = new OpenMaterialService();
        }
        public ApplicationUserManager UserManager
        {
            get
            {
                return Request.GetOwinContext().GetUserManager<ApplicationUserManager>();
            }
        }


        [HttpPost, Route("create-or-update")]
        public async Task<IHttpActionResult> MaterialCreateOrUpdate()
        {
            try
            {
                var _nservice = new OpenMaterialService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
                var model = JsonConvert.DeserializeObject<OpenMaterialModel>(System.Web.HttpContext.Current.Request.Form["Model"]);
                return Ok(await _nservice.MaterialCreateOrUpdate(model, User.Identity));
            }
            catch (Exception ex)
            {
                return Ok(new { Success = false, ex.Message });
            }

        }


        [HttpGet, Route("get/{id}")]
        public async Task<IHttpActionResult> GetMaterialById(Guid id)
        {
            return Ok(await _service.GetMaterialById(id));
        }


        [HttpGet, Route("list")]
        public async Task<IHttpActionResult> GetMaterialList(string name, long? categoryId, int size, int pageNumber)
        {
            return Ok(await _service.GetMaterialList(name, categoryId, size, pageNumber));
        }


        [HttpGet, Route("delete/{id}")]
        public async Task<IHttpActionResult> DeleteMaterialById(Guid id)
        {
            return Ok(await _service.DeleteMaterialById(id));
        }
        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("save-trainee-evaluation-activity")]
        public async Task<IHttpActionResult> SaveEvaluationActivity(string title, string contentType)
        {
            var _service = new OpenMaterialService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.SaveEvaluationActivity(title,contentType, CurrentUser));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get-time-wise-learning-hour-study-report")]
        public async Task<HttpResponseMessage> GetTimeWiseEvaluationStudyReport(DateTime startDate, DateTime endDate, ReportType reportType)
        {
            try
            {
                startDate = startDate.ToKindUtc();
                endDate = endDate.ToKindUtc();
                var _service = new OpenMaterialService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
                byte[] byteArray = reportType == ReportType.Excel ? await _service.GetTimeWiseEvaluationStudyReportExcel(startDate, endDate) : await _service.GetTimeWiseEvaluationStudyReportPdf(startDate, endDate);
                //Create a new response.
                var response = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    //Assign byte array to response content.
                    Content = new ByteArrayContent(byteArray)
                };

                switch (reportType)
                {
                    case ReportType.Pdf:
                        //Set MIME type.
                        response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
                        break;
                    case ReportType.Excel:
                        //Set MIME type.
                        response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                        break;
                }
                //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.

                //    return response;
                return response;
            }
            catch (Exception)
            {
                return new HttpResponseMessage(HttpStatusCode.BadRequest);
            }
        }
    }
}
