﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class MCQAnswer : NumberEntityField
    {
        public long QuestionId { get; set; }
        public virtual MCQQuestion Question { get; set; }
        public string Answered { get; set; }

        public float Mark { get; set; }

        public Guid TraineeExamId { get; set; }
        public virtual TraineeExam TraineeExam { get; set; }

        public DateTime EntryDate { get; set; }
    }

    public class TrueFalseAnswer : NumberEntityField
    {
        public long QuestionId { get; set; }
        public virtual TrueFalseQuestion Question { get; set; }

        public bool Answered { get; set; }

        public float Mark { get; set; }

        public Guid TraineeExamId { get; set; }
        public virtual TraineeExam TraineeExam { get; set; }

        public DateTime EntryDate { get; set; }
    }

    public class FIGAnswer : NumberEntityField
    {
        public long QuestionId { get; set; }
        public virtual FIGQuestion Question { get; set; }
        public string Answered { get; set; }

        public float Mark { get; set; }

        public Guid TraineeExamId { get; set; }
        public virtual TraineeExam TraineeExam { get; set; }

        public DateTime EntryDate { get; set; }
    }

    public class MatchingAnswer : NumberEntityField
    {
        public long QuestionId { get; set; }
        public virtual MatchingQuestion Question { get; set; }
        public string RightSide { get; set; }

        public float Mark { get; set; }

        public Guid TraineeExamId { get; set; }
        public virtual TraineeExam TraineeExam { get; set; }

        public DateTime EntryDate { get; set; }
    }

    public class WrittenAnswer : NumberEntityField
    {
        public long QuestionId { get; set; }
        public virtual WrittenQuestion Question { get; set; }
        public string Answered { get; set; }

        public float Mark { get; set; }

        public Guid TraineeExamId { get; set; }
        public virtual TraineeExam TraineeExam { get; set; }

        public DateTime EntryDate { get; set; }
    }


    public class MCQMockTestAnswer : NumberEntityField
    {
        public long QuestionId { get; set; }
        public virtual MockTestMCQQuestion Question { get; set; }
        public string Answered { get; set; }

        public float Mark { get; set; }

        public Guid TraineeMockTestId { get; set; }
        public virtual TraineeMockTest TraineeMockTest { get; set; }

        public DateTime EntryDate { get; set; }
    }





    public class MCQEvaluationAnswer : NumberEntityField
    {
        public long QuestionId { get; set; }
        public virtual MCQEvaluationQuestion Question { get; set; }
        public string Answered { get; set; }

        public float Mark { get; set; }

        public Guid TraineeExamId { get; set; }
        public virtual TraineeEvaluationExam TraineeExam { get; set; }

        public DateTime EntryDate { get; set; }
    }

    public class TrueFalseEvaluationAnswer : NumberEntityField
    {
        public long QuestionId { get; set; }
        public virtual TrueFalseEvaluationQuestion Question { get; set; }

        public bool Answered { get; set; }

        public float Mark { get; set; }

        public Guid TraineeExamId { get; set; }
        public virtual TraineeEvaluationExam TraineeExam { get; set; }

        public DateTime EntryDate { get; set; }
    }

    public class FIGEvaluationAnswer : NumberEntityField
    {
        public long QuestionId { get; set; }
        public virtual FIGEvaluationQuestion Question { get; set; }

        public string Answered { get; set; }

        public float Mark { get; set; }

        public Guid TraineeExamId { get; set; }
        public virtual TraineeEvaluationExam TraineeExam { get; set; }

        public DateTime EntryDate { get; set; }
    }

    public class MatchingEvaluationAnswer : NumberEntityField
    {
        public long QuestionId { get; set; }
        public virtual MatchingEvaluationQuestion Question { get; set; }

        public string RightSide { get; set; }

        public float Mark { get; set; }

        public Guid TraineeExamId { get; set; }
        public virtual TraineeEvaluationExam TraineeExam { get; set; }

        public DateTime EntryDate { get; set; }
    }

    public class WrittenEvaluationAnswer : NumberEntityField
    {
        public long QuestionId { get; set; }
        public virtual WrittenEvaluationQuestion Question { get; set; }
        public string Answered { get; set; }

        public float Mark { get; set; }

        public Guid TraineeExamId { get; set; }
        public virtual TraineeEvaluationExam TraineeExam { get; set; }

        public DateTime EntryDate { get; set; }
    }
}
