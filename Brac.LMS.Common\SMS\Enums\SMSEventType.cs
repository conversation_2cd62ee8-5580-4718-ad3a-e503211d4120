namespace Brac.LMS.Common.SMS.Enums
{
    /// <summary>
    /// Represents different types of SMS notifications in the LMS system
    /// </summary>
    public enum SMSEventType
    {
        // Authentication Related
        PasswordResetOTP = 11,

        // Course Enrollment
        EnrollCourse = 12,
        EnrollTraineeToCourse = 13,
        EnrollTeammateToCourse = 14,

        // Course Progress
        ContentStudy = 15,
        CertificationTestComplete = 16,
        CertificationResultPublish = 17,
        MockTestComplete = 18,

        // Reminders
        NoProgressReminder = 19,
        LastWeekReminder = 20,
        LastDayReminder = 21,
        FailedToCompleteReminder = 22,
        OneWeekRemain = 23,
        CertificateExpiryReminder = 24,

        PendingNotification = 25,
    }
}