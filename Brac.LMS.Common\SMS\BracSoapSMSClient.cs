using System;
using System.Configuration;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using Brac.LMS.Common.SMS.Models;

namespace Brac.LMS.Common.SMS
{
    public class BracSoapSMSClient
    {
        private const string BASE_URL = "https://esbuat01.bracbank.com:4444/";
        private const string ENDPOINT_PATH = "UBA/SendNotification/V1";
        private readonly string _password;
        private readonly string _channelName;
        private readonly string _channelCode;
        private readonly HttpClient _httpClient;

        public BracSoapSMSClient()
        {
            // Only these three values come from config as per API doc section 3.1
            _password = ConfigurationManager.AppSettings["SMSService:Password"];
            _channelName = ConfigurationManager.AppSettings["SMSService:ChannelName"];
            _channelCode = ConfigurationManager.AppSettings["SMSService:ChannelCode"];

            if (string.IsNullOrEmpty(_password) || string.IsNullOrEmpty(_channelName) || string.IsNullOrEmpty(_channelCode))
                throw new ConfigurationErrorsException("SMS service credentials not properly configured");

            _httpClient = new HttpClient();
            _httpClient.BaseAddress = new Uri(BASE_URL);
        }

        public async Task<bool> SendMessageAsync(string mobileNumber, string message, string smsCode)
        {
            var request = new NotificationRequest
            {
                Password = _password,
                ChannelName = _channelName,
                ChannelCode = _channelCode,
                MobileNumber = mobileNumber,
                SmsCode = smsCode,
                SmsTemplate = message
            };

            var soapEnvelope = CreateSoapEnvelope(request);
            var content = new StringContent(soapEnvelope, Encoding.UTF8, "text/xml");

            LogControl.Write($"SMS Request: {soapEnvelope}");
            var response = await _httpClient.PostAsync(ENDPOINT_PATH, content);
            var responseContent = await response.Content.ReadAsStringAsync();
            LogControl.Write($"SMS Response: {responseContent}");

            var xDoc = XDocument.Parse(responseContent);
            var ns = (XNamespace)"http://bracbank.com/UBASendNotificationES/V1";
            var errorCode = xDoc.Descendants(ns + "errorCode").FirstOrDefault()?.Value;

            return errorCode == "000";
        }

        private string CreateSoapEnvelope(NotificationRequest request)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss");
            var transactionId = $"BracALO{DateTime.Now:yyyyMMddHHmmss}{new Random().Next(1000, 9999)}";

            // Ensure mobile number has country code
            var mobileNumber = request.MobileNumber.StartsWith("+") 
                ? request.MobileNumber 
                : $"+880{request.MobileNumber.TrimStart('0')}";
            LogControl.Write($"");
            return $@"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:v1=""http://bracbank.com/UBASendNotificationES/V1"" xmlns:v11=""http://bracbank.com/utilityCommon/V1"">
    <soapenv:Header/>
    <soapenv:Body>
        <v1:sendNotification>
            <v1:sendNotificationDetails>
                <v1:header>
                    <v11:serviceId>{request.ServiceId}</v11:serviceId>
                    <v11:serviceName>{request.ServiceName}</v11:serviceName>
                    <v11:sourceTimestamp>{timestamp}</v11:sourceTimestamp>
                    <v11:password>{request.Password}</v11:password>
                    <v11:channelName>{request.ChannelName}</v11:channelName>
                    <v11:channelCode>{request.ChannelCode}</v11:channelCode>
                    <v11:globalTransactionId>{transactionId}</v11:globalTransactionId>
                </v1:header>
                <v1:mobileNumber>{mobileNumber}</v1:mobileNumber>
                <v1:smsCode>{request.SmsCode}</v1:smsCode>
                <v1:costCenter>{request.CostCenter}</v1:costCenter>
                <v1:smsTemplate>{request.SmsTemplate}</v1:smsTemplate>
                <v1:smsRemarks>{request.SmsRemarks}</v1:smsRemarks>
            </v1:sendNotificationDetails>
        </v1:sendNotification>
    </soapenv:Body>
</soapenv:Envelope>";
        }
    }
}