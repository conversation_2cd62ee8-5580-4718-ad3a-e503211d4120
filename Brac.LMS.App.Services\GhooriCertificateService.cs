﻿using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity.Validation;
using System.Data.Entity;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Brac.LMS.DB;
using System.IO;
using System.Web.Hosting;
using Microsoft.AspNet.Identity;

namespace Brac.LMS.App.Services
{
    public class GhooriCertificateService : IGhooriCertificateService
    {
        private readonly ApplicationDbContext _context;

        public GhooriCertificateService()
        {
            _context = new ApplicationDbContext();
        }
        public async Task<APIResponse> CertificateCreateOrUpdate(GhooriCertificateModel model, IIdentity identity)
        {
           
        GhooriCertificate item = null;

            bool isEdit = true;
            try
            {
                if (await _context.GhooriCertificates.AnyAsync(x => x.Id != model.Id && x.CourseName == model.CourseName && x.TraineePIN == model.TraineePIN))
                    throw new Exception("Already exists in this course "+ model.CourseName+" with this trainee: " + model.TraineeName +" - "+model.TraineePIN  );
                var file = HttpContext.Current.Request.Files.Count > 0 ? HttpContext.Current.Request.Files[0] : null;

                if (model.Id != null)
                {
                    item = await _context.GhooriCertificates.FindAsync(model.Id);
                    if (item == null) throw new Exception("Certificate not found");

                    if (file != null && file.ContentLength > 0)
                    {
                        item = SaveDocumentFile("DOC" + Utility.RandomString(4, false) + DateTime.Now.ToString("yyyMMddHHmmss"), "/Files/GhooriCertificate/", HttpContext.Current.Request.Files[0], item);
                    }
                }
                else
                {
                    item = new GhooriCertificate();
                    isEdit = false;
                    if (file != null && file.ContentLength > 0)
                    {
                        item = SaveDocumentFile("DOC" + Utility.RandomString(4, false) + DateTime.Now.ToString("yyyMMddHHmmss"), "/Files/GhooriCertificate/", HttpContext.Current.Request.Files[0], item);
                        //// Get the file name and extension
                        //var fileName = Path.GetFileName(file.FileName);
                        //var extension = Path.GetExtension(fileName);

                        //// Generate a unique file name
                        //var uniqueFileName = Guid.NewGuid().ToString() + extension;

                        //// Combine the path where you want to save the file
                        //var filePath = Path.Combine(HttpContext.Current.Server.MapPath("~/App_Data/CertificationFiles"), uniqueFileName);

                        //// Save the file to the server
                        //file.SaveAs(filePath);
                    }
                    else 
                        return new APIResponse
                        {
                            Status = ResponseStatus.Warning,
                            Message = "No file found"
                        };
                }




                item.CourseName = model.CourseName;
                item.TraineeName = model.TraineeName;
                item.TraineePIN=model.TraineePIN;
                item.DateOfCertification = model.DateOfCertification;
                item.SetAuditTrailEntity(identity);
                if (!isEdit)
                {
                    item.Id = Guid.NewGuid();
                    _context.GhooriCertificates.Add(item);
                }
                else
                {
                    _context.Entry(item).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync();
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                };

            }
            catch (DbEntityValidationException e)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())
                };
            }
            catch (Exception ex)
            {
                return new APIResponse { Status = ResponseStatus.Error, Message = ex.Message };
            }
        }

        public Task<APIResponse> DeleteCertificateById(long id)
        {
            throw new NotImplementedException();
        }

        public Task<APIResponse> GetCertificateById(long id)
        {
            throw new NotImplementedException();
        }

        public async Task<APIResponse> GetCertificateList(IIdentity identity, int size, int pageNumber)
        {
            var userId = identity.GetUserId();
            var user = await _context.Users.Where(x => x.Id == userId).FirstOrDefaultAsync();

            var lists = _context.GhooriCertificates.Where(x => x.TraineePIN == user.UserName).AsQueryable();
            var ghooriCertificatesRaw = await lists.OrderBy(x => x.CreatedDate)
                                              .Skip(pageNumber * size).Take(size).ToListAsync();
            var ghooriCertificates = ghooriCertificatesRaw.Select(x => new
            { 
                x.Id,
                x.CourseName,
                DateOfCertification = x.DateOfCertification.Value.ToLocalTime(),
                x.CertficationPath,
                x.CertficationType,
            });
            var count = ghooriCertificates.Count();
            return new APIResponse
            {
                Status = ResponseStatus.Success,
                Data = new
                {
                    Records = ghooriCertificates,
                    Total = count
                }
            };

        }
        private GhooriCertificate SaveDocumentFile(string fileName, string partialPath, HttpPostedFile hpf, GhooriCertificate item)
        {
            try
            {
                string serverPath = HostingEnvironment.MapPath("~"), extension, filePath;

                if (!Directory.Exists(serverPath + partialPath)) Directory.CreateDirectory(serverPath + partialPath);

                if (!string.IsNullOrEmpty(item.CertficationPath) && File.Exists(serverPath + item.CertficationPath))
                {
                    File.Delete(serverPath + item.CertficationPath);
                }

                string[] fileExtensions = { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".jpg", ".jpeg", ".png" };

                extension = Path.GetExtension(hpf.FileName);
                if (fileExtensions.Contains(extension.ToLower()))
                {
                    if (hpf.ContentLength > 102400000)
                    {
                        throw new Exception("File size exceeded. Max file size is 100MB. Your selected file size is " + hpf.ContentLength / 1000 + ".");
                    }
                    filePath = partialPath + fileName + extension;

                    if (File.Exists(serverPath + filePath))
                        File.Delete(serverPath + filePath);

                    hpf.SaveAs(serverPath + filePath);
                    item.CertficationPath = filePath;
                    item.CertficationType = extension;
                    return item;
                }
                else throw new Exception("Only PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, JPG, JPEG or PNG files are allowed to upload.");
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                throw new Exception("SaveDocumentFile Error: " + ex.Message);
            }
        }
    }
    public interface IGhooriCertificateService
    {
        Task<APIResponse> CertificateCreateOrUpdate(GhooriCertificateModel model, IIdentity identity);
        Task<APIResponse> GetCertificateList(IIdentity identity, int size, int pageNumber);
        Task<APIResponse> GetCertificateById(long id);
        Task<APIResponse> DeleteCertificateById(long id);
    }
}
