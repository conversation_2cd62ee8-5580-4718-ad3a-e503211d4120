﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using Brac.LMS.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [Authorize(Roles = "Admin"), RoutePrefix("api/certificate-configuration")]
    public class CertificateConfigController : ApplicationController
    {
        private readonly ICertificateConfigurationService _service;

        public CertificateConfigController()
        {
            _service = new CertificateConfigurationService();
        }

        [HttpPost, Route("create-or-update")]
        public async Task<IHttpActionResult> CertificateConfigCreateOrUpdate()
        {
            var model = JsonConvert.DeserializeObject<CertificateConfigurationModel>(HttpContext.Current.Request.Form["Model"]);
            var _nservice = new CertificateConfigurationService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.CertificateConfigurationCreateOrUpdate(model, User.Identity));
        }

        [HttpGet, Route("get/{courseId}")]
        public async Task<IHttpActionResult> GetCertificateConfig(Guid courseId)
        {
            return Ok(await _service.GetCertificateConfiguration(courseId));
        }

        [HttpGet, Route("get-versions/{courseId}")]
        public async Task<IHttpActionResult> GetCertificateConfigVersions(Guid courseId) // for future if needed
        {
            return Ok(await _service.GetCertificateConfigurationVersions(courseId));
        }

        [HttpGet, Route("get-templates")]
        public IHttpActionResult GetCertificateTemplates()
        {
            return Ok(Enum.GetNames(typeof(CertificateTemplate)).Select(x => new { Id = x, Name = x.Replace("_", " ") }));
        }
    }
}
