﻿using Brac.LMS.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class SubUnit : NumberEntityField
    {
        [Required, StringLength(250)]
        public string Name { get; set; }
        public bool Active { get; set; }


        private DateTime _CreatedDate;
        public DateTime CreatedDate
        {
            get
            { return _CreatedDate; }
            set
            { _CreatedDate = value.ToKindUtc(); }
        }
    }
}
