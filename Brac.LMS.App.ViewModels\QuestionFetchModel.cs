﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.ViewModels
{
    public class MCQFetchModel
    {
        public long Id { get; set; }
        public string Question { get; set; }
        public string Option1 { get; set; }
        public string Option2 { get; set; }
        public string Option3 { get; set; }
        public string Option4 { get; set; }
        public float Mark { get; set; }
    }

    public class BasicQsFetchModel
    {
        public long Id { get; set; }
        public string Question { get; set; }
        public float Mark { get; set; }
    }

    public class MatchingQsFetchModel
    {
        public long Id { get; set; }
        public string LeftSide { get; set; }
        public string RightSide { get; set; }
        public float Mark { get; set; }
    }
}
