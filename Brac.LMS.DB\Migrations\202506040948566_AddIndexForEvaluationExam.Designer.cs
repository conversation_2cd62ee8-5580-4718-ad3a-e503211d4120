﻿// <auto-generated />
namespace Brac.LMS.DB.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.4.4")]
    public sealed partial class AddIndexForEvaluationExam : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(AddIndexForEvaluationExam));
        
        string IMigrationMetadata.Id
        {
            get { return "202506040948566_AddIndexForEvaluationExam"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
