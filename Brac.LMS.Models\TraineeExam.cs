﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public enum ExamStatus { Attended, Submitted, Examined, Published }

    public class TraineeExam : AuditableEntity
    {
        public TraineeExam()
        {
            this.MCQAnswers = new HashSet<MCQAnswer>();
            this.TrueFalseAnswers = new HashSet<TrueFalseAnswer>();
            this.FIGAnswers = new HashSet<FIGAnswer>();
            this.MatchingAnswers = new HashSet<MatchingAnswer>();
            this.WrittenAnswers = new HashSet<WrittenAnswer>();
        }

        public Guid TraineeId { get; set; }
        public virtual Trainee Trainee { get; set; }

        public Guid ExamId { get; set; }
        public virtual CourseExam Exam { get; set; }

        public int TotalMarks { get; set; }
        public float GainedMarks { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        [StringLength(128)]
        public string CheckerId { get; set; }
        public virtual ApplicationUser Checker { get; set; }

        public DateTime? MarkedOn { get; set; }

        public ExamStatus Status { get; set; }
        public bool AutoSubmission { get; set; }
        public bool? Terminated { get; set; }

        public bool CertificateAchieved { get; set; }

        public Guid? GradingPolicyId { get; set; }
        public virtual GradingPolicy GradingPolicy { get; set; }

        public int GradingGroup { get; set; }
        public GradeResult? Result { get; set; }
        public string Grade { get; set; }
        public int GainedPercentage { get; set; }

        public string CheckerComments { get; set; }
        public int Attempts { get; set; }

        public DateTime? PublishDate { get; set; }

        [StringLength(128)]
        public string PublisherId { get; set; }
        public virtual ApplicationUser Publisher { get; set; }

        public virtual ICollection<MCQAnswer> MCQAnswers { get; set; }
        public virtual ICollection<TrueFalseAnswer> TrueFalseAnswers { get; set; }
        public virtual ICollection<FIGAnswer> FIGAnswers { get; set; }
        public virtual ICollection<MatchingAnswer> MatchingAnswers { get; set; }
        public virtual ICollection<WrittenAnswer> WrittenAnswers { get; set; }

    }
}
