{"ast": null, "code": "import * as i1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport * as i4 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i7 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Input, Optional, Component, ViewEncapsulation, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i6 from '@angular/cdk/layout';\nimport { Breakpoints } from '@angular/cdk/layout';\nimport * as i3 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { ComponentPortal } from '@angular/cdk/portal';\nimport { Subject } from 'rxjs';\nimport { takeUntil, take } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate, keyframes } from '@angular/animations';\nimport * as i5 from '@angular/cdk/bidi';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Animations used by MatTooltip.\n * @docs-private\n */\n\nconst matTooltipAnimations = {\n  /** Animation that transitions a tooltip in and out. */\n  tooltipState: trigger('state', [state('initial, void, hidden', style({\n    opacity: 0,\n    transform: 'scale(0)'\n  })), state('visible', style({\n    transform: 'scale(1)'\n  })), transition('* => visible', animate('200ms cubic-bezier(0, 0, 0.2, 1)', keyframes([style({\n    opacity: 0,\n    transform: 'scale(0)',\n    offset: 0\n  }), style({\n    opacity: 0.5,\n    transform: 'scale(0.99)',\n    offset: 0.5\n  }), style({\n    opacity: 1,\n    transform: 'scale(1)',\n    offset: 1\n  })]))), transition('* => hidden', animate('100ms cubic-bezier(0, 0, 0.2, 1)', style({\n    opacity: 0\n  })))])\n};\n/** Time in ms to throttle repositioning after scroll events. */\n\nconst SCROLL_THROTTLE_MS = 20;\n/**\n * CSS class that will be attached to the overlay panel.\n * @deprecated\n * @breaking-change 13.0.0 remove this variable\n */\n\nconst TOOLTIP_PANEL_CLASS = 'mat-tooltip-panel';\nconst PANEL_CLASS = 'tooltip-panel';\n/** Options used to bind passive event listeners. */\n\nconst passiveListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * Time between the user putting the pointer on a tooltip\n * trigger and the long press event being fired.\n */\n\nconst LONGPRESS_DELAY = 500;\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\n\nfunction getMatTooltipInvalidPositionError(position) {\n  return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n/** Injection token that determines the scroll handling while a tooltip is visible. */\n\n\nconst MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken('mat-tooltip-scroll-strategy');\n/** @docs-private */\n\nfunction MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition({\n    scrollThrottle: SCROLL_THROTTLE_MS\n  });\n}\n/** @docs-private */\n\n\nconst MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY\n};\n/** Injection token to be used to override the default options for `matTooltip`. */\n\nconst MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken('mat-tooltip-default-options', {\n  providedIn: 'root',\n  factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\n\nfunction MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    showDelay: 0,\n    hideDelay: 0,\n    touchendHideDelay: 1500\n  };\n}\n\nclass _MatTooltipBase {\n  constructor(_overlay, _elementRef, _scrollDispatcher, _viewContainerRef, _ngZone, _platform, _ariaDescriber, _focusMonitor, scrollStrategy, _dir, _defaultOptions, _document) {\n    this._overlay = _overlay;\n    this._elementRef = _elementRef;\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewContainerRef = _viewContainerRef;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    this._ariaDescriber = _ariaDescriber;\n    this._focusMonitor = _focusMonitor;\n    this._dir = _dir;\n    this._defaultOptions = _defaultOptions;\n    this._position = 'below';\n    this._disabled = false;\n    this._viewInitialized = false;\n    this._pointerExitEventsInitialized = false;\n    this._viewportMargin = 8;\n    this._cssClassPrefix = 'mat';\n    this._showDelay = this._defaultOptions.showDelay;\n    this._hideDelay = this._defaultOptions.hideDelay;\n    /**\n     * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n     * uses a long press gesture to show and hide, however it can conflict with the native browser\n     * gestures. To work around the conflict, Angular Material disables native gestures on the\n     * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n     * elements). The different values for this option configure the touch event handling as follows:\n     * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n     *   browser gestures on particular elements. In particular, it allows text selection on inputs\n     *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n     * - `on` - Enables touch gestures for all elements and disables native\n     *   browser gestures with no exceptions.\n     * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n     *   showing on touch devices.\n     */\n\n    this.touchGestures = 'auto';\n    this._message = '';\n    /** Manually-bound passive event listeners. */\n\n    this._passiveListeners = [];\n    /** Emits when the component is destroyed. */\n\n    this._destroyed = new Subject();\n    this._scrollStrategy = scrollStrategy;\n    this._document = _document;\n\n    if (_defaultOptions) {\n      if (_defaultOptions.position) {\n        this.position = _defaultOptions.position;\n      }\n\n      if (_defaultOptions.touchGestures) {\n        this.touchGestures = _defaultOptions.touchGestures;\n      }\n    }\n\n    _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      if (this._overlayRef) {\n        this._updatePosition(this._overlayRef);\n      }\n    });\n  }\n  /** Allows the user to define the position of the tooltip relative to the parent element */\n\n\n  get position() {\n    return this._position;\n  }\n\n  set position(value) {\n    var _a;\n\n    if (value !== this._position) {\n      this._position = value;\n\n      if (this._overlayRef) {\n        this._updatePosition(this._overlayRef);\n\n        (_a = this._tooltipInstance) === null || _a === void 0 ? void 0 : _a.show(0);\n\n        this._overlayRef.updatePosition();\n      }\n    }\n  }\n  /** Disables the display of the tooltip. */\n\n\n  get disabled() {\n    return this._disabled;\n  }\n\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value); // If tooltip is disabled, hide immediately.\n\n    if (this._disabled) {\n      this.hide(0);\n    } else {\n      this._setupPointerEnterEventsIfNeeded();\n    }\n  }\n  /** The default delay in ms before showing the tooltip after show is called */\n\n\n  get showDelay() {\n    return this._showDelay;\n  }\n\n  set showDelay(value) {\n    this._showDelay = coerceNumberProperty(value);\n  }\n  /** The default delay in ms before hiding the tooltip after hide is called */\n\n\n  get hideDelay() {\n    return this._hideDelay;\n  }\n\n  set hideDelay(value) {\n    this._hideDelay = coerceNumberProperty(value);\n  }\n  /** The message to be displayed in the tooltip */\n\n\n  get message() {\n    return this._message;\n  }\n\n  set message(value) {\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this._message, 'tooltip'); // If the message is not a string (e.g. number), convert it to a string and trim it.\n    // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n    // away the string-conversion: https://github.com/angular/components/issues/20684\n\n\n    this._message = value != null ? String(value).trim() : '';\n\n    if (!this._message && this._isTooltipVisible()) {\n      this.hide(0);\n    } else {\n      this._setupPointerEnterEventsIfNeeded();\n\n      this._updateTooltipMessage();\n\n      this._ngZone.runOutsideAngular(() => {\n        // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n        // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n        // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n        // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n        Promise.resolve().then(() => {\n          this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n        });\n      });\n    }\n  }\n  /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n\n\n  get tooltipClass() {\n    return this._tooltipClass;\n  }\n\n  set tooltipClass(value) {\n    this._tooltipClass = value;\n\n    if (this._tooltipInstance) {\n      this._setTooltipClass(this._tooltipClass);\n    }\n  }\n\n  ngAfterViewInit() {\n    // This needs to happen after view init so the initial values for all inputs have been set.\n    this._viewInitialized = true;\n\n    this._setupPointerEnterEventsIfNeeded();\n\n    this._focusMonitor.monitor(this._elementRef).pipe(takeUntil(this._destroyed)).subscribe(origin => {\n      // Note that the focus monitor runs outside the Angular zone.\n      if (!origin) {\n        this._ngZone.run(() => this.hide(0));\n      } else if (origin === 'keyboard') {\n        this._ngZone.run(() => this.show());\n      }\n    });\n  }\n  /**\n   * Dispose the tooltip when destroyed.\n   */\n\n\n  ngOnDestroy() {\n    const nativeElement = this._elementRef.nativeElement;\n    clearTimeout(this._touchstartTimeout);\n\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n\n      this._tooltipInstance = null;\n    } // Clean up the event listeners set in the constructor\n\n\n    this._passiveListeners.forEach(([event, listener]) => {\n      nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n    });\n\n    this._passiveListeners.length = 0;\n\n    this._destroyed.next();\n\n    this._destroyed.complete();\n\n    this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n\n    this._focusMonitor.stopMonitoring(nativeElement);\n  }\n  /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n\n\n  show(delay = this.showDelay) {\n    if (this.disabled || !this.message || this._isTooltipVisible() && !this._tooltipInstance._showTimeoutId && !this._tooltipInstance._hideTimeoutId) {\n      return;\n    }\n\n    const overlayRef = this._createOverlay();\n\n    this._detach();\n\n    this._portal = this._portal || new ComponentPortal(this._tooltipComponent, this._viewContainerRef);\n    this._tooltipInstance = overlayRef.attach(this._portal).instance;\n\n    this._tooltipInstance.afterHidden().pipe(takeUntil(this._destroyed)).subscribe(() => this._detach());\n\n    this._setTooltipClass(this._tooltipClass);\n\n    this._updateTooltipMessage();\n\n    this._tooltipInstance.show(delay);\n  }\n  /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n\n\n  hide(delay = this.hideDelay) {\n    if (this._tooltipInstance) {\n      this._tooltipInstance.hide(delay);\n    }\n  }\n  /** Shows/hides the tooltip */\n\n\n  toggle() {\n    this._isTooltipVisible() ? this.hide() : this.show();\n  }\n  /** Returns true if the tooltip is currently visible to the user */\n\n\n  _isTooltipVisible() {\n    return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n  }\n  /** Create the overlay config and position strategy */\n\n\n  _createOverlay() {\n    if (this._overlayRef) {\n      return this._overlayRef;\n    }\n\n    const scrollableAncestors = this._scrollDispatcher.getAncestorScrollContainers(this._elementRef); // Create connected position strategy that listens for scroll events to reposition.\n\n\n    const strategy = this._overlay.position().flexibleConnectedTo(this._elementRef).withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`).withFlexibleDimensions(false).withViewportMargin(this._viewportMargin).withScrollableContainers(scrollableAncestors);\n\n    strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n      this._updateCurrentPositionClass(change.connectionPair);\n\n      if (this._tooltipInstance) {\n        if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n          // After position changes occur and the overlay is clipped by\n          // a parent scrollable then close the tooltip.\n          this._ngZone.run(() => this.hide(0));\n        }\n      }\n    });\n    this._overlayRef = this._overlay.create({\n      direction: this._dir,\n      positionStrategy: strategy,\n      panelClass: `${this._cssClassPrefix}-${PANEL_CLASS}`,\n      scrollStrategy: this._scrollStrategy()\n    });\n\n    this._updatePosition(this._overlayRef);\n\n    this._overlayRef.detachments().pipe(takeUntil(this._destroyed)).subscribe(() => this._detach());\n\n    this._overlayRef.outsidePointerEvents().pipe(takeUntil(this._destroyed)).subscribe(() => {\n      var _a;\n\n      return (_a = this._tooltipInstance) === null || _a === void 0 ? void 0 : _a._handleBodyInteraction();\n    });\n\n    this._overlayRef.keydownEvents().pipe(takeUntil(this._destroyed)).subscribe(event => {\n      if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n        event.preventDefault();\n        event.stopPropagation();\n\n        this._ngZone.run(() => this.hide(0));\n      }\n    });\n\n    return this._overlayRef;\n  }\n  /** Detaches the currently-attached tooltip. */\n\n\n  _detach() {\n    if (this._overlayRef && this._overlayRef.hasAttached()) {\n      this._overlayRef.detach();\n    }\n\n    this._tooltipInstance = null;\n  }\n  /** Updates the position of the current tooltip. */\n\n\n  _updatePosition(overlayRef) {\n    const position = overlayRef.getConfig().positionStrategy;\n\n    const origin = this._getOrigin();\n\n    const overlay = this._getOverlayPosition();\n\n    position.withPositions([this._addOffset(Object.assign(Object.assign({}, origin.main), overlay.main)), this._addOffset(Object.assign(Object.assign({}, origin.fallback), overlay.fallback))]);\n  }\n  /** Adds the configured offset to a position. Used as a hook for child classes. */\n\n\n  _addOffset(position) {\n    return position;\n  }\n  /**\n   * Returns the origin position and a fallback position based on the user's position preference.\n   * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n   */\n\n\n  _getOrigin() {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let originPosition;\n\n    if (position == 'above' || position == 'below') {\n      originPosition = {\n        originX: 'center',\n        originY: position == 'above' ? 'top' : 'bottom'\n      };\n    } else if (position == 'before' || position == 'left' && isLtr || position == 'right' && !isLtr) {\n      originPosition = {\n        originX: 'start',\n        originY: 'center'\n      };\n    } else if (position == 'after' || position == 'right' && isLtr || position == 'left' && !isLtr) {\n      originPosition = {\n        originX: 'end',\n        originY: 'center'\n      };\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n\n    const {\n      x,\n      y\n    } = this._invertPosition(originPosition.originX, originPosition.originY);\n\n    return {\n      main: originPosition,\n      fallback: {\n        originX: x,\n        originY: y\n      }\n    };\n  }\n  /** Returns the overlay position and a fallback position based on the user's preference */\n\n\n  _getOverlayPosition() {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let overlayPosition;\n\n    if (position == 'above') {\n      overlayPosition = {\n        overlayX: 'center',\n        overlayY: 'bottom'\n      };\n    } else if (position == 'below') {\n      overlayPosition = {\n        overlayX: 'center',\n        overlayY: 'top'\n      };\n    } else if (position == 'before' || position == 'left' && isLtr || position == 'right' && !isLtr) {\n      overlayPosition = {\n        overlayX: 'end',\n        overlayY: 'center'\n      };\n    } else if (position == 'after' || position == 'right' && isLtr || position == 'left' && !isLtr) {\n      overlayPosition = {\n        overlayX: 'start',\n        overlayY: 'center'\n      };\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n\n    const {\n      x,\n      y\n    } = this._invertPosition(overlayPosition.overlayX, overlayPosition.overlayY);\n\n    return {\n      main: overlayPosition,\n      fallback: {\n        overlayX: x,\n        overlayY: y\n      }\n    };\n  }\n  /** Updates the tooltip message and repositions the overlay according to the new message length */\n\n\n  _updateTooltipMessage() {\n    // Must wait for the message to be painted to the tooltip so that the overlay can properly\n    // calculate the correct positioning based on the size of the text.\n    if (this._tooltipInstance) {\n      this._tooltipInstance.message = this.message;\n\n      this._tooltipInstance._markForCheck();\n\n      this._ngZone.onMicrotaskEmpty.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n        if (this._tooltipInstance) {\n          this._overlayRef.updatePosition();\n        }\n      });\n    }\n  }\n  /** Updates the tooltip class */\n\n\n  _setTooltipClass(tooltipClass) {\n    if (this._tooltipInstance) {\n      this._tooltipInstance.tooltipClass = tooltipClass;\n\n      this._tooltipInstance._markForCheck();\n    }\n  }\n  /** Inverts an overlay position. */\n\n\n  _invertPosition(x, y) {\n    if (this.position === 'above' || this.position === 'below') {\n      if (y === 'top') {\n        y = 'bottom';\n      } else if (y === 'bottom') {\n        y = 'top';\n      }\n    } else {\n      if (x === 'end') {\n        x = 'start';\n      } else if (x === 'start') {\n        x = 'end';\n      }\n    }\n\n    return {\n      x,\n      y\n    };\n  }\n  /** Updates the class on the overlay panel based on the current position of the tooltip. */\n\n\n  _updateCurrentPositionClass(connectionPair) {\n    const {\n      overlayY,\n      originX,\n      originY\n    } = connectionPair;\n    let newPosition; // If the overlay is in the middle along the Y axis,\n    // it means that it's either before or after.\n\n    if (overlayY === 'center') {\n      // Note that since this information is used for styling, we want to\n      // resolve `start` and `end` to their real values, otherwise consumers\n      // would have to remember to do it themselves on each consumption.\n      if (this._dir && this._dir.value === 'rtl') {\n        newPosition = originX === 'end' ? 'left' : 'right';\n      } else {\n        newPosition = originX === 'start' ? 'left' : 'right';\n      }\n    } else {\n      newPosition = overlayY === 'bottom' && originY === 'top' ? 'above' : 'below';\n    }\n\n    if (newPosition !== this._currentPosition) {\n      const overlayRef = this._overlayRef;\n\n      if (overlayRef) {\n        const classPrefix = `${this._cssClassPrefix}-${PANEL_CLASS}-`;\n        overlayRef.removePanelClass(classPrefix + this._currentPosition);\n        overlayRef.addPanelClass(classPrefix + newPosition);\n      }\n\n      this._currentPosition = newPosition;\n    }\n  }\n  /** Binds the pointer events to the tooltip trigger. */\n\n\n  _setupPointerEnterEventsIfNeeded() {\n    // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n    if (this._disabled || !this.message || !this._viewInitialized || this._passiveListeners.length) {\n      return;\n    } // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n    // first tap from firing its click event or can cause the tooltip to open for clicks.\n\n\n    if (this._platformSupportsMouseEvents()) {\n      this._passiveListeners.push(['mouseenter', () => {\n        this._setupPointerExitEventsIfNeeded();\n\n        this.show();\n      }]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n\n      this._passiveListeners.push(['touchstart', () => {\n        // Note that it's important that we don't `preventDefault` here,\n        // because it can prevent click events from firing on the element.\n        this._setupPointerExitEventsIfNeeded();\n\n        clearTimeout(this._touchstartTimeout);\n        this._touchstartTimeout = setTimeout(() => this.show(), LONGPRESS_DELAY);\n      }]);\n    }\n\n    this._addListeners(this._passiveListeners);\n  }\n\n  _setupPointerExitEventsIfNeeded() {\n    if (this._pointerExitEventsInitialized) {\n      return;\n    }\n\n    this._pointerExitEventsInitialized = true;\n    const exitListeners = [];\n\n    if (this._platformSupportsMouseEvents()) {\n      exitListeners.push(['mouseleave', () => this.hide()], ['wheel', event => this._wheelListener(event)]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n\n      const touchendListener = () => {\n        clearTimeout(this._touchstartTimeout);\n        this.hide(this._defaultOptions.touchendHideDelay);\n      };\n\n      exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n    }\n\n    this._addListeners(exitListeners);\n\n    this._passiveListeners.push(...exitListeners);\n  }\n\n  _addListeners(listeners) {\n    listeners.forEach(([event, listener]) => {\n      this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n    });\n  }\n\n  _platformSupportsMouseEvents() {\n    return !this._platform.IOS && !this._platform.ANDROID;\n  }\n  /** Listener for the `wheel` event on the element. */\n\n\n  _wheelListener(event) {\n    if (this._isTooltipVisible()) {\n      const elementUnderPointer = this._document.elementFromPoint(event.clientX, event.clientY);\n\n      const element = this._elementRef.nativeElement; // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n      // won't fire if the user scrolls away using the wheel without moving their cursor. We\n      // work around it by finding the element under the user's cursor and closing the tooltip\n      // if it's not the trigger.\n\n      if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n        this.hide();\n      }\n    }\n  }\n  /** Disables the native browser gestures, based on how the tooltip has been configured. */\n\n\n  _disableNativeGesturesIfNecessary() {\n    const gestures = this.touchGestures;\n\n    if (gestures !== 'off') {\n      const element = this._elementRef.nativeElement;\n      const style = element.style; // If gestures are set to `auto`, we don't disable text selection on inputs and\n      // textareas, because it prevents the user from typing into them on iOS Safari.\n\n      if (gestures === 'on' || element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA') {\n        style.userSelect = style.msUserSelect = style.webkitUserSelect = style.MozUserSelect = 'none';\n      } // If we have `auto` gestures and the element uses native HTML dragging,\n      // we don't set `-webkit-user-drag` because it prevents the native behavior.\n\n\n      if (gestures === 'on' || !element.draggable) {\n        style.webkitUserDrag = 'none';\n      }\n\n      style.touchAction = 'none';\n      style.webkitTapHighlightColor = 'transparent';\n    }\n  }\n\n}\n\n_MatTooltipBase.ɵfac = function _MatTooltipBase_Factory(t) {\n  i0.ɵɵinvalidFactory();\n};\n\n_MatTooltipBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatTooltipBase,\n  inputs: {\n    position: [\"matTooltipPosition\", \"position\"],\n    disabled: [\"matTooltipDisabled\", \"disabled\"],\n    showDelay: [\"matTooltipShowDelay\", \"showDelay\"],\n    hideDelay: [\"matTooltipHideDelay\", \"hideDelay\"],\n    touchGestures: [\"matTooltipTouchGestures\", \"touchGestures\"],\n    message: [\"matTooltip\", \"message\"],\n    tooltipClass: [\"matTooltipClass\", \"tooltipClass\"]\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatTooltipBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i1.Overlay\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i2.ScrollDispatcher\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.Platform\n    }, {\n      type: i4.AriaDescriber\n    }, {\n      type: i4.FocusMonitor\n    }, {\n      type: undefined\n    }, {\n      type: i5.Directionality\n    }, {\n      type: undefined\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    position: [{\n      type: Input,\n      args: ['matTooltipPosition']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['matTooltipDisabled']\n    }],\n    showDelay: [{\n      type: Input,\n      args: ['matTooltipShowDelay']\n    }],\n    hideDelay: [{\n      type: Input,\n      args: ['matTooltipHideDelay']\n    }],\n    touchGestures: [{\n      type: Input,\n      args: ['matTooltipTouchGestures']\n    }],\n    message: [{\n      type: Input,\n      args: ['matTooltip']\n    }],\n    tooltipClass: [{\n      type: Input,\n      args: ['matTooltipClass']\n    }]\n  });\n})();\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\n\n\nclass MatTooltip extends _MatTooltipBase {\n  constructor(overlay, elementRef, scrollDispatcher, viewContainerRef, ngZone, platform, ariaDescriber, focusMonitor, scrollStrategy, dir, defaultOptions, _document) {\n    super(overlay, elementRef, scrollDispatcher, viewContainerRef, ngZone, platform, ariaDescriber, focusMonitor, scrollStrategy, dir, defaultOptions, _document);\n    this._tooltipComponent = TooltipComponent;\n  }\n\n}\n\nMatTooltip.ɵfac = function MatTooltip_Factory(t) {\n  return new (t || MatTooltip)(i0.ɵɵdirectiveInject(i1.Overlay), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.ScrollDispatcher), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.Platform), i0.ɵɵdirectiveInject(i4.AriaDescriber), i0.ɵɵdirectiveInject(i4.FocusMonitor), i0.ɵɵdirectiveInject(MAT_TOOLTIP_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i5.Directionality, 8), i0.ɵɵdirectiveInject(MAT_TOOLTIP_DEFAULT_OPTIONS, 8), i0.ɵɵdirectiveInject(DOCUMENT));\n};\n\nMatTooltip.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatTooltip,\n  selectors: [[\"\", \"matTooltip\", \"\"]],\n  hostAttrs: [1, \"mat-tooltip-trigger\"],\n  exportAs: [\"matTooltip\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTooltip, [{\n    type: Directive,\n    args: [{\n      selector: '[matTooltip]',\n      exportAs: 'matTooltip',\n      host: {\n        'class': 'mat-tooltip-trigger'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i1.Overlay\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i2.ScrollDispatcher\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.Platform\n    }, {\n      type: i4.AriaDescriber\n    }, {\n      type: i4.FocusMonitor\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_TOOLTIP_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: i5.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_TOOLTIP_DEFAULT_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\nclass _TooltipComponentBase {\n  constructor(_changeDetectorRef) {\n    this._changeDetectorRef = _changeDetectorRef;\n    /** Property watched by the animation framework to show or hide the tooltip */\n\n    this._visibility = 'initial';\n    /** Whether interactions on the page should close the tooltip */\n\n    this._closeOnInteraction = false;\n    /** Subject for notifying that the tooltip has been hidden from the view */\n\n    this._onHide = new Subject();\n  }\n  /**\n   * Shows the tooltip with an animation originating from the provided origin\n   * @param delay Amount of milliseconds to the delay showing the tooltip.\n   */\n\n\n  show(delay) {\n    // Cancel the delayed hide if it is scheduled\n    clearTimeout(this._hideTimeoutId); // Body interactions should cancel the tooltip if there is a delay in showing.\n\n    this._closeOnInteraction = true;\n    this._showTimeoutId = setTimeout(() => {\n      this._visibility = 'visible';\n      this._showTimeoutId = undefined;\n\n      this._onShow(); // Mark for check so if any parent component has set the\n      // ChangeDetectionStrategy to OnPush it will be checked anyways\n\n\n      this._markForCheck();\n    }, delay);\n  }\n  /**\n   * Begins the animation to hide the tooltip after the provided delay in ms.\n   * @param delay Amount of milliseconds to delay showing the tooltip.\n   */\n\n\n  hide(delay) {\n    // Cancel the delayed show if it is scheduled\n    clearTimeout(this._showTimeoutId);\n    this._hideTimeoutId = setTimeout(() => {\n      this._visibility = 'hidden';\n      this._hideTimeoutId = undefined; // Mark for check so if any parent component has set the\n      // ChangeDetectionStrategy to OnPush it will be checked anyways\n\n      this._markForCheck();\n    }, delay);\n  }\n  /** Returns an observable that notifies when the tooltip has been hidden from view. */\n\n\n  afterHidden() {\n    return this._onHide;\n  }\n  /** Whether the tooltip is being displayed. */\n\n\n  isVisible() {\n    return this._visibility === 'visible';\n  }\n\n  ngOnDestroy() {\n    clearTimeout(this._showTimeoutId);\n    clearTimeout(this._hideTimeoutId);\n\n    this._onHide.complete();\n  }\n\n  _animationStart() {\n    this._closeOnInteraction = false;\n  }\n\n  _animationDone(event) {\n    const toState = event.toState;\n\n    if (toState === 'hidden' && !this.isVisible()) {\n      this._onHide.next();\n    }\n\n    if (toState === 'visible' || toState === 'hidden') {\n      this._closeOnInteraction = true;\n    }\n  }\n  /**\n   * Interactions on the HTML body should close the tooltip immediately as defined in the\n   * material design spec.\n   * https://material.io/design/components/tooltips.html#behavior\n   */\n\n\n  _handleBodyInteraction() {\n    if (this._closeOnInteraction) {\n      this.hide(0);\n    }\n  }\n  /**\n   * Marks that the tooltip needs to be checked in the next change detection run.\n   * Mainly used for rendering the initial text before positioning a tooltip, which\n   * can be problematic in components with OnPush change detection.\n   */\n\n\n  _markForCheck() {\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Callback for when the timeout in this.show() gets completed.\n   * This method is only needed by the mdc-tooltip, and so it is only implemented\n   * in the mdc-tooltip, not here.\n   */\n\n\n  _onShow() {}\n\n}\n\n_TooltipComponentBase.ɵfac = function _TooltipComponentBase_Factory(t) {\n  return new (t || _TooltipComponentBase)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\n_TooltipComponentBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _TooltipComponentBase\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_TooltipComponentBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }];\n  }, null);\n})();\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\n\n\nclass TooltipComponent extends _TooltipComponentBase {\n  constructor(changeDetectorRef, _breakpointObserver) {\n    super(changeDetectorRef);\n    this._breakpointObserver = _breakpointObserver;\n    /** Stream that emits whether the user has a handset-sized display.  */\n\n    this._isHandset = this._breakpointObserver.observe(Breakpoints.Handset);\n  }\n\n}\n\nTooltipComponent.ɵfac = function TooltipComponent_Factory(t) {\n  return new (t || TooltipComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i6.BreakpointObserver));\n};\n\nTooltipComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TooltipComponent,\n  selectors: [[\"mat-tooltip-component\"]],\n  hostAttrs: [\"aria-hidden\", \"true\"],\n  hostVars: 2,\n  hostBindings: function TooltipComponent_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"zoom\", ctx._visibility === \"visible\" ? 1 : null);\n    }\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 3,\n  vars: 7,\n  consts: [[1, \"mat-tooltip\", 3, \"ngClass\"]],\n  template: function TooltipComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵlistener(\"@state.start\", function TooltipComponent_Template_div_animation_state_start_0_listener() {\n        return ctx._animationStart();\n      })(\"@state.done\", function TooltipComponent_Template_div_animation_state_done_0_listener($event) {\n        return ctx._animationDone($event);\n      });\n      i0.ɵɵpipe(1, \"async\");\n      i0.ɵɵtext(2);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      let tmp_0_0;\n      i0.ɵɵclassProp(\"mat-tooltip-handset\", (tmp_0_0 = i0.ɵɵpipeBind1(1, 5, ctx._isHandset)) == null ? null : tmp_0_0.matches);\n      i0.ɵɵproperty(\"ngClass\", ctx.tooltipClass)(\"@state\", ctx._visibility);\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate(ctx.message);\n    }\n  },\n  directives: [i7.NgClass],\n  pipes: [i7.AsyncPipe],\n  styles: [\".mat-tooltip-panel{pointer-events:none !important}.mat-tooltip{color:#fff;border-radius:4px;margin:14px;max-width:250px;padding-left:8px;padding-right:8px;overflow:hidden;text-overflow:ellipsis}.cdk-high-contrast-active .mat-tooltip{outline:solid 1px}.mat-tooltip-handset{margin:24px;padding-left:16px;padding-right:16px}\\n\"],\n  encapsulation: 2,\n  data: {\n    animation: [matTooltipAnimations.tooltipState]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tooltip-component',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [matTooltipAnimations.tooltipState],\n      host: {\n        // Forces the element to have a layout in IE and Edge. This fixes issues where the element\n        // won't be rendered if the animations are disabled or there is no web animations polyfill.\n        '[style.zoom]': '_visibility === \"visible\" ? 1 : null',\n        'aria-hidden': 'true'\n      },\n      template: \"<div class=\\\"mat-tooltip\\\"\\n     [ngClass]=\\\"tooltipClass\\\"\\n     [class.mat-tooltip-handset]=\\\"(_isHandset | async)?.matches\\\"\\n     [@state]=\\\"_visibility\\\"\\n     (@state.start)=\\\"_animationStart()\\\"\\n     (@state.done)=\\\"_animationDone($event)\\\">{{message}}</div>\\n\",\n      styles: [\".mat-tooltip-panel{pointer-events:none !important}.mat-tooltip{color:#fff;border-radius:4px;margin:14px;max-width:250px;padding-left:8px;padding-right:8px;overflow:hidden;text-overflow:ellipsis}.cdk-high-contrast-active .mat-tooltip{outline:solid 1px}.mat-tooltip-handset{margin:24px;padding-left:16px;padding-right:16px}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i6.BreakpointObserver\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatTooltipModule {}\n\nMatTooltipModule.ɵfac = function MatTooltipModule_Factory(t) {\n  return new (t || MatTooltipModule)();\n};\n\nMatTooltipModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatTooltipModule\n});\nMatTooltipModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],\n  imports: [[A11yModule, CommonModule, OverlayModule, MatCommonModule], MatCommonModule, CdkScrollableModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTooltipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule],\n      exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n      declarations: [MatTooltip, TooltipComponent],\n      providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_TOOLTIP_DEFAULT_OPTIONS, MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER, MatTooltip, MatTooltipModule, SCROLL_THROTTLE_MS, TOOLTIP_PANEL_CLASS, TooltipComponent, _MatTooltipBase, _TooltipComponentBase, getMatTooltipInvalidPositionError, matTooltipAnimations };", "map": {"version": 3, "sources": ["D:/Office Projects/brac-lms/Brac.LMS.View.Admin/node_modules/@angular/material/fesm2015/tooltip.mjs"], "names": ["i1", "Overlay", "OverlayModule", "i4", "A11yModule", "i7", "DOCUMENT", "CommonModule", "i0", "InjectionToken", "Directive", "Inject", "Input", "Optional", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "NgModule", "MatCommonModule", "i2", "CdkScrollableModule", "coerceBooleanProperty", "coerceNumberProperty", "ESCAPE", "hasModifierKey", "i6", "Breakpoints", "i3", "normalizePassiveListenerOptions", "ComponentPortal", "Subject", "takeUntil", "take", "trigger", "state", "style", "transition", "animate", "keyframes", "i5", "matTooltipAnimations", "tooltipState", "opacity", "transform", "offset", "SCROLL_THROTTLE_MS", "TOOLTIP_PANEL_CLASS", "PANEL_CLASS", "passiveListenerOptions", "passive", "LONGPRESS_DELAY", "getMatTooltipInvalidPositionError", "position", "Error", "MAT_TOOLTIP_SCROLL_STRATEGY", "MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY", "overlay", "scrollStrategies", "reposition", "scrollThrottle", "MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER", "provide", "deps", "useFactory", "MAT_TOOLTIP_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY", "showDelay", "<PERSON><PERSON><PERSON><PERSON>", "touchendHideDelay", "_MatTooltipBase", "constructor", "_overlay", "_elementRef", "_scrollDispatcher", "_viewContainerRef", "_ngZone", "_platform", "_ariaDescriber", "_focusMonitor", "scrollStrategy", "_dir", "_defaultOptions", "_document", "_position", "_disabled", "_viewInitialized", "_pointerExitEventsInitialized", "_viewportMargin", "_cssClassPrefix", "_showDelay", "_hideDelay", "touchGestures", "_message", "_passiveListeners", "_destroyed", "_scrollStrategy", "change", "pipe", "subscribe", "_overlayRef", "_updatePosition", "value", "_a", "_tooltipInstance", "show", "updatePosition", "disabled", "hide", "_setupPointerEnterEventsIfNeeded", "message", "removeDescription", "nativeElement", "String", "trim", "_isTooltipVisible", "_updateTooltipMessage", "runOutsideAngular", "Promise", "resolve", "then", "describe", "tooltipClass", "_tooltipClass", "_setTooltipClass", "ngAfterViewInit", "monitor", "origin", "run", "ngOnDestroy", "clearTimeout", "_touchstartTimeout", "dispose", "for<PERSON>ach", "event", "listener", "removeEventListener", "length", "next", "complete", "stopMonitoring", "delay", "_showTimeoutId", "_hideTimeoutId", "overlayRef", "_createOverlay", "_detach", "_portal", "_tooltipComponent", "attach", "instance", "afterHidden", "toggle", "isVisible", "scrollableAncestors", "getAncestorScrollContainers", "strategy", "flexibleConnectedTo", "withTransformOriginOn", "withFlexibleDimensions", "withViewportMargin", "withScrollableContainers", "position<PERSON><PERSON>es", "_updateCurrentPositionClass", "connectionPair", "scrollableViewProperties", "isOverlayClipped", "create", "direction", "positionStrategy", "panelClass", "detachments", "outsidePointerEvents", "_handleBodyInteraction", "keydownEvents", "keyCode", "preventDefault", "stopPropagation", "has<PERSON>tta<PERSON>", "detach", "getConfig", "_get<PERSON><PERSON>in", "_getOverlayPosition", "withPositions", "_addOffset", "Object", "assign", "main", "fallback", "isLtr", "originPosition", "originX", "originY", "ngDevMode", "x", "y", "_invertPosition", "overlayPosition", "overlayX", "overlayY", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "onMicrotaskEmpty", "newPosition", "_currentPosition", "classPrefix", "removePanelClass", "addPanelClass", "_platformSupportsMouseEvents", "push", "_setupPointerExitEventsIfNeeded", "_disableNativeGesturesIfNecessary", "setTimeout", "_addListeners", "exitListeners", "_wheelListener", "touchendListener", "listeners", "addEventListener", "IOS", "ANDROID", "elementUnderPointer", "elementFromPoint", "clientX", "clientY", "element", "contains", "gestures", "nodeName", "userSelect", "msUserSelect", "webkitUserSelect", "MozUserSelect", "draggable", "webkitUserDrag", "touchAction", "webkitTapHighlightColor", "ɵfac", "ɵdir", "type", "ElementRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewContainerRef", "NgZone", "Platform", "AriaDescriber", "FocusMonitor", "undefined", "Directionality", "decorators", "args", "MatTooltip", "elementRef", "scroll<PERSON><PERSON><PERSON>tcher", "viewContainerRef", "ngZone", "platform", "ariaDescriber", "focusMonitor", "dir", "defaultOptions", "TooltipComponent", "selector", "exportAs", "host", "_TooltipComponentBase", "_changeDetectorRef", "_visibility", "_closeOnInteraction", "_onHide", "_onShow", "_animationStart", "_animationDone", "toState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ChangeDetectorRef", "changeDetectorRef", "_breakpointObserver", "_isHandset", "observe", "Handset", "BreakpointObserver", "ɵcmp", "Ng<PERSON><PERSON>", "AsyncPipe", "encapsulation", "None", "changeDetection", "OnPush", "animations", "template", "styles", "MatTooltipModule", "ɵmod", "ɵinj", "imports", "exports", "declarations", "providers"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,sBAApB;AACA,SAASC,OAAT,EAAkBC,aAAlB,QAAuC,sBAAvC;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,UAAT,QAA2B,mBAA3B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,QAAT,EAAmBC,YAAnB,QAAuC,iBAAvC;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,SAAzB,EAAoCC,MAApC,EAA4CC,KAA5C,EAAmDC,QAAnD,EAA6DC,SAA7D,EAAwEC,iBAAxE,EAA2FC,uBAA3F,EAAoHC,QAApH,QAAoI,eAApI;AACA,SAASC,eAAT,QAAgC,wBAAhC;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,SAASC,mBAAT,QAAoC,wBAApC;AACA,SAASC,qBAAT,EAAgCC,oBAAhC,QAA4D,uBAA5D;AACA,SAASC,MAAT,EAAiBC,cAAjB,QAAuC,uBAAvC;AACA,OAAO,KAAKC,EAAZ,MAAoB,qBAApB;AACA,SAASC,WAAT,QAA4B,qBAA5B;AACA,OAAO,KAAKC,EAAZ,MAAoB,uBAApB;AACA,SAASC,+BAAT,QAAgD,uBAAhD;AACA,SAASC,eAAT,QAAgC,qBAAhC;AACA,SAASC,OAAT,QAAwB,MAAxB;AACA,SAASC,SAAT,EAAoBC,IAApB,QAAgC,gBAAhC;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,EAAqDC,SAArD,QAAsE,qBAAtE;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;AACA,MAAMC,oBAAoB,GAAG;AACzB;AACAC,EAAAA,YAAY,EAAER,OAAO,CAAC,OAAD,EAAU,CAC3BC,KAAK,CAAC,uBAAD,EAA0BC,KAAK,CAAC;AAAEO,IAAAA,OAAO,EAAE,CAAX;AAAcC,IAAAA,SAAS,EAAE;AAAzB,GAAD,CAA/B,CADsB,EAE3BT,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;AAAEQ,IAAAA,SAAS,EAAE;AAAb,GAAD,CAAjB,CAFsB,EAG3BP,UAAU,CAAC,cAAD,EAAiBC,OAAO,CAAC,kCAAD,EAAqCC,SAAS,CAAC,CAC7EH,KAAK,CAAC;AAAEO,IAAAA,OAAO,EAAE,CAAX;AAAcC,IAAAA,SAAS,EAAE,UAAzB;AAAqCC,IAAAA,MAAM,EAAE;AAA7C,GAAD,CADwE,EAE7ET,KAAK,CAAC;AAAEO,IAAAA,OAAO,EAAE,GAAX;AAAgBC,IAAAA,SAAS,EAAE,aAA3B;AAA0CC,IAAAA,MAAM,EAAE;AAAlD,GAAD,CAFwE,EAG7ET,KAAK,CAAC;AAAEO,IAAAA,OAAO,EAAE,CAAX;AAAcC,IAAAA,SAAS,EAAE,UAAzB;AAAqCC,IAAAA,MAAM,EAAE;AAA7C,GAAD,CAHwE,CAAD,CAA9C,CAAxB,CAHiB,EAQ3BR,UAAU,CAAC,aAAD,EAAgBC,OAAO,CAAC,kCAAD,EAAqCF,KAAK,CAAC;AAAEO,IAAAA,OAAO,EAAE;AAAX,GAAD,CAA1C,CAAvB,CARiB,CAAV;AAFI,CAA7B;AAcA;;AACA,MAAMG,kBAAkB,GAAG,EAA3B;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,mBAAmB,GAAG,mBAA5B;AACA,MAAMC,WAAW,GAAG,eAApB;AACA;;AACA,MAAMC,sBAAsB,GAAGpB,+BAA+B,CAAC;AAAEqB,EAAAA,OAAO,EAAE;AAAX,CAAD,CAA9D;AACA;AACA;AACA;AACA;;AACA,MAAMC,eAAe,GAAG,GAAxB;AACA;AACA;AACA;AACA;;AACA,SAASC,iCAAT,CAA2CC,QAA3C,EAAqD;AACjD,SAAOC,KAAK,CAAE,qBAAoBD,QAAS,eAA/B,CAAZ;AACH;AACD;;;AACA,MAAME,2BAA2B,GAAG,IAAI7C,cAAJ,CAAmB,6BAAnB,CAApC;AACA;;AACA,SAAS8C,mCAAT,CAA6CC,OAA7C,EAAsD;AAClD,SAAO,MAAMA,OAAO,CAACC,gBAAR,CAAyBC,UAAzB,CAAoC;AAAEC,IAAAA,cAAc,EAAEd;AAAlB,GAApC,CAAb;AACH;AACD;;;AACA,MAAMe,4CAA4C,GAAG;AACjDC,EAAAA,OAAO,EAAEP,2BADwC;AAEjDQ,EAAAA,IAAI,EAAE,CAAC7D,OAAD,CAF2C;AAGjD8D,EAAAA,UAAU,EAAER;AAHqC,CAArD;AAKA;;AACA,MAAMS,2BAA2B,GAAG,IAAIvD,cAAJ,CAAmB,6BAAnB,EAAkD;AAClFwD,EAAAA,UAAU,EAAE,MADsE;AAElFC,EAAAA,OAAO,EAAEC;AAFyE,CAAlD,CAApC;AAIA;;AACA,SAASA,mCAAT,GAA+C;AAC3C,SAAO;AACHC,IAAAA,SAAS,EAAE,CADR;AAEHC,IAAAA,SAAS,EAAE,CAFR;AAGHC,IAAAA,iBAAiB,EAAE;AAHhB,GAAP;AAKH;;AACD,MAAMC,eAAN,CAAsB;AAClBC,EAAAA,WAAW,CAACC,QAAD,EAAWC,WAAX,EAAwBC,iBAAxB,EAA2CC,iBAA3C,EAA8DC,OAA9D,EAAuEC,SAAvE,EAAkFC,cAAlF,EAAkGC,aAAlG,EAAiHC,cAAjH,EAAiIC,IAAjI,EAAuIC,eAAvI,EAAwJC,SAAxJ,EAAmK;AAC1K,SAAKX,QAAL,GAAgBA,QAAhB;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACA,SAAKC,iBAAL,GAAyBA,iBAAzB;AACA,SAAKC,iBAAL,GAAyBA,iBAAzB;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACA,SAAKC,cAAL,GAAsBA,cAAtB;AACA,SAAKC,aAAL,GAAqBA,aAArB;AACA,SAAKE,IAAL,GAAYA,IAAZ;AACA,SAAKC,eAAL,GAAuBA,eAAvB;AACA,SAAKE,SAAL,GAAiB,OAAjB;AACA,SAAKC,SAAL,GAAiB,KAAjB;AACA,SAAKC,gBAAL,GAAwB,KAAxB;AACA,SAAKC,6BAAL,GAAqC,KAArC;AACA,SAAKC,eAAL,GAAuB,CAAvB;AACA,SAAKC,eAAL,GAAuB,KAAvB;AACA,SAAKC,UAAL,GAAkB,KAAKR,eAAL,CAAqBf,SAAvC;AACA,SAAKwB,UAAL,GAAkB,KAAKT,eAAL,CAAqBd,SAAvC;AACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACQ,SAAKwB,aAAL,GAAqB,MAArB;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACA;;AACA,SAAKC,iBAAL,GAAyB,EAAzB;AACA;;AACA,SAAKC,UAAL,GAAkB,IAAIlE,OAAJ,EAAlB;AACA,SAAKmE,eAAL,GAAuBhB,cAAvB;AACA,SAAKG,SAAL,GAAiBA,SAAjB;;AACA,QAAID,eAAJ,EAAqB;AACjB,UAAIA,eAAe,CAAC/B,QAApB,EAA8B;AAC1B,aAAKA,QAAL,GAAgB+B,eAAe,CAAC/B,QAAhC;AACH;;AACD,UAAI+B,eAAe,CAACU,aAApB,EAAmC;AAC/B,aAAKA,aAAL,GAAqBV,eAAe,CAACU,aAArC;AACH;AACJ;;AACDX,IAAAA,IAAI,CAACgB,MAAL,CAAYC,IAAZ,CAAiBpE,SAAS,CAAC,KAAKiE,UAAN,CAA1B,EAA6CI,SAA7C,CAAuD,MAAM;AACzD,UAAI,KAAKC,WAAT,EAAsB;AAClB,aAAKC,eAAL,CAAqB,KAAKD,WAA1B;AACH;AACJ,KAJD;AAKH;AACD;;;AACY,MAARjD,QAAQ,GAAG;AACX,WAAO,KAAKiC,SAAZ;AACH;;AACW,MAARjC,QAAQ,CAACmD,KAAD,EAAQ;AAChB,QAAIC,EAAJ;;AACA,QAAID,KAAK,KAAK,KAAKlB,SAAnB,EAA8B;AAC1B,WAAKA,SAAL,GAAiBkB,KAAjB;;AACA,UAAI,KAAKF,WAAT,EAAsB;AAClB,aAAKC,eAAL,CAAqB,KAAKD,WAA1B;;AACA,SAACG,EAAE,GAAG,KAAKC,gBAAX,MAAiC,IAAjC,IAAyCD,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAACE,IAAH,CAAQ,CAAR,CAAlE;;AACA,aAAKL,WAAL,CAAiBM,cAAjB;AACH;AACJ;AACJ;AACD;;;AACY,MAARC,QAAQ,GAAG;AACX,WAAO,KAAKtB,SAAZ;AACH;;AACW,MAARsB,QAAQ,CAACL,KAAD,EAAQ;AAChB,SAAKjB,SAAL,GAAiBjE,qBAAqB,CAACkF,KAAD,CAAtC,CADgB,CAEhB;;AACA,QAAI,KAAKjB,SAAT,EAAoB;AAChB,WAAKuB,IAAL,CAAU,CAAV;AACH,KAFD,MAGK;AACD,WAAKC,gCAAL;AACH;AACJ;AACD;;;AACa,MAAT1C,SAAS,GAAG;AACZ,WAAO,KAAKuB,UAAZ;AACH;;AACY,MAATvB,SAAS,CAACmC,KAAD,EAAQ;AACjB,SAAKZ,UAAL,GAAkBrE,oBAAoB,CAACiF,KAAD,CAAtC;AACH;AACD;;;AACa,MAATlC,SAAS,GAAG;AACZ,WAAO,KAAKuB,UAAZ;AACH;;AACY,MAATvB,SAAS,CAACkC,KAAD,EAAQ;AACjB,SAAKX,UAAL,GAAkBtE,oBAAoB,CAACiF,KAAD,CAAtC;AACH;AACD;;;AACW,MAAPQ,OAAO,GAAG;AACV,WAAO,KAAKjB,QAAZ;AACH;;AACU,MAAPiB,OAAO,CAACR,KAAD,EAAQ;AACf,SAAKxB,cAAL,CAAoBiC,iBAApB,CAAsC,KAAKtC,WAAL,CAAiBuC,aAAvD,EAAsE,KAAKnB,QAA3E,EAAqF,SAArF,EADe,CAEf;AACA;AACA;;;AACA,SAAKA,QAAL,GAAgBS,KAAK,IAAI,IAAT,GAAgBW,MAAM,CAACX,KAAD,CAAN,CAAcY,IAAd,EAAhB,GAAuC,EAAvD;;AACA,QAAI,CAAC,KAAKrB,QAAN,IAAkB,KAAKsB,iBAAL,EAAtB,EAAgD;AAC5C,WAAKP,IAAL,CAAU,CAAV;AACH,KAFD,MAGK;AACD,WAAKC,gCAAL;;AACA,WAAKO,qBAAL;;AACA,WAAKxC,OAAL,CAAayC,iBAAb,CAA+B,MAAM;AACjC;AACA;AACA;AACA;AACAC,QAAAA,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;AACzB,eAAK1C,cAAL,CAAoB2C,QAApB,CAA6B,KAAKhD,WAAL,CAAiBuC,aAA9C,EAA6D,KAAKF,OAAlE,EAA2E,SAA3E;AACH,SAFD;AAGH,OARD;AASH;AACJ;AACD;;;AACgB,MAAZY,YAAY,GAAG;AACf,WAAO,KAAKC,aAAZ;AACH;;AACe,MAAZD,YAAY,CAACpB,KAAD,EAAQ;AACpB,SAAKqB,aAAL,GAAqBrB,KAArB;;AACA,QAAI,KAAKE,gBAAT,EAA2B;AACvB,WAAKoB,gBAAL,CAAsB,KAAKD,aAA3B;AACH;AACJ;;AACDE,EAAAA,eAAe,GAAG;AACd;AACA,SAAKvC,gBAAL,GAAwB,IAAxB;;AACA,SAAKuB,gCAAL;;AACA,SAAK9B,aAAL,CACK+C,OADL,CACa,KAAKrD,WADlB,EAEKyB,IAFL,CAEUpE,SAAS,CAAC,KAAKiE,UAAN,CAFnB,EAGKI,SAHL,CAGe4B,MAAM,IAAI;AACrB;AACA,UAAI,CAACA,MAAL,EAAa;AACT,aAAKnD,OAAL,CAAaoD,GAAb,CAAiB,MAAM,KAAKpB,IAAL,CAAU,CAAV,CAAvB;AACH,OAFD,MAGK,IAAImB,MAAM,KAAK,UAAf,EAA2B;AAC5B,aAAKnD,OAAL,CAAaoD,GAAb,CAAiB,MAAM,KAAKvB,IAAL,EAAvB;AACH;AACJ,KAXD;AAYH;AACD;AACJ;AACA;;;AACIwB,EAAAA,WAAW,GAAG;AACV,UAAMjB,aAAa,GAAG,KAAKvC,WAAL,CAAiBuC,aAAvC;AACAkB,IAAAA,YAAY,CAAC,KAAKC,kBAAN,CAAZ;;AACA,QAAI,KAAK/B,WAAT,EAAsB;AAClB,WAAKA,WAAL,CAAiBgC,OAAjB;;AACA,WAAK5B,gBAAL,GAAwB,IAAxB;AACH,KANS,CAOV;;;AACA,SAAKV,iBAAL,CAAuBuC,OAAvB,CAA+B,CAAC,CAACC,KAAD,EAAQC,QAAR,CAAD,KAAuB;AAClDvB,MAAAA,aAAa,CAACwB,mBAAd,CAAkCF,KAAlC,EAAyCC,QAAzC,EAAmDxF,sBAAnD;AACH,KAFD;;AAGA,SAAK+C,iBAAL,CAAuB2C,MAAvB,GAAgC,CAAhC;;AACA,SAAK1C,UAAL,CAAgB2C,IAAhB;;AACA,SAAK3C,UAAL,CAAgB4C,QAAhB;;AACA,SAAK7D,cAAL,CAAoBiC,iBAApB,CAAsCC,aAAtC,EAAqD,KAAKF,OAA1D,EAAmE,SAAnE;;AACA,SAAK/B,aAAL,CAAmB6D,cAAnB,CAAkC5B,aAAlC;AACH;AACD;;;AACAP,EAAAA,IAAI,CAACoC,KAAK,GAAG,KAAK1E,SAAd,EAAyB;AACzB,QAAI,KAAKwC,QAAL,IACA,CAAC,KAAKG,OADN,IAEC,KAAKK,iBAAL,MACG,CAAC,KAAKX,gBAAL,CAAsBsC,cAD1B,IAEG,CAAC,KAAKtC,gBAAL,CAAsBuC,cAJ/B,EAIgD;AAC5C;AACH;;AACD,UAAMC,UAAU,GAAG,KAAKC,cAAL,EAAnB;;AACA,SAAKC,OAAL;;AACA,SAAKC,OAAL,GACI,KAAKA,OAAL,IAAgB,IAAIvH,eAAJ,CAAoB,KAAKwH,iBAAzB,EAA4C,KAAKzE,iBAAjD,CADpB;AAEA,SAAK6B,gBAAL,GAAwBwC,UAAU,CAACK,MAAX,CAAkB,KAAKF,OAAvB,EAAgCG,QAAxD;;AACA,SAAK9C,gBAAL,CACK+C,WADL,GAEKrD,IAFL,CAEUpE,SAAS,CAAC,KAAKiE,UAAN,CAFnB,EAGKI,SAHL,CAGe,MAAM,KAAK+C,OAAL,EAHrB;;AAIA,SAAKtB,gBAAL,CAAsB,KAAKD,aAA3B;;AACA,SAAKP,qBAAL;;AACA,SAAKZ,gBAAL,CAAsBC,IAAtB,CAA2BoC,KAA3B;AACH;AACD;;;AACAjC,EAAAA,IAAI,CAACiC,KAAK,GAAG,KAAKzE,SAAd,EAAyB;AACzB,QAAI,KAAKoC,gBAAT,EAA2B;AACvB,WAAKA,gBAAL,CAAsBI,IAAtB,CAA2BiC,KAA3B;AACH;AACJ;AACD;;;AACAW,EAAAA,MAAM,GAAG;AACL,SAAKrC,iBAAL,KAA2B,KAAKP,IAAL,EAA3B,GAAyC,KAAKH,IAAL,EAAzC;AACH;AACD;;;AACAU,EAAAA,iBAAiB,GAAG;AAChB,WAAO,CAAC,CAAC,KAAKX,gBAAP,IAA2B,KAAKA,gBAAL,CAAsBiD,SAAtB,EAAlC;AACH;AACD;;;AACAR,EAAAA,cAAc,GAAG;AACb,QAAI,KAAK7C,WAAT,EAAsB;AAClB,aAAO,KAAKA,WAAZ;AACH;;AACD,UAAMsD,mBAAmB,GAAG,KAAKhF,iBAAL,CAAuBiF,2BAAvB,CAAmD,KAAKlF,WAAxD,CAA5B,CAJa,CAKb;;;AACA,UAAMmF,QAAQ,GAAG,KAAKpF,QAAL,CACZrB,QADY,GAEZ0G,mBAFY,CAEQ,KAAKpF,WAFb,EAGZqF,qBAHY,CAGW,IAAG,KAAKrE,eAAgB,UAHnC,EAIZsE,sBAJY,CAIW,KAJX,EAKZC,kBALY,CAKO,KAAKxE,eALZ,EAMZyE,wBANY,CAMaP,mBANb,CAAjB;;AAOAE,IAAAA,QAAQ,CAACM,eAAT,CAAyBhE,IAAzB,CAA8BpE,SAAS,CAAC,KAAKiE,UAAN,CAAvC,EAA0DI,SAA1D,CAAoEF,MAAM,IAAI;AAC1E,WAAKkE,2BAAL,CAAiClE,MAAM,CAACmE,cAAxC;;AACA,UAAI,KAAK5D,gBAAT,EAA2B;AACvB,YAAIP,MAAM,CAACoE,wBAAP,CAAgCC,gBAAhC,IAAoD,KAAK9D,gBAAL,CAAsBiD,SAAtB,EAAxD,EAA2F;AACvF;AACA;AACA,eAAK7E,OAAL,CAAaoD,GAAb,CAAiB,MAAM,KAAKpB,IAAL,CAAU,CAAV,CAAvB;AACH;AACJ;AACJ,KATD;AAUA,SAAKR,WAAL,GAAmB,KAAK5B,QAAL,CAAc+F,MAAd,CAAqB;AACpCC,MAAAA,SAAS,EAAE,KAAKvF,IADoB;AAEpCwF,MAAAA,gBAAgB,EAAEb,QAFkB;AAGpCc,MAAAA,UAAU,EAAG,GAAE,KAAKjF,eAAgB,IAAG3C,WAAY,EAHf;AAIpCkC,MAAAA,cAAc,EAAE,KAAKgB,eAAL;AAJoB,KAArB,CAAnB;;AAMA,SAAKK,eAAL,CAAqB,KAAKD,WAA1B;;AACA,SAAKA,WAAL,CACKuE,WADL,GAEKzE,IAFL,CAEUpE,SAAS,CAAC,KAAKiE,UAAN,CAFnB,EAGKI,SAHL,CAGe,MAAM,KAAK+C,OAAL,EAHrB;;AAIA,SAAK9C,WAAL,CACKwE,oBADL,GAEK1E,IAFL,CAEUpE,SAAS,CAAC,KAAKiE,UAAN,CAFnB,EAGKI,SAHL,CAGe,MAAM;AAAE,UAAII,EAAJ;;AAAQ,aAAO,CAACA,EAAE,GAAG,KAAKC,gBAAX,MAAiC,IAAjC,IAAyCD,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAACsE,sBAAH,EAAzE;AAAuG,KAHtI;;AAIA,SAAKzE,WAAL,CACK0E,aADL,GAEK5E,IAFL,CAEUpE,SAAS,CAAC,KAAKiE,UAAN,CAFnB,EAGKI,SAHL,CAGemC,KAAK,IAAI;AACpB,UAAI,KAAKnB,iBAAL,MAA4BmB,KAAK,CAACyC,OAAN,KAAkBzJ,MAA9C,IAAwD,CAACC,cAAc,CAAC+G,KAAD,CAA3E,EAAoF;AAChFA,QAAAA,KAAK,CAAC0C,cAAN;AACA1C,QAAAA,KAAK,CAAC2C,eAAN;;AACA,aAAKrG,OAAL,CAAaoD,GAAb,CAAiB,MAAM,KAAKpB,IAAL,CAAU,CAAV,CAAvB;AACH;AACJ,KATD;;AAUA,WAAO,KAAKR,WAAZ;AACH;AACD;;;AACA8C,EAAAA,OAAO,GAAG;AACN,QAAI,KAAK9C,WAAL,IAAoB,KAAKA,WAAL,CAAiB8E,WAAjB,EAAxB,EAAwD;AACpD,WAAK9E,WAAL,CAAiB+E,MAAjB;AACH;;AACD,SAAK3E,gBAAL,GAAwB,IAAxB;AACH;AACD;;;AACAH,EAAAA,eAAe,CAAC2C,UAAD,EAAa;AACxB,UAAM7F,QAAQ,GAAG6F,UAAU,CAACoC,SAAX,GAAuBX,gBAAxC;;AACA,UAAM1C,MAAM,GAAG,KAAKsD,UAAL,EAAf;;AACA,UAAM9H,OAAO,GAAG,KAAK+H,mBAAL,EAAhB;;AACAnI,IAAAA,QAAQ,CAACoI,aAAT,CAAuB,CACnB,KAAKC,UAAL,CAAgBC,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB3D,MAAM,CAAC4D,IAAzB,CAAd,EAA8CpI,OAAO,CAACoI,IAAtD,CAAhB,CADmB,EAEnB,KAAKH,UAAL,CAAgBC,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB3D,MAAM,CAAC6D,QAAzB,CAAd,EAAkDrI,OAAO,CAACqI,QAA1D,CAAhB,CAFmB,CAAvB;AAIH;AACD;;;AACAJ,EAAAA,UAAU,CAACrI,QAAD,EAAW;AACjB,WAAOA,QAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIkI,EAAAA,UAAU,GAAG;AACT,UAAMQ,KAAK,GAAG,CAAC,KAAK5G,IAAN,IAAc,KAAKA,IAAL,CAAUqB,KAAV,IAAmB,KAA/C;AACA,UAAMnD,QAAQ,GAAG,KAAKA,QAAtB;AACA,QAAI2I,cAAJ;;AACA,QAAI3I,QAAQ,IAAI,OAAZ,IAAuBA,QAAQ,IAAI,OAAvC,EAAgD;AAC5C2I,MAAAA,cAAc,GAAG;AAAEC,QAAAA,OAAO,EAAE,QAAX;AAAqBC,QAAAA,OAAO,EAAE7I,QAAQ,IAAI,OAAZ,GAAsB,KAAtB,GAA8B;AAA5D,OAAjB;AACH,KAFD,MAGK,IAAIA,QAAQ,IAAI,QAAZ,IACJA,QAAQ,IAAI,MAAZ,IAAsB0I,KADlB,IAEJ1I,QAAQ,IAAI,OAAZ,IAAuB,CAAC0I,KAFxB,EAEgC;AACjCC,MAAAA,cAAc,GAAG;AAAEC,QAAAA,OAAO,EAAE,OAAX;AAAoBC,QAAAA,OAAO,EAAE;AAA7B,OAAjB;AACH,KAJI,MAKA,IAAI7I,QAAQ,IAAI,OAAZ,IACJA,QAAQ,IAAI,OAAZ,IAAuB0I,KADnB,IAEJ1I,QAAQ,IAAI,MAAZ,IAAsB,CAAC0I,KAFvB,EAE+B;AAChCC,MAAAA,cAAc,GAAG;AAAEC,QAAAA,OAAO,EAAE,KAAX;AAAkBC,QAAAA,OAAO,EAAE;AAA3B,OAAjB;AACH,KAJI,MAKA,IAAI,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AACpD,YAAM/I,iCAAiC,CAACC,QAAD,CAAvC;AACH;;AACD,UAAM;AAAE+I,MAAAA,CAAF;AAAKC,MAAAA;AAAL,QAAW,KAAKC,eAAL,CAAqBN,cAAc,CAACC,OAApC,EAA6CD,cAAc,CAACE,OAA5D,CAAjB;;AACA,WAAO;AACHL,MAAAA,IAAI,EAAEG,cADH;AAEHF,MAAAA,QAAQ,EAAE;AAAEG,QAAAA,OAAO,EAAEG,CAAX;AAAcF,QAAAA,OAAO,EAAEG;AAAvB;AAFP,KAAP;AAIH;AACD;;;AACAb,EAAAA,mBAAmB,GAAG;AAClB,UAAMO,KAAK,GAAG,CAAC,KAAK5G,IAAN,IAAc,KAAKA,IAAL,CAAUqB,KAAV,IAAmB,KAA/C;AACA,UAAMnD,QAAQ,GAAG,KAAKA,QAAtB;AACA,QAAIkJ,eAAJ;;AACA,QAAIlJ,QAAQ,IAAI,OAAhB,EAAyB;AACrBkJ,MAAAA,eAAe,GAAG;AAAEC,QAAAA,QAAQ,EAAE,QAAZ;AAAsBC,QAAAA,QAAQ,EAAE;AAAhC,OAAlB;AACH,KAFD,MAGK,IAAIpJ,QAAQ,IAAI,OAAhB,EAAyB;AAC1BkJ,MAAAA,eAAe,GAAG;AAAEC,QAAAA,QAAQ,EAAE,QAAZ;AAAsBC,QAAAA,QAAQ,EAAE;AAAhC,OAAlB;AACH,KAFI,MAGA,IAAIpJ,QAAQ,IAAI,QAAZ,IACJA,QAAQ,IAAI,MAAZ,IAAsB0I,KADlB,IAEJ1I,QAAQ,IAAI,OAAZ,IAAuB,CAAC0I,KAFxB,EAEgC;AACjCQ,MAAAA,eAAe,GAAG;AAAEC,QAAAA,QAAQ,EAAE,KAAZ;AAAmBC,QAAAA,QAAQ,EAAE;AAA7B,OAAlB;AACH,KAJI,MAKA,IAAIpJ,QAAQ,IAAI,OAAZ,IACJA,QAAQ,IAAI,OAAZ,IAAuB0I,KADnB,IAEJ1I,QAAQ,IAAI,MAAZ,IAAsB,CAAC0I,KAFvB,EAE+B;AAChCQ,MAAAA,eAAe,GAAG;AAAEC,QAAAA,QAAQ,EAAE,OAAZ;AAAqBC,QAAAA,QAAQ,EAAE;AAA/B,OAAlB;AACH,KAJI,MAKA,IAAI,OAAON,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AACpD,YAAM/I,iCAAiC,CAACC,QAAD,CAAvC;AACH;;AACD,UAAM;AAAE+I,MAAAA,CAAF;AAAKC,MAAAA;AAAL,QAAW,KAAKC,eAAL,CAAqBC,eAAe,CAACC,QAArC,EAA+CD,eAAe,CAACE,QAA/D,CAAjB;;AACA,WAAO;AACHZ,MAAAA,IAAI,EAAEU,eADH;AAEHT,MAAAA,QAAQ,EAAE;AAAEU,QAAAA,QAAQ,EAAEJ,CAAZ;AAAeK,QAAAA,QAAQ,EAAEJ;AAAzB;AAFP,KAAP;AAIH;AACD;;;AACA/E,EAAAA,qBAAqB,GAAG;AACpB;AACA;AACA,QAAI,KAAKZ,gBAAT,EAA2B;AACvB,WAAKA,gBAAL,CAAsBM,OAAtB,GAAgC,KAAKA,OAArC;;AACA,WAAKN,gBAAL,CAAsBgG,aAAtB;;AACA,WAAK5H,OAAL,CAAa6H,gBAAb,CAA8BvG,IAA9B,CAAmCnE,IAAI,CAAC,CAAD,CAAvC,EAA4CD,SAAS,CAAC,KAAKiE,UAAN,CAArD,EAAwEI,SAAxE,CAAkF,MAAM;AACpF,YAAI,KAAKK,gBAAT,EAA2B;AACvB,eAAKJ,WAAL,CAAiBM,cAAjB;AACH;AACJ,OAJD;AAKH;AACJ;AACD;;;AACAkB,EAAAA,gBAAgB,CAACF,YAAD,EAAe;AAC3B,QAAI,KAAKlB,gBAAT,EAA2B;AACvB,WAAKA,gBAAL,CAAsBkB,YAAtB,GAAqCA,YAArC;;AACA,WAAKlB,gBAAL,CAAsBgG,aAAtB;AACH;AACJ;AACD;;;AACAJ,EAAAA,eAAe,CAACF,CAAD,EAAIC,CAAJ,EAAO;AAClB,QAAI,KAAKhJ,QAAL,KAAkB,OAAlB,IAA6B,KAAKA,QAAL,KAAkB,OAAnD,EAA4D;AACxD,UAAIgJ,CAAC,KAAK,KAAV,EAAiB;AACbA,QAAAA,CAAC,GAAG,QAAJ;AACH,OAFD,MAGK,IAAIA,CAAC,KAAK,QAAV,EAAoB;AACrBA,QAAAA,CAAC,GAAG,KAAJ;AACH;AACJ,KAPD,MAQK;AACD,UAAID,CAAC,KAAK,KAAV,EAAiB;AACbA,QAAAA,CAAC,GAAG,OAAJ;AACH,OAFD,MAGK,IAAIA,CAAC,KAAK,OAAV,EAAmB;AACpBA,QAAAA,CAAC,GAAG,KAAJ;AACH;AACJ;;AACD,WAAO;AAAEA,MAAAA,CAAF;AAAKC,MAAAA;AAAL,KAAP;AACH;AACD;;;AACAhC,EAAAA,2BAA2B,CAACC,cAAD,EAAiB;AACxC,UAAM;AAAEmC,MAAAA,QAAF;AAAYR,MAAAA,OAAZ;AAAqBC,MAAAA;AAArB,QAAiC5B,cAAvC;AACA,QAAIsC,WAAJ,CAFwC,CAGxC;AACA;;AACA,QAAIH,QAAQ,KAAK,QAAjB,EAA2B;AACvB;AACA;AACA;AACA,UAAI,KAAKtH,IAAL,IAAa,KAAKA,IAAL,CAAUqB,KAAV,KAAoB,KAArC,EAA4C;AACxCoG,QAAAA,WAAW,GAAGX,OAAO,KAAK,KAAZ,GAAoB,MAApB,GAA6B,OAA3C;AACH,OAFD,MAGK;AACDW,QAAAA,WAAW,GAAGX,OAAO,KAAK,OAAZ,GAAsB,MAAtB,GAA+B,OAA7C;AACH;AACJ,KAVD,MAWK;AACDW,MAAAA,WAAW,GAAGH,QAAQ,KAAK,QAAb,IAAyBP,OAAO,KAAK,KAArC,GAA6C,OAA7C,GAAuD,OAArE;AACH;;AACD,QAAIU,WAAW,KAAK,KAAKC,gBAAzB,EAA2C;AACvC,YAAM3D,UAAU,GAAG,KAAK5C,WAAxB;;AACA,UAAI4C,UAAJ,EAAgB;AACZ,cAAM4D,WAAW,GAAI,GAAE,KAAKnH,eAAgB,IAAG3C,WAAY,GAA3D;AACAkG,QAAAA,UAAU,CAAC6D,gBAAX,CAA4BD,WAAW,GAAG,KAAKD,gBAA/C;AACA3D,QAAAA,UAAU,CAAC8D,aAAX,CAAyBF,WAAW,GAAGF,WAAvC;AACH;;AACD,WAAKC,gBAAL,GAAwBD,WAAxB;AACH;AACJ;AACD;;;AACA7F,EAAAA,gCAAgC,GAAG;AAC/B;AACA,QAAI,KAAKxB,SAAL,IACA,CAAC,KAAKyB,OADN,IAEA,CAAC,KAAKxB,gBAFN,IAGA,KAAKQ,iBAAL,CAAuB2C,MAH3B,EAGmC;AAC/B;AACH,KAP8B,CAQ/B;AACA;;;AACA,QAAI,KAAKsE,4BAAL,EAAJ,EAAyC;AACrC,WAAKjH,iBAAL,CAAuBkH,IAAvB,CAA4B,CACxB,YADwB,EAExB,MAAM;AACF,aAAKC,+BAAL;;AACA,aAAKxG,IAAL;AACH,OALuB,CAA5B;AAOH,KARD,MASK,IAAI,KAAKb,aAAL,KAAuB,KAA3B,EAAkC;AACnC,WAAKsH,iCAAL;;AACA,WAAKpH,iBAAL,CAAuBkH,IAAvB,CAA4B,CACxB,YADwB,EAExB,MAAM;AACF;AACA;AACA,aAAKC,+BAAL;;AACA/E,QAAAA,YAAY,CAAC,KAAKC,kBAAN,CAAZ;AACA,aAAKA,kBAAL,GAA0BgF,UAAU,CAAC,MAAM,KAAK1G,IAAL,EAAP,EAAoBxD,eAApB,CAApC;AACH,OARuB,CAA5B;AAUH;;AACD,SAAKmK,aAAL,CAAmB,KAAKtH,iBAAxB;AACH;;AACDmH,EAAAA,+BAA+B,GAAG;AAC9B,QAAI,KAAK1H,6BAAT,EAAwC;AACpC;AACH;;AACD,SAAKA,6BAAL,GAAqC,IAArC;AACA,UAAM8H,aAAa,GAAG,EAAtB;;AACA,QAAI,KAAKN,4BAAL,EAAJ,EAAyC;AACrCM,MAAAA,aAAa,CAACL,IAAd,CAAmB,CAAC,YAAD,EAAe,MAAM,KAAKpG,IAAL,EAArB,CAAnB,EAAsD,CAAC,OAAD,EAAU0B,KAAK,IAAI,KAAKgF,cAAL,CAAoBhF,KAApB,CAAnB,CAAtD;AACH,KAFD,MAGK,IAAI,KAAK1C,aAAL,KAAuB,KAA3B,EAAkC;AACnC,WAAKsH,iCAAL;;AACA,YAAMK,gBAAgB,GAAG,MAAM;AAC3BrF,QAAAA,YAAY,CAAC,KAAKC,kBAAN,CAAZ;AACA,aAAKvB,IAAL,CAAU,KAAK1B,eAAL,CAAqBb,iBAA/B;AACH,OAHD;;AAIAgJ,MAAAA,aAAa,CAACL,IAAd,CAAmB,CAAC,UAAD,EAAaO,gBAAb,CAAnB,EAAmD,CAAC,aAAD,EAAgBA,gBAAhB,CAAnD;AACH;;AACD,SAAKH,aAAL,CAAmBC,aAAnB;;AACA,SAAKvH,iBAAL,CAAuBkH,IAAvB,CAA4B,GAAGK,aAA/B;AACH;;AACDD,EAAAA,aAAa,CAACI,SAAD,EAAY;AACrBA,IAAAA,SAAS,CAACnF,OAAV,CAAkB,CAAC,CAACC,KAAD,EAAQC,QAAR,CAAD,KAAuB;AACrC,WAAK9D,WAAL,CAAiBuC,aAAjB,CAA+ByG,gBAA/B,CAAgDnF,KAAhD,EAAuDC,QAAvD,EAAiExF,sBAAjE;AACH,KAFD;AAGH;;AACDgK,EAAAA,4BAA4B,GAAG;AAC3B,WAAO,CAAC,KAAKlI,SAAL,CAAe6I,GAAhB,IAAuB,CAAC,KAAK7I,SAAL,CAAe8I,OAA9C;AACH;AACD;;;AACAL,EAAAA,cAAc,CAAChF,KAAD,EAAQ;AAClB,QAAI,KAAKnB,iBAAL,EAAJ,EAA8B;AAC1B,YAAMyG,mBAAmB,GAAG,KAAKzI,SAAL,CAAe0I,gBAAf,CAAgCvF,KAAK,CAACwF,OAAtC,EAA+CxF,KAAK,CAACyF,OAArD,CAA5B;;AACA,YAAMC,OAAO,GAAG,KAAKvJ,WAAL,CAAiBuC,aAAjC,CAF0B,CAG1B;AACA;AACA;AACA;;AACA,UAAI4G,mBAAmB,KAAKI,OAAxB,IAAmC,CAACA,OAAO,CAACC,QAAR,CAAiBL,mBAAjB,CAAxC,EAA+E;AAC3E,aAAKhH,IAAL;AACH;AACJ;AACJ;AACD;;;AACAsG,EAAAA,iCAAiC,GAAG;AAChC,UAAMgB,QAAQ,GAAG,KAAKtI,aAAtB;;AACA,QAAIsI,QAAQ,KAAK,KAAjB,EAAwB;AACpB,YAAMF,OAAO,GAAG,KAAKvJ,WAAL,CAAiBuC,aAAjC;AACA,YAAM9E,KAAK,GAAG8L,OAAO,CAAC9L,KAAtB,CAFoB,CAGpB;AACA;;AACA,UAAIgM,QAAQ,KAAK,IAAb,IAAsBF,OAAO,CAACG,QAAR,KAAqB,OAArB,IAAgCH,OAAO,CAACG,QAAR,KAAqB,UAA/E,EAA4F;AACxFjM,QAAAA,KAAK,CAACkM,UAAN,GACIlM,KAAK,CAACmM,YAAN,GACInM,KAAK,CAACoM,gBAAN,GACIpM,KAAK,CAACqM,aAAN,GACI,MAJhB;AAKH,OAXmB,CAYpB;AACA;;;AACA,UAAIL,QAAQ,KAAK,IAAb,IAAqB,CAACF,OAAO,CAACQ,SAAlC,EAA6C;AACzCtM,QAAAA,KAAK,CAACuM,cAAN,GAAuB,MAAvB;AACH;;AACDvM,MAAAA,KAAK,CAACwM,WAAN,GAAoB,MAApB;AACAxM,MAAAA,KAAK,CAACyM,uBAAN,GAAgC,aAAhC;AACH;AACJ;;AAhgBiB;;AAkgBtBrK,eAAe,CAACsK,IAAhB;AAAkGrO,EAAAA,EAAlG;AAAA;;AACA+D,eAAe,CAACuK,IAAhB,kBADkGtO,EAClG;AAAA,QAAgG+D,eAAhG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA,qDAFkG/D,EAElG,mBAA2F+D,eAA3F,EAAwH,CAAC;AAC7GwK,IAAAA,IAAI,EAAErO;AADuG,GAAD,CAAxH,EAE4B,YAAY;AAChC,WAAO,CAAC;AAAEqO,MAAAA,IAAI,EAAE/O,EAAE,CAACC;AAAX,KAAD,EAAuB;AAAE8O,MAAAA,IAAI,EAAEvO,EAAE,CAACwO;AAAX,KAAvB,EAAgD;AAAED,MAAAA,IAAI,EAAE5N,EAAE,CAAC8N;AAAX,KAAhD,EAA+E;AAAEF,MAAAA,IAAI,EAAEvO,EAAE,CAAC0O;AAAX,KAA/E,EAA8G;AAAEH,MAAAA,IAAI,EAAEvO,EAAE,CAAC2O;AAAX,KAA9G,EAAmI;AAAEJ,MAAAA,IAAI,EAAEpN,EAAE,CAACyN;AAAX,KAAnI,EAA0J;AAAEL,MAAAA,IAAI,EAAE5O,EAAE,CAACkP;AAAX,KAA1J,EAAsL;AAAEN,MAAAA,IAAI,EAAE5O,EAAE,CAACmP;AAAX,KAAtL,EAAiN;AAAEP,MAAAA,IAAI,EAAEQ;AAAR,KAAjN,EAAsO;AAAER,MAAAA,IAAI,EAAExM,EAAE,CAACiN;AAAX,KAAtO,EAAmQ;AAAET,MAAAA,IAAI,EAAEQ;AAAR,KAAnQ,EAAwR;AAAER,MAAAA,IAAI,EAAEQ,SAAR;AAAmBE,MAAAA,UAAU,EAAE,CAAC;AAC/SV,QAAAA,IAAI,EAAEpO,MADyS;AAE/S+O,QAAAA,IAAI,EAAE,CAACpP,QAAD;AAFyS,OAAD;AAA/B,KAAxR,CAAP;AAIH,GAPL,EAOuB;AAAE8C,IAAAA,QAAQ,EAAE,CAAC;AACpB2L,MAAAA,IAAI,EAAEnO,KADc;AAEpB8O,MAAAA,IAAI,EAAE,CAAC,oBAAD;AAFc,KAAD,CAAZ;AAGP9I,IAAAA,QAAQ,EAAE,CAAC;AACXmI,MAAAA,IAAI,EAAEnO,KADK;AAEX8O,MAAAA,IAAI,EAAE,CAAC,oBAAD;AAFK,KAAD,CAHH;AAMPtL,IAAAA,SAAS,EAAE,CAAC;AACZ2K,MAAAA,IAAI,EAAEnO,KADM;AAEZ8O,MAAAA,IAAI,EAAE,CAAC,qBAAD;AAFM,KAAD,CANJ;AASPrL,IAAAA,SAAS,EAAE,CAAC;AACZ0K,MAAAA,IAAI,EAAEnO,KADM;AAEZ8O,MAAAA,IAAI,EAAE,CAAC,qBAAD;AAFM,KAAD,CATJ;AAYP7J,IAAAA,aAAa,EAAE,CAAC;AAChBkJ,MAAAA,IAAI,EAAEnO,KADU;AAEhB8O,MAAAA,IAAI,EAAE,CAAC,yBAAD;AAFU,KAAD,CAZR;AAeP3I,IAAAA,OAAO,EAAE,CAAC;AACVgI,MAAAA,IAAI,EAAEnO,KADI;AAEV8O,MAAAA,IAAI,EAAE,CAAC,YAAD;AAFI,KAAD,CAfF;AAkBP/H,IAAAA,YAAY,EAAE,CAAC;AACfoH,MAAAA,IAAI,EAAEnO,KADS;AAEf8O,MAAAA,IAAI,EAAE,CAAC,iBAAD;AAFS,KAAD;AAlBP,GAPvB;AAAA;AA6BA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,UAAN,SAAyBpL,eAAzB,CAAyC;AACrCC,EAAAA,WAAW,CAAChB,OAAD,EAAUoM,UAAV,EAAsBC,gBAAtB,EAAwCC,gBAAxC,EAA0DC,MAA1D,EAAkEC,QAAlE,EAA4EC,aAA5E,EAA2FC,YAA3F,EAAyGjL,cAAzG,EAAyHkL,GAAzH,EAA8HC,cAA9H,EAA8IhL,SAA9I,EAAyJ;AAChK,UAAM5B,OAAN,EAAeoM,UAAf,EAA2BC,gBAA3B,EAA6CC,gBAA7C,EAA+DC,MAA/D,EAAuEC,QAAvE,EAAiFC,aAAjF,EAAgGC,YAAhG,EAA8GjL,cAA9G,EAA8HkL,GAA9H,EAAmIC,cAAnI,EAAmJhL,SAAnJ;AACA,SAAKiE,iBAAL,GAAyBgH,gBAAzB;AACH;;AAJoC;;AAMzCV,UAAU,CAACd,IAAX;AAAA,mBAAuGc,UAAvG,EA3CkGnP,EA2ClG,mBAAmIR,EAAE,CAACC,OAAtI,GA3CkGO,EA2ClG,mBAA0JA,EAAE,CAACwO,UAA7J,GA3CkGxO,EA2ClG,mBAAoLW,EAAE,CAAC8N,gBAAvL,GA3CkGzO,EA2ClG,mBAAoNA,EAAE,CAAC0O,gBAAvN,GA3CkG1O,EA2ClG,mBAAoPA,EAAE,CAAC2O,MAAvP,GA3CkG3O,EA2ClG,mBAA0QmB,EAAE,CAACyN,QAA7Q,GA3CkG5O,EA2ClG,mBAAkSL,EAAE,CAACkP,aAArS,GA3CkG7O,EA2ClG,mBAA+TL,EAAE,CAACmP,YAAlU,GA3CkG9O,EA2ClG,mBAA2V8C,2BAA3V,GA3CkG9C,EA2ClG,mBAAmY+B,EAAE,CAACiN,cAAtY,MA3CkGhP,EA2ClG,mBAAibwD,2BAAjb,MA3CkGxD,EA2ClG,mBAAyeF,QAAze;AAAA;;AACAqP,UAAU,CAACb,IAAX,kBA5CkGtO,EA4ClG;AAAA,QAA2FmP,UAA3F;AAAA;AAAA;AAAA;AAAA,aA5CkGnP,EA4ClG;AAAA;;AACA;AAAA,qDA7CkGA,EA6ClG,mBAA2FmP,UAA3F,EAAmH,CAAC;AACxGZ,IAAAA,IAAI,EAAErO,SADkG;AAExGgP,IAAAA,IAAI,EAAE,CAAC;AACCY,MAAAA,QAAQ,EAAE,cADX;AAECC,MAAAA,QAAQ,EAAE,YAFX;AAGCC,MAAAA,IAAI,EAAE;AACF,iBAAS;AADP;AAHP,KAAD;AAFkG,GAAD,CAAnH,EAS4B,YAAY;AAChC,WAAO,CAAC;AAAEzB,MAAAA,IAAI,EAAE/O,EAAE,CAACC;AAAX,KAAD,EAAuB;AAAE8O,MAAAA,IAAI,EAAEvO,EAAE,CAACwO;AAAX,KAAvB,EAAgD;AAAED,MAAAA,IAAI,EAAE5N,EAAE,CAAC8N;AAAX,KAAhD,EAA+E;AAAEF,MAAAA,IAAI,EAAEvO,EAAE,CAAC0O;AAAX,KAA/E,EAA8G;AAAEH,MAAAA,IAAI,EAAEvO,EAAE,CAAC2O;AAAX,KAA9G,EAAmI;AAAEJ,MAAAA,IAAI,EAAEpN,EAAE,CAACyN;AAAX,KAAnI,EAA0J;AAAEL,MAAAA,IAAI,EAAE5O,EAAE,CAACkP;AAAX,KAA1J,EAAsL;AAAEN,MAAAA,IAAI,EAAE5O,EAAE,CAACmP;AAAX,KAAtL,EAAiN;AAAEP,MAAAA,IAAI,EAAEQ,SAAR;AAAmBE,MAAAA,UAAU,EAAE,CAAC;AACxOV,QAAAA,IAAI,EAAEpO,MADkO;AAExO+O,QAAAA,IAAI,EAAE,CAACpM,2BAAD;AAFkO,OAAD;AAA/B,KAAjN,EAGW;AAAEyL,MAAAA,IAAI,EAAExM,EAAE,CAACiN,cAAX;AAA2BC,MAAAA,UAAU,EAAE,CAAC;AAC1CV,QAAAA,IAAI,EAAElO;AADoC,OAAD;AAAvC,KAHX,EAKW;AAAEkO,MAAAA,IAAI,EAAEQ,SAAR;AAAmBE,MAAAA,UAAU,EAAE,CAAC;AAClCV,QAAAA,IAAI,EAAElO;AAD4B,OAAD,EAElC;AACCkO,QAAAA,IAAI,EAAEpO,MADP;AAEC+O,QAAAA,IAAI,EAAE,CAAC1L,2BAAD;AAFP,OAFkC;AAA/B,KALX,EAUW;AAAE+K,MAAAA,IAAI,EAAEQ,SAAR;AAAmBE,MAAAA,UAAU,EAAE,CAAC;AAClCV,QAAAA,IAAI,EAAEpO,MAD4B;AAElC+O,QAAAA,IAAI,EAAE,CAACpP,QAAD;AAF4B,OAAD;AAA/B,KAVX,CAAP;AAcH,GAxBL;AAAA;;AAyBA,MAAMmQ,qBAAN,CAA4B;AACxBjM,EAAAA,WAAW,CAACkM,kBAAD,EAAqB;AAC5B,SAAKA,kBAAL,GAA0BA,kBAA1B;AACA;;AACA,SAAKC,WAAL,GAAmB,SAAnB;AACA;;AACA,SAAKC,mBAAL,GAA2B,KAA3B;AACA;;AACA,SAAKC,OAAL,GAAe,IAAI/O,OAAJ,EAAf;AACH;AACD;AACJ;AACA;AACA;;;AACI4E,EAAAA,IAAI,CAACoC,KAAD,EAAQ;AACR;AACAX,IAAAA,YAAY,CAAC,KAAKa,cAAN,CAAZ,CAFQ,CAGR;;AACA,SAAK4H,mBAAL,GAA2B,IAA3B;AACA,SAAK7H,cAAL,GAAsBqE,UAAU,CAAC,MAAM;AACnC,WAAKuD,WAAL,GAAmB,SAAnB;AACA,WAAK5H,cAAL,GAAsBwG,SAAtB;;AACA,WAAKuB,OAAL,GAHmC,CAInC;AACA;;;AACA,WAAKrE,aAAL;AACH,KAP+B,EAO7B3D,KAP6B,CAAhC;AAQH;AACD;AACJ;AACA;AACA;;;AACIjC,EAAAA,IAAI,CAACiC,KAAD,EAAQ;AACR;AACAX,IAAAA,YAAY,CAAC,KAAKY,cAAN,CAAZ;AACA,SAAKC,cAAL,GAAsBoE,UAAU,CAAC,MAAM;AACnC,WAAKuD,WAAL,GAAmB,QAAnB;AACA,WAAK3H,cAAL,GAAsBuG,SAAtB,CAFmC,CAGnC;AACA;;AACA,WAAK9C,aAAL;AACH,KAN+B,EAM7B3D,KAN6B,CAAhC;AAOH;AACD;;;AACAU,EAAAA,WAAW,GAAG;AACV,WAAO,KAAKqH,OAAZ;AACH;AACD;;;AACAnH,EAAAA,SAAS,GAAG;AACR,WAAO,KAAKiH,WAAL,KAAqB,SAA5B;AACH;;AACDzI,EAAAA,WAAW,GAAG;AACVC,IAAAA,YAAY,CAAC,KAAKY,cAAN,CAAZ;AACAZ,IAAAA,YAAY,CAAC,KAAKa,cAAN,CAAZ;;AACA,SAAK6H,OAAL,CAAajI,QAAb;AACH;;AACDmI,EAAAA,eAAe,GAAG;AACd,SAAKH,mBAAL,GAA2B,KAA3B;AACH;;AACDI,EAAAA,cAAc,CAACzI,KAAD,EAAQ;AAClB,UAAM0I,OAAO,GAAG1I,KAAK,CAAC0I,OAAtB;;AACA,QAAIA,OAAO,KAAK,QAAZ,IAAwB,CAAC,KAAKvH,SAAL,EAA7B,EAA+C;AAC3C,WAAKmH,OAAL,CAAalI,IAAb;AACH;;AACD,QAAIsI,OAAO,KAAK,SAAZ,IAAyBA,OAAO,KAAK,QAAzC,EAAmD;AAC/C,WAAKL,mBAAL,GAA2B,IAA3B;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;AACI9F,EAAAA,sBAAsB,GAAG;AACrB,QAAI,KAAK8F,mBAAT,EAA8B;AAC1B,WAAK/J,IAAL,CAAU,CAAV;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;AACI4F,EAAAA,aAAa,GAAG;AACZ,SAAKiE,kBAAL,CAAwBQ,YAAxB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIJ,EAAAA,OAAO,GAAG,CAAG;;AA3FW;;AA6F5BL,qBAAqB,CAAC5B,IAAtB;AAAA,mBAAkH4B,qBAAlH,EAnKkGjQ,EAmKlG,mBAAyJA,EAAE,CAAC2Q,iBAA5J;AAAA;;AACAV,qBAAqB,CAAC3B,IAAtB,kBApKkGtO,EAoKlG;AAAA,QAAsGiQ;AAAtG;;AACA;AAAA,qDArKkGjQ,EAqKlG,mBAA2FiQ,qBAA3F,EAA8H,CAAC;AACnH1B,IAAAA,IAAI,EAAErO;AAD6G,GAAD,CAA9H,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEqO,MAAAA,IAAI,EAAEvO,EAAE,CAAC2Q;AAAX,KAAD,CAAP;AAA0C,GAFpF;AAAA;AAGA;AACA;AACA;AACA;;;AACA,MAAMd,gBAAN,SAA+BI,qBAA/B,CAAqD;AACjDjM,EAAAA,WAAW,CAAC4M,iBAAD,EAAoBC,mBAApB,EAAyC;AAChD,UAAMD,iBAAN;AACA,SAAKC,mBAAL,GAA2BA,mBAA3B;AACA;;AACA,SAAKC,UAAL,GAAkB,KAAKD,mBAAL,CAAyBE,OAAzB,CAAiC7P,WAAW,CAAC8P,OAA7C,CAAlB;AACH;;AANgD;;AAQrDnB,gBAAgB,CAACxB,IAAjB;AAAA,mBAA6GwB,gBAA7G,EApLkG7P,EAoLlG,mBAA+IA,EAAE,CAAC2Q,iBAAlJ,GApLkG3Q,EAoLlG,mBAAgLiB,EAAE,CAACgQ,kBAAnL;AAAA;;AACApB,gBAAgB,CAACqB,IAAjB,kBArLkGlR,EAqLlG;AAAA,QAAiG6P,gBAAjG;AAAA;AAAA,6BAA2L,MAA3L;AAAA;AAAA;AAAA;AArLkG7P,MAAAA,EAqLlG;AAAA;AAAA;AAAA,aArLkGA,EAqLlG;AAAA;AAAA;AAAA;AAAA;AAAA;AArLkGA,MAAAA,EAqL6N,4BAA/T;AArLkGA,MAAAA,EAqLkY;AAAA,eAAiB,qBAAjB;AAAA;AAAA,eAA2D,0BAA3D;AAAA,QAApe;AArLkGA,MAAAA,EAqLlG;AArLkGA,MAAAA,EAqLsd,UAAxjB;AArLkGA,MAAAA,EAqLie,eAAnkB;AAAA;;AAAA;AAAA;AArLkGA,MAAAA,EAqL+R,+CArL/RA,EAqL+R,qEAAjY;AArLkGA,MAAAA,EAqL8P,mEAAhW;AArLkGA,MAAAA,EAqLsd,aAAxjB;AArLkGA,MAAAA,EAqLsd,+BAAxjB;AAAA;AAAA;AAAA,eAAo7BH,EAAE,CAACsR,OAAv7B;AAAA,UAA0gCtR,EAAE,CAACuR,SAA7gC;AAAA;AAAA;AAAA;AAAA,eAAsiC,CAACpP,oBAAoB,CAACC,YAAtB;AAAtiC;AAAA;AAAA;;AACA;AAAA,qDAtLkGjC,EAsLlG,mBAA2F6P,gBAA3F,EAAyH,CAAC;AAC9GtB,IAAAA,IAAI,EAAEjO,SADwG;AAE9G4O,IAAAA,IAAI,EAAE,CAAC;AAAEY,MAAAA,QAAQ,EAAE,uBAAZ;AAAqCuB,MAAAA,aAAa,EAAE9Q,iBAAiB,CAAC+Q,IAAtE;AAA4EC,MAAAA,eAAe,EAAE/Q,uBAAuB,CAACgR,MAArH;AAA6HC,MAAAA,UAAU,EAAE,CAACzP,oBAAoB,CAACC,YAAtB,CAAzI;AAA8K+N,MAAAA,IAAI,EAAE;AAC/K;AACA;AACA,wBAAgB,sCAH+J;AAI/K,uBAAe;AAJgK,OAApL;AAKI0B,MAAAA,QAAQ,EAAE,8QALd;AAK8RC,MAAAA,MAAM,EAAE,CAAC,qUAAD;AALtS,KAAD;AAFwG,GAAD,CAAzH,EAQ4B,YAAY;AAAE,WAAO,CAAC;AAAEpD,MAAAA,IAAI,EAAEvO,EAAE,CAAC2Q;AAAX,KAAD,EAAiC;AAAEpC,MAAAA,IAAI,EAAEtN,EAAE,CAACgQ;AAAX,KAAjC,CAAP;AAA2E,GARrH;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMW,gBAAN,CAAuB;;AAEvBA,gBAAgB,CAACvD,IAAjB;AAAA,mBAA6GuD,gBAA7G;AAAA;;AACAA,gBAAgB,CAACC,IAAjB,kBA1MkG7R,EA0MlG;AAAA,QAA8G4R;AAA9G;AACAA,gBAAgB,CAACE,IAAjB,kBA3MkG9R,EA2MlG;AAAA,aAA2I,CAACoD,4CAAD,CAA3I;AAAA,YAAqM,CAACxD,UAAD,EAAaG,YAAb,EAA2BL,aAA3B,EAA0CgB,eAA1C,CAArM,EAAiQA,eAAjQ,EAAkRE,mBAAlR;AAAA;;AACA;AAAA,qDA5MkGZ,EA4MlG,mBAA2F4R,gBAA3F,EAAyH,CAAC;AAC9GrD,IAAAA,IAAI,EAAE9N,QADwG;AAE9GyO,IAAAA,IAAI,EAAE,CAAC;AACC6C,MAAAA,OAAO,EAAE,CAACnS,UAAD,EAAaG,YAAb,EAA2BL,aAA3B,EAA0CgB,eAA1C,CADV;AAECsR,MAAAA,OAAO,EAAE,CAAC7C,UAAD,EAAaU,gBAAb,EAA+BnP,eAA/B,EAAgDE,mBAAhD,CAFV;AAGCqR,MAAAA,YAAY,EAAE,CAAC9C,UAAD,EAAaU,gBAAb,CAHf;AAICqC,MAAAA,SAAS,EAAE,CAAC9O,4CAAD;AAJZ,KAAD;AAFwG,GAAD,CAAzH;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASI,2BAAT,EAAsCG,mCAAtC,EAA2Eb,2BAA3E,EAAwGC,mCAAxG,EAA6IK,4CAA7I,EAA2L+L,UAA3L,EAAuMyC,gBAAvM,EAAyNvP,kBAAzN,EAA6OC,mBAA7O,EAAkQuN,gBAAlQ,EAAoR9L,eAApR,EAAqSkM,qBAArS,EAA4TtN,iCAA5T,EAA+VX,oBAA/V", "sourcesContent": ["import * as i1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport * as i4 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i7 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Input, Optional, Component, ViewEncapsulation, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i6 from '@angular/cdk/layout';\nimport { Breakpoints } from '@angular/cdk/layout';\nimport * as i3 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { ComponentPortal } from '@angular/cdk/portal';\nimport { Subject } from 'rxjs';\nimport { takeUntil, take } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate, keyframes } from '@angular/animations';\nimport * as i5 from '@angular/cdk/bidi';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Animations used by MatTooltip.\n * @docs-private\n */\nconst matTooltipAnimations = {\n    /** Animation that transitions a tooltip in and out. */\n    tooltipState: trigger('state', [\n        state('initial, void, hidden', style({ opacity: 0, transform: 'scale(0)' })),\n        state('visible', style({ transform: 'scale(1)' })),\n        transition('* => visible', animate('200ms cubic-bezier(0, 0, 0.2, 1)', keyframes([\n            style({ opacity: 0, transform: 'scale(0)', offset: 0 }),\n            style({ opacity: 0.5, transform: 'scale(0.99)', offset: 0.5 }),\n            style({ opacity: 1, transform: 'scale(1)', offset: 1 }),\n        ]))),\n        transition('* => hidden', animate('100ms cubic-bezier(0, 0, 0.2, 1)', style({ opacity: 0 }))),\n    ]),\n};\n\n/** Time in ms to throttle repositioning after scroll events. */\nconst SCROLL_THROTTLE_MS = 20;\n/**\n * CSS class that will be attached to the overlay panel.\n * @deprecated\n * @breaking-change 13.0.0 remove this variable\n */\nconst TOOLTIP_PANEL_CLASS = 'mat-tooltip-panel';\nconst PANEL_CLASS = 'tooltip-panel';\n/** Options used to bind passive event listeners. */\nconst passiveListenerOptions = normalizePassiveListenerOptions({ passive: true });\n/**\n * Time between the user putting the pointer on a tooltip\n * trigger and the long press event being fired.\n */\nconst LONGPRESS_DELAY = 500;\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\nfunction getMatTooltipInvalidPositionError(position) {\n    return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n/** Injection token that determines the scroll handling while a tooltip is visible. */\nconst MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken('mat-tooltip-scroll-strategy');\n/** @docs-private */\nfunction MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition({ scrollThrottle: SCROLL_THROTTLE_MS });\n}\n/** @docs-private */\nconst MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY,\n};\n/** Injection token to be used to override the default options for `matTooltip`. */\nconst MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken('mat-tooltip-default-options', {\n    providedIn: 'root',\n    factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY,\n});\n/** @docs-private */\nfunction MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        showDelay: 0,\n        hideDelay: 0,\n        touchendHideDelay: 1500,\n    };\n}\nclass _MatTooltipBase {\n    constructor(_overlay, _elementRef, _scrollDispatcher, _viewContainerRef, _ngZone, _platform, _ariaDescriber, _focusMonitor, scrollStrategy, _dir, _defaultOptions, _document) {\n        this._overlay = _overlay;\n        this._elementRef = _elementRef;\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewContainerRef = _viewContainerRef;\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        this._ariaDescriber = _ariaDescriber;\n        this._focusMonitor = _focusMonitor;\n        this._dir = _dir;\n        this._defaultOptions = _defaultOptions;\n        this._position = 'below';\n        this._disabled = false;\n        this._viewInitialized = false;\n        this._pointerExitEventsInitialized = false;\n        this._viewportMargin = 8;\n        this._cssClassPrefix = 'mat';\n        this._showDelay = this._defaultOptions.showDelay;\n        this._hideDelay = this._defaultOptions.hideDelay;\n        /**\n         * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n         * uses a long press gesture to show and hide, however it can conflict with the native browser\n         * gestures. To work around the conflict, Angular Material disables native gestures on the\n         * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n         * elements). The different values for this option configure the touch event handling as follows:\n         * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n         *   browser gestures on particular elements. In particular, it allows text selection on inputs\n         *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n         * - `on` - Enables touch gestures for all elements and disables native\n         *   browser gestures with no exceptions.\n         * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n         *   showing on touch devices.\n         */\n        this.touchGestures = 'auto';\n        this._message = '';\n        /** Manually-bound passive event listeners. */\n        this._passiveListeners = [];\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        this._scrollStrategy = scrollStrategy;\n        this._document = _document;\n        if (_defaultOptions) {\n            if (_defaultOptions.position) {\n                this.position = _defaultOptions.position;\n            }\n            if (_defaultOptions.touchGestures) {\n                this.touchGestures = _defaultOptions.touchGestures;\n            }\n        }\n        _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n            if (this._overlayRef) {\n                this._updatePosition(this._overlayRef);\n            }\n        });\n    }\n    /** Allows the user to define the position of the tooltip relative to the parent element */\n    get position() {\n        return this._position;\n    }\n    set position(value) {\n        var _a;\n        if (value !== this._position) {\n            this._position = value;\n            if (this._overlayRef) {\n                this._updatePosition(this._overlayRef);\n                (_a = this._tooltipInstance) === null || _a === void 0 ? void 0 : _a.show(0);\n                this._overlayRef.updatePosition();\n            }\n        }\n    }\n    /** Disables the display of the tooltip. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        // If tooltip is disabled, hide immediately.\n        if (this._disabled) {\n            this.hide(0);\n        }\n        else {\n            this._setupPointerEnterEventsIfNeeded();\n        }\n    }\n    /** The default delay in ms before showing the tooltip after show is called */\n    get showDelay() {\n        return this._showDelay;\n    }\n    set showDelay(value) {\n        this._showDelay = coerceNumberProperty(value);\n    }\n    /** The default delay in ms before hiding the tooltip after hide is called */\n    get hideDelay() {\n        return this._hideDelay;\n    }\n    set hideDelay(value) {\n        this._hideDelay = coerceNumberProperty(value);\n    }\n    /** The message to be displayed in the tooltip */\n    get message() {\n        return this._message;\n    }\n    set message(value) {\n        this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this._message, 'tooltip');\n        // If the message is not a string (e.g. number), convert it to a string and trim it.\n        // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n        // away the string-conversion: https://github.com/angular/components/issues/20684\n        this._message = value != null ? String(value).trim() : '';\n        if (!this._message && this._isTooltipVisible()) {\n            this.hide(0);\n        }\n        else {\n            this._setupPointerEnterEventsIfNeeded();\n            this._updateTooltipMessage();\n            this._ngZone.runOutsideAngular(() => {\n                // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n                // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n                // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n                // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n                Promise.resolve().then(() => {\n                    this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n                });\n            });\n        }\n    }\n    /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n    get tooltipClass() {\n        return this._tooltipClass;\n    }\n    set tooltipClass(value) {\n        this._tooltipClass = value;\n        if (this._tooltipInstance) {\n            this._setTooltipClass(this._tooltipClass);\n        }\n    }\n    ngAfterViewInit() {\n        // This needs to happen after view init so the initial values for all inputs have been set.\n        this._viewInitialized = true;\n        this._setupPointerEnterEventsIfNeeded();\n        this._focusMonitor\n            .monitor(this._elementRef)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(origin => {\n            // Note that the focus monitor runs outside the Angular zone.\n            if (!origin) {\n                this._ngZone.run(() => this.hide(0));\n            }\n            else if (origin === 'keyboard') {\n                this._ngZone.run(() => this.show());\n            }\n        });\n    }\n    /**\n     * Dispose the tooltip when destroyed.\n     */\n    ngOnDestroy() {\n        const nativeElement = this._elementRef.nativeElement;\n        clearTimeout(this._touchstartTimeout);\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n            this._tooltipInstance = null;\n        }\n        // Clean up the event listeners set in the constructor\n        this._passiveListeners.forEach(([event, listener]) => {\n            nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n        });\n        this._passiveListeners.length = 0;\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n        this._focusMonitor.stopMonitoring(nativeElement);\n    }\n    /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n    show(delay = this.showDelay) {\n        if (this.disabled ||\n            !this.message ||\n            (this._isTooltipVisible() &&\n                !this._tooltipInstance._showTimeoutId &&\n                !this._tooltipInstance._hideTimeoutId)) {\n            return;\n        }\n        const overlayRef = this._createOverlay();\n        this._detach();\n        this._portal =\n            this._portal || new ComponentPortal(this._tooltipComponent, this._viewContainerRef);\n        this._tooltipInstance = overlayRef.attach(this._portal).instance;\n        this._tooltipInstance\n            .afterHidden()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._detach());\n        this._setTooltipClass(this._tooltipClass);\n        this._updateTooltipMessage();\n        this._tooltipInstance.show(delay);\n    }\n    /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n    hide(delay = this.hideDelay) {\n        if (this._tooltipInstance) {\n            this._tooltipInstance.hide(delay);\n        }\n    }\n    /** Shows/hides the tooltip */\n    toggle() {\n        this._isTooltipVisible() ? this.hide() : this.show();\n    }\n    /** Returns true if the tooltip is currently visible to the user */\n    _isTooltipVisible() {\n        return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n    }\n    /** Create the overlay config and position strategy */\n    _createOverlay() {\n        if (this._overlayRef) {\n            return this._overlayRef;\n        }\n        const scrollableAncestors = this._scrollDispatcher.getAncestorScrollContainers(this._elementRef);\n        // Create connected position strategy that listens for scroll events to reposition.\n        const strategy = this._overlay\n            .position()\n            .flexibleConnectedTo(this._elementRef)\n            .withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`)\n            .withFlexibleDimensions(false)\n            .withViewportMargin(this._viewportMargin)\n            .withScrollableContainers(scrollableAncestors);\n        strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n            this._updateCurrentPositionClass(change.connectionPair);\n            if (this._tooltipInstance) {\n                if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n                    // After position changes occur and the overlay is clipped by\n                    // a parent scrollable then close the tooltip.\n                    this._ngZone.run(() => this.hide(0));\n                }\n            }\n        });\n        this._overlayRef = this._overlay.create({\n            direction: this._dir,\n            positionStrategy: strategy,\n            panelClass: `${this._cssClassPrefix}-${PANEL_CLASS}`,\n            scrollStrategy: this._scrollStrategy(),\n        });\n        this._updatePosition(this._overlayRef);\n        this._overlayRef\n            .detachments()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._detach());\n        this._overlayRef\n            .outsidePointerEvents()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => { var _a; return (_a = this._tooltipInstance) === null || _a === void 0 ? void 0 : _a._handleBodyInteraction(); });\n        this._overlayRef\n            .keydownEvents()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(event => {\n            if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n                event.preventDefault();\n                event.stopPropagation();\n                this._ngZone.run(() => this.hide(0));\n            }\n        });\n        return this._overlayRef;\n    }\n    /** Detaches the currently-attached tooltip. */\n    _detach() {\n        if (this._overlayRef && this._overlayRef.hasAttached()) {\n            this._overlayRef.detach();\n        }\n        this._tooltipInstance = null;\n    }\n    /** Updates the position of the current tooltip. */\n    _updatePosition(overlayRef) {\n        const position = overlayRef.getConfig().positionStrategy;\n        const origin = this._getOrigin();\n        const overlay = this._getOverlayPosition();\n        position.withPositions([\n            this._addOffset(Object.assign(Object.assign({}, origin.main), overlay.main)),\n            this._addOffset(Object.assign(Object.assign({}, origin.fallback), overlay.fallback)),\n        ]);\n    }\n    /** Adds the configured offset to a position. Used as a hook for child classes. */\n    _addOffset(position) {\n        return position;\n    }\n    /**\n     * Returns the origin position and a fallback position based on the user's position preference.\n     * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n     */\n    _getOrigin() {\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        const position = this.position;\n        let originPosition;\n        if (position == 'above' || position == 'below') {\n            originPosition = { originX: 'center', originY: position == 'above' ? 'top' : 'bottom' };\n        }\n        else if (position == 'before' ||\n            (position == 'left' && isLtr) ||\n            (position == 'right' && !isLtr)) {\n            originPosition = { originX: 'start', originY: 'center' };\n        }\n        else if (position == 'after' ||\n            (position == 'right' && isLtr) ||\n            (position == 'left' && !isLtr)) {\n            originPosition = { originX: 'end', originY: 'center' };\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getMatTooltipInvalidPositionError(position);\n        }\n        const { x, y } = this._invertPosition(originPosition.originX, originPosition.originY);\n        return {\n            main: originPosition,\n            fallback: { originX: x, originY: y },\n        };\n    }\n    /** Returns the overlay position and a fallback position based on the user's preference */\n    _getOverlayPosition() {\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        const position = this.position;\n        let overlayPosition;\n        if (position == 'above') {\n            overlayPosition = { overlayX: 'center', overlayY: 'bottom' };\n        }\n        else if (position == 'below') {\n            overlayPosition = { overlayX: 'center', overlayY: 'top' };\n        }\n        else if (position == 'before' ||\n            (position == 'left' && isLtr) ||\n            (position == 'right' && !isLtr)) {\n            overlayPosition = { overlayX: 'end', overlayY: 'center' };\n        }\n        else if (position == 'after' ||\n            (position == 'right' && isLtr) ||\n            (position == 'left' && !isLtr)) {\n            overlayPosition = { overlayX: 'start', overlayY: 'center' };\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getMatTooltipInvalidPositionError(position);\n        }\n        const { x, y } = this._invertPosition(overlayPosition.overlayX, overlayPosition.overlayY);\n        return {\n            main: overlayPosition,\n            fallback: { overlayX: x, overlayY: y },\n        };\n    }\n    /** Updates the tooltip message and repositions the overlay according to the new message length */\n    _updateTooltipMessage() {\n        // Must wait for the message to be painted to the tooltip so that the overlay can properly\n        // calculate the correct positioning based on the size of the text.\n        if (this._tooltipInstance) {\n            this._tooltipInstance.message = this.message;\n            this._tooltipInstance._markForCheck();\n            this._ngZone.onMicrotaskEmpty.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n                if (this._tooltipInstance) {\n                    this._overlayRef.updatePosition();\n                }\n            });\n        }\n    }\n    /** Updates the tooltip class */\n    _setTooltipClass(tooltipClass) {\n        if (this._tooltipInstance) {\n            this._tooltipInstance.tooltipClass = tooltipClass;\n            this._tooltipInstance._markForCheck();\n        }\n    }\n    /** Inverts an overlay position. */\n    _invertPosition(x, y) {\n        if (this.position === 'above' || this.position === 'below') {\n            if (y === 'top') {\n                y = 'bottom';\n            }\n            else if (y === 'bottom') {\n                y = 'top';\n            }\n        }\n        else {\n            if (x === 'end') {\n                x = 'start';\n            }\n            else if (x === 'start') {\n                x = 'end';\n            }\n        }\n        return { x, y };\n    }\n    /** Updates the class on the overlay panel based on the current position of the tooltip. */\n    _updateCurrentPositionClass(connectionPair) {\n        const { overlayY, originX, originY } = connectionPair;\n        let newPosition;\n        // If the overlay is in the middle along the Y axis,\n        // it means that it's either before or after.\n        if (overlayY === 'center') {\n            // Note that since this information is used for styling, we want to\n            // resolve `start` and `end` to their real values, otherwise consumers\n            // would have to remember to do it themselves on each consumption.\n            if (this._dir && this._dir.value === 'rtl') {\n                newPosition = originX === 'end' ? 'left' : 'right';\n            }\n            else {\n                newPosition = originX === 'start' ? 'left' : 'right';\n            }\n        }\n        else {\n            newPosition = overlayY === 'bottom' && originY === 'top' ? 'above' : 'below';\n        }\n        if (newPosition !== this._currentPosition) {\n            const overlayRef = this._overlayRef;\n            if (overlayRef) {\n                const classPrefix = `${this._cssClassPrefix}-${PANEL_CLASS}-`;\n                overlayRef.removePanelClass(classPrefix + this._currentPosition);\n                overlayRef.addPanelClass(classPrefix + newPosition);\n            }\n            this._currentPosition = newPosition;\n        }\n    }\n    /** Binds the pointer events to the tooltip trigger. */\n    _setupPointerEnterEventsIfNeeded() {\n        // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n        if (this._disabled ||\n            !this.message ||\n            !this._viewInitialized ||\n            this._passiveListeners.length) {\n            return;\n        }\n        // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n        // first tap from firing its click event or can cause the tooltip to open for clicks.\n        if (this._platformSupportsMouseEvents()) {\n            this._passiveListeners.push([\n                'mouseenter',\n                () => {\n                    this._setupPointerExitEventsIfNeeded();\n                    this.show();\n                },\n            ]);\n        }\n        else if (this.touchGestures !== 'off') {\n            this._disableNativeGesturesIfNecessary();\n            this._passiveListeners.push([\n                'touchstart',\n                () => {\n                    // Note that it's important that we don't `preventDefault` here,\n                    // because it can prevent click events from firing on the element.\n                    this._setupPointerExitEventsIfNeeded();\n                    clearTimeout(this._touchstartTimeout);\n                    this._touchstartTimeout = setTimeout(() => this.show(), LONGPRESS_DELAY);\n                },\n            ]);\n        }\n        this._addListeners(this._passiveListeners);\n    }\n    _setupPointerExitEventsIfNeeded() {\n        if (this._pointerExitEventsInitialized) {\n            return;\n        }\n        this._pointerExitEventsInitialized = true;\n        const exitListeners = [];\n        if (this._platformSupportsMouseEvents()) {\n            exitListeners.push(['mouseleave', () => this.hide()], ['wheel', event => this._wheelListener(event)]);\n        }\n        else if (this.touchGestures !== 'off') {\n            this._disableNativeGesturesIfNecessary();\n            const touchendListener = () => {\n                clearTimeout(this._touchstartTimeout);\n                this.hide(this._defaultOptions.touchendHideDelay);\n            };\n            exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n        }\n        this._addListeners(exitListeners);\n        this._passiveListeners.push(...exitListeners);\n    }\n    _addListeners(listeners) {\n        listeners.forEach(([event, listener]) => {\n            this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n        });\n    }\n    _platformSupportsMouseEvents() {\n        return !this._platform.IOS && !this._platform.ANDROID;\n    }\n    /** Listener for the `wheel` event on the element. */\n    _wheelListener(event) {\n        if (this._isTooltipVisible()) {\n            const elementUnderPointer = this._document.elementFromPoint(event.clientX, event.clientY);\n            const element = this._elementRef.nativeElement;\n            // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n            // won't fire if the user scrolls away using the wheel without moving their cursor. We\n            // work around it by finding the element under the user's cursor and closing the tooltip\n            // if it's not the trigger.\n            if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n                this.hide();\n            }\n        }\n    }\n    /** Disables the native browser gestures, based on how the tooltip has been configured. */\n    _disableNativeGesturesIfNecessary() {\n        const gestures = this.touchGestures;\n        if (gestures !== 'off') {\n            const element = this._elementRef.nativeElement;\n            const style = element.style;\n            // If gestures are set to `auto`, we don't disable text selection on inputs and\n            // textareas, because it prevents the user from typing into them on iOS Safari.\n            if (gestures === 'on' || (element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA')) {\n                style.userSelect =\n                    style.msUserSelect =\n                        style.webkitUserSelect =\n                            style.MozUserSelect =\n                                'none';\n            }\n            // If we have `auto` gestures and the element uses native HTML dragging,\n            // we don't set `-webkit-user-drag` because it prevents the native behavior.\n            if (gestures === 'on' || !element.draggable) {\n                style.webkitUserDrag = 'none';\n            }\n            style.touchAction = 'none';\n            style.webkitTapHighlightColor = 'transparent';\n        }\n    }\n}\n_MatTooltipBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.2.0\", ngImport: i0, type: _MatTooltipBase, deps: \"invalid\", target: i0.ɵɵFactoryTarget.Directive });\n_MatTooltipBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.2.0\", type: _MatTooltipBase, inputs: { position: [\"matTooltipPosition\", \"position\"], disabled: [\"matTooltipDisabled\", \"disabled\"], showDelay: [\"matTooltipShowDelay\", \"showDelay\"], hideDelay: [\"matTooltipHideDelay\", \"hideDelay\"], touchGestures: [\"matTooltipTouchGestures\", \"touchGestures\"], message: [\"matTooltip\", \"message\"], tooltipClass: [\"matTooltipClass\", \"tooltipClass\"] }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.2.0\", ngImport: i0, type: _MatTooltipBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () {\n        return [{ type: i1.Overlay }, { type: i0.ElementRef }, { type: i2.ScrollDispatcher }, { type: i0.ViewContainerRef }, { type: i0.NgZone }, { type: i3.Platform }, { type: i4.AriaDescriber }, { type: i4.FocusMonitor }, { type: undefined }, { type: i5.Directionality }, { type: undefined }, { type: undefined, decorators: [{\n                        type: Inject,\n                        args: [DOCUMENT]\n                    }] }];\n    }, propDecorators: { position: [{\n                type: Input,\n                args: ['matTooltipPosition']\n            }], disabled: [{\n                type: Input,\n                args: ['matTooltipDisabled']\n            }], showDelay: [{\n                type: Input,\n                args: ['matTooltipShowDelay']\n            }], hideDelay: [{\n                type: Input,\n                args: ['matTooltipHideDelay']\n            }], touchGestures: [{\n                type: Input,\n                args: ['matTooltipTouchGestures']\n            }], message: [{\n                type: Input,\n                args: ['matTooltip']\n            }], tooltipClass: [{\n                type: Input,\n                args: ['matTooltipClass']\n            }] } });\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\nclass MatTooltip extends _MatTooltipBase {\n    constructor(overlay, elementRef, scrollDispatcher, viewContainerRef, ngZone, platform, ariaDescriber, focusMonitor, scrollStrategy, dir, defaultOptions, _document) {\n        super(overlay, elementRef, scrollDispatcher, viewContainerRef, ngZone, platform, ariaDescriber, focusMonitor, scrollStrategy, dir, defaultOptions, _document);\n        this._tooltipComponent = TooltipComponent;\n    }\n}\nMatTooltip.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.2.0\", ngImport: i0, type: MatTooltip, deps: [{ token: i1.Overlay }, { token: i0.ElementRef }, { token: i2.ScrollDispatcher }, { token: i0.ViewContainerRef }, { token: i0.NgZone }, { token: i3.Platform }, { token: i4.AriaDescriber }, { token: i4.FocusMonitor }, { token: MAT_TOOLTIP_SCROLL_STRATEGY }, { token: i5.Directionality, optional: true }, { token: MAT_TOOLTIP_DEFAULT_OPTIONS, optional: true }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive });\nMatTooltip.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.2.0\", type: MatTooltip, selector: \"[matTooltip]\", host: { classAttribute: \"mat-tooltip-trigger\" }, exportAs: [\"matTooltip\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.2.0\", ngImport: i0, type: MatTooltip, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTooltip]',\n                    exportAs: 'matTooltip',\n                    host: {\n                        'class': 'mat-tooltip-trigger',\n                    },\n                }]\n        }], ctorParameters: function () {\n        return [{ type: i1.Overlay }, { type: i0.ElementRef }, { type: i2.ScrollDispatcher }, { type: i0.ViewContainerRef }, { type: i0.NgZone }, { type: i3.Platform }, { type: i4.AriaDescriber }, { type: i4.FocusMonitor }, { type: undefined, decorators: [{\n                        type: Inject,\n                        args: [MAT_TOOLTIP_SCROLL_STRATEGY]\n                    }] }, { type: i5.Directionality, decorators: [{\n                        type: Optional\n                    }] }, { type: undefined, decorators: [{\n                        type: Optional\n                    }, {\n                        type: Inject,\n                        args: [MAT_TOOLTIP_DEFAULT_OPTIONS]\n                    }] }, { type: undefined, decorators: [{\n                        type: Inject,\n                        args: [DOCUMENT]\n                    }] }];\n    } });\nclass _TooltipComponentBase {\n    constructor(_changeDetectorRef) {\n        this._changeDetectorRef = _changeDetectorRef;\n        /** Property watched by the animation framework to show or hide the tooltip */\n        this._visibility = 'initial';\n        /** Whether interactions on the page should close the tooltip */\n        this._closeOnInteraction = false;\n        /** Subject for notifying that the tooltip has been hidden from the view */\n        this._onHide = new Subject();\n    }\n    /**\n     * Shows the tooltip with an animation originating from the provided origin\n     * @param delay Amount of milliseconds to the delay showing the tooltip.\n     */\n    show(delay) {\n        // Cancel the delayed hide if it is scheduled\n        clearTimeout(this._hideTimeoutId);\n        // Body interactions should cancel the tooltip if there is a delay in showing.\n        this._closeOnInteraction = true;\n        this._showTimeoutId = setTimeout(() => {\n            this._visibility = 'visible';\n            this._showTimeoutId = undefined;\n            this._onShow();\n            // Mark for check so if any parent component has set the\n            // ChangeDetectionStrategy to OnPush it will be checked anyways\n            this._markForCheck();\n        }, delay);\n    }\n    /**\n     * Begins the animation to hide the tooltip after the provided delay in ms.\n     * @param delay Amount of milliseconds to delay showing the tooltip.\n     */\n    hide(delay) {\n        // Cancel the delayed show if it is scheduled\n        clearTimeout(this._showTimeoutId);\n        this._hideTimeoutId = setTimeout(() => {\n            this._visibility = 'hidden';\n            this._hideTimeoutId = undefined;\n            // Mark for check so if any parent component has set the\n            // ChangeDetectionStrategy to OnPush it will be checked anyways\n            this._markForCheck();\n        }, delay);\n    }\n    /** Returns an observable that notifies when the tooltip has been hidden from view. */\n    afterHidden() {\n        return this._onHide;\n    }\n    /** Whether the tooltip is being displayed. */\n    isVisible() {\n        return this._visibility === 'visible';\n    }\n    ngOnDestroy() {\n        clearTimeout(this._showTimeoutId);\n        clearTimeout(this._hideTimeoutId);\n        this._onHide.complete();\n    }\n    _animationStart() {\n        this._closeOnInteraction = false;\n    }\n    _animationDone(event) {\n        const toState = event.toState;\n        if (toState === 'hidden' && !this.isVisible()) {\n            this._onHide.next();\n        }\n        if (toState === 'visible' || toState === 'hidden') {\n            this._closeOnInteraction = true;\n        }\n    }\n    /**\n     * Interactions on the HTML body should close the tooltip immediately as defined in the\n     * material design spec.\n     * https://material.io/design/components/tooltips.html#behavior\n     */\n    _handleBodyInteraction() {\n        if (this._closeOnInteraction) {\n            this.hide(0);\n        }\n    }\n    /**\n     * Marks that the tooltip needs to be checked in the next change detection run.\n     * Mainly used for rendering the initial text before positioning a tooltip, which\n     * can be problematic in components with OnPush change detection.\n     */\n    _markForCheck() {\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Callback for when the timeout in this.show() gets completed.\n     * This method is only needed by the mdc-tooltip, and so it is only implemented\n     * in the mdc-tooltip, not here.\n     */\n    _onShow() { }\n}\n_TooltipComponentBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.2.0\", ngImport: i0, type: _TooltipComponentBase, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n_TooltipComponentBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.2.0\", type: _TooltipComponentBase, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.2.0\", ngImport: i0, type: _TooltipComponentBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }]; } });\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\nclass TooltipComponent extends _TooltipComponentBase {\n    constructor(changeDetectorRef, _breakpointObserver) {\n        super(changeDetectorRef);\n        this._breakpointObserver = _breakpointObserver;\n        /** Stream that emits whether the user has a handset-sized display.  */\n        this._isHandset = this._breakpointObserver.observe(Breakpoints.Handset);\n    }\n}\nTooltipComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.2.0\", ngImport: i0, type: TooltipComponent, deps: [{ token: i0.ChangeDetectorRef }, { token: i6.BreakpointObserver }], target: i0.ɵɵFactoryTarget.Component });\nTooltipComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.2.0\", type: TooltipComponent, selector: \"mat-tooltip-component\", host: { attributes: { \"aria-hidden\": \"true\" }, properties: { \"style.zoom\": \"_visibility === \\\"visible\\\" ? 1 : null\" } }, usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-tooltip\\\"\\n     [ngClass]=\\\"tooltipClass\\\"\\n     [class.mat-tooltip-handset]=\\\"(_isHandset | async)?.matches\\\"\\n     [@state]=\\\"_visibility\\\"\\n     (@state.start)=\\\"_animationStart()\\\"\\n     (@state.done)=\\\"_animationDone($event)\\\">{{message}}</div>\\n\", styles: [\".mat-tooltip-panel{pointer-events:none !important}.mat-tooltip{color:#fff;border-radius:4px;margin:14px;max-width:250px;padding-left:8px;padding-right:8px;overflow:hidden;text-overflow:ellipsis}.cdk-high-contrast-active .mat-tooltip{outline:solid 1px}.mat-tooltip-handset{margin:24px;padding-left:16px;padding-right:16px}\\n\"], directives: [{ type: i7.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], pipes: { \"async\": i7.AsyncPipe }, animations: [matTooltipAnimations.tooltipState], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.2.0\", ngImport: i0, type: TooltipComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tooltip-component', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, animations: [matTooltipAnimations.tooltipState], host: {\n                        // Forces the element to have a layout in IE and Edge. This fixes issues where the element\n                        // won't be rendered if the animations are disabled or there is no web animations polyfill.\n                        '[style.zoom]': '_visibility === \"visible\" ? 1 : null',\n                        'aria-hidden': 'true',\n                    }, template: \"<div class=\\\"mat-tooltip\\\"\\n     [ngClass]=\\\"tooltipClass\\\"\\n     [class.mat-tooltip-handset]=\\\"(_isHandset | async)?.matches\\\"\\n     [@state]=\\\"_visibility\\\"\\n     (@state.start)=\\\"_animationStart()\\\"\\n     (@state.done)=\\\"_animationDone($event)\\\">{{message}}</div>\\n\", styles: [\".mat-tooltip-panel{pointer-events:none !important}.mat-tooltip{color:#fff;border-radius:4px;margin:14px;max-width:250px;padding-left:8px;padding-right:8px;overflow:hidden;text-overflow:ellipsis}.cdk-high-contrast-active .mat-tooltip{outline:solid 1px}.mat-tooltip-handset{margin:24px;padding-left:16px;padding-right:16px}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: i6.BreakpointObserver }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatTooltipModule {\n}\nMatTooltipModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.2.0\", ngImport: i0, type: MatTooltipModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatTooltipModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.2.0\", ngImport: i0, type: MatTooltipModule, declarations: [MatTooltip, TooltipComponent], imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule], exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule] });\nMatTooltipModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.2.0\", ngImport: i0, type: MatTooltipModule, providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [[A11yModule, CommonModule, OverlayModule, MatCommonModule], MatCommonModule, CdkScrollableModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.2.0\", ngImport: i0, type: MatTooltipModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule],\n                    exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n                    declarations: [MatTooltip, TooltipComponent],\n                    providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TOOLTIP_DEFAULT_OPTIONS, MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER, MatTooltip, MatTooltipModule, SCROLL_THROTTLE_MS, TOOLTIP_PANEL_CLASS, TooltipComponent, _MatTooltipBase, _TooltipComponentBase, getMatTooltipInvalidPositionError, matTooltipAnimations };\n"]}, "metadata": {}, "sourceType": "module"}