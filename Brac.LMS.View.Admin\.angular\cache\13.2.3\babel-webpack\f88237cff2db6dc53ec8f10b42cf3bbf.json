{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Component, ChangeDetectionStrategy, Input, Directive, Output, NgModule } from '@angular/core';\nimport { getBsVer, parseTriggers } from 'ngx-bootstrap/utils';\nimport * as i3 from 'ngx-bootstrap/positioning';\nimport { PlacementForBs5, checkMargins, PositioningService } from 'ngx-bootstrap/positioning';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { timer } from 'rxjs';\nimport * as i2$1 from 'ngx-bootstrap/component-loader';\nimport { ComponentLoaderFactory } from 'ngx-bootstrap/component-loader';\n/**\n * Configuration service for the Popover directive.\n * You can inject this service, typically in your root component, and customize\n * the values of its properties in order to provide default values for all the\n * popovers used in the application.\n */\n\nfunction PopoverContainerComponent_h3_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.title);\n  }\n}\n\nconst _c0 = [\"*\"];\n\nclass PopoverConfig {\n  constructor() {\n    /** sets disable adaptive position */\n    this.adaptivePosition = true;\n    /**\n     * Placement of a popover. Accepts: \"top\", \"bottom\", \"left\", \"right\", \"auto\"\n     */\n\n    this.placement = 'top';\n    /**\n     * Specifies events that should trigger. Supports a space separated list of\n     * event names.\n     */\n\n    this.triggers = 'click';\n    this.outsideClick = false;\n    /** delay before showing the tooltip */\n\n    this.delay = 0;\n  }\n\n}\n\nPopoverConfig.ɵfac = function PopoverConfig_Factory(t) {\n  return new (t || PopoverConfig)();\n};\n\nPopoverConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: PopoverConfig,\n  factory: PopoverConfig.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PopoverConfig, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\nclass PopoverContainerComponent {\n  constructor(config) {\n    this._placement = 'top';\n    Object.assign(this, config);\n  }\n\n  set placement(value) {\n    if (!this._bsVersions.isBs5) {\n      this._placement = value;\n    } else {\n      this._placement = PlacementForBs5[value];\n    }\n  }\n\n  get _bsVersions() {\n    return getBsVer();\n  }\n\n  checkMarginNecessity() {\n    return checkMargins(this._placement);\n  }\n\n}\n\nPopoverContainerComponent.ɵfac = function PopoverContainerComponent_Factory(t) {\n  return new (t || PopoverContainerComponent)(i0.ɵɵdirectiveInject(PopoverConfig));\n};\n\nPopoverContainerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: PopoverContainerComponent,\n  selectors: [[\"popover-container\"]],\n  hostAttrs: [\"role\", \"tooltip\", 2, \"display\", \"block\"],\n  hostVars: 7,\n  hostBindings: function PopoverContainerComponent_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"id\", ctx.popoverId);\n      i0.ɵɵclassMap(\"popover in popover-\" + ctx._placement + \" \" + \"bs-popover-\" + ctx._placement + \" \" + ctx._placement + \" \" + ctx.containerClass + \" \" + ctx.checkMarginNecessity());\n      i0.ɵɵclassProp(\"show\", !ctx._bsVersions.isBs3)(\"bs3\", ctx._bsVersions.isBs3);\n    }\n  },\n  inputs: {\n    placement: \"placement\",\n    title: \"title\"\n  },\n  ngContentSelectors: _c0,\n  decls: 4,\n  vars: 1,\n  consts: [[1, \"popover-arrow\", \"arrow\"], [\"class\", \"popover-title popover-header\", 4, \"ngIf\"], [1, \"popover-content\", \"popover-body\"], [1, \"popover-title\", \"popover-header\"]],\n  template: function PopoverContainerComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelement(0, \"div\", 0);\n      i0.ɵɵtemplate(1, PopoverContainerComponent_h3_1_Template, 2, 1, \"h3\", 1);\n      i0.ɵɵelementStart(2, \"div\", 2);\n      i0.ɵɵprojection(3);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.title);\n    }\n  },\n  directives: [i2.NgIf],\n  styles: [\".bs3.popover-top[_nghost-%COMP%]{margin-bottom:10px}.bs3.popover.top[_nghost-%COMP%] > .arrow[_ngcontent-%COMP%]{margin-left:-2px}.bs3.popover.top[_nghost-%COMP%]{margin-bottom:10px}.popover.bottom[_nghost-%COMP%] > .arrow[_ngcontent-%COMP%]{margin-left:-4px}.bs3.bs-popover-left[_nghost-%COMP%]{margin-right:.5rem}.bs3.bs-popover-right[_nghost-%COMP%]   .arrow[_ngcontent-%COMP%], .bs3.bs-popover-left[_nghost-%COMP%]   .arrow[_ngcontent-%COMP%]{margin:.3rem 0}\"],\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PopoverContainerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'popover-container',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[attr.id]': 'popoverId',\n        '[class]': '\"popover in popover-\" + _placement + \" \" + \"bs-popover-\" + _placement + \" \" + _placement + \" \" + containerClass + \" \" + checkMarginNecessity()',\n        '[class.show]': '!_bsVersions.isBs3',\n        '[class.bs3]': '_bsVersions.isBs3',\n        role: 'tooltip',\n        style: 'display:block;'\n      },\n      styles: [`\n    :host.bs3.popover-top {\n      margin-bottom: 10px;\n    }\n    :host.bs3.popover.top>.arrow {\n      margin-left: -2px;\n    }\n    :host.bs3.popover.top {\n      margin-bottom: 10px;\n    }\n    :host.popover.bottom>.arrow {\n      margin-left: -4px;\n    }\n    :host.bs3.bs-popover-left {\n      margin-right: .5rem;\n    }\n    :host.bs3.bs-popover-right .arrow, :host.bs3.bs-popover-left .arrow{\n      margin: .3rem 0;\n    }\n    `],\n      template: \"<div class=\\\"popover-arrow arrow\\\"></div>\\n<h3 class=\\\"popover-title popover-header\\\" *ngIf=\\\"title\\\">{{ title }}</h3>\\n<div class=\\\"popover-content popover-body\\\">\\n  <ng-content></ng-content>\\n</div>\\n\"\n    }]\n  }], function () {\n    return [{\n      type: PopoverConfig\n    }];\n  }, {\n    placement: [{\n      type: Input\n    }],\n    title: [{\n      type: Input\n    }]\n  });\n})();\n\nlet id = 0;\n/**\n * A lightweight, extensible directive for fancy popover creation.\n */\n\nclass PopoverDirective {\n  constructor(_config, _elementRef, _renderer, _viewContainerRef, cis, _positionService) {\n    this._elementRef = _elementRef;\n    this._renderer = _renderer;\n    this._positionService = _positionService;\n    /** unique id popover - use for aria-describedby */\n\n    this.popoverId = id++;\n    /** sets disable adaptive position */\n\n    this.adaptivePosition = true;\n    /**\n     * Placement of a popover. Accepts: \"top\", \"bottom\", \"left\", \"right\"\n     */\n\n    this.placement = 'top';\n    /**\n     * Close popover on outside click\n     */\n\n    this.outsideClick = false;\n    /**\n     * Specifies events that should trigger. Supports a space separated list of\n     * event names.\n     */\n\n    this.triggers = 'click';\n    /**\n     * Css class for popover container\n     */\n\n    this.containerClass = '';\n    /**\n     * Delay before showing the tooltip\n     */\n\n    this.delay = 0;\n    this._isInited = false;\n    this._popover = cis.createLoader(_elementRef, _viewContainerRef, _renderer).provide({\n      provide: PopoverConfig,\n      useValue: _config\n    });\n    Object.assign(this, _config);\n    this.onShown = this._popover.onShown;\n    this.onHidden = this._popover.onHidden; // fix: no focus on button on Mac OS #1795\n\n    if (typeof window !== 'undefined') {\n      _elementRef.nativeElement.addEventListener('click', function () {\n        try {\n          _elementRef.nativeElement.focus();\n        } catch (err) {\n          return;\n        }\n      });\n    }\n  }\n  /**\n   * Returns whether or not the popover is currently being shown\n   */\n\n\n  get isOpen() {\n    return this._popover.isShown;\n  }\n\n  set isOpen(value) {\n    if (value) {\n      this.show();\n    } else {\n      this.hide();\n    }\n  }\n  /**\n   * Set attribute aria-describedBy for element directive and\n   * set id for the popover\n   */\n\n\n  setAriaDescribedBy() {\n    this._ariaDescribedby = this.isOpen ? `ngx-popover-${this.popoverId}` : void 0;\n\n    if (this._ariaDescribedby) {\n      if (this._popover.instance) {\n        this._popover.instance.popoverId = this._ariaDescribedby;\n      }\n\n      this._renderer.setAttribute(this._elementRef.nativeElement, 'aria-describedby', this._ariaDescribedby);\n    } else {\n      this._renderer.removeAttribute(this._elementRef.nativeElement, 'aria-describedby');\n    }\n  }\n  /**\n   * Opens an element’s popover. This is considered a “manual” triggering of\n   * the popover.\n   */\n\n\n  show() {\n    if (this._popover.isShown || !this.popover || this._delayTimeoutId) {\n      return;\n    }\n\n    this._positionService.setOptions({\n      modifiers: {\n        flip: {\n          enabled: this.adaptivePosition\n        },\n        preventOverflow: {\n          enabled: this.adaptivePosition\n        }\n      }\n    });\n\n    const showPopover = () => {\n      if (this._delayTimeoutId) {\n        this._delayTimeoutId = undefined;\n      }\n\n      this._popover.attach(PopoverContainerComponent).to(this.container).position({\n        attachment: this.placement\n      }).show({\n        content: this.popover,\n        context: this.popoverContext,\n        placement: this.placement,\n        title: this.popoverTitle,\n        containerClass: this.containerClass\n      });\n\n      if (!this.adaptivePosition && this._popover._componentRef) {\n        this._positionService.calcPosition();\n\n        this._positionService.deletePositionElement(this._popover._componentRef.location);\n      }\n\n      this.isOpen = true;\n      this.setAriaDescribedBy();\n    };\n\n    const cancelDelayedTooltipShowing = () => {\n      if (this._popoverCancelShowFn) {\n        this._popoverCancelShowFn();\n      }\n    };\n\n    if (this.delay) {\n      const _timer = timer(this.delay).subscribe(() => {\n        showPopover();\n        cancelDelayedTooltipShowing();\n      });\n\n      if (this.triggers) {\n        parseTriggers(this.triggers).forEach(trigger => {\n          if (!trigger.close) {\n            return;\n          }\n\n          this._popoverCancelShowFn = this._renderer.listen(this._elementRef.nativeElement, trigger.close, () => {\n            _timer.unsubscribe();\n\n            cancelDelayedTooltipShowing();\n          });\n        });\n      }\n    } else {\n      showPopover();\n    }\n  }\n  /**\n   * Closes an element’s popover. This is considered a “manual” triggering of\n   * the popover.\n   */\n\n\n  hide() {\n    if (this._delayTimeoutId) {\n      clearTimeout(this._delayTimeoutId);\n      this._delayTimeoutId = undefined;\n    }\n\n    if (this.isOpen) {\n      this._popover.hide();\n\n      this.setAriaDescribedBy();\n      this.isOpen = false;\n    }\n  }\n  /**\n   * Toggles an element’s popover. This is considered a “manual” triggering of\n   * the popover.\n   */\n\n\n  toggle() {\n    if (this.isOpen) {\n      return this.hide();\n    }\n\n    this.show();\n  }\n\n  ngOnInit() {\n    // fix: seems there are an issue with `routerLinkActive`\n    // which result in duplicated call ngOnInit without call to ngOnDestroy\n    // read more: https://github.com/valor-software/ngx-bootstrap/issues/1885\n    if (this._isInited) {\n      return;\n    }\n\n    this._isInited = true;\n\n    this._popover.listen({\n      triggers: this.triggers,\n      outsideClick: this.outsideClick,\n      show: () => this.show(),\n      hide: () => this.hide()\n    });\n  }\n\n  ngOnDestroy() {\n    this._popover.dispose();\n  }\n\n}\n\nPopoverDirective.ɵfac = function PopoverDirective_Factory(t) {\n  return new (t || PopoverDirective)(i0.ɵɵdirectiveInject(PopoverConfig), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i2$1.ComponentLoaderFactory), i0.ɵɵdirectiveInject(i3.PositioningService));\n};\n\nPopoverDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: PopoverDirective,\n  selectors: [[\"\", \"popover\", \"\"]],\n  inputs: {\n    adaptivePosition: \"adaptivePosition\",\n    popover: \"popover\",\n    popoverContext: \"popoverContext\",\n    popoverTitle: \"popoverTitle\",\n    placement: \"placement\",\n    outsideClick: \"outsideClick\",\n    triggers: \"triggers\",\n    container: \"container\",\n    containerClass: \"containerClass\",\n    isOpen: \"isOpen\",\n    delay: \"delay\"\n  },\n  outputs: {\n    onShown: \"onShown\",\n    onHidden: \"onHidden\"\n  },\n  exportAs: [\"bs-popover\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PopoverDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[popover]',\n      exportAs: 'bs-popover'\n    }]\n  }], function () {\n    return [{\n      type: PopoverConfig\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: i2$1.ComponentLoaderFactory\n    }, {\n      type: i3.PositioningService\n    }];\n  }, {\n    adaptivePosition: [{\n      type: Input\n    }],\n    popover: [{\n      type: Input\n    }],\n    popoverContext: [{\n      type: Input\n    }],\n    popoverTitle: [{\n      type: Input\n    }],\n    placement: [{\n      type: Input\n    }],\n    outsideClick: [{\n      type: Input\n    }],\n    triggers: [{\n      type: Input\n    }],\n    container: [{\n      type: Input\n    }],\n    containerClass: [{\n      type: Input\n    }],\n    isOpen: [{\n      type: Input\n    }],\n    delay: [{\n      type: Input\n    }],\n    onShown: [{\n      type: Output\n    }],\n    onHidden: [{\n      type: Output\n    }]\n  });\n})();\n\nclass PopoverModule {\n  static forRoot() {\n    return {\n      ngModule: PopoverModule,\n      providers: [ComponentLoaderFactory, PositioningService]\n    };\n  }\n\n}\n\nPopoverModule.ɵfac = function PopoverModule_Factory(t) {\n  return new (t || PopoverModule)();\n};\n\nPopoverModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: PopoverModule\n});\nPopoverModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PopoverModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [PopoverDirective, PopoverContainerComponent],\n      exports: [PopoverDirective],\n      entryComponents: [PopoverContainerComponent]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { PopoverConfig, PopoverContainerComponent, PopoverDirective, PopoverModule };", "map": {"version": 3, "sources": ["D:/Office Projects/brac-lms/Brac.LMS.View.Admin/node_modules/ngx-bootstrap/popover/fesm2015/ngx-bootstrap-popover.mjs"], "names": ["i0", "Injectable", "Component", "ChangeDetectionStrategy", "Input", "Directive", "Output", "NgModule", "getBsVer", "parseTriggers", "i3", "PlacementForBs5", "<PERSON><PERSON><PERSON><PERSON>", "PositioningService", "i2", "CommonModule", "timer", "i2$1", "ComponentLoaderFactory", "PopoverConfig", "constructor", "adaptivePosition", "placement", "triggers", "outsideClick", "delay", "ɵfac", "ɵprov", "type", "args", "providedIn", "PopoverContainerComponent", "config", "_placement", "Object", "assign", "value", "_bsVersions", "isBs5", "checkMarginNecessity", "ɵcmp", "NgIf", "selector", "changeDetection", "OnPush", "host", "role", "style", "styles", "template", "title", "id", "PopoverDirective", "_config", "_elementRef", "_renderer", "_viewContainerRef", "cis", "_positionService", "popoverId", "containerClass", "_isInited", "_popover", "createLoader", "provide", "useValue", "onShown", "onHidden", "window", "nativeElement", "addEventListener", "focus", "err", "isOpen", "isShown", "show", "hide", "setAriaDescribedBy", "_ariaDes<PERSON>by", "instance", "setAttribute", "removeAttribute", "popover", "_delayTimeoutId", "setOptions", "modifiers", "flip", "enabled", "preventOverflow", "showPopover", "undefined", "attach", "to", "container", "position", "attachment", "content", "context", "popoverContext", "popoverTitle", "_componentRef", "calcPosition", "deletePositionElement", "location", "cancelDelayedTooltipShowing", "_popoverCancelShowFn", "_timer", "subscribe", "for<PERSON>ach", "trigger", "close", "listen", "unsubscribe", "clearTimeout", "toggle", "ngOnInit", "ngOnDestroy", "dispose", "ElementRef", "Renderer2", "ViewContainerRef", "ɵdir", "exportAs", "PopoverModule", "forRoot", "ngModule", "providers", "ɵmod", "ɵinj", "imports", "declarations", "exports", "entryComponents"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,SAArB,EAAgCC,uBAAhC,EAAyDC,KAAzD,EAAgEC,SAAhE,EAA2EC,MAA3E,EAAmFC,QAAnF,QAAmG,eAAnG;AACA,SAASC,QAAT,EAAmBC,aAAnB,QAAwC,qBAAxC;AACA,OAAO,KAAKC,EAAZ,MAAoB,2BAApB;AACA,SAASC,eAAT,EAA0BC,YAA1B,EAAwCC,kBAAxC,QAAkE,2BAAlE;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,KAAT,QAAsB,MAAtB;AACA,OAAO,KAAKC,IAAZ,MAAsB,gCAAtB;AACA,SAASC,sBAAT,QAAuC,gCAAvC;AAEA;AACA;AACA;AACA;AACA;AACA;;;;AAmBgGlB,IAAAA,EA+BgiB,2B;AA/BhiBA,IAAAA,EA+B2lB,U;AA/B3lBA,IAAAA,EA+BsmB,e;;;;mBA/BtmBA,E;AAAAA,IAAAA,EA+B2lB,a;AA/B3lBA,IAAAA,EA+B2lB,gC;;;;;;AAjD3rB,MAAMmB,aAAN,CAAoB;AAChBC,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,gBAAL,GAAwB,IAAxB;AACA;AACR;AACA;;AACQ,SAAKC,SAAL,GAAiB,KAAjB;AACA;AACR;AACA;AACA;;AACQ,SAAKC,QAAL,GAAgB,OAAhB;AACA,SAAKC,YAAL,GAAoB,KAApB;AACA;;AACA,SAAKC,KAAL,GAAa,CAAb;AACH;;AAhBe;;AAkBpBN,aAAa,CAACO,IAAd;AAAA,mBAA0GP,aAA1G;AAAA;;AACAA,aAAa,CAACQ,KAAd,kBADgG3B,EAChG;AAAA,SAA8GmB,aAA9G;AAAA,WAA8GA,aAA9G;AAAA,cAAyI;AAAzI;;AACA;AAAA,qDAFgGnB,EAEhG,mBAA2FmB,aAA3F,EAAsH,CAAC;AAC3GS,IAAAA,IAAI,EAAE3B,UADqG;AAE3G4B,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,UAAU,EAAE;AADb,KAAD;AAFqG,GAAD,CAAtH;AAAA;;AAOA,MAAMC,yBAAN,CAAgC;AAC5BX,EAAAA,WAAW,CAACY,MAAD,EAAS;AAChB,SAAKC,UAAL,GAAkB,KAAlB;AACAC,IAAAA,MAAM,CAACC,MAAP,CAAc,IAAd,EAAoBH,MAApB;AACH;;AACY,MAATV,SAAS,CAACc,KAAD,EAAQ;AACjB,QAAI,CAAC,KAAKC,WAAL,CAAiBC,KAAtB,EAA6B;AACzB,WAAKL,UAAL,GAAkBG,KAAlB;AACH,KAFD,MAGK;AACD,WAAKH,UAAL,GAAkBtB,eAAe,CAACyB,KAAD,CAAjC;AACH;AACJ;;AAEc,MAAXC,WAAW,GAAG;AACd,WAAO7B,QAAQ,EAAf;AACH;;AACD+B,EAAAA,oBAAoB,GAAG;AACnB,WAAO3B,YAAY,CAAC,KAAKqB,UAAN,CAAnB;AACH;;AAnB2B;;AAqBhCF,yBAAyB,CAACL,IAA1B;AAAA,mBAAsHK,yBAAtH,EA9BgG/B,EA8BhG,mBAAiKmB,aAAjK;AAAA;;AACAY,yBAAyB,CAACS,IAA1B,kBA/BgGxC,EA+BhG;AAAA,QAA0G+B,yBAA1G;AAAA;AAAA,sBAAsP,SAAtP;AAAA;AAAA;AAAA;AA/BgG/B,MAAAA,EA+BhG;AA/BgGA,MAAAA,EA+BhG;AA/BgGA,MAAAA,EA+BhG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA/BgGA,MAAAA,EA+BhG;AA/BgGA,MAAAA,EA+Bqf,uBAArlB;AA/BgGA,MAAAA,EA+BgiB,sEAAhoB;AA/BgGA,MAAAA,EA+B6mB,4BAA7sB;AA/BgGA,MAAAA,EA+B6pB,gBAA7vB;AA/BgGA,MAAAA,EA+BwrB,eAAxxB;AAAA;;AAAA;AA/BgGA,MAAAA,EA+B4kB,aAA5qB;AA/BgGA,MAAAA,EA+B4kB,8BAA5qB;AAAA;AAAA;AAAA,eAAonCc,EAAE,CAAC2B,IAAvnC;AAAA;AAAA;AAAA;;AACA;AAAA,qDAhCgGzC,EAgChG,mBAA2F+B,yBAA3F,EAAkI,CAAC;AACvHH,IAAAA,IAAI,EAAE1B,SADiH;AAEvH2B,IAAAA,IAAI,EAAE,CAAC;AAAEa,MAAAA,QAAQ,EAAE,mBAAZ;AAAiCC,MAAAA,eAAe,EAAExC,uBAAuB,CAACyC,MAA1E;AAAkFC,MAAAA,IAAI,EAAE;AACnF,qBAAa,WADsE;AAEnF,mBAAW,gJAFwE;AAGnF,wBAAgB,oBAHmE;AAInF,uBAAe,mBAJoE;AAKnFC,QAAAA,IAAI,EAAE,SAL6E;AAMnFC,QAAAA,KAAK,EAAE;AAN4E,OAAxF;AAOIC,MAAAA,MAAM,EAAE,CACN;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KApB+B,CAPZ;AA4BIC,MAAAA,QAAQ,EAAE;AA5Bd,KAAD;AAFiH,GAAD,CAAlI,EA+B4B,YAAY;AAAE,WAAO,CAAC;AAAErB,MAAAA,IAAI,EAAET;AAAR,KAAD,CAAP;AAAmC,GA/B7E,EA+B+F;AAAEG,IAAAA,SAAS,EAAE,CAAC;AAC7FM,MAAAA,IAAI,EAAExB;AADuF,KAAD,CAAb;AAE/E8C,IAAAA,KAAK,EAAE,CAAC;AACRtB,MAAAA,IAAI,EAAExB;AADE,KAAD;AAFwE,GA/B/F;AAAA;;AAqCA,IAAI+C,EAAE,GAAG,CAAT;AACA;AACA;AACA;;AACA,MAAMC,gBAAN,CAAuB;AACnBhC,EAAAA,WAAW,CAACiC,OAAD,EAAUC,WAAV,EAAuBC,SAAvB,EAAkCC,iBAAlC,EAAqDC,GAArD,EAA0DC,gBAA1D,EAA4E;AACnF,SAAKJ,WAAL,GAAmBA,WAAnB;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACA,SAAKG,gBAAL,GAAwBA,gBAAxB;AACA;;AACA,SAAKC,SAAL,GAAiBR,EAAE,EAAnB;AACA;;AACA,SAAK9B,gBAAL,GAAwB,IAAxB;AACA;AACR;AACA;;AACQ,SAAKC,SAAL,GAAiB,KAAjB;AACA;AACR;AACA;;AACQ,SAAKE,YAAL,GAAoB,KAApB;AACA;AACR;AACA;AACA;;AACQ,SAAKD,QAAL,GAAgB,OAAhB;AACA;AACR;AACA;;AACQ,SAAKqC,cAAL,GAAsB,EAAtB;AACA;AACR;AACA;;AACQ,SAAKnC,KAAL,GAAa,CAAb;AACA,SAAKoC,SAAL,GAAiB,KAAjB;AACA,SAAKC,QAAL,GAAgBL,GAAG,CACdM,YADW,CACET,WADF,EACeE,iBADf,EACkCD,SADlC,EAEXS,OAFW,CAEH;AAAEA,MAAAA,OAAO,EAAE7C,aAAX;AAA0B8C,MAAAA,QAAQ,EAAEZ;AAApC,KAFG,CAAhB;AAGAnB,IAAAA,MAAM,CAACC,MAAP,CAAc,IAAd,EAAoBkB,OAApB;AACA,SAAKa,OAAL,GAAe,KAAKJ,QAAL,CAAcI,OAA7B;AACA,SAAKC,QAAL,GAAgB,KAAKL,QAAL,CAAcK,QAA9B,CAnCmF,CAoCnF;;AACA,QAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmC;AAC/Bd,MAAAA,WAAW,CAACe,aAAZ,CAA0BC,gBAA1B,CAA2C,OAA3C,EAAoD,YAAY;AAC5D,YAAI;AACAhB,UAAAA,WAAW,CAACe,aAAZ,CAA0BE,KAA1B;AACH,SAFD,CAGA,OAAOC,GAAP,EAAY;AACR;AACH;AACJ,OAPD;AAQH;AACJ;AACD;AACJ;AACA;;;AACc,MAANC,MAAM,GAAG;AACT,WAAO,KAAKX,QAAL,CAAcY,OAArB;AACH;;AACS,MAAND,MAAM,CAACrC,KAAD,EAAQ;AACd,QAAIA,KAAJ,EAAW;AACP,WAAKuC,IAAL;AACH,KAFD,MAGK;AACD,WAAKC,IAAL;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,kBAAkB,GAAG;AACjB,SAAKC,gBAAL,GAAwB,KAAKL,MAAL,GAAe,eAAc,KAAKd,SAAU,EAA5C,GAAgD,KAAK,CAA7E;;AACA,QAAI,KAAKmB,gBAAT,EAA2B;AACvB,UAAI,KAAKhB,QAAL,CAAciB,QAAlB,EAA4B;AACxB,aAAKjB,QAAL,CAAciB,QAAd,CAAuBpB,SAAvB,GAAmC,KAAKmB,gBAAxC;AACH;;AACD,WAAKvB,SAAL,CAAeyB,YAAf,CAA4B,KAAK1B,WAAL,CAAiBe,aAA7C,EAA4D,kBAA5D,EAAgF,KAAKS,gBAArF;AACH,KALD,MAMK;AACD,WAAKvB,SAAL,CAAe0B,eAAf,CAA+B,KAAK3B,WAAL,CAAiBe,aAAhD,EAA+D,kBAA/D;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIM,EAAAA,IAAI,GAAG;AACH,QAAI,KAAKb,QAAL,CAAcY,OAAd,IAAyB,CAAC,KAAKQ,OAA/B,IAA0C,KAAKC,eAAnD,EAAoE;AAChE;AACH;;AACD,SAAKzB,gBAAL,CAAsB0B,UAAtB,CAAiC;AAC7BC,MAAAA,SAAS,EAAE;AACPC,QAAAA,IAAI,EAAE;AACFC,UAAAA,OAAO,EAAE,KAAKlE;AADZ,SADC;AAIPmE,QAAAA,eAAe,EAAE;AACbD,UAAAA,OAAO,EAAE,KAAKlE;AADD;AAJV;AADkB,KAAjC;;AAUA,UAAMoE,WAAW,GAAG,MAAM;AACtB,UAAI,KAAKN,eAAT,EAA0B;AACtB,aAAKA,eAAL,GAAuBO,SAAvB;AACH;;AACD,WAAK5B,QAAL,CACK6B,MADL,CACY5D,yBADZ,EAEK6D,EAFL,CAEQ,KAAKC,SAFb,EAGKC,QAHL,CAGc;AAAEC,QAAAA,UAAU,EAAE,KAAKzE;AAAnB,OAHd,EAIKqD,IAJL,CAIU;AACNqB,QAAAA,OAAO,EAAE,KAAKd,OADR;AAENe,QAAAA,OAAO,EAAE,KAAKC,cAFR;AAGN5E,QAAAA,SAAS,EAAE,KAAKA,SAHV;AAIN4B,QAAAA,KAAK,EAAE,KAAKiD,YAJN;AAKNvC,QAAAA,cAAc,EAAE,KAAKA;AALf,OAJV;;AAWA,UAAI,CAAC,KAAKvC,gBAAN,IAA0B,KAAKyC,QAAL,CAAcsC,aAA5C,EAA2D;AACvD,aAAK1C,gBAAL,CAAsB2C,YAAtB;;AACA,aAAK3C,gBAAL,CAAsB4C,qBAAtB,CAA4C,KAAKxC,QAAL,CAAcsC,aAAd,CAA4BG,QAAxE;AACH;;AACD,WAAK9B,MAAL,GAAc,IAAd;AACA,WAAKI,kBAAL;AACH,KArBD;;AAsBA,UAAM2B,2BAA2B,GAAG,MAAM;AACtC,UAAI,KAAKC,oBAAT,EAA+B;AAC3B,aAAKA,oBAAL;AACH;AACJ,KAJD;;AAKA,QAAI,KAAKhF,KAAT,EAAgB;AACZ,YAAMiF,MAAM,GAAG1F,KAAK,CAAC,KAAKS,KAAN,CAAL,CAAkBkF,SAAlB,CAA4B,MAAM;AAC7ClB,QAAAA,WAAW;AACXe,QAAAA,2BAA2B;AAC9B,OAHc,CAAf;;AAIA,UAAI,KAAKjF,QAAT,EAAmB;AACfd,QAAAA,aAAa,CAAC,KAAKc,QAAN,CAAb,CACKqF,OADL,CACcC,OAAD,IAAa;AACtB,cAAI,CAACA,OAAO,CAACC,KAAb,EAAoB;AAChB;AACH;;AACD,eAAKL,oBAAL,GAA4B,KAAKlD,SAAL,CAAewD,MAAf,CAAsB,KAAKzD,WAAL,CAAiBe,aAAvC,EAAsDwC,OAAO,CAACC,KAA9D,EAAqE,MAAM;AACnGJ,YAAAA,MAAM,CAACM,WAAP;;AACAR,YAAAA,2BAA2B;AAC9B,WAH2B,CAA5B;AAIH,SATD;AAUH;AACJ,KAjBD,MAkBK;AACDf,MAAAA,WAAW;AACd;AACJ;AACD;AACJ;AACA;AACA;;;AACIb,EAAAA,IAAI,GAAG;AACH,QAAI,KAAKO,eAAT,EAA0B;AACtB8B,MAAAA,YAAY,CAAC,KAAK9B,eAAN,CAAZ;AACA,WAAKA,eAAL,GAAuBO,SAAvB;AACH;;AACD,QAAI,KAAKjB,MAAT,EAAiB;AACb,WAAKX,QAAL,CAAcc,IAAd;;AACA,WAAKC,kBAAL;AACA,WAAKJ,MAAL,GAAc,KAAd;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIyC,EAAAA,MAAM,GAAG;AACL,QAAI,KAAKzC,MAAT,EAAiB;AACb,aAAO,KAAKG,IAAL,EAAP;AACH;;AACD,SAAKD,IAAL;AACH;;AACDwC,EAAAA,QAAQ,GAAG;AACP;AACA;AACA;AACA,QAAI,KAAKtD,SAAT,EAAoB;AAChB;AACH;;AACD,SAAKA,SAAL,GAAiB,IAAjB;;AACA,SAAKC,QAAL,CAAciD,MAAd,CAAqB;AACjBxF,MAAAA,QAAQ,EAAE,KAAKA,QADE;AAEjBC,MAAAA,YAAY,EAAE,KAAKA,YAFF;AAGjBmD,MAAAA,IAAI,EAAE,MAAM,KAAKA,IAAL,EAHK;AAIjBC,MAAAA,IAAI,EAAE,MAAM,KAAKA,IAAL;AAJK,KAArB;AAMH;;AACDwC,EAAAA,WAAW,GAAG;AACV,SAAKtD,QAAL,CAAcuD,OAAd;AACH;;AA5LkB;;AA8LvBjE,gBAAgB,CAAC1B,IAAjB;AAAA,mBAA6G0B,gBAA7G,EAvQgGpD,EAuQhG,mBAA+ImB,aAA/I,GAvQgGnB,EAuQhG,mBAAyKA,EAAE,CAACsH,UAA5K,GAvQgGtH,EAuQhG,mBAAmMA,EAAE,CAACuH,SAAtM,GAvQgGvH,EAuQhG,mBAA4NA,EAAE,CAACwH,gBAA/N,GAvQgGxH,EAuQhG,mBAA4PiB,IAAI,CAACC,sBAAjQ,GAvQgGlB,EAuQhG,mBAAoSU,EAAE,CAACG,kBAAvS;AAAA;;AACAuC,gBAAgB,CAACqE,IAAjB,kBAxQgGzH,EAwQhG;AAAA,QAAiGoD,gBAAjG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA,qDAzQgGpD,EAyQhG,mBAA2FoD,gBAA3F,EAAyH,CAAC;AAC9GxB,IAAAA,IAAI,EAAEvB,SADwG;AAE9GwB,IAAAA,IAAI,EAAE,CAAC;AAAEa,MAAAA,QAAQ,EAAE,WAAZ;AAAyBgF,MAAAA,QAAQ,EAAE;AAAnC,KAAD;AAFwG,GAAD,CAAzH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAE9F,MAAAA,IAAI,EAAET;AAAR,KAAD,EAA0B;AAAES,MAAAA,IAAI,EAAE5B,EAAE,CAACsH;AAAX,KAA1B,EAAmD;AAAE1F,MAAAA,IAAI,EAAE5B,EAAE,CAACuH;AAAX,KAAnD,EAA2E;AAAE3F,MAAAA,IAAI,EAAE5B,EAAE,CAACwH;AAAX,KAA3E,EAA0G;AAAE5F,MAAAA,IAAI,EAAEX,IAAI,CAACC;AAAb,KAA1G,EAAiJ;AAAEU,MAAAA,IAAI,EAAElB,EAAE,CAACG;AAAX,KAAjJ,CAAP;AAA2L,GAHrO,EAGuP;AAAEQ,IAAAA,gBAAgB,EAAE,CAAC;AAC5PO,MAAAA,IAAI,EAAExB;AADsP,KAAD,CAApB;AAEvO8E,IAAAA,OAAO,EAAE,CAAC;AACVtD,MAAAA,IAAI,EAAExB;AADI,KAAD,CAF8N;AAIvO8F,IAAAA,cAAc,EAAE,CAAC;AACjBtE,MAAAA,IAAI,EAAExB;AADW,KAAD,CAJuN;AAMvO+F,IAAAA,YAAY,EAAE,CAAC;AACfvE,MAAAA,IAAI,EAAExB;AADS,KAAD,CANyN;AAQvOkB,IAAAA,SAAS,EAAE,CAAC;AACZM,MAAAA,IAAI,EAAExB;AADM,KAAD,CAR4N;AAUvOoB,IAAAA,YAAY,EAAE,CAAC;AACfI,MAAAA,IAAI,EAAExB;AADS,KAAD,CAVyN;AAYvOmB,IAAAA,QAAQ,EAAE,CAAC;AACXK,MAAAA,IAAI,EAAExB;AADK,KAAD,CAZ6N;AAcvOyF,IAAAA,SAAS,EAAE,CAAC;AACZjE,MAAAA,IAAI,EAAExB;AADM,KAAD,CAd4N;AAgBvOwD,IAAAA,cAAc,EAAE,CAAC;AACjBhC,MAAAA,IAAI,EAAExB;AADW,KAAD,CAhBuN;AAkBvOqE,IAAAA,MAAM,EAAE,CAAC;AACT7C,MAAAA,IAAI,EAAExB;AADG,KAAD,CAlB+N;AAoBvOqB,IAAAA,KAAK,EAAE,CAAC;AACRG,MAAAA,IAAI,EAAExB;AADE,KAAD,CApBgO;AAsBvO8D,IAAAA,OAAO,EAAE,CAAC;AACVtC,MAAAA,IAAI,EAAEtB;AADI,KAAD,CAtB8N;AAwBvO6D,IAAAA,QAAQ,EAAE,CAAC;AACXvC,MAAAA,IAAI,EAAEtB;AADK,KAAD;AAxB6N,GAHvP;AAAA;;AA+BA,MAAMqH,aAAN,CAAoB;AACF,SAAPC,OAAO,GAAG;AACb,WAAO;AACHC,MAAAA,QAAQ,EAAEF,aADP;AAEHG,MAAAA,SAAS,EAAE,CAAC5G,sBAAD,EAAyBL,kBAAzB;AAFR,KAAP;AAIH;;AANe;;AAQpB8G,aAAa,CAACjG,IAAd;AAAA,mBAA0GiG,aAA1G;AAAA;;AACAA,aAAa,CAACI,IAAd,kBAjTgG/H,EAiThG;AAAA,QAA2G2H;AAA3G;AACAA,aAAa,CAACK,IAAd,kBAlTgGhI,EAkThG;AAAA,YAAoI,CAACe,YAAD,CAApI;AAAA;;AACA;AAAA,qDAnTgGf,EAmThG,mBAA2F2H,aAA3F,EAAsH,CAAC;AAC3G/F,IAAAA,IAAI,EAAErB,QADqG;AAE3GsB,IAAAA,IAAI,EAAE,CAAC;AACCoG,MAAAA,OAAO,EAAE,CAAClH,YAAD,CADV;AAECmH,MAAAA,YAAY,EAAE,CAAC9E,gBAAD,EAAmBrB,yBAAnB,CAFf;AAGCoG,MAAAA,OAAO,EAAE,CAAC/E,gBAAD,CAHV;AAICgF,MAAAA,eAAe,EAAE,CAACrG,yBAAD;AAJlB,KAAD;AAFqG,GAAD,CAAtH;AAAA;AAUA;AACA;AACA;;;AAEA,SAASZ,aAAT,EAAwBY,yBAAxB,EAAmDqB,gBAAnD,EAAqEuE,aAArE", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Component, ChangeDetectionStrategy, Input, Directive, Output, NgModule } from '@angular/core';\nimport { getBsVer, parseTriggers } from 'ngx-bootstrap/utils';\nimport * as i3 from 'ngx-bootstrap/positioning';\nimport { PlacementForBs5, checkMargins, PositioningService } from 'ngx-bootstrap/positioning';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { timer } from 'rxjs';\nimport * as i2$1 from 'ngx-bootstrap/component-loader';\nimport { ComponentLoaderFactory } from 'ngx-bootstrap/component-loader';\n\n/**\n * Configuration service for the Popover directive.\n * You can inject this service, typically in your root component, and customize\n * the values of its properties in order to provide default values for all the\n * popovers used in the application.\n */\nclass PopoverConfig {\n    constructor() {\n        /** sets disable adaptive position */\n        this.adaptivePosition = true;\n        /**\n         * Placement of a popover. Accepts: \"top\", \"bottom\", \"left\", \"right\", \"auto\"\n         */\n        this.placement = 'top';\n        /**\n         * Specifies events that should trigger. Supports a space separated list of\n         * event names.\n         */\n        this.triggers = 'click';\n        this.outsideClick = false;\n        /** delay before showing the tooltip */\n        this.delay = 0;\n    }\n}\nPopoverConfig.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: PopoverConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nPopoverConfig.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: PopoverConfig, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: PopoverConfig, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }] });\n\nclass PopoverContainerComponent {\n    constructor(config) {\n        this._placement = 'top';\n        Object.assign(this, config);\n    }\n    set placement(value) {\n        if (!this._bsVersions.isBs5) {\n            this._placement = value;\n        }\n        else {\n            this._placement = PlacementForBs5[value];\n        }\n    }\n    ;\n    get _bsVersions() {\n        return getBsVer();\n    }\n    checkMarginNecessity() {\n        return checkMargins(this._placement);\n    }\n}\nPopoverContainerComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: PopoverContainerComponent, deps: [{ token: PopoverConfig }], target: i0.ɵɵFactoryTarget.Component });\nPopoverContainerComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.1.1\", type: PopoverContainerComponent, selector: \"popover-container\", inputs: { placement: \"placement\", title: \"title\" }, host: { attributes: { \"role\": \"tooltip\" }, properties: { \"attr.id\": \"popoverId\", \"class\": \"\\\"popover in popover-\\\" + _placement + \\\" \\\" + \\\"bs-popover-\\\" + _placement + \\\" \\\" + _placement + \\\" \\\" + containerClass + \\\" \\\" + checkMarginNecessity()\", \"class.show\": \"!_bsVersions.isBs3\", \"class.bs3\": \"_bsVersions.isBs3\" }, styleAttribute: \"display:block;\" }, ngImport: i0, template: \"<div class=\\\"popover-arrow arrow\\\"></div>\\n<h3 class=\\\"popover-title popover-header\\\" *ngIf=\\\"title\\\">{{ title }}</h3>\\n<div class=\\\"popover-content popover-body\\\">\\n  <ng-content></ng-content>\\n</div>\\n\", styles: [\":host.bs3.popover-top{margin-bottom:10px}:host.bs3.popover.top>.arrow{margin-left:-2px}:host.bs3.popover.top{margin-bottom:10px}:host.popover.bottom>.arrow{margin-left:-4px}:host.bs3.bs-popover-left{margin-right:.5rem}:host.bs3.bs-popover-right .arrow,:host.bs3.bs-popover-left .arrow{margin:.3rem 0}\\n\"], directives: [{ type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: PopoverContainerComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'popover-container', changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '[attr.id]': 'popoverId',\n                        '[class]': '\"popover in popover-\" + _placement + \" \" + \"bs-popover-\" + _placement + \" \" + _placement + \" \" + containerClass + \" \" + checkMarginNecessity()',\n                        '[class.show]': '!_bsVersions.isBs3',\n                        '[class.bs3]': '_bsVersions.isBs3',\n                        role: 'tooltip',\n                        style: 'display:block;'\n                    }, styles: [\n                        `\n    :host.bs3.popover-top {\n      margin-bottom: 10px;\n    }\n    :host.bs3.popover.top>.arrow {\n      margin-left: -2px;\n    }\n    :host.bs3.popover.top {\n      margin-bottom: 10px;\n    }\n    :host.popover.bottom>.arrow {\n      margin-left: -4px;\n    }\n    :host.bs3.bs-popover-left {\n      margin-right: .5rem;\n    }\n    :host.bs3.bs-popover-right .arrow, :host.bs3.bs-popover-left .arrow{\n      margin: .3rem 0;\n    }\n    `\n                    ], template: \"<div class=\\\"popover-arrow arrow\\\"></div>\\n<h3 class=\\\"popover-title popover-header\\\" *ngIf=\\\"title\\\">{{ title }}</h3>\\n<div class=\\\"popover-content popover-body\\\">\\n  <ng-content></ng-content>\\n</div>\\n\" }]\n        }], ctorParameters: function () { return [{ type: PopoverConfig }]; }, propDecorators: { placement: [{\n                type: Input\n            }], title: [{\n                type: Input\n            }] } });\n\nlet id = 0;\n/**\n * A lightweight, extensible directive for fancy popover creation.\n */\nclass PopoverDirective {\n    constructor(_config, _elementRef, _renderer, _viewContainerRef, cis, _positionService) {\n        this._elementRef = _elementRef;\n        this._renderer = _renderer;\n        this._positionService = _positionService;\n        /** unique id popover - use for aria-describedby */\n        this.popoverId = id++;\n        /** sets disable adaptive position */\n        this.adaptivePosition = true;\n        /**\n         * Placement of a popover. Accepts: \"top\", \"bottom\", \"left\", \"right\"\n         */\n        this.placement = 'top';\n        /**\n         * Close popover on outside click\n         */\n        this.outsideClick = false;\n        /**\n         * Specifies events that should trigger. Supports a space separated list of\n         * event names.\n         */\n        this.triggers = 'click';\n        /**\n         * Css class for popover container\n         */\n        this.containerClass = '';\n        /**\n         * Delay before showing the tooltip\n         */\n        this.delay = 0;\n        this._isInited = false;\n        this._popover = cis\n            .createLoader(_elementRef, _viewContainerRef, _renderer)\n            .provide({ provide: PopoverConfig, useValue: _config });\n        Object.assign(this, _config);\n        this.onShown = this._popover.onShown;\n        this.onHidden = this._popover.onHidden;\n        // fix: no focus on button on Mac OS #1795\n        if (typeof window !== 'undefined') {\n            _elementRef.nativeElement.addEventListener('click', function () {\n                try {\n                    _elementRef.nativeElement.focus();\n                }\n                catch (err) {\n                    return;\n                }\n            });\n        }\n    }\n    /**\n     * Returns whether or not the popover is currently being shown\n     */\n    get isOpen() {\n        return this._popover.isShown;\n    }\n    set isOpen(value) {\n        if (value) {\n            this.show();\n        }\n        else {\n            this.hide();\n        }\n    }\n    /**\n     * Set attribute aria-describedBy for element directive and\n     * set id for the popover\n     */\n    setAriaDescribedBy() {\n        this._ariaDescribedby = this.isOpen ? `ngx-popover-${this.popoverId}` : void 0;\n        if (this._ariaDescribedby) {\n            if (this._popover.instance) {\n                this._popover.instance.popoverId = this._ariaDescribedby;\n            }\n            this._renderer.setAttribute(this._elementRef.nativeElement, 'aria-describedby', this._ariaDescribedby);\n        }\n        else {\n            this._renderer.removeAttribute(this._elementRef.nativeElement, 'aria-describedby');\n        }\n    }\n    /**\n     * Opens an element’s popover. This is considered a “manual” triggering of\n     * the popover.\n     */\n    show() {\n        if (this._popover.isShown || !this.popover || this._delayTimeoutId) {\n            return;\n        }\n        this._positionService.setOptions({\n            modifiers: {\n                flip: {\n                    enabled: this.adaptivePosition\n                },\n                preventOverflow: {\n                    enabled: this.adaptivePosition\n                }\n            }\n        });\n        const showPopover = () => {\n            if (this._delayTimeoutId) {\n                this._delayTimeoutId = undefined;\n            }\n            this._popover\n                .attach(PopoverContainerComponent)\n                .to(this.container)\n                .position({ attachment: this.placement })\n                .show({\n                content: this.popover,\n                context: this.popoverContext,\n                placement: this.placement,\n                title: this.popoverTitle,\n                containerClass: this.containerClass\n            });\n            if (!this.adaptivePosition && this._popover._componentRef) {\n                this._positionService.calcPosition();\n                this._positionService.deletePositionElement(this._popover._componentRef.location);\n            }\n            this.isOpen = true;\n            this.setAriaDescribedBy();\n        };\n        const cancelDelayedTooltipShowing = () => {\n            if (this._popoverCancelShowFn) {\n                this._popoverCancelShowFn();\n            }\n        };\n        if (this.delay) {\n            const _timer = timer(this.delay).subscribe(() => {\n                showPopover();\n                cancelDelayedTooltipShowing();\n            });\n            if (this.triggers) {\n                parseTriggers(this.triggers)\n                    .forEach((trigger) => {\n                    if (!trigger.close) {\n                        return;\n                    }\n                    this._popoverCancelShowFn = this._renderer.listen(this._elementRef.nativeElement, trigger.close, () => {\n                        _timer.unsubscribe();\n                        cancelDelayedTooltipShowing();\n                    });\n                });\n            }\n        }\n        else {\n            showPopover();\n        }\n    }\n    /**\n     * Closes an element’s popover. This is considered a “manual” triggering of\n     * the popover.\n     */\n    hide() {\n        if (this._delayTimeoutId) {\n            clearTimeout(this._delayTimeoutId);\n            this._delayTimeoutId = undefined;\n        }\n        if (this.isOpen) {\n            this._popover.hide();\n            this.setAriaDescribedBy();\n            this.isOpen = false;\n        }\n    }\n    /**\n     * Toggles an element’s popover. This is considered a “manual” triggering of\n     * the popover.\n     */\n    toggle() {\n        if (this.isOpen) {\n            return this.hide();\n        }\n        this.show();\n    }\n    ngOnInit() {\n        // fix: seems there are an issue with `routerLinkActive`\n        // which result in duplicated call ngOnInit without call to ngOnDestroy\n        // read more: https://github.com/valor-software/ngx-bootstrap/issues/1885\n        if (this._isInited) {\n            return;\n        }\n        this._isInited = true;\n        this._popover.listen({\n            triggers: this.triggers,\n            outsideClick: this.outsideClick,\n            show: () => this.show(),\n            hide: () => this.hide()\n        });\n    }\n    ngOnDestroy() {\n        this._popover.dispose();\n    }\n}\nPopoverDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: PopoverDirective, deps: [{ token: PopoverConfig }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ViewContainerRef }, { token: i2$1.ComponentLoaderFactory }, { token: i3.PositioningService }], target: i0.ɵɵFactoryTarget.Directive });\nPopoverDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.1.1\", type: PopoverDirective, selector: \"[popover]\", inputs: { adaptivePosition: \"adaptivePosition\", popover: \"popover\", popoverContext: \"popoverContext\", popoverTitle: \"popoverTitle\", placement: \"placement\", outsideClick: \"outsideClick\", triggers: \"triggers\", container: \"container\", containerClass: \"containerClass\", isOpen: \"isOpen\", delay: \"delay\" }, outputs: { onShown: \"onShown\", onHidden: \"onHidden\" }, exportAs: [\"bs-popover\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: PopoverDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[popover]', exportAs: 'bs-popover' }]\n        }], ctorParameters: function () { return [{ type: PopoverConfig }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ViewContainerRef }, { type: i2$1.ComponentLoaderFactory }, { type: i3.PositioningService }]; }, propDecorators: { adaptivePosition: [{\n                type: Input\n            }], popover: [{\n                type: Input\n            }], popoverContext: [{\n                type: Input\n            }], popoverTitle: [{\n                type: Input\n            }], placement: [{\n                type: Input\n            }], outsideClick: [{\n                type: Input\n            }], triggers: [{\n                type: Input\n            }], container: [{\n                type: Input\n            }], containerClass: [{\n                type: Input\n            }], isOpen: [{\n                type: Input\n            }], delay: [{\n                type: Input\n            }], onShown: [{\n                type: Output\n            }], onHidden: [{\n                type: Output\n            }] } });\n\nclass PopoverModule {\n    static forRoot() {\n        return {\n            ngModule: PopoverModule,\n            providers: [ComponentLoaderFactory, PositioningService]\n        };\n    }\n}\nPopoverModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: PopoverModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nPopoverModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: PopoverModule, declarations: [PopoverDirective, PopoverContainerComponent], imports: [CommonModule], exports: [PopoverDirective] });\nPopoverModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: PopoverModule, imports: [[CommonModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: PopoverModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    declarations: [PopoverDirective, PopoverContainerComponent],\n                    exports: [PopoverDirective],\n                    entryComponents: [PopoverContainerComponent]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { PopoverConfig, PopoverContainerComponent, PopoverDirective, PopoverModule };\n"]}, "metadata": {}, "sourceType": "module"}