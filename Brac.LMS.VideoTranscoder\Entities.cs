﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.VideoTranscoder
{
    public class VideoMasterInfo
    {
        [Key]
        public long Id { get; set; }
        public string DBName { get; set; }
        public string TableName { get; set; }
        public string OriginColumn { get; set; }
        public string DestinationColumn { get; set; }
        public string Condition { get; set; }
        public string BasePath { get; set; }

        public string GetFetchScript()
        {
            return string.Format("SELECT Id, {0} FROM [{1}].[dbo].[{2}] WHERE {3}", OriginColumn, DBName, TableName, string.IsNullOrEmpty(Condition) ? "1=1" : Condition);
        }
    }

    public class VideoFile
    {
        public Guid Id { get; set; }
        public string FilePath { get; set; }
        public string S3Path { get; set; }
    }
}
