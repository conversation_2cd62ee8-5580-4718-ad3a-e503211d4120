﻿using Brac.LMS.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.Services
{
    public static class ServiceHelper
    {
        public static FileType GetFileType(string path)
        {
            var extMappings = new Dictionary<string, FileType>(StringComparer.InvariantCultureIgnoreCase)
            {
                 {".doc", FileType.DOC },
                 {".docx", FileType.DOC },
                 {".jpe", FileType.IMG },
                 {".jpeg", FileType.IMG },
                 {".jpg", FileType.IMG },
                 {".m3u8", FileType.HLS },
                 {".mp4", FileType.MP4 },
                 {".mp4v", FileType.MP4 },
                 {".pdf", FileType.PDF },
                 {".png", FileType.IMG },
                 {".ppt", FileType.PPT },
                 {".pptx", FileType.PPT },
                 {".xls", FileType.EXCEL },
                 {".xlsx", FileType.EXCEL }
            };

            var ext = Path.GetExtension(path);
            if (extMappings.ContainsKey(ext)) return extMappings[ext];
            else throw new Exception("Invalid file provided");
        }
    }
}
