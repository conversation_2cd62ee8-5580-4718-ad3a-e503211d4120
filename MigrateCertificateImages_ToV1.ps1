# PowerShell Script to migrate existing certificate images to v1 version folders
# IMPORTANT: Only moves files from course-specific directories, NOT direct files
# Run this script from the API project root directory

param(
    [string]$ImagePath = "Images\CertificateConfiguration",
    [string]$BackupPath = "Images\CertificateConfiguration_Backup"
)

Write-Host "Starting Certificate Image Migration to v1 folders..." -ForegroundColor Green
Write-Host "IMPORTANT: Only processing course-specific directories (GUID folders)" -ForegroundColor Yellow
Write-Host "Files in root CertificateConfiguration folder will NOT be moved" -ForegroundColor Yellow

# Create backup directory
if (!(Test-Path $ImagePath)) {
    Write-Host "ERROR: Source path does not exist: $ImagePath" -ForegroundColor Red
    # exit 1
}

if (!(Test-Path $BackupPath)) {
    New-Item -ItemType Directory -Path $BackupPath -Force
    Write-Host "Created backup directory: $BackupPath" -ForegroundColor Yellow
}

# GUID pattern for course directories (exactly 36 characters with hyphens)
$guidPattern = '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$'

# Get all course directories (GUID folders only)
$courseDirs = Get-ChildItem -Path $ImagePath -Directory | Where-Object { 
    $_.Name -match $guidPattern 
}

Write-Host "Found $($courseDirs.Count) course directories (GUID folders) to process" -ForegroundColor Cyan

# Also show what we're NOT processing
$otherDirs = Get-ChildItem -Path $ImagePath -Directory | Where-Object { 
    $_.Name -notmatch $guidPattern 
}
if ($otherDirs.Count -gt 0) {
    Write-Host "Skipping $($otherDirs.Count) non-course directories: $($otherDirs.Name -join ', ')" -ForegroundColor Yellow
}

$totalMoved = 0
$totalSkipped = 0

foreach ($courseDir in $courseDirs) {
    $coursePath = $courseDir.FullName
    $courseId = $courseDir.Name
    
    Write-Host "`nProcessing course: $courseId" -ForegroundColor White
    
    # Create backup of this course directory
    $backupCoursePath = Join-Path $BackupPath $courseId
    if (Test-Path $coursePath) {
        Copy-Item -Path $coursePath -Destination $backupCoursePath -Recurse -Force
        Write-Host "  ✓ Backup created" -ForegroundColor Green
    }
    
    # Get all files in the course directory (not in subdirectories)
    $allFiles = Get-ChildItem -Path $coursePath -File
    $imageFiles = $allFiles | Where-Object { 
        $_.Extension -match '\.(jpg|jpeg|png|gif|bmp)$' 
    }
    
    if ($imageFiles.Count -gt 0) {
        # Create v1 directory
        $v1Path = Join-Path $coursePath "v1"
        if (!(Test-Path $v1Path)) {
            New-Item -ItemType Directory -Path $v1Path -Force
            Write-Host "  ✓ Created v1 directory" -ForegroundColor Green
        }
        
        # Move image files to v1 directory
        foreach ($imageFile in $imageFiles) {
            try {
                $destinationPath = Join-Path $v1Path $imageFile.Name
                Move-Item -Path $imageFile.FullName -Destination $destinationPath -Force
                Write-Host "  ✓ Moved: $($imageFile.Name)" -ForegroundColor Gray
                $totalMoved++
            }
            catch {
                Write-Host "  ✗ Failed to move: $($imageFile.Name) - $($_.Exception.Message)" -ForegroundColor Red
            }
        }
        
        Write-Host "  ✓ Migrated $($imageFiles.Count) images to v1/" -ForegroundColor Green
    } else {
        Write-Host "  ⚠ No image files found in course directory" -ForegroundColor Yellow
        $totalSkipped++
    }
    
    # Show any non-image files that were left
    $otherFiles = $allFiles | Where-Object { 
        $_.Extension -notmatch '\.(jpg|jpeg|png|gif|bmp)$' 
    }
    if ($otherFiles.Count -gt 0) {
        Write-Host "  ℹ Left $($otherFiles.Count) non-image files: $($otherFiles.Name -join ', ')" -ForegroundColor Cyan
    }
}

Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "MIGRATION COMPLETED!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green
Write-Host "Total images moved: $totalMoved" -ForegroundColor White
Write-Host "Course directories processed: $($courseDirs.Count)" -ForegroundColor White
Write-Host "Course directories skipped: $totalSkipped" -ForegroundColor White
Write-Host "`nBackup location: $BackupPath" -ForegroundColor Yellow
Write-Host "`nNEXT STEPS:" -ForegroundColor Cyan
Write-Host "1. Run the SQL script to update database paths" -ForegroundColor White
Write-Host "2. Test certificate generation with existing data" -ForegroundColor White
Write-Host "3. Verify images are loading correctly in admin panel" -ForegroundColor White

Read-Host "Press Enter to exit"
