﻿using Brac.LMS.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public enum NotificationType
    {
        Random,
        CourseEnrolmentByAdmin, 
        SelfEnrolmentInCourse, 
        NewAvailableCourse, 
        SequenceCompletion, 
        NoProgress30, 
        LastWeekReminder, 
        LastDayReminder, 
        FailureToComplete, 
        SuccessfullCompletion, 
        OneWeekRemain, 
        CertificateExpiry, 
        ForumComment, 
        ForumReply, 
        ForumTopicCreated, 
        ForumApproval, 
        CertificateTestAnswerSubmission, 
        EvaluationTestAnswerSubmission, 
        CertificateTestResultPublish, 
        EvaluationTestResultPublish, 
        DeleteOldNotification
    }
    public enum Navigation { None, CourseDetails, CoursePreview, CourseReview, ForumComment, ForumReply, ForumPostDetails, CertificateTestResult, EvaluationTestResult, CertificateTestAnswersheet, EvaluationTestAnswersheet }

    public class Notification : EntityField
    {
        public string Title { get; set; }
        public string Details { get; set; }

        private DateTime _CreatedOn;
        public DateTime CreatedOn
        {
            get
            { return _CreatedOn; }
            set
            { _CreatedOn = value.ToKindUtc(); }
        }

        public UserType TargetUserType { get; set; }
        public NotificationType NotificationType { get; set; }

        //[StringLength(128)]
        //public string RefId { get; set; }

        [StringLength(128)]
        public string SourceId { get; set; }

        public Guid? TargetTraineeId { get; set; }
        public virtual Trainee TargetTrainee { get; set; }

        public DateTime? SeenTime { get; set; }
        public Navigation NavigateTo { get; set; }
        public string Payload { get; set; }
    }
}
