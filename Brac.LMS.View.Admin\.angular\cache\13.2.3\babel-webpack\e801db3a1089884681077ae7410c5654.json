{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SMSConfigurationRoutingModule } from './sms-configuration-routing.module';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport let SMSConfigurationModule = /*#__PURE__*/(() => {\n  class SMSConfigurationModule {}\n\n  SMSConfigurationModule.ɵfac = function SMSConfigurationModule_Factory(t) {\n    return new (t || SMSConfigurationModule)();\n  };\n\n  SMSConfigurationModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SMSConfigurationModule\n  });\n  SMSConfigurationModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, SMSConfigurationRoutingModule, SharedModule]]\n  });\n  return SMSConfigurationModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}