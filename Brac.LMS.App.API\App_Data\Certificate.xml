<?xml version="1.0" standalone="yes"?>
<xs:schema id="NewDataSet" xmlns="" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
  <xs:element name="NewDataSet" msdata:IsDataSet="true" msdata:MainDataTable="EmployeeCertificate" msdata:UseCurrentLocale="true">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="EmployeeCertificate">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Institute" type="xs:string" minOccurs="0" />
              <xs:element name="Address" type="xs:string" minOccurs="0" />
              <xs:element name="TextRecognition" type="xs:string" minOccurs="0" />
              <xs:element name="EmpName" type="xs:string" minOccurs="0" />
              <xs:element name="EmpDesignation" type="xs:string" minOccurs="0" />
              <xs:element name="CourseDescription" type="xs:string" minOccurs="0" />
              <xs:element name="CertificateDate" type="xs:string" minOccurs="0" />
              <xs:element name="PrincipalName" type="xs:string" minOccurs="0" />
              <xs:element name="MDName" type="xs:string" minOccurs="0" />
              <xs:element name="PrincipalDesignation" type="xs:string" minOccurs="0" />
              <xs:element name="MDDesignation" type="xs:string" minOccurs="0" />
              <xs:element name="PicCertificate" type="xs:base64Binary" minOccurs="0" />
              <xs:element name="PicWritten" type="xs:base64Binary" minOccurs="0" />
              <xs:element name="PicPrincipal" type="xs:base64Binary" minOccurs="0" />
              <xs:element name="PicMD" type="xs:base64Binary" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>