﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using Brac.LMS.DB;
using Microsoft.AspNet.Identity.Owin;
using System;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [System.Web.Http.RoutePrefix("api/ghoori-learning")]
    public class GhooriCertificateController :   ApplicationController
    {
        private readonly IGhooriCertificateService _service;
        public ApplicationUserManager UserManager
        {
            get
            {
                return Request.GetOwinContext().GetUserManager<ApplicationUserManager>();
            }
        }
        public GhooriCertificateController()
        {

            _service = new GhooriCertificateService();
        }
       

        [Authorize, HttpPost, Route("certificate-entry")]
        public async Task<IHttpActionResult> CertificateCreateOrUpdate()
        {
            try
            {
                var model = new GhooriCertificateModel
                {
                    CourseName = HttpContext.Current.Request.Form["CourseName"],
                    TraineePIN = HttpContext.Current.Request.Form["TraineePIN"],
                    TraineeName = HttpContext.Current.Request.Form["TraineeName"],
                    DateOfCertification = DateTime.Parse(HttpContext.Current.Request.Form["DateOfCertification"]),
                    CertficationType = HttpContext.Current.Request.Form["CertificationType"],
                    CertficationPath = HttpContext.Current.Request.Form["CertificationPath"],
                };
                    //var model = Newtonsoft.Json.JsonConvert.DeserializeObject<GhooriCertificateModel>(System.Web.HttpContext.Current.Request.Form["Model"]);
                    return Ok(await _service.CertificateCreateOrUpdate(model, User.Identity));
            }
            catch (Exception ex)
            {
                return Ok(new { Success = false, ex.Message });
            }
        }
        [Authorize, HttpGet, Route("get-certificate")]
        public async Task<IHttpActionResult> GetCertficates(int size, int pageNumber)
        {
            try
            {
                return Ok(await _service.GetCertificateList(User.Identity,size,pageNumber));
            }
            catch (Exception ex)
            {
                return Ok(new { Success = false, ex.Message });
            }
        }
    }
}