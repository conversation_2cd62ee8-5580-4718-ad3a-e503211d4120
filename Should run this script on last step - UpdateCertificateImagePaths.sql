-- <PERSON><PERSON>t to update certificate image paths after migration to version folders
-- Run this AFTER moving the images using the PowerShell script

BEGIN TRANSACTION;

DECLARE @versionFolder VARCHAR(10) = 'v1';

PRINT 'Updating certificate image paths to version folder: ' + @versionFolder;

-- Update Person1SignPath
UPDATE CertificateConfiguration
SET Person1SignPath = REPLACE(Person1SignPath,
    '/Images/CertificateConfiguration/' + CONVERT(VARCHAR(36), CourseId) + '/',
    '/Images/CertificateConfiguration/' + CONVERT(VARCHAR(36), CourseId) + '/' + @versionFolder + '/')
WHERE Person1SignPath IS NOT NULL
  AND Person1SignPath NOT LIKE '%/v[0-9]/%'  -- Don't update if already versioned
  AND Person1SignPath LIKE '/Images/CertificateConfiguration/%';

-- Update Person2SignPath
UPDATE CertificateConfiguration
SET Person2SignPath = REPLACE(Person2SignPath,
    '/Images/CertificateConfiguration/' + CONVERT(VARCHAR(36), CourseId) + '/',
    '/Images/CertificateConfiguration/' + CONVERT(VARCHAR(36), CourseId) + '/' + @versionFolder + '/')
WHERE Person2SignPath IS NOT NULL
  AND Person2SignPath NOT LIKE '%/v[0-9]/%'  -- Don't update if already versioned
  AND Person2SignPath LIKE '/Images/CertificateConfiguration/%';

-- Update Person3SignPath
UPDATE CertificateConfiguration
SET Person3SignPath = REPLACE(Person3SignPath,
    '/Images/CertificateConfiguration/' + CONVERT(VARCHAR(36), CourseId) + '/',
    '/Images/CertificateConfiguration/' + CONVERT(VARCHAR(36), CourseId) + '/' + @versionFolder + '/')
WHERE Person3SignPath IS NOT NULL
  AND Person3SignPath NOT LIKE '%/v[0-9]/%'  -- Don't update if already versioned
  AND Person3SignPath LIKE '/Images/CertificateConfiguration/%';

-- Update TemplatePath
UPDATE CertificateConfiguration
SET TemplatePath = REPLACE(TemplatePath,
    '/Images/CertificateConfiguration/' + CONVERT(VARCHAR(36), CourseId) + '/',
    '/Images/CertificateConfiguration/' + CONVERT(VARCHAR(36), CourseId) + '/' + @versionFolder + '/')
WHERE TemplatePath IS NOT NULL
  AND TemplatePath NOT LIKE '%/v[0-9]/%'  -- Don't update if already versioned
  AND TemplatePath LIKE '/Images/CertificateConfiguration/%';

-- Show updated records
SELECT
    CourseId,
    Version,
    Person1SignPath,
    Person2SignPath,
    Person3SignPath,
    TemplatePath
FROM CertificateConfiguration
WHERE Person1SignPath LIKE '%/v[0-9]/%'
   OR Person2SignPath LIKE '%/v[0-9]/%'
   OR Person3SignPath LIKE '%/v[0-9]/%'
   OR TemplatePath LIKE '%/v[0-9]/%';

PRINT 'Image paths updated successfully!';

COMMIT TRANSACTION;
