﻿using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Media.API.Controllers;
using Brac.LMS.Models;
using DocumentFormat.OpenXml.Office2010.PowerPoint;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;

namespace Brac.LMS.App.Services
{
    public class AuditLogHelper
    {

        private AuditLog _auditLog;
        public AuditLogHelper()
        {
            
            _auditLog = new AuditLog();
            _auditLog.Controller = "";
            _auditLog.Method = "";
            _auditLog.CreatedOn = DateTime.Now.ToKindLocal();
        }
        public AuditLogHelper(string controller,string method)
        {
            var req=HttpContext.Current.Request.Headers;
            _auditLog = new AuditLog();
            _auditLog.Controller = controller.Length<=30?controller:controller.Substring(0, 30); 
            _auditLog.Method = method.Length <= 30 ? method : method.Substring(0, 30);
            _auditLog.CreatedOn = DateTime.Now.ToKindLocal();
            _auditLog.Request = JsonConvert.SerializeObject(req);
        }
        public AuditLog auditLog { get { return this._auditLog; } set { } }
        public async Task AddSuccessAudit(AuditLog model,string responseContent, ApplicationDbContext _context)
        {
            model.Status = true;
            model.Response = null;
            model.StatusCode = HttpStatusCode.OK.ToString();
            model.EndTime = DateTime.Now.ToKindLocal();
            _context.AuditLogs.Add(model);
            await _context.SaveChangesAsync();
        }

        public async Task AddErrorAudit(AuditLog model, string responseContent, ApplicationDbContext _context)
        {
            try
            {
                model.Status = false;
                model.Response = JsonConvert.SerializeObject(responseContent);
                model.StatusCode = HttpStatusCode.ExpectationFailed.ToString().Length <= 10 ? HttpStatusCode.ExpectationFailed.ToString() : HttpStatusCode.ExpectationFailed.ToString().Substring(0, 10);
                model.EndTime = DateTime.Now.ToKindLocal();
                _context.AuditLogs.Add(model);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
       
    }

    public class ControllerInfo<T> where T : ApiController
    {
        public string Controller { get; set; }
        public string Action { get; set; }
        public int Id { get; set; }
        public ControllerInfo<T> GetControllerInfo()
        {
            var controller = Activator.CreateInstance<T>();
            var controllerName = controller.ControllerContext.ControllerDescriptor?.ControllerName;
            var actionName = controller.ActionContext.ActionDescriptor?.ActionName;

            return new ControllerInfo<T>
            {
                Controller = controllerName,
                Action = actionName
            };
        }
    }
}
