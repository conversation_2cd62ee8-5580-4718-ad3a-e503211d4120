﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public enum OMAllowFor { All, Division, Department, Unit, Trainee }

    public class OpenMaterial : AuditableEntity
    {
        public MaterialType MaterialType { get; set; }


        [Required, StringLength(250)]
        public string Title { get; set; }


        [Required, Column(TypeName = "VARCHAR"), StringLength(150)]
        public string FilePath { get; set; }

        public long FileSizeKb { get; set; }

        public int VideoDurationSecond { get; set; }


        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string S3Path { get; set; }

        public FileType FileType { get; set; }


        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string ImagePath { get; set; }

        public long? CategoryId { get; set; }
        public virtual LhCategory Category { get; set; }

        public OMAllowFor AllowFor { get; set; }
        public long? DivisionId { get; set; }
        public virtual Division Division { get; set; }

        public long? DepartmentId { get; set; }
        public virtual Department Department { get; set; }

        public long? UnitId { get; set; }
        public virtual Unit Unit { get; set; }

        public virtual ICollection<Trainee> Trainees { get; set; }

        public bool Active { get; set; }
        public int? Order { get; set; }
    }
}
