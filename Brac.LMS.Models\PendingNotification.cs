﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class PendingNotification : EntityField
    {
        public string Title { get; set; }
        public string Details { get; set; }
        public DateTime CreatedOn { get; set; }
        public NotificationType NotificationType { get; set; }
        public Navigation NavigateTo { get; set; }
        public string Payload { get; set; }
        [StringLength(128)]
        public string SourceId { get; set; }
    }
}
