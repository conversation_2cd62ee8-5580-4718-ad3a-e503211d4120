﻿using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using ClosedXML.Excel;
using FastReport;
using FastReport.Export.Pdf;
using iTextSharp.text.pdf;
using iTextSharp.text;
using SendEmailApp;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Data.Entity.Validation;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;
using System.Web.Hosting;
using WMPLib;
using System.Windows.Forms.DataVisualization.Charting;
using DocumentFormat.OpenXml.Spreadsheet;
using Font = System.Drawing.Font;
using Color = System.Drawing.Color;
using DocumentFormat.OpenXml.Presentation;
using DocumentFormat.OpenXml.Office2013.Drawing.ChartStyle;
using Newtonsoft.Json;
using Org.BouncyCastle.Asn1.Ocsp;
using System.Net;
using System.Net.Http;
using Brac.LMS.Media.API.Controllers;
using System.Web.UI.WebControls;
using DocumentFormat.OpenXml.Office2010.ExcelAc;
using DocumentFormat.OpenXml.EMMA;
using Org.BouncyCastle.Crypto.Tls;
using Org.BouncyCastle.Ocsp;
using Microsoft.Owin.BuilderProperties;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Window;
using Image = iTextSharp.text.Image;
using System.Web.Http.Results;
using Org.BouncyCastle.Asn1.Crmf;
using System.Data.Entity.Migrations;

namespace Brac.LMS.App.Services
{
    public class CourseService : ICourseService
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditLogHelper _auditLogHelper;
        private readonly AuditLog audit;
        private readonly ApplicationUser _appUser;
        private  TraineeCourseModel _traineeCourseModel;
        //public CourseService()
        //{
        //    _context = new ApplicationDbContext();
        //    _auditLogHelper = new AuditLogHelper();
        //    audit = _auditLogHelper.auditLog;
        //}
        public CourseService(string controller, string method, ApplicationUser applicationUser)
        {
            _auditLogHelper = new AuditLogHelper(controller, method);
            audit = _auditLogHelper.auditLog;
            _appUser = applicationUser;
            audit.CreatedBy = _appUser.Id;
            _context = new ApplicationDbContext();
            _traineeCourseModel = new TraineeCourseModel();
        }

        #region Course
        public async Task<APIResponse> CourseCreateOrUpdate(CourseModel model, ApplicationUser user)
        {
            Course item = null;
            bool isEdit = true;
            var notiEvent = await _context.NotificationEvents.FirstOrDefaultAsync(x => x.Type == NotificationType.NewAvailableCourse);
            try
            {
                if (user.UserType == UserType.Admin && model.TrainerId == null)
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Please select a trainer for this course"
                    };


                if (await _context.Courses.AnyAsync(x => x.Id != model.Id && x.Title == model.Title))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Already exists: " + model.Title
                    };

                if (model.Id.HasValue)
                {
                    item = await _context.Courses.FindAsync(model.Id);
                    if (item == null) return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Course not found"
                    };
                }
                else
                {
                    item = new Course
                    {
                        Code = Utility.GenerateCode(await _context.Courses.OrderByDescending(x => x.CreatedDate).Select(x => x.Code).FirstOrDefaultAsync(), "C", 4)
                    };
                    isEdit = false;
                }

                item.Title = model.Title;
                item.ShortTitle = model.ShortTitle;
                item.Description = model.Description;
                item.Active = model.Active;
                item.SelfEnrollment = model.SelfEnrollment;
                item.ExpiryMonth = model.ExpiryMonth;
                item.CertificateExpiryMonth = model.CertificateExpiryMonth;
                item.CategoryId = model.CategoryId;

                //item.TrainerId = user.UserType == UserType.Admin ? model.TrainerId : user.Trainer.Id;

                if (HttpContext.Current.Request.Files.Count > 0)
                    item.ImagePath = Utility.SaveImage(item.Code + Utility.RandomString(3, false), "/Images/Course/", HttpContext.Current.Request.Files[0], item.ImagePath, 400, 210);

                item.SetAuditTrailEntity(user.User.Identity);

                if (!isEdit)
                {
                    item.Id = Guid.NewGuid();
                    _context.Courses.Add(item);
                }
                else
                {
                    _context.Entry(item).State = EntityState.Modified;
                }

                await _context.SaveChangesAsync();
                //if (!item.Active || !item.Published)
                //{
                //   await DeleteNotifications(item.Id);
                //}
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                };
            }
            catch (DbEntityValidationException ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                List<string> errorList = new List<string>();
                foreach (var errors in ex.EntityValidationErrors)
                {
                    foreach (var validationError in errors.ValidationErrors)
                    {
                        // get the error message
                        errorList.Add(validationError.ErrorMessage);
                    }
                }

                return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = string.Join(" ", errorList)
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }
        }
        public async Task<APIResponse> GetCourseList(string name, long? categoryId, int size, int pageNumber, ApplicationUser user)
        {
            try
            {
                var query = _context.Courses.AsQueryable();


                //if (user.UserType == UserType.Trainer) query = query.Where(x => x.TrainerId == user.Trainer.Id);
                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Title.Contains(name) || x.Code.Contains(name));
                if (categoryId.HasValue) query = query.Where(x => x.CategoryId == categoryId);
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderByDescending(x => x.CreatedDate)
                .Skip(pageNumber * size).Take(size);
                var data = await filteredQuery.Select(x => new
                {
                    x.Id,
                    x.Code,
                    x.Title,
                    x.ShortTitle,
                    x.Description,
                    x.Rating,
                    x.NoOfRating,
                    x.Active,
                    x.SelfEnrollment,
                    x.Published,
                    x.DependencySet,
                    x.ImagePath,
                    x.ExpiryMonth,
                    x.CertificateExpiryMonth,
                    Category = x.Category != null ? x.Category.Name : null,
                    x.CreatedDate
                }).ToListAsync();

                var count = await ((((!string.IsNullOrEmpty(name)) || (categoryId.HasValue)) || ((!string.IsNullOrEmpty(name)) && (categoryId.HasValue))) ? filteredQuery.CountAsync() : _context.Courses.CountAsync());

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data, Total = count }
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }
        public async Task<APIResponse> GetEnrolledAndAvailalbleCourse(string name, long? categoryId, int size, int pageNumber, int? courseStatus, ApplicationUser user)
        {
            try
            {
                var queryCourse = _context.Courses.Where(x => x.Active && x.Published).AsQueryable();
                var querycourseEnrollments = _context.CourseEnrollments.Where(x => x.TraineeId == user.Trainee.Id).AsQueryable();
                var query = (from c in queryCourse
                             join ce in querycourseEnrollments on c.Id equals ce.CourseId into course_enrollment
                             from cec in course_enrollment.DefaultIfEmpty()
                             where (c.SelfEnrollment)
                             select new { Course = c, Enrollment = cec == null ? false : true }).AsQueryable();
                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Course.Title.Contains(name) || x.Course.Code.Contains(name));
                if (categoryId.HasValue) query = query.Where(x => x.Course.CategoryId == categoryId);
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderBy(x => x.Enrollment).ThenByDescending(x => x.Course.CreatedDate)
                .Skip(pageNumber * size).Take(size);
                var final = filteredQuery.AsEnumerable().Select(x => new
                {
                    x.Course.Id,
                    x.Course.Code,
                    x.Course.Title,
                    x.Course.CategoryId,
                    x.Course.Description,
                    x.Course.Rating,
                    x.Course.NoOfRating,
                    x.Course.Active,
                    x.Course.SelfEnrollment,
                    x.Course.Published,
                    x.Course.DependencySet,
                    x.Course.ImagePath,
                    //x.Course.NoOfContents,
                    x.Course.ExpiryMonth,
                    TraineeCourseModel = this._traineeCourseModel = TraineeCourseDetails(x.Course.Id, user.Trainee.Id),
                    //x.NoOfContents,
                    //x.NoOfContentsStudied,

                    NoOfContents = this._traineeCourseModel.TotalLectures,
                    NoOfContentsStudied = this._traineeCourseModel.TotalCompletedLectures,
                    Status = this._traineeCourseModel.TotalLectures == this._traineeCourseModel.TotalCompletedLectures ? 1 : 0,
                    x.Course.CertificateExpiryMonth,
                    CategoryName = x.Course.Category.Name != null ? x.Course.Category.Name : null,
                    x.Course.CreatedDate,
                    Enrollment = x.Enrollment,

                });
                if (courseStatus.HasValue)
                {
                    if (courseStatus.Value==0)
                        final = final.Where(x => x.Status==0);
                    else
                        final = final.Where(x => x.Status==1);
                }
                var data = final.ToList();
                var count = ((!string.IsNullOrEmpty(name) || (categoryId.HasValue)|| (courseStatus.HasValue)) || ((!string.IsNullOrEmpty(name) && (categoryId.HasValue)&& (courseStatus.HasValue))) ? data.Count() : await (_context.Courses.Where(x => x.Active && x.Published).CountAsync())) ;
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data, Total = count }
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<byte[]> GetCourseListExcel(int timeZoneOffset)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            try
            {
                var data = await _context.Courses.Where(x => x.Active).OrderBy(x => x.Code).ThenBy(x => x.CreatedDate)
                .Select(x => new
                {
                    x.Code,
                    x.Title,
                    x.ExpiryMonth,
                    x.CertificateExpiryMonth,
                    x.SelfEnrollment,
                    Category = x.Category != null ? x.Category.Name : null,
                    x.Published,
                }).ToListAsync();
                if (!data.Any()) throw new Exception("No course found");

                var headerColumns = new List<string> { "SL#", "Course Name", "Course Expiry", "Certificate Expiry", "Self Enrollment", "Published" };

                ExcelManager.GetTextLineElement("Course List (Active Only)", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                ExcelManager.GetTextLineElement("Report Generation Time: " + Utility.UTCToLocal(DateTime.UtcNow, timeZoneOffset).ToString("dd-MMM-yyyy HH:mm:ss tt"), ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                rowNo++;

                ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);
                int counter = 0;
                foreach (var item in data)
                {
                    counter++;
                    rowNo++;
                    colNo = 1;
                    ExcelManager.GetTableDataCell(counter, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center, isBold: true);
                    ExcelManager.GetTableDataCell(item.Title, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                    ExcelManager.GetTableDataCell(item.ExpiryMonth.HasValue ? item.ExpiryMonth + " months" : " ", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                    ExcelManager.GetTableDataCell(item.CertificateExpiryMonth.HasValue ? item.CertificateExpiryMonth + " months" : " ", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                    ExcelManager.GetTableDataCell(item.SelfEnrollment ? "YES" : "NO", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                    ExcelManager.GetTableDataCell(item.Published ? "YES" : "NO", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                }

                for (int i = 1; i <= headerColumns.Count; i++)
                    ws.Column(i).AdjustToContents();

                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);

                    return ms.ToArray();
                }
            }
            catch (Exception ioex)
            {

                ExcelManager.GetTextLineElement("Excel File Generation Error: " + ioex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
        }

        public async Task<APIResponse> GetCourseById(Guid id, ApplicationUser user)
        {
            try
            {
                var query = _context.Courses.Where(t => t.Id == id).AsQueryable();
                //if (user.UserType == UserType.Trainer) query = query.Where(x => x.TrainerId == user.Trainer.Id);

                var data = await query
                .Select(t => new
                {
                    t.Id,
                    t.Title,
                    t.ShortTitle,
                    t.Description,
                    t.ImagePath,
                    t.ExpiryMonth,
                    t.SelfEnrollment,
                    t.CertificateExpiryMonth,
                    t.Published,
                    t.CategoryId,
                    t.Active
                }).FirstOrDefaultAsync();

                if (data == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Course not found"
                };

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetCourseDropDownList(ApplicationUser user)
        {
            try
            {
                var query = _context.Courses.Where(x => x.Active).OrderBy(o => o.Title).AsQueryable();
                //if (user.UserType == UserType.Trainer) query = query.Where(x => x.TrainerId == user.Trainer.Id);

                var data = await query
                .Select(t => new
                {
                    t.Id,
                    t.Title
                }).ToListAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> PublishOrUnpublishCourse(Guid id, IIdentity identity)
        {
            var isEdit = true;
            PendingNotification pendingNotification = null;
            //List<List<string>> tokens = new List<List<string>> { new List<string>() };

            try
            {
                var course = await _context.Courses.FindAsync(id);
                if (course == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Course not found"
                };
                course.Published = !course.Published;
                course.SetAuditTrailEntity(identity);
                _context.Entry(course).State = EntityState.Modified;

                if (course.Published && course.SelfEnrollment)
                {
                    var notiEvent = await _context.NotificationEvents.FirstOrDefaultAsync(x => x.Type == NotificationType.NewAvailableCourse);
                    if (notiEvent != null)
                    {
                        //var trainees = await _context.Trainees.Where(x => x.Active).Select(x => new { x.Id, x.PhoneNo, Tokens = x.Devices.Select(y => y.Token).ToList() }).ToListAsync();
                        //var smsSender = notiEvent.SMS ? new SMSSender() : null;
                        pendingNotification = new PendingNotification()
                        {
                            Id = Guid.NewGuid(),
                            CreatedOn = DateTime.UtcNow,
                            NotificationType = NotificationType.NewAvailableCourse,
                            Title = "New Course Available",
                            Details = $"A new course \"{course.Title}\" has been introdueced. This course is open for all. You can enrol this course if you needed.",
                            Payload = course.Id.ToString(),
                            NavigateTo = Navigation.CoursePreview
                        };
                        _context.PendingNotifications.Add(pendingNotification);

                        //foreach (var trainee in trainees)
                        //{
                        //    pendingNotification = new PendingNotification()
                        //    {
                        //        Id = Guid.NewGuid(),
                        //        CreatedOn = DateTime.UtcNow,
                        //        NotificationType = NotificationType.NewAvailableCourse,
                        //        Title = "New Course Available",
                        //        Details = $"A new course \"{ course.Title }\" has been introdueced. This course is open for all. You can enrol this course if you needed.",
                        //        RefId = course.Id.ToString()
                        //    };

                        //    if (notiEvent.InApp)
                        //    {
                        //        _context.PendingNotifications.Add(pendingNotification);
                        //        if (tokens.Last().Count + trainee.Tokens.Count() <= 500) tokens.Last().AddRange(trainee.Tokens);
                        //        else tokens.Add(trainee.Tokens);
                        //    }
                        //    if (notiEvent.Email) _context.NotificationEmails.Add(new NotificationEmail { Id = Guid.NewGuid(), NotificationId = notification.Id });
                        //    if (notiEvent.SMS)
                        //    {
                        //        if (!string.IsNullOrEmpty(trainee.PhoneNo) && trainee.PhoneNo.Length >= 10) await smsSender.SendAsync(trainee.PhoneNo, notification.Details);
                        //    }
                        //}
                    }
                }

                await _context.SaveChangesAsync();
                //if (course.Published == false || course.Active == false)
                //{
                //    await DeleteNotifications(id);
                //}
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);
                //
                //var fcmClient = new FirebaseMessagingClient();

                //foreach (var token in tokens)
                //{
                //    await fcmClient.SendNotifications(token.ToArray(), notification.Title, new
                //    {
                //        CourseId = course.Id,
                //        CourseTitle = course.Title,
                //        CourseImagePath = course.ImagePath,
                //        PublishedDate = DateTime.UtcNow
                //    });
                //}
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved"),
                    Data = course.Published
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetCourseMaterialsWithExams(Guid id)
        {
            try
            {
                var data = await _context.CourseMaterials
                    .GroupJoin(_context.CourseContentDependencies, x => new { x.CourseId, ContentId = x.Id }, y => new { y.CourseId, y.ContentId }, (x, y) => new
                    {
                        Material = x,
                        Dependencies = y
                    }).SelectMany(x => x.Dependencies.DefaultIfEmpty(), (y, z) => new { y.Material, Dependency = z })
                    .Where(x => x.Material.CourseId == id)
                    .Select(x => new { x.Material.Id, Name = x.Material.Title, Type = x.Material.MaterialType.ToString(), ContentType = "Material", x.Material.CreatedDate, Sequence = x.Dependency != null ? x.Dependency.Sequence : 0 })
                    .Union(_context.CourseMockTests
                    .GroupJoin(_context.CourseContentDependencies, x => new { x.CourseId, ContentId = x.Id }, y => new { y.CourseId, y.ContentId }, (x, y) => new
                    {
                        Exam = x,
                        Dependencies = y
                    }).SelectMany(x => x.Dependencies.DefaultIfEmpty(), (y, z) => new { y.Exam, Dependency = z })
                    .Where(x => x.Exam.CourseId == id)
                    .Select(x => new { x.Exam.Id, Name = x.Exam.ExamName, Type = "MockTest", ContentType = "Exam", x.Exam.CreatedDate, Sequence = x.Dependency != null ? x.Dependency.Sequence : 0 })).ToListAsync();



                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Contents = data.Any(x => x.Sequence > 0) ? data.Where(x => x.Sequence > 0).OrderBy(x => x.Sequence).ToList() : data.OrderBy(x => x.CreatedDate).ToList(),
                        NonConditionalContents = data.Any(x => x.Sequence > 0) ? data.Where(x => x.Sequence == 0).OrderByDescending(x => x.ContentType).ThenBy(x => x.Type).ToList() : data.Where(x => x.Sequence == -1).ToList()
                    }
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> SaveContentDependencies(Guid id, List<CourseContentDependencyModel> contents, IIdentity identity)
        {
            var isEdit = true;
            try
            {
                var course = await _context.Courses.FindAsync(id);
                if (course == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Course not found"
                };

                var contentDependencies = await _context.CourseContentDependencies.Where(x => x.CourseId == id).ToListAsync();

                CourseContentDependency contentDependency;

                int sequence = 0;
                foreach (var content in contents)
                {
                    sequence++;
                    contentDependency = contentDependencies.FirstOrDefault(x => x.ContentId == content.ContentId);
                    if (contentDependency == null)
                    {
                        contentDependency = new CourseContentDependency
                        {
                            CourseId = id,
                            ContentId = content.ContentId,
                            Type = content.ContentType,
                            Sequence = sequence
                        };
                        contentDependency.SetAuditTrailEntity(identity);
                        _context.CourseContentDependencies.Add(contentDependency);
                    }
                    else
                    {
                        contentDependency.Sequence = sequence;
                        contentDependency.SetAuditTrailEntity(identity);
                        contentDependency.Modified = true;
                        isEdit = false;

                        _context.Entry(contentDependency).State = EntityState.Modified;
                    }
                }

                foreach (var item in contentDependencies.Where(x => !x.Modified))
                {
                    _context.Entry(item).State = EntityState.Deleted;
                }

                course.Published = true;
                course.DependencySet = true;
                course.SetAuditTrailEntity(identity);
                _context.Entry(course).State = EntityState.Modified;
                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved"),
                    Data = new { course.Published, course.DependencySet }
                };
            }
            catch (DbEntityValidationException ex)
            {
                List<string> errorList = new List<string>();
                foreach (var errors in ex.EntityValidationErrors)
                {
                    foreach (var validationError in errors.ValidationErrors)
                    {
                        // get the error message
                        errorList.Add(validationError.ErrorMessage);
                    }
                }
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(errorList), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = string.Join(" ", errorList)
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetCourseRatings(Guid id)
        {
            try
            {
                var data = await _context.CourseFeedbacks.Where(t => t.CourseId == id)
                .Select(t => new
                {
                    t.Id,
                    t.Trainee.PIN,
                    t.Trainee.Name,
                    t.Trainee.User.ImagePath,
                    t.Rating,
                    t.Comment
                }).ToListAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }
        #endregion

        #region Course Materials
        public async Task<APIResponse> GetCourseMaterialList(string name, Guid courseId, int size, int pageNumber)
        {
            try
            {
                var query = _context.CourseMaterials.Where(x => x.CourseId == courseId).AsQueryable();
                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Title.Contains(name) || x.MaterialType.ToString().Contains(name));
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderByDescending(x => new { x.MaterialType, x.Title })
                            .Skip(pageNumber * size).Take(size);
                var data = await filteredQuery
                              .GroupJoin(_context.MaterialResources, x => x.Id, y => y.MaterialId, (x, y) => new
                              {
                                  Material = x,
                                  Resources = y
                              })
                            .Select(x => new { x.Material.Id, Course = x.Material.Course.Title, x.Material.CourseId, x.Material.Title, x.Material.RequiredStudyTimeSec, MaterialType = x.Material.MaterialType.ToString(), x.Material.CreatedDate, x.Material.FilePath, x.Material.S3Path, x.Material.YoutubeID, x.Material.ExternalLink, Resources = x.Resources.Count(), x.Material.CanDownload })
                            .ToListAsync();


                var count = await ((((!string.IsNullOrEmpty(name)) || (courseId!=null)) || ((!string.IsNullOrEmpty(name)) && (courseId != null))) ? query.CountAsync() : _context.CourseMaterials.Where(x => x.CourseId == courseId).CountAsync());
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data, Total = count }
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> MaterialCreateOrUpdate(CourseMaterialModel model, IIdentity identity)
        {
            CourseMaterial item = null;
            bool isEdit = true;
            int? prevReqStudyTime = null;
            try
            {
                if (await _context.CourseMaterials.AnyAsync(x => x.Id != model.Id && x.CourseId == model.CourseId && x.Title == model.Title))
                    return new APIResponse { Status = ResponseStatus.Warning, Message = "Already exists in this course: " + model.Title };

                if (model.Id != Guid.Empty)
                {
                    item = await _context.CourseMaterials.FindAsync(model.Id);
                    if (item == null)
                        return new APIResponse { Status = ResponseStatus.Warning, Message = "Material not found" };

                    prevReqStudyTime = item.RequiredStudyTimeSec;
                }
                else
                {
                    item = new CourseMaterial();
                    isEdit = false;

                    if (string.IsNullOrEmpty(model.YoutubeID) && HttpContext.Current.Request.Files.Count == 0)
                        return new APIResponse { Status = ResponseStatus.Warning, Message = "No file supplied" };

                    var course = await _context.Courses.FindAsync(model.CourseId);
                    if (course != null)
                    {
                        course.NoOfContents++;
                        _context.Entry(course).State = EntityState.Modified;
                    }
                }

                if (HttpContext.Current.Request.Files.Count > 0)
                    switch (model.MaterialType)
                    {
                        case MaterialType.Document:
                            if (HttpContext.Current.Request.Files.Count > 0)
                                item = SaveDocumentFile("DOC" + Utility.RandomString(4, false) + DateTime.Now.ToString("yyyMMddHHmmss"), "/Files/CourseMaterial/Documents/", HttpContext.Current.Request.Files[0], item);
                            break;
                        case MaterialType.Video:
                            if (HttpContext.Current.Request.Files.Count > 0)
                                item = SaveVideoFile("VID" + Utility.RandomString(4, false) + DateTime.Now.ToString("yyyMMddHHmmss"), "/Files/CourseMaterial/Videos/", HttpContext.Current.Request.Files[0], item);
                            break;
                        default:
                            item.YoutubeID = null;
                            break;
                    }
                else
                    item.YoutubeID = model.YoutubeID;
                item.CourseId = model.CourseId;
                item.MaterialType = model.MaterialType;
                item.Title = model.Title;

                if (item.MaterialType == MaterialType.Document)
                {
                    item.RequiredStudyTimeSec = model.RequiredStudyTimeSec;
                    item.CanDownload = !string.IsNullOrEmpty(item.FilePath) && item.FilePath.Split('.').Last().ToLower() == "pdf" ? model.CanDownload : true;
                }

                item.SetAuditTrailEntity(identity);
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);
                if (!isEdit)
                {
                    item.Id = Guid.NewGuid();
                    _context.CourseMaterials.Add(item);

                    if (item.RequiredStudyTimeSec > 0)
                    {
                        var traineeActivities = await _context.TraineeCourseActivities.Where(x => x.CourseId == item.CourseId).ToListAsync();
                        foreach (var traineeActivity in traineeActivities)
                        {
                            traineeActivity.NoOfContents++;

                            traineeActivity.GenerateProgress();
                            _context.Entry(traineeActivity).State = EntityState.Modified;
                        }
                    }
                }
                else
                {
                    if (prevReqStudyTime.HasValue && (prevReqStudyTime.Value > 0 && item.RequiredStudyTimeSec == 0) || (prevReqStudyTime == 0 && item.RequiredStudyTimeSec > 0))
                    {
                        var traineeActivities = await _context.TraineeCourseActivities.Where(x => x.CourseId == item.CourseId).ToListAsync();
                        foreach (var traineeActivity in traineeActivities)
                        {
                            if (prevReqStudyTime.Value > 0 && item.RequiredStudyTimeSec == 0) traineeActivity.NoOfContents--;
                            else traineeActivity.NoOfContents++;

                            traineeActivity.GenerateProgress();
                            _context.Entry(traineeActivity).State = EntityState.Modified;
                        }
                    }
                    _context.Entry(item).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                    }
                };

            }
            catch (DbEntityValidationException ex)
            {
                List<string> errorList = new List<string>();
                foreach (var errors in ex.EntityValidationErrors)
                {
                    foreach (var validationError in errors.ValidationErrors)
                    {
                        // get the error message
                        errorList.Add(validationError.ErrorMessage);
                    }
                }
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(errorList), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = string.Join(" ", errorList)
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse { Status = ResponseStatus.Error, Message = ex.Message };
            }
        }

        private CourseMaterial SaveVideoFile(string fileName, string partialPath, HttpPostedFile hpf, CourseMaterial item)
        {
            try
            {
                string serverPath = HostingEnvironment.MapPath("~"), extension, filePath;

                if (!Directory.Exists(serverPath + partialPath)) Directory.CreateDirectory(serverPath + partialPath);

                if (!string.IsNullOrEmpty(item.FilePath) && File.Exists(serverPath + item.FilePath))
                {
                    File.Delete(serverPath + item.FilePath);
                }

                string[] mediaExtensions = { ".AVI", ".MP4", ".DIVX", ".WMV" };

                extension = Path.GetExtension(hpf.FileName);
                if (mediaExtensions.Contains(extension.ToUpper()))
                {
                    if (hpf.ContentLength > 524288000)
                    {
                        throw new Exception("File size exceeded. Max file size is 500MB. Your selected file size is " + hpf.ContentLength / (1024 * 1024) + ".");
                    }
                    filePath = partialPath + fileName + extension;

                    if (File.Exists(serverPath + filePath))
                        File.Delete(serverPath + filePath);

                    hpf.SaveAs(serverPath + filePath);

                    WindowsMediaPlayer wmp = new WindowsMediaPlayer();
                    IWMPMedia mediainfo = wmp.newMedia(serverPath + filePath);

                    //var info = new FFmpegMediaInfo(serverPath + filePath, Generator.FFMPEG_EXE_PATH, Generator.FFPROBE_EXE_PATH);

                    item.FileSizeKb = (long)(hpf.ContentLength / 1024);
                    item.VideoDurationSecond = (int)mediainfo.duration;
                    item.RequiredStudyTimeSec = item.VideoDurationSecond;
                    item.FileType = ServiceHelper.GetFileType(serverPath + filePath);

                    item.FilePath = filePath;

                    return item;
                }
                else throw new Exception("Only AVI, MP4, DIVX or WMV video files are allowed to upload.");
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                throw new Exception("SaveVideoFile Error: " + ex.Message);
            }
        }

        private CourseMaterial SaveDocumentFile(string fileName, string partialPath, HttpPostedFile hpf, CourseMaterial item)
        {
            try
            {
                string serverPath = HostingEnvironment.MapPath("~"), extension, filePath;

                if (!Directory.Exists(serverPath + partialPath)) Directory.CreateDirectory(serverPath + partialPath);

                if (!string.IsNullOrEmpty(item.FilePath) && File.Exists(serverPath + item.FilePath))
                {
                    File.Delete(serverPath + item.FilePath);
                }

                string[] fileExtensions = { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".jpg", ".jpeg", ".png" };

                extension = Path.GetExtension(hpf.FileName);
                if (fileExtensions.Contains(extension.ToLower()))
                {
                    if (hpf.ContentLength > 102400000)
                    {
                        throw new Exception("File size exceeded. Max file size is 100MB. Your selected file size is " + hpf.ContentLength / 1000 + ".");
                    }
                    filePath = partialPath + fileName + extension;

                    if (File.Exists(serverPath + filePath))
                        File.Delete(serverPath + filePath);

                    hpf.SaveAs(serverPath + filePath);
                    item.FilePath = filePath;
                    item.FileSizeKb = (long)(hpf.ContentLength / 1024);
                    item.FileType = ServiceHelper.GetFileType(serverPath + filePath);

                    return item;
                }
                else throw new Exception("Only PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, JPG, JPEG or PNG files are allowed to upload.");
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                throw new Exception("SaveDocumentFile Error: " + ex.Message);
            }
        }

        public async Task<APIResponse> DeleteMaterialById(Guid id)
        {
            try
            {
                var item = await _context.CourseMaterials.FindAsync(id);
                if (item == null)
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Course material not found"
                    };

                var data = await _context.CourseMaterialStudies.AnyAsync(x => x.MaterialId == item.Id);
                if (data) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "You can not delete this material as this material has already been studied by one or more trainees"
                };

                string serverPath = HostingEnvironment.MapPath("~");
                if (!string.IsNullOrEmpty(item.FilePath) && File.Exists(serverPath + item.FilePath))
                    File.Delete(serverPath + item.FilePath);

                _context.Entry(item).State = EntityState.Deleted;

                if (item.RequiredStudyTimeSec > 0)
                {
                    var traineeActivities = await _context.TraineeCourseActivities.Where(x => x.CourseId == item.CourseId).ToListAsync();
                    foreach (var traineeActivity in traineeActivities)
                    {
                        traineeActivity.NoOfContents--;

                        traineeActivity.GenerateProgress();
                        _context.Entry(traineeActivity).State = EntityState.Modified;
                    }
                }

                var course = await _context.Courses.FindAsync(item.CourseId);
                if (course != null)
                {
                    course.NoOfContents--;
                    _context.Entry(course).State = EntityState.Modified;
                }

                await _context.SaveChangesAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Deleted!"
                };

            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetMaterialsDropdownList(Guid courseId)
        {
            try
            {
                var data = await _context.CourseMaterials.Where(x => x.CourseId == courseId).OrderBy(o => o.Title)
                .Select(t => new
                {
                    t.Id,
                    t.Title,
                    MaterialType = t.MaterialType.ToString(),
                    t.FilePath
                }).ToListAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data }
                };

            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetMaterialResources(Guid id)
        {
            try
            {
                var data = await _context.MaterialResources.Where(t => t.MaterialId == id)
                .Select(t => new
                {
                    t.Id,
                    t.MaterialId,
                    Material = t.Material.Title,
                    t.Title,
                    t.FilePath,
                    t.FileSizeKb
                }).ToListAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> ResourceCreateOrUpdate(MaterialResourceModel model, IIdentity identity)
        {
            MaterialResource item = null;
            bool isEdit = true;
            try
            {
                if (await _context.MaterialResources.AnyAsync(x => x.Id != model.Id && x.MaterialId == model.MaterialId && x.Title == model.Title))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Already exists in this course material: " + model.Title
                    };


                if (model.Id != Guid.Empty)
                {
                    item = await _context.MaterialResources.FindAsync(model.Id);
                    if (item == null) return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Material not found"
                    };
                }
                else
                {
                    item = new MaterialResource();
                    isEdit = false;

                    if (HttpContext.Current.Request.Files.Count == 0) return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "No file supplied"
                    };
                }

                if (HttpContext.Current.Request.Files.Count > 0)
                {
                    item = SaveResourceFile("DOC" + Utility.RandomString(4, false) + DateTime.Now.ToString("yyyMMddHHmmss"), "/Files/MaterialResource/", HttpContext.Current.Request.Files[0], item);
                }

                item.MaterialId = model.MaterialId;
                item.Title = model.Title;

                item.SetAuditTrailEntity(identity);

                if (!isEdit)
                {
                    item.Id = Guid.NewGuid();
                    _context.MaterialResources.Add(item);
                }
                else
                {
                    _context.Entry(item).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                };

            }
            catch (DbEntityValidationException ex)
            {
                List<string> errorList = new List<string>();
                foreach (var errors in ex.EntityValidationErrors)
                {
                    foreach (var validationError in errors.ValidationErrors)
                    {
                        // get the error message
                        errorList.Add(validationError.ErrorMessage);
                    }
                }
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(errorList), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = string.Join(" ", errorList)
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetMaterialResourcesDropdownList(Guid materialId)
        {
            try
            {
                var data = await _context.MaterialResources.Where(x => x.MaterialId == materialId).OrderBy(o => o.Title)
                .Select(t => new
                {
                    t.Id,
                    t.Title
                }).ToListAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data }
                };

            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        private MaterialResource SaveResourceFile(string fileName, string partialPath, HttpPostedFile hpf, MaterialResource item)
        {
            try
            {
                string serverPath = HostingEnvironment.MapPath("~"), extension, filePath;

                if (!Directory.Exists(serverPath + partialPath)) Directory.CreateDirectory(serverPath + partialPath);

                if (!string.IsNullOrEmpty(item.FilePath) && File.Exists(serverPath + item.FilePath))
                {
                    File.Delete(serverPath + item.FilePath);
                }

                string[] fileExtensions = { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".jpg", ".jpeg", ".png" };

                extension = Path.GetExtension(hpf.FileName);
                if (fileExtensions.Contains(extension.ToLower()))
                {
                    if (hpf.ContentLength > 102400000)
                    {
                        throw new Exception("File size exceeded. Max file size is 100MB. Your selected file size is " + hpf.ContentLength / 1000 + ".");
                    }
                    filePath = partialPath + fileName + extension;

                    if (File.Exists(serverPath + filePath))
                        File.Delete(serverPath + filePath);

                    hpf.SaveAs(serverPath + filePath);
                    item.FilePath = filePath;
                    item.FileSizeKb = (long)(hpf.ContentLength / 1024);

                    return item;
                }
                else throw new Exception("Only PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, JPG, JPEG or PNG files are allowed to upload.");
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                throw new Exception("SaveDocumentFile Error: " + ex.Message);
            }
        }

        public async Task<APIResponse> DeleteResourceById(Guid id)
        {
            try
            {
                var item = await _context.MaterialResources.FindAsync(id);
                if (item == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Course material resource not found"
                };

                string serverPath = HostingEnvironment.MapPath("~");
                if (File.Exists(serverPath + item.FilePath))
                    File.Delete(serverPath + item.FilePath);

                _context.Entry(item).State = EntityState.Deleted;

                await _context.SaveChangesAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Deleted!"
                };

            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }
        #endregion

        public async Task<APIResponse> GetCourseEnrollmentCount(int limit, ApplicationUser user)
        {
            try
            {
                var query = _context.CourseEnrollments.Where(x => x.Course.Active).AsQueryable();
                //if (user.UserType == UserType.Trainer) query = query.Where(x => x.Course.TrainerId == user.Trainer.Id);

                var data = await query
                    .GroupBy(x => new { x.Course.Id, x.Course.Title })
                .Select(x => new
                {
                    x.Key.Id,
                    x.Key.Title,
                    Count = x.Count()
                }).OrderByDescending(o => o.Count).ThenBy(t => t.Title)
                .Take(limit).ToListAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data }
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<byte[]> GetCourseEnrollmentCountExcel(ApplicationUser user)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            try
            {
                var query = _context.CourseEnrollments.Where(x => x.Course.Active).AsQueryable();
                //if (user.UserType == UserType.Trainer) query = query.Where(x => x.Course.TrainerId == user.Trainer.Id);

                var data = await query
                    .GroupBy(x => new { x.Course.Code, x.Course.Title })
                .Select(x => new
                {
                    x.Key.Code,
                    x.Key.Title,
                    Count = x.Count()
                }).OrderByDescending(o => o.Count).ToListAsync();


                ExcelManager.GetTextLineElement("Course Wise Enrolled Employees Report", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 3);

                ExcelManager.GetTextLineElement("Report Generation Time: " + DateTime.Now.ToString("dd-MMM-yyyy HH:mm:ss tt \"GMT\"zzz"), ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 3);
                rowNo++;

                var headerColumns = new List<string> { "Course Id", "Course Name", "No Of Trainee Enrolled" };
                ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);

                foreach (var item in data)
                {
                    rowNo++;
                    colNo = 1;
                    ExcelManager.GetTableDataCell(item.Code, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                    ExcelManager.GetTableDataCell(item.Title, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                    ExcelManager.GetTableDataCell(item.Count, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                }

                for (int i = 0; i < headerColumns.Count; i++)
                {
                    ws.Column(i + 1).AdjustToContents();
                }

                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);

                    return ms.ToArray();
                }
            }
            catch (Exception ioex)
            {
                ExcelManager.GetTextLineElement("Excel File Generation Error: " + ioex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);

                    return ms.ToArray();
                }
            }
        }

        public async Task<byte[]> GetCourseEnrollmentByDateExcel(DateTime fromDate, DateTime toDate, ApplicationUser user)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            try
            {
                fromDate = Utility.UnspecifiedToUTC(fromDate);
                toDate = Utility.UnspecifiedToUTC(toDate).AddDays(1).AddTicks(-2);

                var query = _context.CourseEnrollments.Where(x => x.EnrollmentDate >= fromDate && x.EnrollmentDate <= toDate && x.Course.Active).AsQueryable();
                //if (user.UserType == UserType.Trainer) query = query.Where(x => x.Course.TrainerId == user.Trainer.Id);

                var data = await query
                    .Select(x => new { x.Course.Title, x.Course.Code })
                .GroupBy(x => new { x.Title, x.Code })
                .OrderBy(x => x.Key.Title)
                .Select(t => new
                {
                    CourseID = t.Key.Code,
                    CourseName = t.Key.Title,
                    Count = t.Count()
                }).ToListAsync();


                ExcelManager.GetTextLineElement("Course Enrollment Report", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 3);

                ExcelManager.GetTextLineElement("ReportFrom: " + fromDate.ToString("dd-MMM-yyyy") + " To: " + toDate.ToString("dd-MMM-yyyy"), ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 3);

                ExcelManager.GetTextLineElement("Report Generation Time: " + DateTime.Now.ToString("dd-MMM-yyyy HH:mm:ss tt \"GMT\"zzz"), ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 3);
                rowNo++;

                var headerColumns = new List<string> { "Course Id", "Course Name", "Difficulty Level", "No Of Enrollments" };
                ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);

                foreach (var item in data)
                {
                    rowNo++;
                    colNo = 1;
                    ExcelManager.GetTableDataCell(item.CourseID, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                    ExcelManager.GetTableDataCell(item.CourseName, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                    ExcelManager.GetTableDataCell(item.Count, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                }

                for (int i = 0; i < headerColumns.Count; i++)
                {
                    ws.Column(i + 1).AdjustToContents();
                }

                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);

                    return ms.ToArray();
                }
            }
            catch (Exception ioex)
            {
                ExcelManager.GetTextLineElement("Excel File Generation Error: " + ioex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);

                    return ms.ToArray();
                }
            }
        }

        public async Task<byte[]> GetCourseWiseEnrollmentsExcel(Guid? courseId, ApplicationUser user)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            try
            {
                var query = _context.CourseEnrollments.Where(x => x.Course.Active).AsQueryable();
                //if (user.UserType == UserType.Trainer) query = query.Where(x => x.Course.TrainerId == user.Trainer.Id);

                if (courseId.HasValue) query = query.Where(x => x.CourseId == courseId);

                var result = await query.Select(x => new { x.Trainee.PIN, x.Trainee.Name, x.Trainee.Email, Division = x.Trainee.Division.Name, x.Trainee.PhoneNo, Course = x.Course.Title, x.EnrollmentDate }).ToListAsync();


                ExcelManager.GetTextLineElement("Course Wise Enrollments Report", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 9);

                ExcelManager.GetTextLineElement("Report Generation Time: " + DateTime.Now.ToString("dd-MMM-yyyy HH:mm:ss tt \"GMT\"zzz"), ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 9);
                rowNo++;

                var headerColumns = new List<string> { "PIN", "Name", "Email Address", "Designation", "Mobile", "Course", "Enrolled Date" };
                ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);

                foreach (var item in result)
                {
                    rowNo++;
                    colNo = 1;
                    ExcelManager.GetTableDataCell(item.PIN, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left, dataType: XLDataType.Text);
                    ExcelManager.GetTableDataCell(item.Name, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                    ExcelManager.GetTableDataCell(item.Email, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                    ExcelManager.GetTableDataCell(item.Division, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                    ExcelManager.GetTableDataCell(item.PhoneNo, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left, dataType: XLDataType.Text);
                    ExcelManager.GetTableDataCell(item.Course, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                    ExcelManager.GetTableDataCell(Utility.UTCToLocal(item.EnrollmentDate), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                }

                for (int i = 0; i < headerColumns.Count; i++)
                {
                    ws.Column(i + 1).AdjustToContents();
                }

                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);

                    return ms.ToArray();
                }
            }
            catch (Exception ioex)
            {
                ExcelManager.GetTextLineElement("Excel File Generation Error: " + ioex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);

                    return ms.ToArray();
                }
            }
        }

        public async Task<APIResponse> EnrollTraineeToCourse(TraineeEnrollModel model, IIdentity identity)
        {
            try
            {
                var courseEnrollmentQuery = _context.CourseEnrollments.Where(x => x.CourseId == model.CourseId).AsQueryable();
                var traineeActivityQuery = _context.TraineeCourseActivities.Where(x => x.CourseId == model.CourseId).AsQueryable();
                var traineeBookmarkQuery = _context.CourseBookmarks.Where(x => x.CourseId == model.CourseId).AsQueryable();

                List<string> traineePins = new List<string>();

                if (model.DivisionId.HasValue)
                {
                    courseEnrollmentQuery = courseEnrollmentQuery.Where(x => x.Trainee.DivisionId == model.DivisionId);
                    traineeActivityQuery = traineeActivityQuery.Where(x => model.Trainees.Contains(x.TraineeId));
                    traineeBookmarkQuery = traineeBookmarkQuery.Where(x => model.Trainees.Contains(x.TraineeId));
                }
                else if (HttpContext.Current.Request.Files.Count > 0)
                {
                    HttpPostedFile file = HttpContext.Current.Request.Files[0];
                    var data = ExcelParser.Parse(file);

                    var header = data.Item1;
                    var lines = data.Item2.ToList();

                    var indexes = new Dictionary<int, ColumnType>
                        {
                            {0, ColumnType.String }
                        };

                    await Validator.VerifyUploadFileValue(lines, indexes);
                    traineePins = lines.Select(x => x[0]).Distinct().ToList();

                    courseEnrollmentQuery = courseEnrollmentQuery.Where(x => traineePins.Contains(x.Trainee.PIN));
                    traineeActivityQuery = traineeActivityQuery.Where(x => traineePins.Contains(x.Trainee.PIN));
                    traineeBookmarkQuery = traineeBookmarkQuery.Where(x => traineePins.Contains(x.Trainee.PIN));
                }

                var traineeEnrollments = await courseEnrollmentQuery.ToListAsync();
                var traineeActivities = await traineeActivityQuery.ToListAsync();
                var traineeBookmarks = await traineeBookmarkQuery.ToListAsync();

                var noOfContents = await _context.CourseMaterials.CountAsync(x => x.CourseId == model.CourseId && x.RequiredStudyTimeSec > 0);
                var hasCertification = await _context.CourseExams.AnyAsync(x => x.CourseId == model.CourseId);
                var course = await _context.Courses.Where(x => x.Id == model.CourseId).Select(x => new { x.Title, x.ImagePath }).FirstOrDefaultAsync();
                var notiEvent = await _context.NotificationEvents.FirstOrDefaultAsync(x => x.Type == NotificationType.CourseEnrolmentByAdmin);

                CourseEnrollment enrollment;
                CourseBookmark bookmark;
                Notification notification = null;
                SMSSender smsSender = notiEvent?.SMS ?? false ? new SMSSender() : null;
                string traineeMobile = null;
                List<string> tokens = new List<string>();

                if (model.Trainees.Any())
                {
                    foreach (var traineeId in model.Trainees)
                    {
                        enrollment = traineeEnrollments.FirstOrDefault(x => x.TraineeId == traineeId);
                        bookmark = traineeBookmarks.FirstOrDefault(x => x.TraineeId == traineeId);


                        if (enrollment == null)
                        {
                            enrollment = new CourseEnrollment { CourseId = model.CourseId, TraineeId = traineeId, EnrollmentDate = DateTime.UtcNow };
                            if (bookmark != null)
                            {
                                bookmark.EnrollmentDate = DateTime.UtcNow;
                                _context.Entry(bookmark).State = EntityState.Modified;
                            }

                            _context.CourseEnrollments.Add(enrollment);

                            if (notiEvent != null)
                            {
                                notification = enrollment.OnNewEnrolmentByAdmin(course.Title);

                                if (notiEvent.InApp)
                                {
                                    _context.Notifications.Add(notification);
                                    tokens.AddRange(await _context.TraineeDevices.Where(x => x.TraineeId == traineeId).Select(x => x.Token).ToArrayAsync());
                                }
                                if (notiEvent.Email) _context.NotificationEmails.Add(new NotificationEmail { Id = Guid.NewGuid(), NotificationId = notification.Id });
                                if (notiEvent.SMS)
                                {
                                    traineeMobile = await _context.Trainees.Where(x => x.Id == traineeId).Select(x => x.PhoneNo).FirstOrDefaultAsync();
                                    if (!string.IsNullOrEmpty(traineeMobile) && traineeMobile.Length >= 10) await smsSender.SendAsync(traineeMobile, notification.Details, Common.SMS.Enums.SMSEventType.EnrollTraineeToCourse);
                                }
                            }

                        }
                        else
                        {
                            traineeEnrollments.Remove(enrollment);
                            if (bookmark != null)
                            {
                                bookmark.EnrollmentDate = null;
                                _context.Entry(bookmark).State = EntityState.Modified;
                            }
                        }

                        var activity = traineeActivities.FirstOrDefault(x => x.TraineeId == traineeId) ?? new TraineeCourseActivity
                        {
                            CourseId = model.CourseId,
                            TraineeId = traineeId,
                            NoOfContents = noOfContents,
                            HasCertification = hasCertification
                        };
                        activity.SetAuditTrailEntity(identity);

                        if (activity.Id > 0) _context.Entry(activity).State = EntityState.Modified;
                        else _context.TraineeCourseActivities.Add(activity);
                    }

                    if (traineeEnrollments.Any())
                    {
                        var prevEnrolledTraineeIds = traineeEnrollments.Select(x => x.TraineeId).ToList();

                        var existingTraineeActivities = await _context.TraineeCourseActivities
                            .Where(x => prevEnrolledTraineeIds.Contains(x.TraineeId) && x.CourseId == model.CourseId && x.Progress > 0)
                            .Select(x => x.Trainee.Name).ToListAsync();

                        if (existingTraineeActivities.Any())
                            return new APIResponse
                            {
                                Status = ResponseStatus.Warning,
                                Message = $"You can't remove course enrollment for {(existingTraineeActivities.Count > 1 ? "these trainees" : "this trainee")}: {string.Join(", ", existingTraineeActivities)}. <br/> Because {(existingTraineeActivities.Count > 1 ? "they have" : "he/she has")} already some activities in this course."
                            };

                        foreach (var item in traineeEnrollments)
                        {
                            bookmark = traineeBookmarks.FirstOrDefault(x => x.TraineeId == item.TraineeId);
                            if (bookmark != null)
                            {
                                bookmark.EnrollmentDate = null;
                                _context.Entry(bookmark).State = EntityState.Modified;
                            }
                            _context.Entry(item).State = EntityState.Deleted;
                        }
                    }
                }

                if (traineePins.Any())
                {
                    var trainees = await _context.Trainees.Where(x => traineePins.Contains(x.PIN)).Select(x => new { x.Id, x.PIN, x.PhoneNo }).ToListAsync();
                    foreach (var traineePIN in traineePins)
                    {
                        enrollment = traineeEnrollments.FirstOrDefault(x => x.Trainee.PIN == traineePIN);
                        bookmark = traineeBookmarks.FirstOrDefault(x => x.Trainee.PIN == traineePIN);
                        var trainee = trainees.FirstOrDefault(x => x.PIN == traineePIN);
                        if (enrollment == null)
                        {
                            enrollment = new CourseEnrollment { CourseId = model.CourseId, TraineeId = trainee.Id, EnrollmentDate = DateTime.UtcNow };
                            if (bookmark != null)
                            {
                                bookmark.EnrollmentDate = DateTime.UtcNow;
                                _context.Entry(bookmark).State = EntityState.Modified;
                            }

                            _context.CourseEnrollments.Add(enrollment);

                            if (notiEvent != null)
                            {
                                notification = enrollment.OnNewEnrolmentByAdmin(course.Title);

                                if (notiEvent.InApp)
                                {
                                    _context.Notifications.Add(notification);
                                    tokens.AddRange(await _context.TraineeDevices.Where(x => x.TraineeId == enrollment.TraineeId).Select(x => x.Token).ToArrayAsync());
                                }
                                if (notiEvent.Email) _context.NotificationEmails.Add(new NotificationEmail { Id = Guid.NewGuid(), NotificationId = notification.Id });
                                if (notiEvent.SMS)
                                {
                                    if (!string.IsNullOrEmpty(trainee.PhoneNo) && traineeMobile.Length >= 10) await smsSender.SendAsync(trainee.PhoneNo, notification.Details, Common.SMS.Enums.SMSEventType.EnrollTraineeToCourse);
                                }
                            }

                        }

                        var activity = traineeActivities.FirstOrDefault(x => x.TraineeId == trainee.Id) ?? new TraineeCourseActivity
                        {
                            CourseId = model.CourseId,
                            TraineeId = trainee.Id,
                            NoOfContents = noOfContents,
                            HasCertification = hasCertification
                        };
                        activity.SetAuditTrailEntity(identity);

                        if (activity.Id > 0) _context.Entry(activity).State = EntityState.Modified;
                        else _context.TraineeCourseActivities.Add(activity);
                    }
                }

                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);
                if (tokens.Any() && notification != null)
                {
                    try
                    {
                        do
                        {
                            await new FirebaseMessagingClient().SendNotifications(tokens.Take(Math.Min(tokens.Count, 20)).ToArray(), notification.Title, notification.Details, new
                            {
                                NavigateTo = notification.NavigateTo.ToString(),
                                notification.Payload,
                                notification.Id,
                                NotificationType = notification.NotificationType.ToString()
                            });
                            tokens.RemoveRange(0, Math.Min(tokens.Count, 20));
                        } while (tokens.Any());
                    }
                    catch (Exception excep)
                    {
                        await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(excep), _context);
                        LogControl.Write(excep.Message);
                        LogControl.Write(excep.StackTrace);
                    }


                }
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Enrolled"
                };
            }
            catch (DbEntityValidationException ex)
            {
                List<string> errorList = new List<string>();
                foreach (var errors in ex.EntityValidationErrors)
                {
                    foreach (var validationError in errors.ValidationErrors)
                    {
                        // get the error message
                        errorList.Add(validationError.ErrorMessage);
                    }
                }
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(errorList), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = string.Join(" ", errorList)
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }
        }

        public async Task<APIResponse> CancelCourseEnrollmentOfTrainee(Guid courseId, Guid traineeId)
        {
            try
            {
                var enrollment = await _context.CourseEnrollments.Where(x => x.CourseId == courseId && x.TraineeId == traineeId).FirstOrDefaultAsync();
                if (enrollment == null) throw new Exception("No enrollment found");

                var activity = await _context.TraineeCourseActivities.Where(x => x.CourseId == courseId && x.TraineeId == traineeId).FirstOrDefaultAsync();
                var bookmark = await _context.CourseBookmarks.Where(x => x.CourseId == courseId && x.TraineeId == traineeId).FirstOrDefaultAsync();

                if (bookmark != null)
                {
                    bookmark.EnrollmentDate = null;
                    _context.Entry(bookmark).State = EntityState.Modified;
                }

                if (activity != null)
                {
                    //if (activity.MaterialStudies.Any())
                    //    activity.MaterialStudies.Clear();
                    _context.Entry(activity).State = EntityState.Deleted;
                }

                var notifications = await _context.Notifications.Where(x => x.NotificationType == NotificationType.CourseEnrolmentByAdmin || x.NotificationType == NotificationType.SelfEnrolmentInCourse).ToListAsync();

                foreach (var notification in notifications)
                    _context.Entry(notification).State = EntityState.Deleted;

                _context.Entry(enrollment).State = EntityState.Deleted;

                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Cancelled"
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }
        }

        public async Task<APIResponse> GetTraineesByCourseId(Guid courseId, long divisionId)
        {
            try
            {
                var data = await _context.Trainees.Where(x => x.Active && x.DivisionId == divisionId)
                    .GroupJoin(_context.CourseEnrollments.Where(x => x.CourseId == courseId), x => x.Id, y => y.TraineeId, (x, y) => new
                    {
                        Trainee = x,
                        Enrollments = y
                    }).SelectMany(x => x.Enrollments.DefaultIfEmpty(), (y, z) => new { y.Trainee, Enrollment = z })
                    .Select(x => new { CourseId = courseId, x.Trainee.Id, x.Trainee.PIN, x.Trainee.Position, x.Trainee.Email, x.Trainee.Name, x.Trainee.PhoneNo, selected = x.Enrollment != null })
                    .OrderBy(x => x.Name).ToListAsync();



                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);


                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }
        }

        public async Task<APIResponse> GetTraineesByCourseIdToNotify(Guid courseId, Guid examId)
        {
            try
            {
                DateTime now = DateTime.UtcNow;

                var data = await _context.Trainees.Where(x => x.Active)
                    .GroupJoin(_context.CourseEnrollments.Where(x => x.CourseId == courseId), x => x.Id, y => y.TraineeId, (x, y) => new
                    {
                        Trainee = x,
                        Enrollments = y
                    }).SelectMany(x => x.Enrollments.DefaultIfEmpty(), (y, z) => new { y.Trainee, Enrollment = z })

                    .Select(x => new { CourseId = courseId, x.Trainee.Id, x.Trainee.PIN, Division = x.Trainee.Division, x.Trainee.Email, x.Trainee.Name, x.Trainee.PhoneNo, selected = x.Enrollment != null })

                    .GroupJoin(_context.TraineeExams.Where(x => x.ExamId == examId), x => x.Id, y => y.TraineeId, (x, y) => new
                    {
                        TraineeEnrollments = x,
                        TraineeExam = y
                    }).SelectMany(x => x.TraineeExam.DefaultIfEmpty(), (y, z) => new { y.TraineeEnrollments, TraineeExam = z })

                     .Select(x => new
                     {
                         CourseId = courseId,
                         ExamId = x.TraineeExam != null ? x.TraineeExam.ExamId : Guid.Empty,
                         x.TraineeEnrollments.Id,
                         x.TraineeEnrollments.PIN,
                         Division = x.TraineeEnrollments.Division,
                         x.TraineeEnrollments.Email,
                         x.TraineeEnrollments.Name,
                         x.TraineeEnrollments.PhoneNo,
                         Enrolled = x.TraineeEnrollments.selected,
                         takenExam = x.TraineeExam != null,
                         isBelated = _context.CourseExams.Where(y => y.Id == examId).Where(a => a.EndDate < now).Any() ? true : false
                     })

                    .OrderBy(x => x.Name).ToListAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data }
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);


                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }
        }

        public async Task<APIResponse> GetEnrolledTraineeList(Guid courseId, long? divisionId)
        {
            try
            {
                var query = _context.CourseEnrollments.Where(x => x.CourseId == courseId).AsQueryable();

                if (divisionId.HasValue) query = query.Where(x => x.Trainee.DivisionId == divisionId);

                var data = await query.Select(y => new { y.CourseId, y.TraineeId, y.Trainee.PIN, y.Trainee.Name, y.Trainee.PhoneNo, y.Trainee.Email, Division = y.Trainee.Division.Name, y.EnrollmentDate, y.ExpireDate }).ToListAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data }
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);


                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }
        }

        public async Task<APIResponse> GetNotifiedTrainees(Guid courseId, Guid examId)
        {
            try
            {
                var list = await _context.TraineeCourseExamConfigurations.Where(x => x.ExamId == examId)
                   .Select(x => new { x.Id, x.Trainee.PIN, x.Trainee.Email, x.StartDate, x.EndDate, Date = x.ModifiedDate.HasValue ? x.ModifiedDate : x.CreatedDate })
                    .OrderBy(x => x.Email).ToListAsync();
                var data = list.Select(x => new
                {
                    x.Id,
                    x.PIN,
                    x.Email,
                    StartDate = x.StartDate.ToString("dd-MMM-yyyy HH:mm:ss tt"),
                    EndDate = x.EndDate.ToString("dd-MMM-yyyy HH:mm:ss tt"),
                    Date = x.Date.Value.ToString("dd-MMM-yyyy HH:mm:ss tt"),
                }).ToList();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data }
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);


                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }

        }

        public async Task<APIResponse> NotifyTrainee(NotifyTraineeModel model, ApplicationUser user)
        {



            DbContextTransaction transaction = null;
            try
            {
                //DateTime start = Utility.CreateDateTime(model.StartDate, model.StartTime);
                //DateTime end = Utility.CreateDateTime(model.EndDate, model.EndTime);
                int count = 0;
                var start = model.StartDate.HasValue ? Utility.UnspecifiedToUTC(model.StartDate.Value) : null as DateTime?;
                var end = model.EndDate.HasValue ? Utility.UnspecifiedToUTC(model.EndDate.Value) : null as DateTime?;

                //transaction = _context.Database.BeginTransaction(System.Data.IsolationLevel.ReadUncommitted);

                List<Trainee> trainees = new List<Trainee>();
                CourseExam exam = null;
                bool isEdit = false;
                string bcc = null;
                var institute = await _context.Configurations.Select(x => x.Name).FirstOrDefaultAsync();
                string appUrl = System.Configuration.ConfigurationManager.AppSettings["TraineeUrl"];
                var em = new MailSendService();
                StreamReader reader = File.OpenText(HostingEnvironment.MapPath("~/Files/EmailTemplates/notification-for-enrollment.html"));

                StringBuilder sb;
                using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {

                    for (int i = 0; i < model.Trainees.Count(); i++)
                    {
                        Guid id = model.Trainees[i];
                        Trainee trainee = await _context.Trainees.FindAsync(id);
                        if (trainee == null)
                            return new APIResponse
                            {
                                Status = ResponseStatus.Warning,
                                Message = "This trainee does not exist "
                            };

                        trainees.Add(trainee);
                        bcc += trainee.Email + ";";
                    }

                    exam = await _context.CourseExams.Where(x => x.Id == model.examId).FirstOrDefaultAsync();
                    if (exam == null)
                        return new APIResponse
                        {
                            Status = ResponseStatus.Warning,
                            Message = "This course exam does not exist "
                        };

                    sb = new StringBuilder(reader.ReadToEnd());
                    sb = sb.Replace("#institute#", institute);
                    sb = sb.Replace("#account-type#", "E-Learning Solution - Trainee");
                    sb = sb.Replace("#link#", appUrl);
                    sb = sb.Replace("#full-name#", user.FirstName + " " + user.LastName);
                    sb = sb.Replace("#name#", exam.Course.Title);
                    sb = sb.Replace("#start#", start.Value.ToLongDateString() + " " + start.Value.ToShortTimeString());
                    sb = sb.Replace("#end#", end.Value.ToLongDateString() + " " + end.Value.ToShortTimeString());
                    em.SendMail(user.Email, null, bcc, "You are requested to take the exam -" + exam.Course.Title + "( " + exam.Course.Code + " ) - E-Learning Solution - Admin", sb.ToString());


                    scope.Complete();
                }
                foreach (var trainee in trainees)
                {
                    count++;
                    TraineeCourseExamConfiguration traineeCourseExamConfiguration;
                    traineeCourseExamConfiguration = await _context.TraineeCourseExamConfigurations.Where(x => x.TraineeId == trainee.Id && x.ExamId == exam.Id).FirstOrDefaultAsync();

                    if (traineeCourseExamConfiguration != null)
                    {
                        isEdit = true;
                    }

                    else
                    {
                        traineeCourseExamConfiguration = new TraineeCourseExamConfiguration();
                        isEdit = false;
                    }

                    traineeCourseExamConfiguration.TraineeId = trainee.Id;
                    traineeCourseExamConfiguration.ExamId = exam.Id;
                    traineeCourseExamConfiguration.StartDate = start.Value;
                    traineeCourseExamConfiguration.EndDate = end.Value;


                    traineeCourseExamConfiguration.SetAuditTrailEntity(user.User.Identity);

                    if (!isEdit)
                    {
                        Guid obj = Guid.NewGuid();
                        traineeCourseExamConfiguration.Id = obj;
                        _context.TraineeCourseExamConfigurations.Add(traineeCourseExamConfiguration);
                    }
                    else
                    {
                        _context.Entry(traineeCourseExamConfiguration).State = EntityState.Modified;
                    }
                    await _context.SaveChangesAsync();
                }
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Notified to " + count + " Trainees"
                };


            }
            catch (DbEntityValidationException ex)
            {
                transaction?.Rollback();
                List<string> errorList = new List<string>();
                foreach (var errors in ex.EntityValidationErrors)
                {
                    foreach (var validationError in errors.ValidationErrors)
                    {
                        // get the error message
                        errorList.Add(validationError.ErrorMessage);
                    }
                }
                transaction?.Rollback();
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(errorList), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" ", errorList)
                };


            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);
                transaction?.Rollback();
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }

            finally
            {
                transaction?.Dispose();
            }

        }

        //private void SendToEmailManager(Guid materialId, Guid courseId, string materialTitle)
        //{
        //    MasterEmail masterEmail = null;
        //    try
        //    {
        //        masterEmail = new MasterEmail
        //        {
        //            Id = Guid.NewGuid(),
        //            CourseId = courseId,
        //            ContentId = materialId,
        //            ContentName = materialTitle,
        //            EmailStatus = EmailStatus.Pending,
        //            EntryOn = Utility.CurrentDateTime(),
        //            LastModification = Utility.CurrentDateTime()
        //        };
        //        _context.MasterEmails.Add(masterEmail);

        //    }
        //    catch (Exception ex)
        //    {
        //        throw new Exception("Error on SendToEmailManager: " + ex.Message);
        //    }
        //}



        public async Task<APIResponse> GetEnrolledCourseDropDownList(ApplicationUser user)
        {
            try
            {
                var data = await _context.CourseEnrollments.Where(x => x.Course.Active && x.TraineeId == user.Trainee.Id).OrderBy(o => o.Course.Title)
                .Select(t => new
                {
                    t.Course.Id,
                    t.Course.Title
                }).ToListAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data }
                };

            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);


                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<byte[]> GetCourseWiseTraineeStudyReportExcel(Guid courseId)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            try
            {

                var course = await _context.Courses.Where(x => x.Id == courseId).Select(x => x.Title).FirstOrDefaultAsync();
                var company = await _context.Configurations.FirstOrDefaultAsync();

                //var query = _context.TraineeCourseActivities.Where(x => x.CourseId == courseId).AsQueryable();

                var data = await _context.TraineeCourseActivities.Where(x => x.CourseId == courseId)
                    .Select(x => new { x.TraineeId, x.Trainee.Name, x.Trainee.PhoneNo, x.Trainee.PIN, Division = x.Trainee.Division.Name, x.Trainee.Email, x.CreatedDate, LastStudyDate = x.MaterialStudies.Max(y => (DateTime?)y.StudyDate) })
                    .OrderBy(x => x.PIN)
                    .ToListAsync();

                var traineeIds = data.Select(x => x.TraineeId).ToList();
                var traineeExamsAll = await _context.TraineeExams.Where(x => x.Exam.CourseId == courseId && traineeIds.Contains(x.TraineeId))
                    .Select(x => new { x.Id, x.TraineeId, x.Exam.Course.Title, x.GainedMarks, x.Grade, x.ExamId, x.MarkedOn, x.CertificateAchieved, x.Attempts, x.CreatedDate }).OrderBy(x => x.CreatedDate).ToListAsync();
                var traineeExams = await _context.TraineeExams.Where(x => x.Exam.CourseId == courseId && traineeIds.Contains(x.TraineeId) && x.CertificateAchieved)
                    .Select(x => new { x.Id, x.TraineeId, x.Exam.Course.Title, x.GainedMarks, x.Grade, x.ExamId, x.MarkedOn, x.CertificateAchieved, x.Attempts, x.TotalMarks }).OrderByDescending(x => x.GainedMarks).ToListAsync();
                var traineeExamAttempts = await _context.TraineeExamAttempts.Where(x => x.Exam.CourseId == courseId && traineeIds.Contains(x.TraineeId))
                    .Select(x => new { x.TraineeId, x.Attempt }).ToListAsync();
                var traineeCertificates = await _context.TraineeCertificates.Where(x => x.Course.Id == courseId && traineeIds.Contains(x.TraineeId))
                    .Select(x => new { x.TraineeId, x.Result, x.GainedPercentage, x.Grade, x.TotalMarks, certificateGrade = x.Grade, x.ExpiryDate, x.Attempts, x.Expired }).ToListAsync();


                var headerColumns = new List<string> { "PIN", "Trainee Name", "Email", "Phone", "Division", "Study Start Date", "Test Pass Date", "Final Study Date", "Test Score", "No of Attempt", "Marks 1", "Marks 2", "Marks 3", "Best Score", "Certificate Expired (Yes/No)", "Expiration Date" };

                ExcelManager.GetTextLineElement(company.Name, ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                ExcelManager.GetTextLineElement(company.Address + " , " + company.ContactNo, ++rowNo, ws, fontSize: 8, isBold: false, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                rowNo++;

                ExcelManager.GetTextLineElement("Course Wise Trainee's Study Report", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                ExcelManager.GetTextLineElement("Course: " + course, ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                rowNo++;
                if (data.Count > 0)
                {
                    ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);

                    foreach (var item in data)
                    {
                        rowNo++;
                        colNo = 1;
                        var traineeExam = traineeExams.FirstOrDefault(x => x.TraineeId == item.TraineeId);
                        //var traineeExamByTraineeIdList = traineeExams.Where(x => x.TraineeId == item.TraineeId).Select(x=>x).ToList();
                        //var traineeExam = traineeExams.Where(x=>x.GainedMarks == traineeExamByTraineeIdList.Max(i=>i.GainedMarks)).FirstOrDefault();
                        var traineeExamAll1 = traineeExamsAll.Where(x => x.TraineeId == item.TraineeId).Count()>0? traineeExamsAll.Where(x => x.TraineeId == item.TraineeId).FirstOrDefault():null;
                        var traineeExamAll2 = traineeExamsAll.Where(x => x.TraineeId == item.TraineeId).Count() > 1 ? traineeExamsAll.Where(x => x.TraineeId == item.TraineeId).Skip(1).FirstOrDefault():null;
                        var traineeExamAll3 = traineeExamsAll.Where(x => x.TraineeId == item.TraineeId).Count() > 2 ? traineeExamsAll.Where(x => x.TraineeId == item.TraineeId).LastOrDefault():null;
                        var traineeExamAttempt = traineeExamAttempts.FirstOrDefault(x => x.TraineeId == item.TraineeId);
                        var traineeCertificate = traineeCertificates.FirstOrDefault(x => x.TraineeId == item.TraineeId);
                        ExcelManager.GetTableDataCell(item.PIN, 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(item.Name, 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(item.Email, 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(item.PhoneNo, 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(item.Division, 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(item.CreatedDate.ToLocalTime().ToString("dd-MMM-yyyy"), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        ExcelManager.GetTableDataCell(traineeExam?.MarkedOn?.ToLocalTime().ToString("dd-MMM-yyyy") ?? "", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        ExcelManager.GetTableDataCell(item.LastStudyDate?.ToLocalTime().ToString("dd-MMM-yyyy") ?? item.CreatedDate.ToString("dd-MMM-yyyy"), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        // ExcelManager.GetTableDataCell(traineeExams.FirstOrDefault(x => x.TraineeId == item.TraineeId)?.ExamName ?? "", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        ExcelManager.GetTableDataCell(traineeExam?.TotalMarks ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeExamsAll?.Where(x => x.TraineeId == item.TraineeId).Count() ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeExamAll1?.GainedMarks ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeExamAll2?.GainedMarks ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeExamAll3?.GainedMarks ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeExam?.GainedMarks ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeCertificate?.Expired == true ? "Yes" : traineeCertificate?.Expired == null ? "" : "No", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeCertificate?.ExpiryDate?.ToLocalTime().ToString("dd-MMM-yyyy"), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                    }

                    for (int i = 0; i < headerColumns.Count; i++)
                    {
                        ws.Column(i + 1).AdjustToContents();
                    }
                }
                else
                {
                    ExcelManager.GetTextLineElement("No record found", ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                }

                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);

                    return ms.ToArray();
                }
            }
            catch (Exception ibex)
            {
                LogControl.LogException(ibex);

                ExcelManager.GetTextLineElement("Error: " + ibex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
        }
        public async Task<byte[]> GetCourseWiseTraineeStudyReportPdf(Guid courseId)
        {
            var document = new iTextSharp.text.Document(iTextSharp.text.PageSize.A4, 36, 36, 100, 36);
            document.SetPageSize(iTextSharp.text.PageSize.A4.Rotate());
            var reportStream = new MemoryStream();

            try
            {
                PdfWriter writer = PdfWriter.GetInstance(document, reportStream);
                writer.PageEvent = new PDFWriterEvents(true, true);
                writer.CloseStream = false;

                // Openning the Document
                document.Open();


                var course = await _context.Courses.Where(x => x.Id == courseId).Select(x => x.Title).FirstOrDefaultAsync();
                var company = await _context.Configurations.FirstOrDefaultAsync();

                //var query = _context.TraineeCourseActivities.Where(x => x.CourseId == courseId).AsQueryable();

                var data = await _context.TraineeCourseActivities.Where(x => x.CourseId == courseId)
                    .Select(x => new { x.TraineeId, x.Trainee.Name, x.Trainee.PhoneNo, x.Trainee.PIN, Division = x.Trainee.Division.Name, x.Trainee.Email, x.CreatedDate, LastStudyDate = x.MaterialStudies.Max(y => (DateTime?)y.StudyDate) })
                    .OrderBy(x => x.PIN)
                    .ToListAsync();

                var traineeIds = data.Select(x => x.TraineeId).ToList();
                //var traineeExamsAll = await _context.TraineeExams.Where(x => x.Exam.CourseId == courseId && traineeIds.Contains(x.TraineeId))
                //    .Select(x => new { x.Id, x.TraineeId, x.Exam.Course.Title, x.GainedMarks, x.Grade, x.ExamId, x.MarkedOn, x.CertificateAchieved, x.Attempts, x.CreatedDate }).OrderBy(x => x.CreatedDate).ToListAsync();
                var traineeExams = await _context.TraineeExams.Where(x => x.Exam.CourseId == courseId && traineeIds.Contains(x.TraineeId) && x.CertificateAchieved)
                    .Select(x => new { x.Id, x.TraineeId, x.Exam.Course.Title, x.GainedMarks, x.Grade, x.ExamId, x.MarkedOn, x.CertificateAchieved, x.Attempts, x.TotalMarks }).OrderByDescending(x => x.GainedMarks).ToListAsync();
                //var traineeExamAttempts = await _context.TraineeExamAttempts.Where(x => x.Exam.CourseId == courseId && traineeIds.Contains(x.TraineeId))
                //    .Select(x => new { x.TraineeId, x.Attempt }).ToListAsync();
                //var traineeCertificates = await _context.TraineeCertificates.Where(x => x.Course.Id == courseId && traineeIds.Contains(x.TraineeId))
                //    .Select(x => new { x.TraineeId, x.Result, x.GainedPercentage, x.Grade, x.TotalMarks, certificateGrade = x.Grade, x.ExpiryDate, x.Attempts, x.Expired }).ToListAsync();

                if (!data.Any()) throw new Exception("No data found");

                #region Page Header
                PdfPTable header = new PdfPTable(1);
                header.TotalWidth = 180;
                header.DefaultCell.Border = 0;
                header.AddCell(PDFManager.GetTextLineElement(company.Name, 12f, isBold: true));
                header.AddCell(PDFManager.GetTextLineElement(company.Address + " , " + company.ContactNo, isBold: false, fontSize: 8, leftToRightPadding: new float[] { 4, 2, 4, 2 }));
                header.WriteSelectedRows(0, 3, writer.PageSize.Width - document.LeftMargin - document.RightMargin - 180, writer.PageSize.GetTop(10), writer.DirectContent);
                if (!string.IsNullOrEmpty(company.LogoPath))
                {
                    Image img = Image.GetInstance(HostingEnvironment.MapPath("~") + company.LogoPath);
                    img.SetAbsolutePosition(document.LeftMargin, writer.PageSize.GetTop(70));
                    img.ScaleAbsolute(200f, 60f);
                    document.Add(img);
                }
                PdfPTable line = new PdfPTable(1);
                line.TotalWidth = writer.PageSize.Width;
                line.DefaultCell.Border = 0;
                line.AddCell(new Paragraph(new Chunk(new iTextSharp.text.pdf.draw.LineSeparator(0.0F, 100.0F, BaseColor.BLACK, Element.ALIGN_CENTER, 3))));
                line.WriteSelectedRows(0, 1, 0, writer.PageSize.GetTop(header.TotalHeight + 10), writer.DirectContent);
                #endregion

                document.Add(PDFManager.GetTextLineElement("Course Wise Study Report", fontSize: 16, isBold: true, isUnderlined: false, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));

                document.Add(PDFManager.GetTextLineElement("Course: " + course, fontSize: 14, isBold: true, isUnderlined: false, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                #region Table
                var table = new PdfPTable(9) { WidthPercentage = 100 };
                table.SetWidths(new float[] { 5, 40, 18, 10, 10, 10, 10, 10, 7 });
                table.AddCell(PDFManager.GetTableHeaderCell("PIN", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Trainee Name", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Email", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Phone", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Division", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Study Start Date", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Test Pass Date", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Final Study Date", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Test Score", 8f, true, false, PDFAlignment.Center));

                foreach (var item in data)
                {
                    var traineeExam = traineeExams.FirstOrDefault(x => x.TraineeId == item.TraineeId);
                    table.AddCell(PDFManager.GetTableDataCell(item.PIN, PDFAlignment.Center, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(item.Name, PDFAlignment.Center, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(item.Email, PDFAlignment.Center, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(item.PhoneNo, PDFAlignment.Center, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(item.Division, PDFAlignment.Center, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(item.CreatedDate.ToLocalTime().ToString("dd-MMM-yyyy"), PDFAlignment.Center, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(traineeExam?.MarkedOn?.ToLocalTime().ToString("dd-MMM-yyyy") ?? "", PDFAlignment.Center, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(item.LastStudyDate?.ToLocalTime().ToString("dd-MMM-yyyy") ?? item.CreatedDate.ToString("dd-MMM-yyyy"), PDFAlignment.Center, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(traineeExam?.GainedMarks ?? 0, PDFAlignment.Center, false, true, 8));
                }
                document.Add(table);
                #endregion

                // Closing the Document
                document.Close();
                return reportStream.ToArray();
            }
            catch (DocumentException dex)
            {
                document.Add(PDFManager.GetTextLineElement(dex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
            // Catching System.IO.IOException if any
            catch (Exception ioex)
            {
                document.Add(PDFManager.GetTextLineElement(ioex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }

        }

        public async Task<byte[]> GetCourseWiseTestReportExcel(DateTime startDate, DateTime endDate, Guid courseId, ExamType type)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            try
            {

                var course = await _context.Courses.Where(x => x.Id == courseId).Select(x => x.Title).FirstOrDefaultAsync();
                var company = await _context.Configurations.FirstOrDefaultAsync();

                var query = _context.CourseEnrollments.Where(x => x.CourseId == courseId).AsQueryable();

                var data = await query.Select(x => new { x.TraineeId, x.Trainee.Name, x.Trainee.PhoneNo, x.Trainee.Email })
                    .OrderBy(x => x.Name)
                    .ToListAsync();

                var traineeIds = data.Select(x => x.TraineeId).ToList();

                var traineeExams = await _context.TraineeExams.Where(x => (x.CreatedDate >= startDate && x.CreatedDate <= endDate) && x.Exam.CourseId == courseId && traineeIds.Contains(x.TraineeId))
                    .Select(x => new { x.TraineeId, Course = x.Exam.Course.Title, x.GainedMarks, x.ExamId, x.MarkedOn }).ToListAsync();

                //var certificateExams = await _context.CourseExams.Where(x => x.ExamType == ExamType.CertificationTest && x.CourseId == courseId).Select(x => new { x.Id, x.ExamName }).ToListAsync();

                var headerColumns = new List<string> { "Trainee Name", "Email", "Phone", "Test Name", "Score", "Test Pass Date", };

                ExcelManager.GetTextLineElement(company.Name, ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                ExcelManager.GetTextLineElement(company.Address + " , " + company.ContactNo, ++rowNo, ws, fontSize: 8, isBold: false, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                rowNo++;

                ExcelManager.GetTextLineElement(type == ExamType.CertificationTest ? "Course Wise Trainee's Certification Tests Report" : "Course Wise Trainee's Comprehension Tests Report", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                ExcelManager.GetTextLineElement("Course: " + course, ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                rowNo++;
                if (data.Count > 0)
                {
                    ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);

                    foreach (var item in data)
                    {
                        var exam = traineeExams.Where(x => x.TraineeId == item.TraineeId).OrderByDescending(x => x.GainedMarks).Select(x => new { x.Course, x.MarkedOn, x.GainedMarks }).FirstOrDefault();
                        if (exam == null) continue;
                        rowNo++;
                        colNo = 1;
                        ExcelManager.GetTableDataCell(item.Name, 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(item.Email, 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(item.PhoneNo, 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(exam?.Course, 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(exam?.GainedMarks.ToString(), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        // ExcelManager.GetTableDataCell(item.CreatedDate.ToString("dd-MMM-yyyy"), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        ExcelManager.GetTableDataCell(exam?.MarkedOn.Value.ToString("dd-MMM-yyyy") ?? "", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        // ExcelManager.GetTableDataCell(item.LastStudyDate.ToString("dd-MMM-yyyy") ?? item.CreatedDate.ToString("dd-MMM-yyyy"), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        // ExcelManager.GetTableDataCell(traineeExams.FirstOrDefault(x => x.TraineeId == item.TraineeId)?.ExamName ?? "", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);

                    }

                    for (int i = 0; i < headerColumns.Count; i++)
                    {
                        ws.Column(i + 1).AdjustToContents();
                    }
                }
                else
                {
                    ExcelManager.GetTextLineElement("No record found", ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                }

                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);

                    return ms.ToArray();
                }
            }
            catch (Exception ibex)
            {
                LogControl.LogException(ibex);

                ExcelManager.GetTextLineElement("Error: " + ibex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
        }

        public async Task<byte[]> GetTraineeWiseCourseStudyReportExcel(Guid traineeId)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            try
            {
                var trainee = await _context.Trainees.Where(x => x.Id == traineeId).Select(x => new { x.Name, x.PhoneNo, x.PIN, Division = x.Division.Name, x.Email }).FirstOrDefaultAsync();

                var company = await _context.Configurations.FirstOrDefaultAsync();

                var query = _context.CourseEnrollments.Where(x => x.TraineeId == traineeId).AsQueryable();

                var data = await query
                    .GroupJoin(_context.TraineeCourseActivities, x => new { x.TraineeId, x.CourseId }, y => new { y.TraineeId, y.CourseId }, (x, y) => new
                    {
                        Enrollment = x,
                        Activities = y
                    }).SelectMany(x => x.Activities.DefaultIfEmpty(), (y, z) => new { y.Enrollment, Activity = z })
                    .Select(x => new { x.Enrollment.TraineeId, x.Enrollment.Course.Title, x.Enrollment.CourseId, x.Enrollment.Trainee.Name, x.Enrollment.Trainee.PhoneNo, x.Enrollment.Trainee.PIN, Division = x.Enrollment.Trainee.Division.Name, x.Enrollment.Trainee.Email, FirstStudyDate = x.Activity != null ? x.Activity.FirstStudyDate : default(DateTime?), LastStudyDate = x.Activity.LastStudyDate != null ? x.Activity.LastStudyDate : default(DateTime?) })
                    .OrderBy(x => x.Title)
                    .ToListAsync();

                var traineeIds = data.Select(x => x.TraineeId).ToList();
                var traineeCertificates = await _context.TraineeCertificates.Where(x => !x.Expired && traineeIds.Contains(x.TraineeId))
                    .Select(x => new { x.TraineeId, x.CourseId, x.GainedPercentage, x.TraineeExamId, x.TraineeExam, x.CertificateDate, x.GainedMarks, x.TotalMarks, x.Grade, x.Expired, x.ExpiryDate, x.Attempts }).ToListAsync();
                var attempts = await _context.TraineeExamAttempts.
                 Join(_context.CourseExams,
                 t => t.ExamId,
                 ce => ce.Id,
                 (t, ce) => new { t, ce }).
                 Join(_context.Courses,
                 ce => ce.ce.CourseId,
                 c => c.Id,
                 (ce, c) => new { ce, c }).
                 Join(_context.TraineeCourseActivities,
                 c => c.c.Id,
                 tca => tca.CourseId,
                 (c, tca) => new { c, tca }).Select(e => new
                 {
                     e
                 })
                 .ToListAsync();
                //var certificateExams = await _context.CourseExams.Where(x => x.ExamType == ExamType.CertificationTest && x.CourseId == courseId).Select(x => new { x.Id, x.ExamName }).ToListAsync();

                var headerColumns = new List<string> { "Course Title", "Study Start Date", "Test Pass Date", "Final Study Date", "No of Attempt", "Gained Marks", "Total Marks", "Score Mark %", "Score Grade", "Certificate Expired (Yes/No)", "Expiration Date" };

                ExcelManager.GetTextLineElement(company.Name, ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                ExcelManager.GetTextLineElement(company.Address + " , " + company.ContactNo, ++rowNo, ws, fontSize: 8, isBold: false, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                rowNo++;

                ExcelManager.GetTextLineElement("Trainee Wise Course's Study Report", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                //ExcelManager.GetTextLineElement("Trainee: " + trainee, ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                rowNo++;
                ExcelManager.GetTableHeaderCell(new List<string> { "PIN", "Trainee Name", "Email", "Phone", "Division" }, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);
                rowNo++;
                colNo = 1;
                ExcelManager.GetTableDataCell(trainee.PIN, 10, rowNo, colNo++, ws);
                ExcelManager.GetTableDataCell(trainee.Name, 10, rowNo, colNo++, ws);
                ExcelManager.GetTableDataCell(trainee.Email, 10, rowNo, colNo++, ws);
                ExcelManager.GetTableDataCell(trainee.PhoneNo, 10, rowNo, colNo++, ws);
                ExcelManager.GetTableDataCell(trainee.Division, 10, rowNo, colNo++, ws);

                rowNo++;
                if (data.Count > 0)
                {
                    ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);

                    foreach (var item in data)
                    {
                        rowNo++;
                        colNo = 1;
                        ExcelManager.GetTableDataCell(item.Title, 10, rowNo, colNo++, ws);

                        var certificate = traineeCertificates.FirstOrDefault(x => x.TraineeId == item.TraineeId && x.CourseId == item.CourseId);
                        var attempt = attempts.FirstOrDefault(x => x.e.tca.TraineeId == item.TraineeId && x.e.c.c.Id == item.CourseId);
                        ExcelManager.GetTableDataCell(item.FirstStudyDate?.ToLocalTime().ToString("dd-MMM-yyyy"), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        ExcelManager.GetTableDataCell(certificate?.CertificateDate.ToLocalTime().ToString("dd-MMM-yyyy") ?? "", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        ExcelManager.GetTableDataCell(item.LastStudyDate?.ToLocalTime().ToString("dd-MMM-yyyy") ?? "", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        // ExcelManager.GetTableDataCell(traineeExams.FirstOrDefault(x => x.TraineeId == item.TraineeId)?.ExamName ?? "", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        ExcelManager.GetTableDataCell((attempt?.e.c.ce.t.Attempt), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(certificate?.GainedMarks ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(certificate?.TotalMarks ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(certificate?.GainedPercentage ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(certificate?.Grade, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(certificate?.Expired == true ? "Yes" : certificate?.Expired == null ? "" : "No", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(certificate?.ExpiryDate?.ToLocalTime().ToString("dd-MMM-yyyy"), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                    }

                    for (int i = 0; i < headerColumns.Count; i++)
                    {
                        ws.Column(i + 1).AdjustToContents();
                    }
                }
                else
                {
                    ExcelManager.GetTextLineElement("No record found", ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                }

                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);

                    return ms.ToArray();
                }
            }
            catch (Exception ibex)
            {
                LogControl.LogException(ibex);

                ExcelManager.GetTextLineElement("Error: " + ibex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
        }
        public async Task<byte[]> GetTraineeWiseCourseStudyReportPdf(Guid traineeId)
        {
            var document = new iTextSharp.text.Document(iTextSharp.text.PageSize.A4, 36, 36, 100, 36);
            document.SetPageSize(iTextSharp.text.PageSize.A4.Rotate());
            var reportStream = new MemoryStream();

            try
            {
                PdfWriter writer = PdfWriter.GetInstance(document, reportStream);
                writer.PageEvent = new PDFWriterEvents(true, true);
                writer.CloseStream = false;

                // Openning the Document
                document.Open();

                var trainee = await _context.Trainees.Where(x => x.Id == traineeId).Select(x => new { x.Name, x.PhoneNo, x.PIN, Division = x.Division.Name, x.Email }).FirstOrDefaultAsync();

                var company = await _context.Configurations.FirstOrDefaultAsync();

                var query = _context.CourseEnrollments.Where(x => x.TraineeId == traineeId).AsQueryable();

                var data = await query
                    .GroupJoin(_context.TraineeCourseActivities, x => new { x.TraineeId, x.CourseId }, y => new { y.TraineeId, y.CourseId }, (x, y) => new
                    {
                        Enrollment = x,
                        Activities = y
                    }).SelectMany(x => x.Activities.DefaultIfEmpty(), (y, z) => new { y.Enrollment, Activity = z })
                    .Select(x => new { x.Enrollment.TraineeId, x.Enrollment.Course.Title, x.Enrollment.CourseId, x.Enrollment.Trainee.Name, x.Enrollment.Trainee.PhoneNo, x.Enrollment.Trainee.PIN, Division = x.Enrollment.Trainee.Division.Name, x.Enrollment.Trainee.Email, FirstStudyDate = x.Activity != null ? x.Activity.FirstStudyDate : default(DateTime?), LastStudyDate = x.Activity.LastStudyDate != null ? x.Activity.LastStudyDate : default(DateTime?) })
                    .OrderBy(x => x.Title)
                    .ToListAsync();

                var traineeIds = data.Select(x => x.TraineeId).ToList();
                var traineeCertificates = await _context.TraineeCertificates.Where(x => !x.Expired && traineeIds.Contains(x.TraineeId))
                    .Select(x => new { x.TraineeId, x.CourseId, x.GainedPercentage, x.TraineeExamId, x.TraineeExam, x.CertificateDate, x.GainedMarks, x.TotalMarks, x.Grade, x.Expired, x.ExpiryDate, x.Attempts }).ToListAsync();
                var attempts = await _context.TraineeExamAttempts.
                 Join(_context.CourseExams,
                 t => t.ExamId,
                 ce => ce.Id,
                 (t, ce) => new { t, ce }).
                 Join(_context.Courses,
                 ce => ce.ce.CourseId,
                 c => c.Id,
                 (ce, c) => new { ce, c }).
                 Join(_context.TraineeCourseActivities,
                 c => c.c.Id,
                 tca => tca.CourseId,
                 (c, tca) => new { c, tca }).Select(e => new
                 {
                     e
                 })
                 .ToListAsync();
                if (!data.Any()) throw new Exception("No data found");

                #region Page Header
                PdfPTable header = new PdfPTable(1);
                header.TotalWidth = 180;
                header.DefaultCell.Border = 0;
                header.AddCell(PDFManager.GetTextLineElement(company.Name, 12f, isBold: true));
                header.AddCell(PDFManager.GetTextLineElement(company.Address + " , " + company.ContactNo, isBold: false, fontSize: 8, leftToRightPadding: new float[] { 4, 2, 4, 2 }));
                header.WriteSelectedRows(0, 3, writer.PageSize.Width - document.LeftMargin - document.RightMargin - 180, writer.PageSize.GetTop(10), writer.DirectContent);
                if (!string.IsNullOrEmpty(company.LogoPath))
                {
                    Image img = Image.GetInstance(HostingEnvironment.MapPath("~") + company.LogoPath);
                    img.SetAbsolutePosition(document.LeftMargin, writer.PageSize.GetTop(70));
                    img.ScaleAbsolute(200f, 60f);
                    document.Add(img);
                }
                PdfPTable line = new PdfPTable(1);
                line.TotalWidth = writer.PageSize.Width;
                line.DefaultCell.Border = 0;
                line.AddCell(new Paragraph(new Chunk(new iTextSharp.text.pdf.draw.LineSeparator(0.0F, 100.0F, BaseColor.BLACK, Element.ALIGN_CENTER, 3))));
                line.WriteSelectedRows(0, 1, 0, writer.PageSize.GetTop(header.TotalHeight + 10), writer.DirectContent);
                #endregion

                document.Add(PDFManager.GetTextLineElement("Trainee Wise Course's Study Report", fontSize: 16, isBold: true, isUnderlined: false, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));

                #region Table
                var table = new PdfPTable(5) { WidthPercentage = 100 };
                table.SetWidths(new float[] { 10, 25, 25, 20, 10 });
                table.AddCell(PDFManager.GetTableHeaderCell("PIN", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Trainee Name", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Email", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Phone", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Division", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableDataCell(trainee.PIN, PDFAlignment.Center, false, true, 8));
                table.AddCell(PDFManager.GetTableDataCell(trainee.Name, PDFAlignment.Center, false, true, 8));
                table.AddCell(PDFManager.GetTableDataCell(trainee.Email, PDFAlignment.Center, false, true, 8));
                table.AddCell(PDFManager.GetTableDataCell(trainee.PhoneNo, PDFAlignment.Center, false, true, 8));
                table.AddCell(PDFManager.GetTableDataCell(trainee.Division, PDFAlignment.Center, false, true, 8));
                document.Add(table);

                table = new PdfPTable(5) { WidthPercentage = 100 };
                table.SetWidths(new float[] { 25, 25, 20, 20, 10 });
                table.AddCell(PDFManager.GetTableHeaderCell("Course Title", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Study Start Date", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Test Pass Date", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Final Study Date", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Score", 8f, true, false, PDFAlignment.Center));

                foreach (var item in data)
                {
                    var certificate = traineeCertificates.FirstOrDefault(x => x.TraineeId == item.TraineeId && x.CourseId == item.CourseId);
                    table.AddCell(PDFManager.GetTableDataCell(item.Title, PDFAlignment.Left, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(item.FirstStudyDate?.ToLocalTime().ToString("dd-MMM-yyyy"), PDFAlignment.Center, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(certificate?.CertificateDate.ToLocalTime().ToString("dd-MMM-yyyy") ?? "", PDFAlignment.Center, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(item.LastStudyDate?.ToLocalTime().ToString("dd-MMM-yyyy"), PDFAlignment.Center, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(certificate?.GainedPercentage ?? 0, PDFAlignment.Center, false, true, 8));
                }
                document.Add(table);
                #endregion

                // Closing the Document
                document.Close();
                return reportStream.ToArray();
            }
            catch (DocumentException dex)
            {
                document.Add(PDFManager.GetTextLineElement(dex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
            // Catching System.IO.IOException if any
            catch (Exception ioex)
            {
                document.Add(PDFManager.GetTextLineElement(ioex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }

        }
        //public async Task<byte[]> GetTimeWiseCourseStudyReportExcel(DateTime startDate, DateTime endDate)
        //{
        //    IXLWorkbook wb = new XLWorkbook();
        //    IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
        //    int rowNo = 0, colNo;

        //    try
        //    {
        //        var courseEnrollments = await _context.CourseEnrollments
        //            .Where(x => DbFunctions.TruncateTime(x.EnrollmentDate) >= DbFunctions.TruncateTime(startDate)
        //                && DbFunctions.TruncateTime(x.EnrollmentDate) <= DbFunctions.TruncateTime(endDate))
        //            .ToListAsync();
        //        var company = await _context.Configurations.FirstOrDefaultAsync();
        //        var traineeIds = courseEnrollments.Select(x => x.TraineeId).ToList();
        //        var courseIds = courseEnrollments.Select(x => x.CourseId).ToList();
        //        var trainees = await _context.Trainees.Where(x => traineeIds.Contains(x.Id)).ToListAsync();
        //        var courses = await _context.Courses.Where(x => courseIds.Contains(x.Id)).ToListAsync();
        //        //var certificateExams = await _context.CourseExams.Where(x => x.ExamType == ExamType.CertificationTest && x.CourseId == courseId).Select(x => new { x.Id, x.ExamName }).ToListAsync();
        //        var traineeExams = await _context.TraineeExams.Where(x => courseIds.Contains(x.Exam.CourseId) && traineeIds.Contains(x.TraineeId))
        //           .Select(x => new { x.TraineeId, x.Exam.CourseId, Course = x.Exam.Course.Title, x.GainedMarks, x.ExamId, x.MarkedOn, x.TotalMarks, x.Grade, x.Result, x.GainedPercentage }).OrderByDescending(x => x.GainedMarks).ToListAsync();
        //        var TraineeCourseActivities = await _context.TraineeCourseActivities.Where(x => courseIds.Contains(x.CourseId) && traineeIds.Contains(x.TraineeId)).ToListAsync();
        //        var headerColumns = new List<string> { "PIN", "Name", "Date of Joining", "Department", "Division", "Course Name", "Institute", "Enrollment Date", "Study Start From", "Last Study Date","Pass Date", "Gained Marks", "Total Marks", "Score %", "Grade", "Result" };


        //        ExcelManager.GetTextLineElement(company.Name, ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

        //        ExcelManager.GetTextLineElement(company.Address + " , " + company.ContactNo, ++rowNo, ws, fontSize: 8, isBold: false, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

        //        rowNo++;

        //        ExcelManager.GetTextLineElement("Time Wise Course's Study Report", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
        //        //ExcelManager.GetTextLineElement("Trainee: " + trainee, ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
        //        rowNo++;
        //        colNo = 1;
        //        if (courseEnrollments.Count > 0)
        //        {
        //            ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);

        //            foreach (var item in courseEnrollments)
        //            {
        //                rowNo++;
        //                colNo = 1;
        //                var trainee = trainees.FirstOrDefault(x => x.Id == item.TraineeId);
        //                var course = courses.FirstOrDefault(x => x.Id == item.CourseId);
        //                var traineeExam = traineeExams.FirstOrDefault(x => x.TraineeId == trainee.Id && x.CourseId == course.Id);
        //                //var traineeExamByTraineeIdList = traineeExams.Where(x => x.TraineeId == trainee.Id && x.CourseId == course.Id).Select(x => x).ToList();
        //                //var traineeExam = traineeExamByTraineeIdList.Where(x => x.GainedMarks == traineeExamByTraineeIdList.Max(i => i.GainedMarks)).FirstOrDefault();
        //                var TraineeCourseActivity = TraineeCourseActivities.FirstOrDefault(x => x.TraineeId == trainee.Id && x.CourseId == course.Id);
        //                ExcelManager.GetTableDataCell(trainee?.PIN, 10, rowNo, colNo++, ws);
        //                ExcelManager.GetTableDataCell(trainee?.Name, 10, rowNo, colNo++, ws);
        //                ExcelManager.GetTableDataCell(trainee?.DateOfJoining.Value.ToString("dd-MMM-yyyy"), 10, rowNo, colNo++, ws);
        //                ExcelManager.GetTableDataCell(trainee?.Department?.Name, 10, rowNo, colNo++, ws);
        //                ExcelManager.GetTableDataCell(trainee?.Division?.Name, 10, rowNo, colNo++, ws);
        //                ExcelManager.GetTableDataCell(course?.Title, 10, rowNo, colNo++, ws);
        //                ExcelManager.GetTableDataCell("BBL ALO", 10, rowNo, colNo++, ws);
        //                ExcelManager.GetTableDataCell(item?.EnrollmentDate.ToString("dd-MMM-yyyy"), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
        //                ExcelManager.GetTableDataCell(TraineeCourseActivity?.FirstStudyDate?.ToString("dd-MMM-yyyy"), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
        //                ExcelManager.GetTableDataCell(TraineeCourseActivity?.LastStudyDate?.ToString("dd-MMM-yyyy"), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
        //                ExcelManager.GetTableDataCell(traineeExam?.MarkedOn?.ToString("dd-MMM-yyyy") ?? "", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
        //                ExcelManager.GetTableDataCell(traineeExam?.GainedMarks ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
        //                ExcelManager.GetTableDataCell(traineeExam?.TotalMarks ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
        //                ExcelManager.GetTableDataCell(traineeExam?.GainedPercentage ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
        //                ExcelManager.GetTableDataCell(traineeExam?.Grade, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
        //                ExcelManager.GetTableDataCell(traineeExam?.Result, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);

        //            }

        //            for (int i = 0; i < headerColumns.Count; i++)
        //            {
        //                ws.Column(i + 1).AdjustToContents();
        //            }
        //        }
        //        else
        //        {
        //            ExcelManager.GetTextLineElement("No record found", ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
        //        }

        //        // Closing the Document
        //        using (var ms = new MemoryStream())
        //        {
        //            wb.SaveAs(ms);

        //            return ms.ToArray();
        //        }
        //    }
        //    catch (Exception ibex)
        //    {
        //        LogControl.LogException(ibex);

        //        ExcelManager.GetTextLineElement("Error: " + ibex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
        //        using (var ms = new MemoryStream())
        //        {
        //            wb.SaveAs(ms);
        //            return ms.ToArray();
        //        }
        //    }
        //}

        public async Task<byte[]> GetTimeWiseCourseStudyReportExcel(DateTime startDate, DateTime endDate)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            try
            {
                var courseEnrollments = await _context.CourseEnrollments
                    .Where(x => x.EnrollmentDate >= startDate && x.EnrollmentDate <= endDate)
                    .ToListAsync();

                var company = await _context.Configurations.FirstOrDefaultAsync();
                var traineeIds = courseEnrollments.Select(x => x.TraineeId).ToList();
                var courseIds = courseEnrollments.Select(x => x.CourseId).ToList();

                var trainees = await _context.Trainees
                    .Where(x => traineeIds.Contains(x.Id))
                    .ToListAsync();

                var courses = await _context.Courses
                    .Where(x => courseIds.Contains(x.Id))
                    .ToListAsync();

                var traineeExams = await _context.TraineeExams
                    .Where(x => courseIds.Contains(x.Exam.CourseId) && traineeIds.Contains(x.TraineeId))
                    .Select(x => new
                    {
                        x.TraineeId,
                        x.Exam.CourseId,
                        Course = x.Exam.Course.Title,
                        x.GainedMarks,
                        x.ExamId,
                        x.MarkedOn,
                        x.TotalMarks,
                        x.Grade,
                        x.Result,
                        x.GainedPercentage
                    })
                    .OrderByDescending(x => x.GainedMarks)
                    .ToListAsync();

                var traineeCourseActivities = await _context.TraineeCourseActivities
                    .Where(x => courseIds.Contains(x.CourseId) && traineeIds.Contains(x.TraineeId))
                    .ToListAsync();

                var headerColumns = new List<string>
        {
            "PIN", "Name", "Date of Joining", "Department", "Division", "Course Name",
            "Institute", "Enrollment Date", "Study Start From", "Last Study Date",
            "Pass Date", "Gained Marks", "Total Marks", "Score %", "Grade", "Result"
        };

                ExcelManager.GetTextLineElement(company?.Name ?? "Company Name", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                ExcelManager.GetTextLineElement(company != null ? $"{company.Address} , {company.ContactNo}" : "Company Address, Contact No", ++rowNo, ws, fontSize: 8, isBold: false, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                rowNo++;

                ExcelManager.GetTextLineElement("Time Wise Course's Study Report", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                rowNo++;

                if (courseEnrollments.Any())
                {
                    ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);

                    foreach (var item in courseEnrollments)
                    {
                        rowNo++;
                        colNo = 1;

                        var trainee = trainees.FirstOrDefault(x => x.Id == item.TraineeId);
                        var course = courses.FirstOrDefault(x => x.Id == item.CourseId);
                        var traineeExam = traineeExams.FirstOrDefault(x => x.TraineeId == trainee?.Id && x.CourseId == course?.Id);
                        var traineeCourseActivity = traineeCourseActivities.FirstOrDefault(x => x.TraineeId == trainee?.Id && x.CourseId == course?.Id);

                        ExcelManager.GetTableDataCell(trainee?.PIN ?? "N/A", 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(trainee?.Name ?? "N/A", 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(trainee?.DateOfJoining?.ToLocalTime().ToString("dd-MMM-yyyy") ?? "N/A", 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(trainee?.Department?.Name ?? "N/A", 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(trainee?.Division?.Name ?? "N/A", 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(course?.Title ?? "N/A", 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell("BBL ALO", 10, rowNo, colNo++, ws); // Assuming this value is constant
                        ExcelManager.GetTableDataCell(item?.EnrollmentDate.ToLocalTime().ToString("dd-MMM-yyyy") ?? "N/A", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeCourseActivity?.FirstStudyDate?.ToLocalTime().ToString("dd-MMM-yyyy") ?? "N/A", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeCourseActivity?.LastStudyDate?.ToLocalTime().ToString("dd-MMM-yyyy") ?? "N/A", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeExam?.MarkedOn?.ToLocalTime().ToString("dd-MMM-yyyy") ?? "N/A", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        ExcelManager.GetTableDataCell(traineeExam?.GainedMarks ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeExam?.TotalMarks ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeExam?.GainedPercentage ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeExam?.Grade ?? "N/A", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeExam?.Result != null && Enum.IsDefined(typeof(GradeResult), traineeExam.Result) ? Enum.Parse(typeof(GradeResult), traineeExam.Result.ToString()).ToString() : "N/A", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);

                    }

                    for (int i = 0; i < headerColumns.Count; i++)
                    {
                        ws.Column(i + 1).AdjustToContents();
                    }
                }
                else
                {
                    ExcelManager.GetTextLineElement("No record found", ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                }

                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                ExcelManager.GetTextLineElement("Error: " + ex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
        }

        public async Task<byte[]> GetTimeWiseCourseStudyReportPdf(DateTime startDate, DateTime endDate)
        {
            var document = new iTextSharp.text.Document(iTextSharp.text.PageSize.A4, 36, 36, 100, 36);
            document.SetPageSize(iTextSharp.text.PageSize.A4.Rotate());
            var reportStream = new MemoryStream();

            try
            {
                PdfWriter writer = PdfWriter.GetInstance(document, reportStream);
                writer.PageEvent = new PDFWriterEvents(true, true);
                writer.CloseStream = false;

                // Openning the Document
                document.Open();
                var courseEnrollments = await _context.CourseEnrollments
                    .Where(x => DbFunctions.TruncateTime(x.EnrollmentDate) >= DbFunctions.TruncateTime(startDate)
                        && DbFunctions.TruncateTime(x.EnrollmentDate) <= DbFunctions.TruncateTime(endDate))
                    .ToListAsync();
                var company = await _context.Configurations.FirstOrDefaultAsync();
                var traineeIds = courseEnrollments.Select(x => x.TraineeId).ToList();
                var courseIds = courseEnrollments.Select(x => x.CourseId).ToList();
                var trainees = await _context.Trainees.Where(x => traineeIds.Contains(x.Id)).ToListAsync();
                var courses = await _context.Courses.Where(x => courseIds.Contains(x.Id)).ToListAsync();
                //var certificateExams = await _context.CourseExams.Where(x => x.ExamType == ExamType.CertificationTest && x.CourseId == courseId).Select(x => new { x.Id, x.ExamName }).ToListAsync();
                var traineeExams = await _context.TraineeExams.Where(x => courseIds.Contains(x.Exam.CourseId) && traineeIds.Contains(x.TraineeId))
                   .Select(x => new { x.TraineeId, x.Exam.CourseId, Course = x.Exam.Course.Title, x.GainedMarks, x.ExamId, x.MarkedOn, x.TotalMarks, x.Grade, x.Result, x.GainedPercentage }).OrderByDescending(x => x.GainedMarks).ToListAsync();
                var TraineeCourseActivities = await _context.TraineeCourseActivities.Where(x => courseIds.Contains(x.CourseId) && traineeIds.Contains(x.TraineeId)).ToListAsync();
                #region Page Header
                PdfPTable header = new PdfPTable(1);
                header.TotalWidth = 180;
                header.DefaultCell.Border = 0;
                header.AddCell(PDFManager.GetTextLineElement(company.Name, 12f, isBold: true));
                header.AddCell(PDFManager.GetTextLineElement(company.Address + " , " + company.ContactNo, isBold: false, fontSize: 8, leftToRightPadding: new float[] { 4, 2, 4, 2 }));
                header.WriteSelectedRows(0, 3, writer.PageSize.Width - document.LeftMargin - document.RightMargin - 180, writer.PageSize.GetTop(10), writer.DirectContent);
                if (!string.IsNullOrEmpty(company.LogoPath))
                {
                    Image img = Image.GetInstance(HostingEnvironment.MapPath("~") + company.LogoPath);
                    img.SetAbsolutePosition(document.LeftMargin, writer.PageSize.GetTop(70));
                    img.ScaleAbsolute(200f, 60f);
                    document.Add(img);
                }
                PdfPTable line = new PdfPTable(1);
                line.TotalWidth = writer.PageSize.Width;
                line.DefaultCell.Border = 0;
                line.AddCell(new Paragraph(new Chunk(new iTextSharp.text.pdf.draw.LineSeparator(0.0F, 100.0F, BaseColor.BLACK, Element.ALIGN_CENTER, 3))));
                line.WriteSelectedRows(0, 1, 0, writer.PageSize.GetTop(header.TotalHeight + 10), writer.DirectContent);
                #endregion
                document.Add(PDFManager.GetTextLineElement("Time Wise Course's Study Report", fontSize: 16, isBold: true, isUnderlined: false, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                #region Table
                var table = new PdfPTable(15) { WidthPercentage = 100 };
                table.SetWidths(new int[] { 10, 15, 15, 15, 15, 15, 15, 15, 10, 10, 10, 10, 10, 10, 10 });
                table.AddCell(PDFManager.GetTableHeaderCell("PIN", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Trainee Name", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Joining Date", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Department", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Division", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Course Name", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Enrollment Date", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("First Study Date", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Last Study Date", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Pass Date", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Gained Marks", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Total Marks", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Score %", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Grade", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Result", 8f, true, false, PDFAlignment.Center));
                if (courseEnrollments.Count > 0)
                {
                    foreach (var item in courseEnrollments)
                    {
                        var trainee = trainees.FirstOrDefault(x => x.Id == item.TraineeId);
                        var course = courses.FirstOrDefault(x => x.Id == item.CourseId);
                        var traineeExam = traineeExams.FirstOrDefault(x => x.TraineeId == trainee.Id && x.CourseId == course.Id);
                        //var traineeExamByTraineeIdList = traineeExams.Where(x => x.TraineeId == trainee.Id && x.CourseId == course.Id).Select(x => x).ToList();
                        //var traineeExam = traineeExamByTraineeIdList.Where(x => x.GainedMarks == traineeExamByTraineeIdList.Max(i => i.GainedMarks)).FirstOrDefault();
                        var TraineeCourseActivity = TraineeCourseActivities.FirstOrDefault(x => x.TraineeId == trainee.Id && x.CourseId == course.Id);
                        table.AddCell(PDFManager.GetTableDataCell(trainee?.PIN, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(trainee?.Name, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(trainee?.DateOfJoining?.ToLocalTime().ToString("dd-MMM-yyyy") ?? "N/A", PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(trainee?.Department?.Name, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(trainee?.Division?.Name, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(course?.Title, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(item?.EnrollmentDate.ToLocalTime().ToString("dd-MMM-yyyy"), PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell((bool)TraineeCourseActivity?.FirstStudyDate.HasValue ? TraineeCourseActivity?.FirstStudyDate.Value.ToLocalTime() : null, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell((bool)TraineeCourseActivity?.LastStudyDate.HasValue ? TraineeCourseActivity?.LastStudyDate.Value.ToLocalTime() : null, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(traineeExam?.MarkedOn?.ToLocalTime().ToString("dd-MMM-yyyy") ?? "", PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(traineeExam?.GainedMarks ?? 0, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(traineeExam?.TotalMarks ?? 0, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(traineeExam?.GainedPercentage ?? 0, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(traineeExam?.Grade, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(traineeExam?.Result != null && Enum.IsDefined(typeof(GradeResult), traineeExam.Result) ? Enum.Parse(typeof(GradeResult), traineeExam.Result.ToString()).ToString() : "N/A", PDFAlignment.Center, false, true, 8));
                    }
                }
                document.Add(table);
                #endregion

                // Closing the Document
                document.Close();
                return reportStream.ToArray();

            }
            catch (DocumentException dex)
            {


                document.Add(PDFManager.GetTextLineElement(dex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
            // Catching System.IO.IOException if any
            catch (Exception ioex)
            {


                document.Add(PDFManager.GetTextLineElement(ioex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
        }
        public async Task<byte[]> GetExamWiseCorrectAnswerRateReportExcel(Guid courseId)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            try
            {

                var course = await _context.Courses.Where(x => x.Id == courseId).Select(x => x.Title).FirstOrDefaultAsync();
                var company = await _context.Configurations.FirstOrDefaultAsync();

                var examIds = await _context.CourseExams.Where(x => x.CourseId == courseId)
                    .Select(x => x.Id).ToListAsync();

                var query = _context.MCQAnswers.Where(x => examIds.Contains(x.Question.ExamId))
                    .GroupBy(x => new { x.Question.Question, x.QuestionId, x.Question.ExamId })
                    .Select(x => new { x.Key.Question, x.Key.QuestionId, x.Key.ExamId, Total = x.Count(), CorrectAnswered = x.Count(y => y.Mark > 0), Type = QuesType.MCQ, TypeNormalize = "MCQ" }).AsQueryable();

                query = query.Union(_context.TrueFalseAnswers.Where(x => examIds.Contains(x.Question.ExamId))
                     .GroupBy(x => new { x.Question.Question, x.QuestionId, x.Question.ExamId })
                     .Select(x => new { x.Key.Question, x.Key.QuestionId, x.Key.ExamId, Total = x.Count(), CorrectAnswered = x.Count(y => y.Mark > 0), Type = QuesType.TrueFalse, TypeNormalize = "True/False" }).AsQueryable());

                query = query.Union(_context.FIGAnswers.Where(x => examIds.Contains(x.Question.ExamId))
                     .GroupBy(x => new { x.Question.Question, x.QuestionId, x.Question.ExamId })
                     .Select(x => new { x.Key.Question, x.Key.QuestionId, x.Key.ExamId, Total = x.Count(), CorrectAnswered = x.Count(y => y.Mark > 0), Type = QuesType.FIG, TypeNormalize = "Fill in the gap" }).AsQueryable());

                query = query.Union(_context.MatchingAnswers.Where(x => examIds.Contains(x.Question.ExamId))
                     .GroupBy(x => new { Question = x.Question.LeftSide, x.QuestionId, x.Question.ExamId })
                     .Select(x => new { x.Key.Question, x.Key.QuestionId, x.Key.ExamId, Total = x.Count(), CorrectAnswered = x.Count(y => y.Mark > 0), Type = QuesType.Matching, TypeNormalize = "Matching" }).AsQueryable());

                query = query.Union(_context.WrittenAnswers.Where(x => examIds.Contains(x.Question.ExamId))
                     .GroupBy(x => new { Question = x.Question.Question, x.QuestionId, x.Question.ExamId })
                     .Select(x => new { x.Key.Question, x.Key.QuestionId, x.Key.ExamId, Total = x.Count(), CorrectAnswered = x.Count(y => y.Mark > 0), Type = QuesType.Written, TypeNormalize = "Written" }).AsQueryable());

                var data = await query.OrderBy(x => x.Type).ThenBy(x => x.Question).ToListAsync();

                var headerColumns = new List<string> { "Question", "Type", "Total Q.", "C. Answered", "C. Ans. Rate" };

                ExcelManager.GetTextLineElement(company.Name, ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                ExcelManager.GetTextLineElement(company.Address + " , " + company.ContactNo, ++rowNo, ws, fontSize: 8, isBold: false, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                rowNo++;

                ExcelManager.GetTextLineElement("Correct Answer Rate of Certificate Test", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                rowNo++;
                ExcelManager.GetTextLineElement("Course: " + course, ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                rowNo++;
                ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);
                int qNo = 0;
                foreach (var item in data)
                {
                    qNo++;
                    rowNo++;
                    colNo = 1;
                    ExcelManager.GetTableDataCell(qNo + ". " + item.Question, 10, rowNo, colNo++, ws);
                    ExcelManager.GetTableDataCell(item.TypeNormalize, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                    ExcelManager.GetTableDataCell(item.Total, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                    ExcelManager.GetTableDataCell(item.CorrectAnswered, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                    ExcelManager.GetTableDataCell(((item.CorrectAnswered * 100) / item.Total).ToString("N2"), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                }

                for (int i = 0; i < headerColumns.Count; i++)
                {
                    ws.Column(i + 1).AdjustToContents();
                }

                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);

                    return ms.ToArray();
                }
            }
            catch (Exception ibex)
            {
                LogControl.LogException(ibex);

                ExcelManager.GetTextLineElement("Excel File Generation Error: " + ibex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
        }

        public async Task<byte[]> GetExamWiseCorrectAnswerRateReportPdf(Guid courseId)
        {
            var document = new iTextSharp.text.Document(iTextSharp.text.PageSize.A4, 36, 36, 100, 36);
            document.SetPageSize(iTextSharp.text.PageSize.A4.Rotate());
            var reportStream = new MemoryStream();

            try
            {
                PdfWriter writer = PdfWriter.GetInstance(document, reportStream);
                writer.PageEvent = new PDFWriterEvents(true, true);
                writer.CloseStream = false;

                // Openning the Document
                document.Open();

                var course = await _context.Courses.Where(x => x.Id == courseId).Select(x => x.Title).FirstOrDefaultAsync();
                var company = await _context.Configurations.FirstOrDefaultAsync();

                var examIds = await _context.CourseExams.Where(x => x.CourseId == courseId)
                    .Select(x => x.Id).ToListAsync();

                var query = _context.MCQAnswers.Where(x => examIds.Contains(x.Question.ExamId))
                    .GroupBy(x => new { x.Question.Question, x.QuestionId, x.Question.ExamId })
                    .Select(x => new { x.Key.Question, x.Key.QuestionId, x.Key.ExamId, Total = x.Count(), CorrectAnswered = x.Count(y => y.Mark > 0), Type = QuesType.MCQ, TypeNormalize = "MCQ" }).AsQueryable();

                query = query.Union(_context.TrueFalseAnswers.Where(x => examIds.Contains(x.Question.ExamId))
                     .GroupBy(x => new { x.Question.Question, x.QuestionId, x.Question.ExamId })
                     .Select(x => new { x.Key.Question, x.Key.QuestionId, x.Key.ExamId, Total = x.Count(), CorrectAnswered = x.Count(y => y.Mark > 0), Type = QuesType.TrueFalse, TypeNormalize = "True/False" }).AsQueryable());

                query = query.Union(_context.FIGAnswers.Where(x => examIds.Contains(x.Question.ExamId))
                     .GroupBy(x => new { x.Question.Question, x.QuestionId, x.Question.ExamId })
                     .Select(x => new { x.Key.Question, x.Key.QuestionId, x.Key.ExamId, Total = x.Count(), CorrectAnswered = x.Count(y => y.Mark > 0), Type = QuesType.FIG, TypeNormalize = "Fill in the gap" }).AsQueryable());

                query = query.Union(_context.MatchingAnswers.Where(x => examIds.Contains(x.Question.ExamId))
                     .GroupBy(x => new { Question = x.Question.LeftSide, x.QuestionId, x.Question.ExamId })
                     .Select(x => new { x.Key.Question, x.Key.QuestionId, x.Key.ExamId, Total = x.Count(), CorrectAnswered = x.Count(y => y.Mark > 0), Type = QuesType.Matching, TypeNormalize = "Matching" }).AsQueryable());

                query = query.Union(_context.WrittenAnswers.Where(x => examIds.Contains(x.Question.ExamId))
                     .GroupBy(x => new { Question = x.Question.Question, x.QuestionId, x.Question.ExamId })
                     .Select(x => new { x.Key.Question, x.Key.QuestionId, x.Key.ExamId, Total = x.Count(), CorrectAnswered = x.Count(y => y.Mark > 0), Type = QuesType.Written, TypeNormalize = "Written" }).AsQueryable());

                var data = await query.OrderBy(x => x.Type).ThenBy(x => x.Question).ToListAsync();
                if (!data.Any()) throw new Exception("No data found");

                #region Page Header
                PdfPTable header = new PdfPTable(1);
                header.TotalWidth = 180;
                header.DefaultCell.Border = 0;
                header.AddCell(PDFManager.GetTextLineElement(company.Name, 12f, isBold: true));
                header.AddCell(PDFManager.GetTextLineElement(company.Address + " , " + company.ContactNo, isBold: false, fontSize: 8, leftToRightPadding: new float[] { 4, 2, 4, 2 }));
                header.WriteSelectedRows(0, 3, writer.PageSize.Width - document.LeftMargin - document.RightMargin - 180, writer.PageSize.GetTop(10), writer.DirectContent);
                if (!string.IsNullOrEmpty(company.LogoPath))
                {
                    Image img = Image.GetInstance(HostingEnvironment.MapPath("~") + company.LogoPath);
                    img.SetAbsolutePosition(document.LeftMargin, writer.PageSize.GetTop(70));
                    img.ScaleAbsolute(200f, 60f);
                    document.Add(img);
                }
                PdfPTable line = new PdfPTable(1);
                line.TotalWidth = writer.PageSize.Width;
                line.DefaultCell.Border = 0;
                line.AddCell(new Paragraph(new Chunk(new iTextSharp.text.pdf.draw.LineSeparator(0.0F, 100.0F, BaseColor.BLACK, Element.ALIGN_CENTER, 3))));
                line.WriteSelectedRows(0, 1, 0, writer.PageSize.GetTop(header.TotalHeight + 10), writer.DirectContent);
                #endregion

                document.Add(PDFManager.GetTextLineElement("Correct Answer Rate of Certificate Test", fontSize: 16, isBold: true, isUnderlined: false, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));

                document.Add(PDFManager.GetTextLineElement("Course: " + course, fontSize: 14, isBold: true, isUnderlined: false, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                #region Table
                var table = new PdfPTable(5) { WidthPercentage = 100 };
                table.SetWidths(new float[] { 20, 20, 20, 20, 20 });
                table.AddCell(PDFManager.GetTableHeaderCell("Question", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Type", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Total Q.", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("C. Answered", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("C. Ans. Rate", 8f, true, false, PDFAlignment.Center));
                int qNo = 0;
                foreach (var item in data)
                {
                    qNo++;
                    table.AddCell(PDFManager.GetTableDataCell(qNo + ". " + item.Question, PDFAlignment.Center, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(item.TypeNormalize, PDFAlignment.Center, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(item.Total, PDFAlignment.Center, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(item.CorrectAnswered, PDFAlignment.Center, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(((item.CorrectAnswered * 100) / item.Total).ToString("N2"), PDFAlignment.Center, false, true, 8));
                }
                document.Add(table);
                #endregion

                // Closing the Document
                document.Close();
                return reportStream.ToArray();
            }
            catch (DocumentException dex)
            {
                document.Add(PDFManager.GetTextLineElement(dex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
            // Catching System.IO.IOException if any
            catch (Exception ioex)
            {
                document.Add(PDFManager.GetTextLineElement(ioex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }

        }

        #region Trainee Panel APIs

        public async Task<APIResponse> GetMyCourseList(int limit, int? courseStatus, ApplicationUser user, DateTime? keyDate = null)
        {
            try
            {
                //var query = _context.CourseEnrollments.Where(x => x.TraineeId == user.Trainee.Id && x.Course.Published && x.Course.Active);

                var query = from c in _context.Courses
                            join tca in _context.TraineeCourseActivities on c.Id equals tca.CourseId
                            join ce in _context.CourseEnrollments on c.Id equals ce.CourseId
                            where tca.TraineeId == user.Trainee.Id && ce.TraineeId == user.Trainee.Id && c.Published && c.Active
                            select new
                            {
                                c.Id,
                                c.Rating,
                                c.Title,
                                c.CategoryId,
                                c.Description,
                                c.ImagePath,
                                tca.NoOfContents,
                                tca.NoOfContentsStudied,
                                ce.EnrollmentDate,
                                IsPublished = c.Published,
                                IsActive = c.Active,
                                TcaTraineeId = tca.TraineeId,
                                CeTraineeId = ce.TraineeId,
                                c.NoOfRating,
                                tca.Progress
                            };

                if (keyDate.HasValue) query = query.Where(x => x.EnrollmentDate < keyDate);

                var final = query.AsEnumerable().Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.Description,
                    x.ImagePath,
                    x.EnrollmentDate,
                    TraineeCourseModel = this._traineeCourseModel = TraineeCourseDetails(x.Id, user.Trainee.Id),
                    //x.NoOfContents,
                    //x.NoOfContentsStudied,

                    NoOfContents = this._traineeCourseModel.TotalLectures,
                    NoOfContentsStudied = this._traineeCourseModel.TotalCompletedLectures,
                    Status = this._traineeCourseModel.TotalLectures==this._traineeCourseModel.TotalCompletedLectures?1:0,
                    x.Rating,
                    x.NoOfRating,
                    x.Progress
                    //x.Course.Id,
                    //x.Course.Title,
                    //x.Course.Description,
                    //x.Course.ImagePath,
                    //x.EnrollmentDate,
                    //x.Course.NoOfContents,
                    //x.Course.Rating,
                    //x.Course.NoOfRating
                });
                //.Take(limit)
                if (courseStatus.HasValue)
                {
                    if (courseStatus.Value==0)
                        final = final.Where(x => x.Status==0);
                    else
                        final = final.Where(x => x.Status==1);
                }
                var data =final.OrderByDescending(x => x.EnrollmentDate).ToList();

                //foreach (var item in data)
                //{
                //    item.EnrollmentDate = ((DateTime)item.EnrollmentDate).ToKindUtc();
                //}


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Data = data, Key = data.Any() ? data.Min(x => x.EnrollmentDate) : default(DateTime?) }
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException != null ? ex.InnerException.InnerException?.Message ?? ex.InnerException.Message : ex.Message
                };
            }
        }

        public async Task<APIResponse> GetMyCourseList(string name, long? categoryId, int size, int pageNumber, int? courseStatus, ApplicationUser user)
        {
            try
            {
                //var query = _context.CourseEnrollments.Where(x => x.TraineeId == user.Trainee.Id && x.Course.Published && x.Course.Active);

                var query = from c in _context.Courses
                            join tca in _context.TraineeCourseActivities on c.Id equals tca.CourseId
                            join ce in _context.CourseEnrollments on c.Id equals ce.CourseId
                            where tca.TraineeId == user.Trainee.Id && ce.TraineeId == user.Trainee.Id && c.Published && c.Active
                            select new
                            {
                                c.Id,
                                c.Rating,
                                c.Title,
                                c.CategoryId,
                                c.Description,
                                c.ImagePath,
                                tca.NoOfContents,
                                tca.NoOfContentsStudied,
                                ce.EnrollmentDate,
                                c.Published,
                                c.Active,
                                tca.TraineeId,
                                CeTraineeId = ce.TraineeId,
                                tca.Progress
                            };


                //if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Course.Title.Contains(name));
                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Title.Contains(name));
                //if (categoryId.HasValue) query = query.Where(x => x.Course.CategoryId == categoryId);
                if (categoryId.HasValue) query = query.Where(x => x.CategoryId == categoryId);
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderByDescending(x => x.EnrollmentDate)
                .Skip(pageNumber * size).Take(size);
                var CourseContents = new TraineeCourseModel();
                var final = filteredQuery.AsEnumerable().Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.Description,
                    x.ImagePath,
                    x.EnrollmentDate,
                    TraineeCourseModel = this._traineeCourseModel = TraineeCourseDetails(x.Id, x.TraineeId),
                    //x.NoOfContents,
                    //x.NoOfContentsStudied,

                    NoOfContents = this._traineeCourseModel.TotalLectures,
                    NoOfContentsStudied = this._traineeCourseModel.TotalCompletedLectures,
                    Status = this._traineeCourseModel.TotalLectures==this._traineeCourseModel.TotalCompletedLectures?1:0,
                    x.Rating,
                    x.Published,
                    x.Active,
                    x.TraineeId,
                    x.CeTraineeId,
                    x.Progress,
                    //x.Course.Id,
                    //x.Course.Title,
                    //x.Course.Description,
                    //x.Course.ImagePath,
                    //x.EnrollmentDate,
                    //x.Course.NoOfContents,
                    //x.Course.Rating,
                    //x.Course.NoOfRating
                });
                if (courseStatus.HasValue)
                {
                    if(courseStatus.Value==0)
                        final =final.Where(x=>x.Status==0);
                    else
                        final =final.Where(x => x.Status==1);
                }
                var data= final.ToList();


                var count = ((!string.IsNullOrEmpty(name) || (categoryId.HasValue)|| (courseStatus.HasValue)) || (!string.IsNullOrEmpty(name) && (categoryId.HasValue) && (courseStatus.HasValue))) ? data.Count : await (query.CountAsync());

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Records = data,
                        //Records2=queryValue,
                        Total = count
                    }
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException != null ? ex.InnerException.InnerException?.Message ?? ex.InnerException.Message : ex.Message
                };
            }
        }

        public TraineeCourseModel TraineeCourseDetails(Guid courseId,Guid traineeId)
        {
            var mockTests = from cmt in _context.CourseMockTests
                                   join c in _context.Courses on cmt.CourseId equals c.Id
                                   join ccd in _context.CourseContentDependencies on cmt.Id equals ccd.ContentId
                                   where c.Id == courseId && ccd.Sequence > 0
                                   select
                                   new { CouseMockTestId = cmt.Id };

            var traineeMockTests = from tmt in _context.TraineeMockTests
                            join cmt in _context.CourseMockTests on tmt.ExamId equals cmt.Id
                            join c in _context.Courses on cmt.CourseId equals c.Id
                            where c.Id == courseId && tmt.TraineeId == traineeId
                            select
                            new { CouseMockTestId = cmt.Id };

            var courseMaterials = from cm in _context.CourseMaterials
                                  join c in _context.Courses on cm.CourseId equals c.Id
                                  join ccd in _context.CourseContentDependencies on cm.Id equals ccd.ContentId
                                  where c.Id == courseId && ccd.Sequence > 0
                                         select new
                                         {
                                             CoueseMaterial = cm.Id,
                                         };

            var traineeCourseMaterials = from tca in _context.TraineeCourseActivities
                            join c in _context.Courses on tca.CourseId equals c.Id
                            join cm in _context.CourseMaterials on c.Id equals cm.CourseId
                            join cms in _context.CourseMaterialStudies on new { tcaId = tca.Id, cmId = cm.Id } equals new { tcaId = cms.ActivityId, cmId = cms.MaterialId }
                            where c.Id == courseId && tca.TraineeId == traineeId
                            select new
                            {
                                CoueseMaterialStudy = cms.MaterialId,
                            };
            var traineeCourseDetails = new TraineeCourseModel()
            {
                TotalLectures = mockTests.Distinct().Count() + courseMaterials.Distinct().Count(),
                TotalCompltedMockTests = traineeMockTests.Distinct().Count(),
                TotalCompltedMaterials = traineeCourseMaterials.Distinct().Count(),
                TotalCompletedLectures = traineeMockTests.Distinct().Count() + traineeCourseMaterials.Distinct().Count()
            };

            return traineeCourseDetails;
        }
        public async Task<APIResponse> GetAvailableCourseList(int limit, ApplicationUser user, DateTime? keyDate = null)
        {
            try
            {
                var query = _context.Courses.Where(x => x.Published)
                    .GroupJoin(_context.CourseEnrollments, x => new { CourseId = x.Id, TraineeId = user.Trainee.Id }, y => new { y.CourseId, y.TraineeId }, (x, y) => new
                    {
                        Course = x,
                        Enrollments = y
                    }).SelectMany(x => x.Enrollments.DefaultIfEmpty(), (y, z) => new { y.Course, Enrollment = z })
                    .Where(x => x.Course.SelfEnrollment && x.Enrollment == null).AsQueryable();

                if (keyDate.HasValue) query = query.Where(x => x.Course.CreatedDate < keyDate);

                var data = await query.Select(x => new
                {
                    x.Course.Id,
                    x.Course.Title,
                    x.Course.Description,
                    x.Course.ImagePath,
                    x.Course.CreatedDate,
                    x.Course.NoOfContents,
                    x.Course.Rating,
                    x.Course.NoOfRating
                }).OrderByDescending(x => x.CreatedDate)
                .Take(limit).ToListAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Data = data, Key = data.Any() ? data.Min(x => x.CreatedDate) : default(DateTime?) }
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException != null ? ex.InnerException.InnerException?.Message ?? ex.InnerException.Message : ex.Message
                };
            }
        }

        public async Task<APIResponse> GetAvailableCourseList(string name, long? categoryId, int size, int pageNumber, ApplicationUser user)
        {
            try
            {
                var query = _context.Courses.Where(x => x.Published)
                    .GroupJoin(_context.CourseEnrollments, x => new { CourseId = x.Id, TraineeId = user.Trainee.Id }, y => new { y.CourseId, y.TraineeId }, (x, y) => new
                    {
                        Course = x,
                        Enrollments = y
                    }).SelectMany(x => x.Enrollments.DefaultIfEmpty(), (y, z) => new { y.Course, Enrollment = z })
                    .Where(x => x.Course.SelfEnrollment && x.Enrollment == null).AsQueryable();

                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Course.Title.Contains(name));
                if (categoryId.HasValue) query = query.Where(x => x.Course.CategoryId == categoryId);

                var data = await query.Select(x => new
                {
                    x.Course.Id,
                    x.Course.Title,
                    x.Course.Description,
                    x.Course.ImagePath,
                    x.Course.CreatedDate,
                    x.Course.NoOfContents,
                    x.Course.Rating,
                    x.Course.NoOfRating
                }).OrderByDescending(x => x.CreatedDate)
                .Skip(pageNumber * size).Take(size).ToListAsync();

                var count = await query.CountAsync();



                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Records = data,
                        Total = count
                    }
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException != null ? ex.InnerException.InnerException?.Message ?? ex.InnerException.Message : ex.Message
                };
            }
        }

        public async Task<APIResponse> GetLearningHourList(int limit, ApplicationUser user, DateTime? keyDate = null)
        {
            try
            {
                var query = _context.OpenMaterials.Where(x => x.AllowFor == OMAllowFor.All && x.Active == true)
                    .Select(x => new { x.Id, x.Title, x.FilePath, x.FileSizeKb, x.VideoDurationSecond, x.S3Path, x.ImagePath, Type = x.MaterialType.ToString(), StartDate = default(DateTime?), EndDate = default(DateTime?), Date = x.CreatedDate }).AsQueryable();

                query = query.Union(_context.OpenMaterials.Where(x => x.AllowFor != OMAllowFor.All && x.AllowFor != OMAllowFor.Trainee && (x.DivisionId == null || x.DivisionId == user.Trainee.DivisionId) && (x.DepartmentId == null || x.DepartmentId == user.Trainee.DepartmentId) && (x.UnitId == null || x.UnitId == user.Trainee.UnitId) && x.Active == true)
                    .Select(x => new { x.Id, x.Title, x.FilePath, x.FileSizeKb, x.VideoDurationSecond, x.S3Path, x.ImagePath, Type = x.MaterialType.ToString(), StartDate = default(DateTime?), EndDate = default(DateTime?), Date = x.CreatedDate }).AsQueryable());

                query = query.Union(_context.OpenMaterials.Where(x => x.AllowFor == OMAllowFor.Trainee && x.Trainees.Select(y => y.Id).Contains(user.Trainee.Id) && x.Active == true)
                    .Select(x => new { x.Id, x.Title, x.FilePath, x.FileSizeKb, x.VideoDurationSecond, x.S3Path, x.ImagePath, Type = x.MaterialType.ToString(), StartDate = default(DateTime?), EndDate = default(DateTime?), Date = x.CreatedDate }).AsQueryable());

                query = query.Union(_context.EvaluationExams.Where(x => x.AllowFor == OMAllowFor.All && x.Active == true)
                    .Select(x => new { x.Id, Title = x.ExamName, FilePath = "", FileSizeKb = default(long), VideoDurationSecond = 0, S3Path = "", x.ImagePath, Type = "Exam", x.StartDate, x.EndDate, Date = x.StartDate != null ? x.StartDate.Value : x.CreatedDate }).AsQueryable());

                query = query.Union(_context.EvaluationExams.Where(x => x.AllowFor != OMAllowFor.All && x.AllowFor != OMAllowFor.Trainee && (x.DivisionId == null || x.DivisionId == user.Trainee.DivisionId) && (x.DepartmentId == null || x.DepartmentId == user.Trainee.DepartmentId) && (x.UnitId == null || x.UnitId == user.Trainee.UnitId) && x.Active == true)
                    .Select(x => new { x.Id, Title = x.ExamName, FilePath = "", FileSizeKb = default(long), VideoDurationSecond = 0, S3Path = "", x.ImagePath, Type = "Exam", x.StartDate, x.EndDate, Date = x.StartDate != null ? x.StartDate.Value : x.CreatedDate }).AsQueryable());

                query = query.Union(_context.EvaluationExams.Where(x => x.AllowFor == OMAllowFor.Trainee && x.Trainees.Select(y => y.Id).Contains(user.Trainee.Id) && x.Active == true)
                    .Select(x => new { x.Id, Title = x.ExamName, FilePath = "", FileSizeKb = default(long), VideoDurationSecond = 0, S3Path = "", x.ImagePath, Type = "Exam", x.StartDate, x.EndDate, Date = x.StartDate != null ? x.StartDate.Value : x.CreatedDate }).AsQueryable());

                if (keyDate.HasValue) query = query.Where(x => x.Date < keyDate);

                var data = await query.OrderByDescending(x => x.Date)
                .Take(limit).ToListAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Data = data, Key = data.Any() ? data.Min(x => x.Date) : default(DateTime?) }
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException != null ? ex.InnerException.InnerException?.Message ?? ex.InnerException.Message : ex.Message
                };
            }
        }
        public async Task<APIResponse> GetLearningHourList(string name, long? categoryId, int size, int pageNumber, ApplicationUser user)
        {
            try
            {
                var query = _context.OpenMaterials.Where(x => x.AllowFor == OMAllowFor.All && x.Active == true).Select(x => new { x.Id, x.Title, x.FilePath, x.FileSizeKb, x.VideoDurationSecond, x.S3Path, x.ImagePath, Type = x.MaterialType.ToString(), StartDate = default(DateTime?), EndDate = default(DateTime?), Date = x.CreatedDate, x.CategoryId, Order = x.Order }).AsQueryable();

                query = query.Union(_context.OpenMaterials.Where(x => x.AllowFor != OMAllowFor.All && x.AllowFor != OMAllowFor.Trainee && (x.DivisionId == null || x.DivisionId == user.Trainee.DivisionId) && (x.DepartmentId == null || x.DepartmentId == user.Trainee.DepartmentId) && x.Active == true && (x.UnitId == null || x.UnitId == user.Trainee.UnitId)).Select(x => new { x.Id, x.Title, x.FilePath, x.FileSizeKb, x.VideoDurationSecond, x.S3Path, x.ImagePath, Type = x.MaterialType.ToString(), StartDate = default(DateTime?), EndDate = default(DateTime?), Date = x.CreatedDate, x.CategoryId, Order = x.Order }).AsQueryable());

                //query = query.Union(_context.OpenMaterials.Where(x => x.AllowFor == OMAllowFor.Division && x.DivisionId == user.Trainee.DivisionId).Select(x => new { x.Title, x.FilePath, x.FileSizeKb, x.VideoDurationSecond, x.S3Path }).AsQueryable());

                //query = query.Union(_context.OpenMaterials.Where(x => x.AllowFor == OMAllowFor.Department && x.DepartmentId == user.Trainee.DepartmentId).Select(x => new { x.Title, x.FilePath, x.FileSizeKb, x.VideoDurationSecond, x.S3Path }).AsQueryable());

                //query = query.Union(_context.OpenMaterials.Where(x => x.AllowFor == OMAllowFor.Unit && x.UnitId == user.Trainee.UnitId).Select(x => new { x.Title, x.FilePath, x.FileSizeKb, x.VideoDurationSecond, x.S3Path }).AsQueryable());

                query = query.Union(_context.OpenMaterials.Where(x => x.AllowFor == OMAllowFor.Trainee && x.Active == true && x.Trainees.Select(y => y.Id).Contains(user.Trainee.Id))
                    .Select(x => new { x.Id, x.Title, x.FilePath, x.FileSizeKb, x.VideoDurationSecond, x.S3Path, x.ImagePath, Type = x.MaterialType.ToString(), StartDate = default(DateTime?), EndDate = default(DateTime?), Date = x.CreatedDate, x.CategoryId, Order = x.Order }).AsQueryable());

                query = query.Union(_context.EvaluationExams.Where(x => x.AllowFor == OMAllowFor.All && x.Active == true)
                    .Select(x => new { x.Id, Title = x.ExamName, FilePath = "", FileSizeKb = default(long), VideoDurationSecond = 0, S3Path = "", x.ImagePath, Type = "Exam", x.StartDate, x.EndDate, Date = x.StartDate != null ? x.StartDate.Value : x.CreatedDate, x.CategoryId, Order = x.Order }).AsQueryable());

                query = query.Union(_context.EvaluationExams.Where(x => x.AllowFor != OMAllowFor.All && x.AllowFor != OMAllowFor.Trainee && (x.DivisionId == null || x.DivisionId == user.Trainee.DivisionId) && (x.DepartmentId == null || x.DepartmentId == user.Trainee.DepartmentId) && (x.UnitId == null || x.UnitId == user.Trainee.UnitId) && x.Active == true)
                    .Select(x => new { x.Id, Title = x.ExamName, FilePath = "", FileSizeKb = default(long), VideoDurationSecond = 0, S3Path = "", x.ImagePath, Type = "Exam", x.StartDate, x.EndDate, Date = x.StartDate != null ? x.StartDate.Value : x.CreatedDate, x.CategoryId, Order = x.Order }).AsQueryable());

                query = query.Union(_context.EvaluationExams.Where(x => x.AllowFor == OMAllowFor.Trainee && x.Trainees.Select(y => y.Id).Contains(user.Trainee.Id) && x.Active == true)
                    .Select(x => new { x.Id, Title = x.ExamName, FilePath = "", FileSizeKb = default(long), VideoDurationSecond = 0, S3Path = "", x.ImagePath, Type = "Exam", x.StartDate, x.EndDate, Date = x.StartDate != null ? x.StartDate.Value : x.CreatedDate, x.CategoryId, Order = x.Order }).AsQueryable());

                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Title.Contains(name));
                if (categoryId.HasValue) query = query.Where(x => x.CategoryId == categoryId);
                //var queries = query.GroupBy(d => new { d.Title, d.Order }).OrderBy(d => d.Key.Order)
                //.Select(g => new
                //{
                //    Title = g.Key.Title,
                //    Order = g.Key.Order,
                //    List = g.ToList()
                //}).AsQueryable();
                //var filteredQuery = queries.Count() <= pageNumber * size ? queries : queries
                //    .Skip(pageNumber * size).Take(size);
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderBy(x=>x.Order)
                    .Skip(pageNumber * size).Take(size);
                var data = filteredQuery.ToList();
                var count = await (((!string.IsNullOrEmpty(name) || categoryId.HasValue) || (!string.IsNullOrEmpty(name) && categoryId.HasValue)) ? filteredQuery.CountAsync() : query.CountAsync());
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Total = count,
                        Records = data
                    }
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);


                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException != null ? ex.InnerException.InnerException?.Message ?? ex.InnerException.Message : ex.Message
                };
            }
        }
        public async Task<APIResponse> GetLearningHourGroupByList(string name, long? categoryId, int size, int pageNumber, ApplicationUser user)
        {
            try
            {
                var query = _context.OpenMaterials.Where(x => x.AllowFor == OMAllowFor.All && x.Active == true).Select(x => new { x.Id, x.Title, x.FilePath, x.FileSizeKb, x.VideoDurationSecond, x.S3Path, x.ImagePath, Type = x.MaterialType.ToString(), StartDate = default(DateTime?), EndDate = default(DateTime?), Date = x.CreatedDate, x.CategoryId, Order = x.Order }).AsQueryable();

                query = query.Union(_context.OpenMaterials.Where(x => x.AllowFor != OMAllowFor.All && x.AllowFor != OMAllowFor.Trainee && (x.DivisionId == null || x.DivisionId == user.Trainee.DivisionId) && (x.DepartmentId == null || x.DepartmentId == user.Trainee.DepartmentId) && x.Active == true && (x.UnitId == null || x.UnitId == user.Trainee.UnitId)).Select(x => new { x.Id, x.Title, x.FilePath, x.FileSizeKb, x.VideoDurationSecond, x.S3Path, x.ImagePath, Type = x.MaterialType.ToString(), StartDate = default(DateTime?), EndDate = default(DateTime?), Date = x.CreatedDate, x.CategoryId, Order = x.Order }).AsQueryable());
                query = query.Union(_context.OpenMaterials.Where(x => x.AllowFor == OMAllowFor.Trainee && x.Active == true && x.Trainees.Select(y => y.Id).Contains(user.Trainee.Id))
                    .Select(x => new { x.Id, x.Title, x.FilePath, x.FileSizeKb, x.VideoDurationSecond, x.S3Path, x.ImagePath, Type = x.MaterialType.ToString(), StartDate = default(DateTime?), EndDate = default(DateTime?), Date = x.CreatedDate, x.CategoryId, Order = x.Order }).AsQueryable());

                query = query.Union(_context.EvaluationExams.Where(x => x.AllowFor == OMAllowFor.All && x.Active == true)
                    .Select(x => new { x.Id, Title = x.ExamName, FilePath = "", FileSizeKb = default(long), VideoDurationSecond = 0, S3Path = "", x.ImagePath, Type = "Exam", x.StartDate, x.EndDate, Date = x.StartDate != null ? x.StartDate.Value : x.CreatedDate, x.CategoryId, Order = x.Order }).AsQueryable());

                query = query.Union(_context.EvaluationExams.Where(x => x.AllowFor != OMAllowFor.All && x.AllowFor != OMAllowFor.Trainee && (x.DivisionId == null || x.DivisionId == user.Trainee.DivisionId) && (x.DepartmentId == null || x.DepartmentId == user.Trainee.DepartmentId) && (x.UnitId == null || x.UnitId == user.Trainee.UnitId) && x.Active == true)
                    .Select(x => new { x.Id, Title = x.ExamName, FilePath = "", FileSizeKb = default(long), VideoDurationSecond = 0, S3Path = "", x.ImagePath, Type = "Exam", x.StartDate, x.EndDate, Date = x.StartDate != null ? x.StartDate.Value : x.CreatedDate, x.CategoryId, Order = x.Order }).AsQueryable());

                query = query.Union(_context.EvaluationExams.Where(x => x.AllowFor == OMAllowFor.Trainee && x.Trainees.Select(y => y.Id).Contains(user.Trainee.Id) && x.Active == true)
                    .Select(x => new { x.Id, Title = x.ExamName, FilePath = "", FileSizeKb = default(long), VideoDurationSecond = 0, S3Path = "", x.ImagePath, Type = "Exam", x.StartDate, x.EndDate, Date = x.StartDate != null ? x.StartDate.Value : x.CreatedDate, x.CategoryId, Order = x.Order }).AsQueryable());


                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Title.Contains(name));
                if (categoryId.HasValue) query = query.Where(x => x.CategoryId == categoryId);
                var queries = query.GroupBy(d => new { d.Title, d.Order }).OrderBy(d => d.Key.Order)
                .Select(g => new
                {
                    Title = g.Key.Title,
                    Order = g.Key.Order,
                    List = g.ToList()
                }).AsQueryable();
                var filteredQuery = queries.Count() <= pageNumber * size ? queries : queries
                    .Skip(pageNumber * size).Take(size);
                var data= filteredQuery.AsQueryable();
                var count = await (((!string.IsNullOrEmpty(name) || categoryId.HasValue) || (!string.IsNullOrEmpty(name) && categoryId.HasValue)) ? filteredQuery.CountAsync() : queries.CountAsync());
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Total = count,
                        GroupByTitleRecords = data
                    }
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);


                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException != null ? ex.InnerException.InnerException?.Message ?? ex.InnerException.Message : ex.Message
                };
            }
        }

        public async Task<APIResponse> GetMyBookmarkedCourseList(string name, long? categoryId, int size, int pageNumber, ApplicationUser user)
        {
            try
            {
                var query = _context.CourseBookmarks.Where(x => x.TraineeId == user.Trainee.Id && x.Course.Published).AsQueryable();

                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Course.Title.Contains(name));
                if (categoryId.HasValue) query = query.Where(x => x.Course.CategoryId == categoryId);
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderByDescending(x => x.EnrollmentDate)
                   .Skip(pageNumber * size).Take(size);
                var data =  query.AsEnumerable()
                    .GroupJoin(_context.CourseEnrollments, x => new { x.CourseId, TraineeId = user.Trainee.Id }, y => new { y.CourseId, y.TraineeId }, (x, y) => new
                    {
                        x.Course.Id,
                        x.Course.Title,
                        x.Course.Description,
                        x.Course.ImagePath,
                        x.EnrollmentDate,
                        //x.Course.NoOfContents,
                        TraineeCourseModel = this._traineeCourseModel = TraineeCourseDetails(x.Course.Id, user.Trainee.Id),
                        //x.NoOfContents,
                        //x.NoOfContentsStudied,

                        NoOfContents = this._traineeCourseModel.TotalLectures,
                        NoOfContentsStudied = this._traineeCourseModel.TotalCompletedLectures,
                        x.Course.Rating,
                        x.Course.NoOfRating,
                        Enrollment = y.Any()
                    }).ToList();

                var count = await (((!string.IsNullOrEmpty(name) || (categoryId.HasValue)) || ((!string.IsNullOrEmpty(name) && (categoryId.HasValue))) ? filteredQuery.CountAsync() : _context.CourseBookmarks.Where(x => x.TraineeId == user.Trainee.Id && x.Course.Published).CountAsync()));



                return new APIResponse
                {

                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Records = data,
                        Total = count
                    }
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);


                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException != null ? ex.InnerException.InnerException?.Message ?? ex.InnerException.Message : ex.Message
                };
            }
        }

        public async Task<APIResponse> BookmarkOrUnbookmarkCourse(Guid id, ApplicationUser user)
        {
            bool bookmark = true;
            try
            {
                var item = await _context.CourseBookmarks.FirstOrDefaultAsync(x => x.TraineeId == user.Trainee.Id && x.CourseId == id);
                if (item == null)
                {
                    item = new CourseBookmark
                    {
                        TraineeId = user.Trainee.Id,
                        CourseId = id,
                        BookmarkDate = DateTime.UtcNow
                    };
                    _context.CourseBookmarks.Add(item);
                }
                else
                {
                    bookmark = false;
                    _context.Entry(item).State = EntityState.Deleted;
                }

                await _context.SaveChangesAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (bookmark ? "Bookmarked" : "Bookmark Removed"),
                    Data = bookmark
                };
            }
            catch (DbEntityValidationException ex)
            {
                List<string> errorList = new List<string>();
                foreach (var errors in ex.EntityValidationErrors)
                {
                    foreach (var validationError in errors.ValidationErrors)
                    {
                        // get the error message
                        errorList.Add(validationError.ErrorMessage);
                    }
                }

                return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = string.Join(" ", errorList)
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);


                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }


        #region Course Details API
        public async Task<APIResponse> GetCoursePreview(Guid id, ApplicationUser user)
        {
            try
            {
                var data = await _context.Courses.Where(x => x.Id == id && x.Published)
                    .GroupJoin(_context.CourseEnrollments, x => new { CourseId = x.Id, TraineeId = user.Trainee.Id }, y => new { y.CourseId, y.TraineeId }, (x, y) => new
                    {
                        Course = x,
                        Enrollments = y
                    }).SelectMany(x => x.Enrollments.DefaultIfEmpty(), (y, z) => new { y.Course, Enrollment = z })
                    .GroupJoin(_context.CourseBookmarks, x => new { CourseId = x.Course.Id, TraineeId = user.Trainee.Id }, y => new { y.CourseId, y.TraineeId }, (x, y) => new
                    {
                        x.Course,
                        x.Enrollment,
                        Bookmarked = y.Any()
                    })
                    .Select(x => new
                    {
                        x.Course.Id,
                        x.Course.Title,
                        x.Course.Description,
                        x.Course.ImagePath,
                        x.Course.Rating,
                        x.Course.NoOfRating,
                        Bookmarked = x.Bookmarked,
                        Enrollment = x.Enrollment != null
                    }).FirstOrDefaultAsync();

                var isManager = await _context.Trainees.AnyAsync(x => x.LineManagerPIN == user.Trainee.PIN);

                var contentQuery = _context.CourseMaterials.Where(x => x.CourseId == id)
                   .GroupJoin(_context.CourseContentDependencies, x => new { x.CourseId, ContentId = x.Id }, y => new { y.CourseId, y.ContentId }, (x, y) => new
                   {
                       Material = x,
                       Dependencies = y
                   }).SelectMany(x => x.Dependencies.DefaultIfEmpty(), (y, z) => new { y.Material, Sequence = z != null ? z.Sequence : 100 })
                   .Select(x => new
                   {
                       x.Material.Id,
                       x.Material.Title,
                       Type = x.Material.MaterialType.ToString(),
                       x.Material.FileSizeKb,
                       x.Material.VideoDurationSecond,
                       x.Sequence,
                       HasResources = x.Material.Resources.Any()
                   }).AsQueryable();

                contentQuery = contentQuery.Union(_context.CourseMockTests.Where(x => x.CourseId == id)
                     .GroupJoin(_context.CourseContentDependencies, x => new { x.CourseId, ContentId = x.Id }, y => new { y.CourseId, y.ContentId }, (x, y) => new
                     {
                         Exam = x,
                         Dependencies = y
                     }).SelectMany(x => x.Dependencies.DefaultIfEmpty(), (y, z) => new { y.Exam, Sequence = z != null ? z.Sequence : 100 })
                    .Select(x => new { x.Exam.Id, Title = x.Exam.ExamName, Type = "MockTest", FileSizeKb = default(long), VideoDurationSecond = 0, x.Sequence, HasResources = false }).AsQueryable());

                var contents = await contentQuery.OrderBy(x => x.Sequence).ToListAsync();

                var certificateTest = await _context.CourseExams.Where(x => x.CourseId == id)
                    .Select(x => new { x.Id, Title = x.Course.Title, Type = "CertificateTest", FileSizeKb = default(long), VideoDurationSecond = 0, Sequence = 0, HasResources = false }).FirstOrDefaultAsync();

                if (certificateTest != null) contents.Add(certificateTest);


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        data.Id,
                        data.Title,
                        data.Description,
                        data.ImagePath,
                        data.Rating,
                        data.NoOfRating,
                        data.Bookmarked,
                        data.Enrollment,
                        IsManager = isManager,
                        Contents = contents
                    }
                };

            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);


                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }

        }

        public async Task<APIResponse> GetMyTraineesToEnrol(Guid id, ApplicationUser user)
        {
            try
            {
                var data = await _context.Trainees.Where(x => x.LineManagerPIN == user.Trainee.PIN)
                    .GroupJoin(_context.CourseEnrollments.Where(x => x.CourseId == id), x => x.Id, y => y.TraineeId, (x, y) => new
                    {
                        x.Id,
                        x.PIN,
                        x.Name,
                        x.Position,
                        x.User.ImagePath,
                        Enrolled = y.Any()
                    }).ToListAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);


                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }

        }

        public async Task<APIResponse> EnrollTeammatesToCourse(Guid id, List<Guid> traineeIds, IIdentity identity)
        {
            try
            {
                var traineeEnrollments = await _context.CourseEnrollments.Where(x => x.CourseId == id && traineeIds.Contains(x.TraineeId)).ToListAsync();
                var traineeActivities = await _context.TraineeCourseActivities.Where(x => x.CourseId == id && traineeIds.Contains(x.TraineeId)).ToListAsync();

                var noOfContents = await _context.CourseMaterials.CountAsync(x => x.CourseId == id && x.RequiredStudyTimeSec > 0);
                var hasCertification = await _context.CourseExams.AnyAsync(x => x.CourseId == id);
                var course = await _context.Courses.Where(x => x.Id == id).Select(x => new { x.Title, x.ImagePath }).FirstOrDefaultAsync();
                var notiEvent = await _context.NotificationEvents.FirstOrDefaultAsync(x => x.Type == NotificationType.CourseEnrolmentByAdmin);

                CourseEnrollment enrollment;
                TraineeCourseActivity activity;
                Notification notification = null;
                SMSSender smsSender = notiEvent?.SMS ?? false ? new SMSSender() : null;
                string traineeMobile = null;
                List<string> tokens = new List<string>();

                foreach (var traineeId in traineeIds)
                {
                    enrollment = traineeEnrollments.FirstOrDefault(x => x.TraineeId == traineeId);


                    if (enrollment == null)
                    {
                        enrollment = new CourseEnrollment { CourseId = id, TraineeId = traineeId, EnrollmentDate = DateTime.UtcNow };

                        _context.CourseEnrollments.Add(enrollment);

                        if (notiEvent != null)
                        {
                            notification = enrollment.OnNewEnrolmentByManager(course.Title);

                            if (notiEvent.InApp)
                            {
                                _context.Notifications.Add(notification);
                                tokens.AddRange(await _context.TraineeDevices.Where(x => x.TraineeId == traineeId).Select(x => x.Token).ToArrayAsync());
                            }
                            if (notiEvent.Email) _context.NotificationEmails.Add(new NotificationEmail { Id = Guid.NewGuid(), NotificationId = notification.Id });
                            if (notiEvent.SMS)
                            {
                                traineeMobile = await _context.Trainees.Where(x => x.Id == traineeId).Select(x => x.PhoneNo).FirstOrDefaultAsync();
                                if (!string.IsNullOrEmpty(traineeMobile) && traineeMobile.Length >= 10) await smsSender.SendAsync(traineeMobile, notification.Details, Common.SMS.Enums.SMSEventType.EnrollTeammateToCourse);
                            }
                        }
                        activity = traineeActivities.FirstOrDefault(x => x.TraineeId == traineeId) ?? new TraineeCourseActivity
                        {
                            CourseId = id,
                            TraineeId = traineeId,
                            NoOfContents = noOfContents,
                            HasCertification = hasCertification
                        };
                        activity.SetAuditTrailEntity(identity);

                        if (activity.Id > 0) _context.Entry(activity).State = EntityState.Modified;
                        else _context.TraineeCourseActivities.Add(activity);
                    }
                }

                await _context.SaveChangesAsync();

                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);
                if (tokens.Any() && notification != null)
                {
                    try
                    {
                        do
                        {
                            await new FirebaseMessagingClient().SendNotifications(tokens.Take(Math.Min(tokens.Count, 20)).ToArray(), notification.Title, notification.Details, new
                            {
                                NavigateTo = notification.NavigateTo.ToString(),
                                notification.Payload,
                                notification.Id,
                                NotificationType = notification.NotificationType.ToString()
                            });
                            tokens.RemoveRange(0, Math.Min(tokens.Count, 20));
                        } while (tokens.Any());
                    }
                    catch (Exception exc)
                    {

                        LogControl.Write(exc.Message);
                        LogControl.Write(exc.StackTrace);
                    }
                }


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Enrolled"
                };
            }
            catch (DbEntityValidationException ex)
            {
                List<string> errorList = new List<string>();
                foreach (var errors in ex.EntityValidationErrors)
                {
                    foreach (var validationError in errors.ValidationErrors)
                    {
                        // get the error message
                        errorList.Add(validationError.ErrorMessage);
                    }
                }
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(errorList), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = string.Join(" ", errorList)
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }
        }

        public async Task<APIResponse> GetCourseDetails(Guid id)
        {
            //await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(contents), _context);
            //
            try
            {
                var enrollment = await _context.CourseEnrollments.Where(x => x.TraineeId == _appUser.Trainee.Id && x.CourseId == id)
                    .GroupJoin(_context.TraineeCourseActivities, x => new { x.CourseId, x.TraineeId }, y => new { y.CourseId, y.TraineeId }, (x, y) => new
                    {
                        x.ExpireDate,
                        CertificateAchieved = y.Select(z => z.CertificateAchieved).FirstOrDefault()
                    }).FirstOrDefaultAsync();
                if (enrollment == null)
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "You didn't enroll this course yet"
                    };

                var course = await _context.Courses.Where(x => x.Id == id)
                    .GroupJoin(_context.CourseBookmarks, x => new { CourseId = x.Id, TraineeId = _appUser.Trainee.Id }, y => new { y.CourseId, y.TraineeId }, (x, y) => new
                    {
                        x.Id,
                        x.Title,
                        x.Description,
                        x.ImagePath,
                        x.Rating,
                        x.NoOfRating,
                        x.Published,
                        Bookmarked = y.Any()
                    }).FirstOrDefaultAsync();

                if (!course.Published) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "This course has been removed by System Administrator"
                };

                var isManager = await _context.Trainees.AnyAsync(x => x.LineManagerPIN == _appUser.Trainee.PIN);


                var contents = await _context.CourseMaterials.Where(x => x.CourseId == id)
                   .GroupJoin(_context.CourseContentDependencies, x => new { x.CourseId, ContentId = x.Id }, y => new { y.CourseId, y.ContentId }, (x, y) => new
                   {
                       Material = x,
                       Dependencies = y
                   }).SelectMany(x => x.Dependencies.DefaultIfEmpty(), (y, z) => new { y.Material, Sequence = z != null ? z.Sequence : 100 })
                    .GroupJoin(_context.CourseMaterialStudies.Where(x => x.Activity.TraineeId == _appUser.Trainee.Id), x => x.Material.Id, y => y.MaterialId, (x, y) => new
                    {
                        x.Material,
                        x.Sequence,
                        Studies = y
                    }).SelectMany(x => x.Studies.DefaultIfEmpty(), (y, z) => new { y.Material, y.Sequence, Study = z })
                   .Select(x => new CourseContentModel
                   {
                       Id = x.Material.Id,
                       Title = x.Material.Title,
                       Type = x.Material.MaterialType.ToString(),
                       FilePath = x.Material.FilePath,
                       S3Path = x.Material.S3Path,
                       YoutubeID = x.Material.YoutubeID,
                       ExternalLink = x.Material.ExternalLink,
                       FileSizeKb = x.Material.FileSizeKb,
                       VideoDurationSecond = x.Material.VideoDurationSecond,
                       RequiredStudyTimeSec = x.Material.RequiredStudyTimeSec,
                       Sequence = x.Sequence,
                       CanDownload = x.Material.CanDownload,
                       Studied = x.Study != null && x.Study.Completed,
                       LastStudyTimeSec = x.Study != null ? x.Study.StudyTimeSec : default(int?),
                       Resources = x.Material.Resources.Select(y => new MaterialResourceFetchModel { Title = y.Title, FilePath = y.FilePath, FileSizeKb = y.FileSizeKb }).ToList()
                   }).ToListAsync();

                contents.AddRange(await _context.CourseMockTests.Where(x => x.CourseId == id)
                     .GroupJoin(_context.CourseContentDependencies, x => new { x.CourseId, ContentId = x.Id }, y => new { y.CourseId, y.ContentId }, (x, y) => new
                     {
                         Exam = x,
                         Dependencies = y
                     }).SelectMany(x => x.Dependencies.DefaultIfEmpty(), (y, z) => new { y.Exam, Sequence = z != null ? z.Sequence : 100 })
                    .GroupJoin(_context.TraineeMockTests.Where(x => x.TraineeId == _appUser.Trainee.Id), x => x.Exam.Id, y => y.ExamId, (x, y) => new
                    {
                        x.Exam,
                        x.Sequence,
                        Studied = y.Any()
                    })
                    .Select(x => new CourseContentModel
                    {
                        Id = x.Exam.Id,
                        Title = x.Exam.ExamName,
                        Type = "MockTest",
                        Sequence = x.Sequence,
                        Studied = x.Studied
                    }).ToListAsync());

                contents = contents.OrderBy(x => x.Sequence).ToList();

                contents.ForEach(x => x.Restricted = !x.Studied && x.Sequence > 1 && x.Sequence < 100 && contents.Any(y => y.Sequence > 0 && y.Sequence < x.Sequence && !y.Studied));
                contents = contents.Where(x => x.Sequence < 100).ToList();
                var noOfLectures = contents.Count;
                var totalDuration = TimeSpan.FromSeconds(contents.Select(x => x.RequiredStudyTimeSec).Sum());
                var certificateTest = await _context.CourseExams.Where(x => x.CourseId == id)
                     .GroupJoin(_context.TraineeExams.Where(x => x.TraineeId == _appUser.Trainee.Id), x => x.Id, y => y.ExamId, (x, y) => new
                     {
                         Exam = x,
                         TraineeExams = y
                     })
                    .Select(x => new CourseContentModel
                    {
                        Id = x.Exam.Id,
                        Title = x.Exam.Course.Title,
                        Type = "CertificateTest",
                        Studied = x.TraineeExams.Any()
                    })
                    .FirstOrDefaultAsync();

                if (certificateTest != null)
                {
                    certificateTest.Restricted = !certificateTest.Studied && contents.Any(y => y.Sequence > 0 && y.Sequence < 100 && !y.Studied) || (enrollment.ExpireDate.HasValue && enrollment.ExpireDate < DateTime.Today);
                    contents.Add(certificateTest);
                }
                //await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        course.Id,
                        course.Title,
                        course.Description,
                        course.ImagePath,
                        course.Rating,
                        course.NoOfRating,
                        course.Bookmarked,
                        enrollment.CertificateAchieved,
                        IsManager = isManager,
                        TotalLecture = noOfLectures,
                        TotalDuration= string.Format( "{0:%h}hrs {0:%m} min", totalDuration),
                        Contents = contents,
                        FeedbackGiven = await _context.CourseFeedbacks.AnyAsync(x => x.CourseId == id && x.TraineeId == _appUser.Trainee.Id)
                    }
                };

            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }

        }

        public async Task<APIResponse> GetCourseContents(Guid id, ApplicationUser user)
        {

            try
            {

                var enrollment = await _context.CourseEnrollments.Where(x => x.TraineeId == user.Trainee.Id && x.CourseId == id)
                    .Select(x => new { x.CourseId, x.ExpireDate }).FirstOrDefaultAsync();
                if (enrollment == null)
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "You didn't enroll this course yet"
                    };

                var contents = await _context.CourseMaterials.Where(x => x.CourseId == id)
                   .GroupJoin(_context.CourseContentDependencies.Where(k=>k.Sequence > 0), x => new { x.CourseId, ContentId = x.Id }, y => new { y.CourseId, y.ContentId }, (x, y) => new
                   {
                       x.Id,
                       Sequence = y.Any() ? y.Select(z => z.Sequence).FirstOrDefault() : 100
                   })
                    .GroupJoin(_context.CourseMaterialStudies.Where(x => x.Activity.TraineeId == user.Trainee.Id), x => x.Id, y => y.MaterialId, (x, y) => new CourseContentRestrictionModel
                    {
                        Id = x.Id,
                        Sequence = x.Sequence,
                        Studied = y.Any(z => z.Completed),
                        LastStudyTimeSec = y.Select(z => z.StudyTimeSec).FirstOrDefault()
                    }).ToListAsync();

                contents.AddRange(await _context.CourseMockTests.Where(x => x.CourseId == id)
                     .GroupJoin(_context.CourseContentDependencies.Where(k => k.Sequence > 0), x => new { x.CourseId, ContentId = x.Id }, y => new { y.CourseId, y.ContentId }, (x, y) => new
                     {
                         x.Id,
                         Sequence = y.Any() ? y.Select(z => z.Sequence).FirstOrDefault() : 100
                     })
                    .GroupJoin(_context.TraineeMockTests.Where(x => x.TraineeId == user.Trainee.Id), x => x.Id, y => y.ExamId, (x, y) => new CourseContentRestrictionModel
                    {
                        Id = x.Id,
                        Sequence = x.Sequence,
                        Studied = y.Any()
                    }).ToListAsync());

                contents = contents.OrderBy(x => x.Sequence).ToList();

                contents.ForEach(x => x.Restricted = !x.Studied && x.Sequence > 1 && x.Sequence < 100 && contents.Any(y => y.Sequence > 0 && y.Sequence < x.Sequence && !y.Studied));

                var certificateTest = await _context.CourseExams.Where(x => x.CourseId == id)
                     .GroupJoin(_context.TraineeExams.Where(x => x.TraineeId == user.Trainee.Id), x => x.Id, y => y.ExamId, (x, y) => new
                     {
                         Exam = x,
                         TraineeExams = y
                     })
                    .Select(x => new CourseContentRestrictionModel
                    {
                        Id = x.Exam.Id,
                        Studied = x.TraineeExams.Any()
                    })
                    .FirstOrDefaultAsync();

                if (certificateTest != null)
                {
                    certificateTest.Restricted = !certificateTest.Studied && contents.Any(y => y.Sequence > 0 && y.Sequence < 100 && !y.Studied) || (enrollment.ExpireDate.HasValue && enrollment.ExpireDate < DateTime.Today);
                    contents.Add(certificateTest);
                }
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = contents
                };

            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject("Inner Exception: "+ex.InnerException.Message + ".  Message: "+ ex.Message), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }

        }

        public async Task<APIResponse> GetCourseContentsDropdownList(DiscussionType disscussionType, Guid courseId)
        {
            try
            {
                switch (disscussionType)
                {
                    case DiscussionType.Video:
                        return new APIResponse
                        {
                            Status = ResponseStatus.Success,
                            Data = await _context.CourseMaterials.Where(x => x.CourseId == courseId && x.MaterialType == MaterialType.Video).OrderBy(o => o.Title)
                                .Select(t => new
                                {
                                    t.Id,
                                    t.Title
                                }).ToListAsync()
                        };
                    case DiscussionType.Document:
                        return new APIResponse
                        {
                            Status = ResponseStatus.Success,
                            Data = await _context.CourseMaterials.Where(x => x.CourseId == courseId && x.MaterialType == MaterialType.Document).OrderBy(o => o.Title)
                                .Select(t => new
                                {
                                    t.Id,
                                    t.Title
                                }).ToListAsync()
                        };
                    case DiscussionType.MockTest:
                        return new APIResponse
                        {
                            Status = ResponseStatus.Success,
                            Data = await _context.CourseMockTests.Where(x => x.CourseId == courseId).OrderBy(o => o.ExamName)
                                .Select(t => new
                                {
                                    t.Id,
                                    Title = t.ExamName
                                }).ToListAsync()
                        };
                    case DiscussionType.CertificationTest:
                        return new APIResponse
                        {
                            Status = ResponseStatus.Success,
                            Data = await _context.CourseExams.Where(x => x.CourseId == courseId).OrderBy(o => o.Course.Title)
                                .Select(t => new
                                {
                                    t.Id,
                                    t.Course.Title
                                }).ToListAsync()
                        };
                }
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Invalid Request"
                };

            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);


                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetCourseDiscussions(Guid id, ApplicationUser user)
        {
            try
            {
                var list = await _context.CourseDiscussions.Where(x => x.CourseId == id)
                    .Select(x => new
                    {
                        x.Id,
                        x.Commentator.ImagePath,
                        DisscussionType = x.DiscussionType.ToString(),
                        Material = x.Material != null ? x.Material.Title : null,
                        Exam = x.Exam != null ? x.Exam.Course.Title : null,
                        MockTest = x.MockTest != null ? x.MockTest.ExamName : null,
                        x.Comment,
                        CommentatorType = x.CommentatorType.ToString(),
                        x.CommentTime,
                        IsSelfComment = x.CommentatorId == user.Id,
                        Commentator = x.Commentator.FirstName + " " + x.Commentator.LastName
                    })
                    .OrderByDescending(x => new { x.CommentTime })
                    .ToListAsync();

                var data = list.Select(x => new
                {
                    x.Id,
                    x.ImagePath,
                    x.DisscussionType,
                    x.Material,
                    x.Exam,
                    x.MockTest,
                    x.Comment,
                    x.CommentatorType,
                    CommentTime = x.CommentTime.ToKindUtc().ToLocalTime(),
                    x.IsSelfComment,
                    x.Commentator
                }).ToList();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);


                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }
        public async Task<APIResponse> GetCourseDiscussionById(int id, ApplicationUser user)
        {
            try
            {
                var list = await _context.CourseDiscussions.Where(x => x.Id == id)
                    .Select(x => new
                    {
                        x.Id,
                        x.CourseId,
                        x.Commentator.ImagePath,
                        DisscussionType = x.DiscussionType.ToString(),
                        DisscussionTypeId = x.DiscussionType,
                        MaterialId = x.Material != null ? x.Material.Id.ToString() : null,
                        Material = x.Material != null ? x.Material.Title : null,
                        Exam = x.Exam != null ? x.Exam.Course.Title : null,
                        ExamId = x.Exam != null ? x.Exam.Id.ToString() : null,
                        MockTestId = x.MockTest != null ? x.MockTest.Id.ToString() : null,
                        MockTest = x.MockTest != null ? x.MockTest.ExamName : null,
                        x.Comment,
                        CommentatorType = x.CommentatorType.ToString(),
                        x.CommentTime,
                        IsSelfComment = x.CommentatorId == user.Id,
                        Commentator = x.Commentator.FirstName + " " + x.Commentator.LastName
                    })
                    .OrderByDescending(x => new { x.CommentTime })
                    .ToListAsync();

                var data = list.Select(x => new
                {
                    x.Id,
                    x.CourseId,
                    x.ImagePath,
                    x.DisscussionType,
                    x.DisscussionTypeId,
                    x.MaterialId,
                    x.Material,
                    x.ExamId,
                    x.Exam,
                    x.MockTestId,
                    x.MockTest,
                    x.Comment,
                    x.CommentatorType,
                    CommentTime = x.CommentTime.ToKindUtc(),
                    x.IsSelfComment,
                    x.Commentator
                }).FirstOrDefault();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);


                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }


        public async Task<APIResponse> CourseDiscussionSave(CourseDiscussionModel model, ApplicationUser user)
        {
            try
            {
                var isEdit = false;
                if (!await _context.Courses.AnyAsync(x => x.Id == model.CourseId)) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Course not found"
                };

                var item = new CourseDiscussion
                {
                    CommentatorId = user.Id,
                    CourseId = model.CourseId,
                    Comment = model.Comment,
                    DiscussionType = model.DiscussionType ?? DiscussionType.Course,
                    CommentTime = DateTime.UtcNow
                };
                if (model.Id != null)
                {
                    item.Id =(long) model.Id;
                    isEdit=true;
                }
                if (model.MaterialId.HasValue)
                {
                    if (!await _context.CourseMaterials.AnyAsync(x => x.Id == model.MaterialId))
                        return new APIResponse
                        {
                            Status = ResponseStatus.Warning,
                            Message = "Material not found"
                        };
                    item.MaterialId = model.MaterialId;
                }

                if (model.MockTestId.HasValue)
                {
                    if (!await _context.CourseMockTests.AnyAsync(x => x.Id == model.MockTestId)) return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Mock test not found"
                    };
                    item.MockTestId = model.MockTestId;
                }

                if (model.ExamId.HasValue)
                {
                    if (!await _context.CourseExams.AnyAsync(x => x.Id == model.ExamId)) return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Exam not found"
                    };
                    item.ExamId = model.ExamId;
                }

                _context.CourseDiscussions.AddOrUpdate(item);
                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = isEdit ? "Successfully Updated" : "Successfully Saved"
                };
            }
            catch (DbEntityValidationException ex)
            {
                List<string> errorList = new List<string>();
                foreach (var errors in ex.EntityValidationErrors)
                {
                    foreach (var validationError in errors.ValidationErrors)
                    {
                        // get the error message
                        errorList.Add(validationError.ErrorMessage);
                    }
                }
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(errorList), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = string.Join(" ", errorList)
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> DeleteCourseDiscussion(long id)
        {
            try
            {
                var item = await _context.CourseDiscussions.FindAsync(id);
                if (item == null) throw new Exception("Post not found");

                _context.Entry(item).State = EntityState.Deleted;

                await _context.SaveChangesAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Deleted"
                };

            }
            catch (DbEntityValidationException ex)
            {
                List<string> errorList = new List<string>();
                foreach (var errors in ex.EntityValidationErrors)
                {
                    foreach (var validationError in errors.ValidationErrors)
                    {
                        // get the error message
                        errorList.Add(validationError.ErrorMessage);
                    }
                }


                return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = string.Join(" ", errorList)
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);


                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> EnrollCourse(Guid id, ApplicationUser user)
        {
            List<string> tokens = new List<string>();
            Notification notification = null;
            try
            {
                var course = await _context.Courses.Where(x => x.Id == id).Select(x => new { x.Id, x.ExpiryMonth, x.Published, x.Title, x.ImagePath }).FirstOrDefaultAsync();
                if (course == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Course not found. Something is wrong. Please try later."
                };

                if (!course.Published) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "This course has been removed by System Administrator."
                };

                if (await _context.CourseEnrollments.AnyAsync(x => x.TraineeId == user.Trainee.Id && x.CourseId == course.Id))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "You have alrady enrolled this course"
                    };

                var item = new CourseEnrollment
                {
                    TraineeId = user.Trainee.Id,
                    CourseId = id,
                    EnrollmentDate = DateTime.UtcNow
                };

                if (course.ExpiryMonth.HasValue)
                    item.ExpireDate = item.EnrollmentDate.Date.AddMonths(course.ExpiryMonth.Value).AddDays(1).AddTicks(-1);

                var bookmarkCourse = await _context.CourseBookmarks.FirstOrDefaultAsync(x => x.TraineeId == user.Trainee.Id && x.CourseId == id);
                if (bookmarkCourse != null)
                {
                    bookmarkCourse.EnrollmentDate = item.EnrollmentDate;
                    _context.Entry(bookmarkCourse).State = EntityState.Modified;
                }

                _context.CourseEnrollments.Add(item);

                var activity = new TraineeCourseActivity
                {
                    CourseId = id,
                    TraineeId = user.Trainee.Id,
                    NoOfContents = await _context.CourseMaterials.CountAsync(x => x.CourseId == id && x.RequiredStudyTimeSec > 0),
                    HasCertification = await _context.CourseExams.AnyAsync(x => x.CourseId == id)
                };
                activity.SetAuditTrailEntity(user.User.Identity);
                _context.TraineeCourseActivities.Add(activity);

                var notiEvent = await _context.NotificationEvents.FirstOrDefaultAsync(x => x.Type == NotificationType.SelfEnrolmentInCourse);
                if (notiEvent != null)
                {
                    notification = item.OnSelfEnrolment(course.Title);

                    if (notiEvent.InApp)
                    {
                        _context.Notifications.Add(notification);
                        tokens = await _context.TraineeDevices.Where(x => x.TraineeId == user.Trainee.Id).Select(x => x.Token).ToListAsync();
                    }
                    if (notiEvent.Email) _context.NotificationEmails.Add(new NotificationEmail { Id = Guid.NewGuid(), NotificationId = notification.Id });
                    if (notiEvent.SMS && !string.IsNullOrEmpty(user.Trainee.PhoneNo) && user.Trainee.PhoneNo.Length >= 10)
                        await new SMSSender().SendAsync(user.Trainee.PhoneNo, notification.Details, Common.SMS.Enums.SMSEventType.EnrollCourse);
                }

                await _context.SaveChangesAsync();


                if (tokens.Any() && notification != null)
                {
                    try
                    {
                        do
                        {
                            await new FirebaseMessagingClient().SendNotifications(tokens.Take(Math.Min(tokens.Count, 20)).ToArray(), notification.Title, notification.Details, new
                            {
                                NavigateTo = notification.NavigateTo.ToString(),
                                notification.Payload,
                                notification.Id,
                                NotificationType = notification.NotificationType.ToString()
                            });
                            tokens.RemoveRange(0, Math.Min(tokens.Count, 20));
                        } while (tokens.Any());
                    }
                    catch (Exception pEx)
                    {

                        LogControl.LogException(pEx);

                    }
                }


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Enrolled"
                };
            }
            catch (DbEntityValidationException ex)
            {
                List<string> errorList = new List<string>();
                foreach (var errors in ex.EntityValidationErrors)
                {
                    foreach (var validationError in errors.ValidationErrors)
                    {
                        // get the error message
                        errorList.Add(validationError.ErrorMessage);
                    }
                }

                return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = string.Join(" ", errorList)
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> VideoLeftHistory(Guid materialId, ApplicationUser user)
        {
            try
            {
                var studyTimeSec = await _context.CourseMaterialStudies.Where(x => x.MaterialId == materialId && x.Material.MaterialType == MaterialType.Video && x.Activity.TraineeId == user.Trainee.Id).Select(x => x.StudyTimeSec).FirstOrDefaultAsync();
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = studyTimeSec
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);


                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> CourseContentStudied(Guid materialId, int studyTimeSec, ApplicationUser user)
        {
            var prevCompleted = false;
            List<string> tokens = new List<string>();
            Notification notification = null;
            try
            {
                var material = await _context.CourseMaterials.FindAsync(materialId);
                if (material == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Something is wrong. Content not found. Please try later."
                };


                var activity = await _context.TraineeCourseActivities.FirstOrDefaultAsync(x => x.CourseId == material.CourseId && x.TraineeId == user.Trainee.Id) ??
                               new TraineeCourseActivity
                               {
                                   CourseId = material.CourseId,
                                   TraineeId = user.Trainee.Id,
                                   NoOfContents = await _context.CourseMaterials.CountAsync(x => x.CourseId == material.CourseId && x.RequiredStudyTimeSec > 0),
                                   HasCertification = await _context.CourseExams.AnyAsync(x => x.CourseId == material.CourseId),
                                   MaterialStudies = new List<CourseMaterialStudy>()
                               };

                var study = activity.MaterialStudies.FirstOrDefault(x => x.MaterialId == material.Id) ?? new CourseMaterialStudy
                {
                    MaterialId = material.Id
                };
                prevCompleted = study.Completed;

                if (material.MaterialType == MaterialType.Document) study.StudyTimeSec += studyTimeSec;
                else study.StudyTimeSec = studyTimeSec > study.StudyTimeSec ? studyTimeSec : study.StudyTimeSec;
                study.StudyDate = DateTime.UtcNow;
                study.Completed = material.RequiredStudyTimeSec <= study.StudyTimeSec;

                if (!activity.FirstStudyDate.HasValue)
                    activity.FirstStudyDate = study.StudyDate;
                else activity.LastStudyDate = study.StudyDate;
                LogControl.Write("prev Completed" + prevCompleted + " study Completed" + study.Completed);
                if (!prevCompleted && study.Completed)
                {
                    activity.NoOfContentsStudied++;
                    activity.GenerateProgress();
                }

                if (study.Id == 0) activity.MaterialStudies.Add(study);

                activity.SetAuditTrailEntity(user.User.Identity);

                if (activity.Id > 0)
                {
                    _context.Entry(activity).State = EntityState.Modified;
                }
                else
                {
                    _context.TraineeCourseActivities.Add(activity);
                }

                LogControl.Write("prev Completed" + prevCompleted + " study Completed" + study.Completed);
                if (!prevCompleted && study.Completed)
                {
                    var notiEvent = await _context.NotificationEvents.FirstOrDefaultAsync(x => x.Type == NotificationType.SequenceCompletion);
                    if (notiEvent != null)
                    {
                        notification = new Notification()
                        {
                            Id = Guid.NewGuid(),
                            CreatedOn = DateTime.UtcNow,
                            NotificationType = NotificationType.SequenceCompletion,
                            NavigateTo = Navigation.CourseDetails,
                            TargetUserType = UserType.Trainee,
                            TargetTraineeId = activity.TraineeId,
                            Title = "Sequence Completion",
                            Details = $"You have successfully completed the course content: {material.Title}",
                            Payload = material.CourseId.ToString(),
                            SourceId = material.Id.ToString(),
                        };
                        if (notiEvent.InApp)
                        {
                            LogControl.Write("notiEvent.InApp" + notiEvent.InApp);
                            _context.Notifications.Add(notification);

                            tokens = await _context.TraineeDevices.Where(x => x.TraineeId == notification.TargetTraineeId).Select(x => x.Token).ToListAsync();
                        }
                        if (notiEvent.Email)
                        {
                            LogControl.Write("notiEvent.Email" + notiEvent.Email);
                            _context.NotificationEmails.Add(new NotificationEmail { Id = Guid.NewGuid(), NotificationId = notification.Id });
                        }
                        if (notiEvent.SMS)
                        {
                            LogControl.Write("notiEvent.SMS" + notiEvent.SMS);
                            if (!string.IsNullOrEmpty(user.Trainee.PhoneNo) && user.Trainee.PhoneNo.Length >= 10) await new SMSSender().SendAsync(user.Trainee.PhoneNo, notification.Details, Common.SMS.Enums.SMSEventType.ContentStudy);
                        }
                    }
                }

                await _context.SaveChangesAsync();

                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);
                if (tokens.Any() && notification != null)
                {
                    try
                    {
                        do
                        {
                            await new FirebaseMessagingClient().SendNotifications(tokens.Take(Math.Min(tokens.Count, 20)).ToArray(), notification.Title, notification.Details, new
                            {
                                NavigateTo = notification.NavigateTo.ToString(),
                                notification.Payload,
                                notification.Id,
                                NotificationType = notification.NotificationType.ToString()
                            });
                            tokens.RemoveRange(0, Math.Min(tokens.Count, 20));
                        } while (tokens.Any());
                    }
                    catch (Exception excp)
                    {
                        LogControl.LogException(excp);
                    }

                }


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Saved Successfully",
                    Data = new { Current = study.Completed, NextUnlock = !prevCompleted && study.Completed, LastStudyTimeSec = study.StudyTimeSec }
                };
            }
            catch (DbEntityValidationException ex)
            {
                LogControl.LogException(ex);
                List<string> errorList = new List<string>();
                foreach (var errors in ex.EntityValidationErrors)
                {
                    foreach (var validationError in errors.ValidationErrors)
                    {
                        // get the error message
                        errorList.Add(validationError.ErrorMessage);
                        //LogControl.Write(validationError.ErrorMessage);

                    }
                }

                return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = string.Join(" ", errorList)
                };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);


                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetCourseFAQs(Guid id)
        {
            try
            {
                var data = await _context.FAQs.Where(x => x.CourseId == id).OrderBy(x => x.Sequence)
                    .Select(x => new
                    {
                        x.Id,
                        x.Question,
                        x.Answer
                    }).ToListAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetFeedbackQuestions(Guid id, ApplicationUser user)
        {
            try
            {
                var list = await _context.FeedbackQuestions.Where(x => x.EvaluationType == EvaluationType.Course)
                    .GroupJoin(_context.CourseFeedbackAnswers.Where(x => x.Feedback.CourseId == id && x.Feedback.TraineeId == user.Trainee.Id), x => x.Id, y => y.QuestionId, (x, y) => new
                    {
                        x.Id,
                        x.QuestionGroup,
                        x.Question,
                        x.QuestionType,
                        x.Options,
                        Answered = y.Select(z => z.Answer).FirstOrDefault()
                    }).OrderBy(x => x.Id).ToListAsync();

                var myfeedBack = await _context.CourseFeedbacks.Where(x => x.CourseId == id && x.TraineeId == user.Trainee.Id)
                    .Select(x => new { x.Rating, x.Comment }).FirstOrDefaultAsync();

                var data = new
                {
                    myfeedBack?.Rating,
                    myfeedBack?.Comment,
                    Feedbacks = list.GroupBy(x => x.QuestionGroup)
                    .Select(y => new
                    {
                        Group = y.Key.ToString(),
                        Questions = y.Select(x => new
                        {
                            x.Id,
                            x.Question,
                            QuestionType = x.QuestionType.ToString(),
                            Options = !string.IsNullOrEmpty(x.Options) ? Newtonsoft.Json.JsonConvert.DeserializeObject<List<string>>(x.Options) : new List<string>(),
                            Answers = !string.IsNullOrEmpty(x.Answered) ? x.Answered.Split(new string[] { "&*" }, StringSplitOptions.None).ToList() : new List<string>()
                        }).ToList()
                    }).ToList()
                };


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException != null ? ex.InnerException.InnerException?.Message ?? ex.InnerException.Message : ex.Message
                };
            }

        }
        #endregion


        public async Task<APIResponse> GetMyCourseProgress(string name, long? categoryId, int size, int pageNumber, ApplicationUser user)
        {
            try
            {
                var query = _context.CourseEnrollments.Where(x => x.TraineeId == user.Trainee.Id && x.Course.Published).AsQueryable();

                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Course.Title.Contains(name));
                if (categoryId.HasValue) query = query.Where(x => x.Course.CategoryId == categoryId);
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderByDescending(x => x.EnrollmentDate)
               .Skip(pageNumber * size).Take(size);
                var data = await filteredQuery
                    .GroupJoin(_context.TraineeCourseActivities, x => new { x.CourseId, x.TraineeId }, y => new { y.CourseId, y.TraineeId }, (x, y) => new
                    {
                        Enrollment = x,
                        Activities = y
                    }).SelectMany(x => x.Activities.DefaultIfEmpty(), (y, z) => new
                    {
                        y.Enrollment,
                        NoOfContentsStudied = z != null ? z.NoOfContentsStudied : 0,
                        Progress = z != null ? z.Progress : 0
                    })
                    .GroupJoin(_context.TraineeCertificates.Where(x => !x.Expired), x => new { x.Enrollment.CourseId, x.Enrollment.TraineeId }, y => new { y.TraineeExam.Exam.CourseId, y.TraineeId }, (x, y) => new
                    {
                        x.Enrollment,
                        x.NoOfContentsStudied,
                        x.Progress,
                        Certificates = y
                    }).SelectMany(x => x.Certificates.DefaultIfEmpty(), (y, z) => new
                    {
                        y.Enrollment,
                        y.NoOfContentsStudied,
                        y.Progress,
                        Certificate = z
                    })
                    .Select(x => new
                    {
                        x.Enrollment.Course.Id,
                        x.Enrollment.Course.Title,
                        x.Enrollment.EnrollmentDate,
                        x.Enrollment.Course.NoOfContents,
                        x.NoOfContentsStudied,
                        CertificateAchieved = x.Certificate != null,
                        CertificateAttempts = x.Certificate != null ? x.Certificate.Attempts : 0,
                        GainedPercentage = x.Certificate != null ? x.Certificate.GainedPercentage : 0,
                        Result = x.Certificate != null ? x.Certificate.Result.ToString() : null,
                        Progress = x.Progress
                    })
                    .ToListAsync();

                var count = await ((((!string.IsNullOrEmpty(name)) || (categoryId.HasValue)) || ((!string.IsNullOrEmpty(name)) && (categoryId.HasValue))) ? filteredQuery.CountAsync() : _context.CourseEnrollments.Where(x => x.TraineeId == user.Trainee.Id && x.Course.Published).CountAsync());


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Records = data,
                        Total = count
                    }
                };

            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }

        }

        public async Task<byte[]> GetCourseCertificate(Guid id, ApplicationUser user)
        {
            MemoryStream stream = new MemoryStream();
            try
            {
                string serverPath = HostingEnvironment.MapPath("~");

                // Get trainee's certificate with its specific configuration version
                var certificateData = await _context.TraineeCertificates
                    .Include(x => x.CertificateConfiguration)
                    .Where(x => !x.Expired && x.CourseId == id && x.TraineeId == user.Trainee.Id)
                    .Select(x => new {
                        x.Course.Title,
                        x.CertificateDate,
                        Trainee = x.Trainee.Name,
                        CertificateConfiguration = x.CertificateConfiguration
                    }).FirstOrDefaultAsync();

                if (certificateData == null) throw new Exception("Certificate not found for this course.");

                // Use the specific configuration version linked to this certificate
                var config = certificateData.CertificateConfiguration;

                // Fallback to latest configuration if certificate doesn't have a linked configuration (for old data)
                if (config == null)
                {
                    config = await _context.CertificateConfigurations
                        .Where(x => x.CourseId == id)
                        .OrderByDescending(x => x.Version)
                        .FirstOrDefaultAsync();
                }

                if (config == null) throw new Exception("Certificate template is not configured for this course. Please try again later.");

                string dataPath = HostingEnvironment.MapPath("~/App_Data/" + config.Template.ToString() + ".xml");
                if (!File.Exists(dataPath)) throw new Exception(config.Template.ToString() + ".xml file not found");

                string reportPath = HostingEnvironment.MapPath("~/App_Data/" + config.Template.ToString() + ".frx");
                if (!File.Exists(reportPath)) throw new Exception(config.Template.ToString() + ".frx file not found");

                //string certificateImagePath = config.TemplatePath!=null? HostingEnvironment.MapPath(config.TemplatePath) : HostingEnvironment.MapPath("~/App_Data/" + config.Template.ToString() + ".jpg");
                string certificateImagePath = config.TemplatePath != null ? (serverPath + config.TemplatePath) : HostingEnvironment.MapPath("~/App_Data/" + config.Template.ToString() + ".jpg");
                if (!File.Exists(certificateImagePath)) throw new Exception(config.Template.ToString() + ".jpg image not found");

                var data = new { certificateData.Title, certificateData.CertificateDate, certificateData.Trainee };



                var table = new System.Data.DataTable();
                table.Columns.Add("TraineeName", typeof(string));
                table.Columns.Add("CourseTitle", typeof(string));
                table.Columns.Add("CertificateDate", typeof(DateTime));
                table.Columns.Add("SignatoryText1", typeof(string));
                table.Columns.Add("SignatoryText2", typeof(string));
                table.Columns.Add("SignatoryText3", typeof(string));
                table.Columns.Add("SignatoryDesignationText1", typeof(string));
                table.Columns.Add("SignatoryDesignationText2", typeof(string));
                table.Columns.Add("SignatoryDesignationText3", typeof(string));
                table.Columns.Add("CourseDescription", typeof(string));
                table.Columns.Add("PicCertificate", typeof(byte[]));
                table.Columns.Add("SignaturePicture1", typeof(byte[]));
                table.Columns.Add("SignaturePicture2", typeof(byte[]));
                table.Columns.Add("SignaturePicture3", typeof(byte[]));

                table.Rows.Add(
                    data.Trainee,
                    data.Title,
                    data.CertificateDate,
                    config.Person1Name,
                    config.Person2Name,
                    config.Person3Name,
                    config.Designation1,
                    config.Designation2,
                    config.Designation3,
                    config.CourseDescription,
                    Utility.GetBytesFromImage(certificateImagePath),
                    config.Person1SignPath != null ? Utility.GetBytesFromImage(serverPath + config.Person1SignPath) : null,
                    config.Person2SignPath != null ? Utility.GetBytesFromImage(serverPath + config.Person2SignPath) : null,
                    config.Person3SignPath != null ? Utility.GetBytesFromImage(serverPath + config.Person3SignPath) : null);


                table.TableName = "Certificate";
                table.WriteXmlSchema(dataPath);

                using (Report report = new Report())
                {
                    report.Load(reportPath); //Load report
                    report.RegisterData(table, "Certificate");

                    LineObject signatureLine;
                    TextObject signFootText;
                    if (table.Rows[0].Field<string>(3) == null)
                    {
                        signatureLine = (LineObject)report.FindObject("SignatureLine1");
                        signatureLine.Visible = false;

                        signFootText = (TextObject)report.FindObject("SignFootText1");
                        signFootText.Visible = false;
                    }

                    if (table.Rows[0].Field<string>(4) == null)
                    {
                        signatureLine = (LineObject)report.FindObject("SignatureLine2");
                        signatureLine.Visible = false;

                        signFootText = (TextObject)report.FindObject("SignFootText2");
                        signFootText.Visible = false;
                    }

                    if (table.Rows[0].Field<string>(5) == null)
                    {
                        signatureLine = (LineObject)report.FindObject("SignatureLine3");
                        signatureLine.Visible = false;

                        signFootText = (TextObject)report.FindObject("SignFootText3");
                        signFootText.Visible = false;
                    }

                    // Two phases of preparation to exclude the display of any dialogs
                    report.PreparePhase1();
                    report.PreparePhase2();

                    PDFExport pdf = new PDFExport();
                    // We use the flow to store the report, so as not to produce files
                    System.Threading.Thread.Sleep(100);
                    report.Export(pdf, stream);
                }

                return stream.ToArray();
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                throw new Exception("Error on Certificate Generation: " + ex.Message);
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                stream.Dispose();
            }
        }
        #endregion

        public async Task<APIResponse> HasCertificateExam(Guid id)
        {
            try
            {
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(_context.CourseExams.AnyAsync(x => x.CourseId == id)), _context);
                return new APIResponse { Status = ResponseStatus.Success, Data = await _context.CourseExams.AnyAsync(x => x.CourseId == id) };
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                return new APIResponse { Status = ResponseStatus.Error, Message = ex.InnerException?.Message ?? ex.Message };
            }
        }


        public async Task<byte[]> GetCourseProgressReportExcel(Guid? courseId, long? divisionId, DateTime? startDate, DateTime? endDate)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            try
            {
                var totalTrainees = await _context.Trainees.CountAsync(x => x.Active);
                var queryEnrollments = _context.CourseEnrollments.AsQueryable();
                var queryInProgress = _context.TraineeCourseActivities.Where(x => x.Progress > 0 && x.Progress < 100).AsQueryable();
                var queryCompleted = _context.TraineeCourseActivities.Where(x => x.Progress == 100).AsQueryable();

                string division = null, course = null;

                if (courseId.HasValue && courseId != Guid.Empty)
                {
                    course = await _context.Courses.Where(x => x.Id == courseId).Select(x => x.Title).FirstOrDefaultAsync();
                    queryEnrollments = queryEnrollments.Where(x => x.CourseId == courseId);
                    queryInProgress = queryInProgress.Where(x => x.CourseId == courseId);
                    queryCompleted = queryCompleted.Where(x => x.CourseId == courseId);
                }

                if (divisionId.HasValue && divisionId > 0)
                {
                    division = await _context.Divisions.Where(x => x.Id == divisionId).Select(x => x.Name).FirstOrDefaultAsync();
                    queryEnrollments = queryEnrollments.Where(x => x.Trainee.DivisionId == divisionId);
                    queryInProgress = queryInProgress.Where(x => x.Trainee.DivisionId == divisionId);
                    queryCompleted = queryCompleted.Where(x => x.Trainee.DivisionId == divisionId);
                }

                if (startDate.HasValue && endDate.HasValue)
                {
                    queryEnrollments = queryEnrollments.Where(x => x.EnrollmentDate >= startDate && x.EnrollmentDate <= endDate);
                    queryInProgress = queryInProgress.Where(x => x.CreatedDate >= startDate && x.CreatedDate <= endDate);
                    queryCompleted = queryCompleted.Where(x => x.CreatedDate >= startDate && x.CreatedDate <= endDate);
                }

                var enrollments = await queryEnrollments
                    .GroupBy(x => new { x.Trainee.Division.Id, x.Trainee.Division.Name })
                    .Select(x => new
                    {
                        DivisionId = x.Key.Id,
                        DivisionName = x.Key.Name,
                        Enrollments = x.GroupBy(y => new { y.Course.Id, y.Course.Title, y.Course.SelfEnrollment })
                        .Select(y => new { y.Key.Id, y.Key.Title, y.Key.SelfEnrollment, NoOfTrainees = y.Distinct().Count() }).ToList()
                    }).ToListAsync();

                var inProgress = await queryInProgress
                    .GroupBy(x => new { x.Trainee.DivisionId, x.CourseId })
                    .Select(x => new
                    {
                        x.Key.DivisionId,
                        x.Key.CourseId,
                        NoOfTrainees = x.Distinct().Count()
                    }).ToListAsync();

                var completed = await queryCompleted
                    .GroupBy(x => new { x.Trainee.DivisionId, x.CourseId })
                    .Select(x => new
                    {
                        x.Key.DivisionId,
                        x.Key.CourseId,
                        NoOfTrainees = x.Distinct().Count()
                    }).ToListAsync();

                var company = await _context.Configurations.FirstOrDefaultAsync();

                ExcelManager.GetTextLineElement(company.Name, ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 4);

                ExcelManager.GetTextLineElement(company.Address + " , " + company.ContactNo, ++rowNo, ws, fontSize: 8, isBold: false, alignment: XLAlignmentHorizontalValues.Center, colspan: 4);

                rowNo++;

                ExcelManager.GetTextLineElement("Course Progress Report", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 4);
                ExcelManager.GetTextLineElement("Division: " + (division ?? "All") + " | Course: " + (course ?? "All"), ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 4);
                if (startDate.HasValue && endDate.HasValue)
                    ExcelManager.GetTextLineElement("Duration: " + startDate.Value.ToString("dd-MMM-yyyy") + " to " + endDate.Value.ToString("dd-MMM-yyyy"), ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 4);

                int noOfTrainees;
                rowNo++;

                if (enrollments.Any())
                {
                    foreach (var divisionalEnrollment in enrollments)
                    {
                        rowNo++;
                        colNo = 1;
                        ExcelManager.GetTextLineElement("Division: " + divisionalEnrollment.DivisionName, rowNo, ws, colNo++, 4, 10, isBold: true, bgColor: "#cccccc");

                        foreach (var item in divisionalEnrollment.Enrollments)
                        {
                            rowNo++;
                            colNo = 1;
                            ExcelManager.GetTextLineElement("Course: " + item.Title, rowNo, ws, colNo++, 4, 10, isBold: true, bgColor: "#efefef");

                            rowNo++;
                            colNo = 1;
                            ExcelManager.GetTableDataCell("Enrolled in the Course", 10, rowNo, colNo++, ws, isBold: true);
                            ExcelManager.GetTableDataCell(item.NoOfTrainees, 10, rowNo, colNo++, ws);
                            ExcelManager.GetTableDataCell((item.SelfEnrollment ? Math.Round((item.NoOfTrainees * 100f) / totalTrainees) : 100) + " In %", 10, rowNo, colNo++, ws);
                            ExcelManager.GetTableDataCell(item.NoOfTrainees + " out of " + (item.SelfEnrollment ? totalTrainees : item.NoOfTrainees), 10, rowNo, colNo++, ws);

                            rowNo++;
                            colNo = 1;
                            noOfTrainees = inProgress.Find(x => x.CourseId == item.Id && x.DivisionId == divisionalEnrollment.DivisionId)?.NoOfTrainees ?? 0;
                            ExcelManager.GetTableDataCell("In Progess", 10, rowNo, colNo++, ws, isBold: true);
                            ExcelManager.GetTableDataCell(noOfTrainees, 10, rowNo, colNo++, ws);
                            ExcelManager.GetTableDataCell(Math.Round((item.NoOfTrainees * 100f) / noOfTrainees) + " In %", 10, rowNo, colNo++, ws);
                            ExcelManager.GetTableDataCell(noOfTrainees + " out of " + item.NoOfTrainees, 10, rowNo, colNo++, ws);

                            rowNo++;
                            colNo = 1;
                            noOfTrainees = completed.Find(x => x.CourseId == item.Id && x.DivisionId == divisionalEnrollment.DivisionId)?.NoOfTrainees ?? 0;
                            ExcelManager.GetTableDataCell("Completed", 10, rowNo, colNo++, ws, isBold: true);
                            ExcelManager.GetTableDataCell(noOfTrainees, 10, rowNo, colNo++, ws);
                            ExcelManager.GetTableDataCell(Math.Round((item.NoOfTrainees * 100f) / noOfTrainees) + " In %", 10, rowNo, colNo++, ws);
                            ExcelManager.GetTableDataCell(noOfTrainees + " out of " + item.NoOfTrainees, 10, rowNo, colNo++, ws);

                            rowNo++;
                            colNo = 1;
                            noOfTrainees = totalTrainees - item.NoOfTrainees;
                            ExcelManager.GetTableDataCell("Not Enrolled yet", 10, rowNo, colNo++, ws, isBold: true);
                            ExcelManager.GetTableDataCell(noOfTrainees, 10, rowNo, colNo++, ws);
                            ExcelManager.GetTableDataCell((item.SelfEnrollment ? Math.Round((totalTrainees * 100f) / noOfTrainees) : 100) + " In %", 10, rowNo, colNo++, ws);
                            ExcelManager.GetTableDataCell(noOfTrainees + " out of " + (item.SelfEnrollment ? totalTrainees : 0), 10, rowNo, colNo++, ws);

                            rowNo++;
                        }


                    }

                    for (int i = 0; i < 4; i++)
                    {
                        ws.Column(i + 1).AdjustToContents();
                    }
                }
                else
                {
                    ExcelManager.GetTextLineElement("No record found", ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 4);
                }

                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);
                    return ms.ToArray();
                }
            }
            catch (Exception ibex)
            {
                ExcelManager.GetTextLineElement("Error: " + ibex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);

                    return ms.ToArray();
                }
            }
        }

        public async Task<byte[]> GetCourseProgressReportPdf(Guid? courseId, long? divisionId, DateTime? startDate, DateTime? endDate)
        {

            var document = new Document(PageSize.A4.Rotate(), 10f, 10f, 100f, 0f);
            //document.SetPageSize(PageSize.A4);
            var reportStream = new MemoryStream();

            try
            {
                PdfWriter writer = PdfWriter.GetInstance(document, reportStream);
                writer.PageEvent = new PDFWriterEvents(true, true);
                writer.CloseStream = false;

                // Openning the Document
                document.Open();

                var totalTrainees = await _context.Trainees.CountAsync(x => x.Active);
                var queryEnrollments = _context.CourseEnrollments.AsQueryable();
                var queryInProgress = _context.TraineeCourseActivities.Where(x => x.Progress > 0 && x.Progress < 100).AsQueryable();
                var queryCompleted = _context.TraineeCourseActivities.Where(x => x.Progress == 100).AsQueryable();

                string division = null, course = null;

                if (courseId.HasValue && courseId != Guid.Empty)
                {
                    course = await _context.Courses.Where(x => x.Id == courseId).Select(x => x.Title).FirstOrDefaultAsync();
                    queryEnrollments = queryEnrollments.Where(x => x.CourseId == courseId);
                    queryInProgress = queryInProgress.Where(x => x.CourseId == courseId);
                    queryCompleted = queryCompleted.Where(x => x.CourseId == courseId);
                }

                if (divisionId.HasValue && divisionId > 0)
                {
                    division = await _context.Divisions.Where(x => x.Id == divisionId).Select(x => x.Name).FirstOrDefaultAsync();
                    queryEnrollments = queryEnrollments.Where(x => x.Trainee.DivisionId == divisionId);
                    queryInProgress = queryInProgress.Where(x => x.Trainee.DivisionId == divisionId);
                    queryCompleted = queryCompleted.Where(x => x.Trainee.DivisionId == divisionId);
                }

                if (startDate.HasValue && endDate.HasValue)
                {
                    queryEnrollments = queryEnrollments.Where(x => x.EnrollmentDate >= startDate && x.EnrollmentDate <= endDate);
                    queryInProgress = queryInProgress.Where(x => x.CreatedDate >= startDate && x.CreatedDate <= endDate);
                    queryCompleted = queryCompleted.Where(x => x.CreatedDate >= startDate && x.CreatedDate <= endDate);
                }

                var enrollments = await queryEnrollments
                    .GroupBy(x => new { x.Trainee.Division.Id, x.Trainee.Division.Name })
                    .Select(x => new
                    {
                        DivisionId = x.Key.Id,
                        DivisionName = x.Key.Name,
                        Enrollments = x.GroupBy(y => new { y.Course.Id, y.Course.Title, y.Course.SelfEnrollment })
                        .Select(y => new { y.Key.Id, y.Key.Title, y.Key.SelfEnrollment, NoOfTrainees = y.Distinct().Count() }).ToList()
                    }).ToListAsync();

                var inProgress = await queryInProgress
                    .GroupBy(x => new { x.Trainee.DivisionId, x.CourseId })
                    .Select(x => new
                    {
                        x.Key.DivisionId,
                        x.Key.CourseId,
                        NoOfTrainees = x.Distinct().Count()
                    }).ToListAsync();

                var completed = await queryCompleted
                    .GroupBy(x => new { x.Trainee.DivisionId, x.CourseId })
                    .Select(x => new
                    {
                        x.Key.DivisionId,
                        x.Key.CourseId,
                        NoOfTrainees = x.Distinct().Count()
                    }).ToListAsync();

                var company = await _context.Configurations.FirstOrDefaultAsync();


                #region Page Header
                PdfPTable header = new PdfPTable(1);
                header.TotalWidth = 180;
                header.DefaultCell.Border = 0;
                header.AddCell(PDFManager.GetTextLineElement(company.Name, 12f, isBold: true));
                header.AddCell(PDFManager.GetTextLineElement(company.Address + " , " + company.ContactNo, isBold: false, fontSize: 8, leftToRightPadding: new float[] { 4, 2, 4, 2 }));
                header.WriteSelectedRows(0, 3, writer.PageSize.Width - document.LeftMargin - document.RightMargin - 180, writer.PageSize.GetTop(10), writer.DirectContent);
                if (!string.IsNullOrEmpty(company.LogoPath))
                {
                    Image img = Image.GetInstance(HostingEnvironment.MapPath("~") + company.LogoPath);
                    img.SetAbsolutePosition(document.LeftMargin, writer.PageSize.GetTop(70));
                    img.ScaleAbsolute(200f, 60f);
                    document.Add(img);
                }
                PdfPTable line = new PdfPTable(1);
                line.TotalWidth = writer.PageSize.Width;
                line.DefaultCell.Border = 0;
                line.AddCell(new Paragraph(new Chunk(new iTextSharp.text.pdf.draw.LineSeparator(0.0F, 100.0F, BaseColor.BLACK, Element.ALIGN_CENTER, 3))));
                line.WriteSelectedRows(0, 1, 0, writer.PageSize.GetTop(header.TotalHeight + 10), writer.DirectContent);
                #endregion

                document.Add(PDFManager.GetTextLineElement("Trainees' Course History Report", 14f, alignment: PDFAlignment.Center, isBold: true, leftToRightPadding: new float[] { 4, 20, 4, 4 }));

                document.Add(PDFManager.GetTextLineElement("Division: " + (division ?? "All") + " | Course: " + (course ?? "All"), 10f, alignment: PDFAlignment.Center, isBold: true, leftToRightPadding: new float[] { 4, 20, 4, 4 }));
                if (startDate.HasValue && endDate.HasValue)
                    document.Add(PDFManager.GetTextLineElement("Duration: " + startDate.Value.ToString("dd-MMM-yyyy") + " to " + endDate.Value.ToString("dd-MMM-yyyy"), 10f, alignment: PDFAlignment.Center, isBold: true));

                document.Add(PDFManager.GetTextLineElement(" ", 20f, alignment: PDFAlignment.Center));
                int noOfTrainees;
                if (enrollments.Any())
                {
                    var table = new PdfPTable(5) { WidthPercentage = 100 };
                    table.SetWidths(new float[] { 30, 30, 10, 10, 20 });
                    foreach (var divisionalEnrollment in enrollments)
                    {
                        document.Add(PDFManager.GetTextLineElement("Division: " + (division ?? "All") + " | Course: " + (course ?? "All"), 10f, alignment: PDFAlignment.Center, isBold: true, leftToRightPadding: new float[] { 4, 20, 4, 4 }));

                        foreach (var item in divisionalEnrollment.Enrollments)
                        {

                            table.AddCell(PDFManager.GetTableHeaderCell("Division: " + divisionalEnrollment.DivisionName, 8f, true, false, PDFAlignment.Left));
                            table.AddCell(PDFManager.GetTableHeaderCell("Enrolled in the Course", 8f, true, false, PDFAlignment.Left));
                            table.AddCell(PDFManager.GetTableHeaderCell(item.NoOfTrainees, 8f, true, false, PDFAlignment.Left));
                            table.AddCell(PDFManager.GetTableHeaderCell((item.SelfEnrollment ? Math.Round((item.NoOfTrainees * 100f) / totalTrainees) : 100) + " In %", 8f, true, false, PDFAlignment.Left));
                            table.AddCell(PDFManager.GetTableHeaderCell(item.NoOfTrainees + " out of " + (item.SelfEnrollment ? totalTrainees : item.NoOfTrainees), 8f, true, false, PDFAlignment.Left));

                            noOfTrainees = inProgress.Find(x => x.CourseId == item.Id && x.DivisionId == divisionalEnrollment.DivisionId)?.NoOfTrainees ?? 0;
                            table.AddCell(PDFManager.GetTableHeaderCell("Division: " + divisionalEnrollment.DivisionName, 8f, true, false, PDFAlignment.Left));
                            table.AddCell(PDFManager.GetTableHeaderCell("In Progess", 8f, true, false, PDFAlignment.Left));
                            table.AddCell(PDFManager.GetTableHeaderCell(noOfTrainees, 8f, true, false, PDFAlignment.Left));
                            table.AddCell(PDFManager.GetTableHeaderCell(Math.Round((item.NoOfTrainees * 100f) / noOfTrainees) + " In %", 8f, true, false, PDFAlignment.Left));
                            table.AddCell(PDFManager.GetTableHeaderCell(noOfTrainees + " out of " + item.NoOfTrainees, 8f, true, false, PDFAlignment.Left));

                            noOfTrainees = completed.Find(x => x.CourseId == item.Id && x.DivisionId == divisionalEnrollment.DivisionId)?.NoOfTrainees ?? 0;
                            table.AddCell(PDFManager.GetTableHeaderCell("Division: " + divisionalEnrollment.DivisionName, 8f, true, false, PDFAlignment.Left));
                            table.AddCell(PDFManager.GetTableHeaderCell("Completed", 8f, true, false, PDFAlignment.Left));
                            table.AddCell(PDFManager.GetTableHeaderCell(noOfTrainees, 8f, true, false, PDFAlignment.Left));
                            table.AddCell(PDFManager.GetTableHeaderCell(Math.Round((item.NoOfTrainees * 100f) / noOfTrainees) + " In %", 8f, true, false, PDFAlignment.Left));
                            table.AddCell(PDFManager.GetTableHeaderCell(noOfTrainees + " out of " + item.NoOfTrainees, 8f, true, false, PDFAlignment.Left));

                            noOfTrainees = totalTrainees - item.NoOfTrainees;
                            table.AddCell(PDFManager.GetTableHeaderCell("Division: " + divisionalEnrollment.DivisionName, 8f, true, false, PDFAlignment.Left));
                            table.AddCell(PDFManager.GetTableHeaderCell("Not Enrolled yet", 8f, true, false, PDFAlignment.Left));
                            table.AddCell(PDFManager.GetTableHeaderCell(noOfTrainees, 8f, true, false, PDFAlignment.Left));
                            table.AddCell(PDFManager.GetTableHeaderCell((item.SelfEnrollment ? Math.Round((totalTrainees * 100f) / noOfTrainees) : 100) + " In %", 8f, true, false, PDFAlignment.Left));
                            table.AddCell(PDFManager.GetTableHeaderCell(noOfTrainees + " out of " + (item.SelfEnrollment ? totalTrainees : 0), 8f, true, false, PDFAlignment.Left));
                            document.Add(table);
                            table = new PdfPTable(5) { WidthPercentage = 100 };
                            table.SetWidths(new float[] { 30, 30, 10, 10, 20 });

                        }
                    }
                }

                document.Close();

                //byte[] bytes = reportStream.ToArray();
                //FileStream fs = new FileStream(@"D:\somepath.pdf", FileMode.OpenOrCreate);
                //fs.Write(bytes, 0, bytes.Length);
                //fs.Close();
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);

                return reportStream.ToArray();
            }
            catch (DocumentException dex)
            {


                document.Add(PDFManager.GetTextLineElement(dex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
            // Catching System.IO.IOException if any
            catch (Exception ioex)
            {


                document.Add(PDFManager.GetTextLineElement(ioex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
        }

        public async Task<byte[]> GetGradeSummaryPDF(Guid? courseId, long? divisionId, DateTime? startDate, DateTime? endDate)
        {
            var document = new Document(PageSize.A4, 36, 36, 36, 36);
            //document.SetPageSize(PageSize.A4);
            var reportStream = new MemoryStream();

            try
            {
                PdfWriter writer = PdfWriter.GetInstance(document, reportStream);
                writer.PageEvent = new PDFWriterEvents(true, true);
                writer.CloseStream = false;

                // Openning the Document
                document.Open();

                string divisionName = null, courseName = null;

                var query = _context.TraineeExams.AsQueryable();

                if (courseId.HasValue && courseId != Guid.Empty)
                {
                    courseName = await _context.Courses.Where(x => x.Id == courseId).Select(x => x.Title).FirstOrDefaultAsync();
                    query = query.Where(x => x.Exam.CourseId == courseId);
                }

                if (divisionId.HasValue && divisionId > 0)
                {
                    divisionName = await _context.Divisions.Where(x => x.Id == divisionId).Select(x => x.Name).FirstOrDefaultAsync();
                    query = query.Where(x => x.Trainee.DivisionId == divisionId);
                }

                if (startDate.HasValue && endDate.HasValue)
                {
                    query = query.Where(x => x.CreatedDate >= startDate && x.CreatedDate <= endDate);
                }

                var data = await query
                    .GroupBy(x => new { x.Trainee.Division.Id, x.Trainee.Division.Name })
                    .OrderBy(x => x.Key.Name)
                    .Select(x => new
                    {
                        DivisionId = x.Key.Id,
                        DivisionName = x.Key.Name,
                        Courses = x.GroupBy(y => new { y.Exam.Course.Id, y.Exam.Course.Title })
                        .OrderBy(y => y.Key.Title)
                        .Select(y => new
                        {
                            y.Key.Id,
                            y.Key.Title,
                            Grades = y.GroupBy(z => z.Grade).Select(z => new { Grade = z.Key, NoOfTrainees = z.Count() })
                    .OrderBy(z => z.Grade).ToList()
                        }).ToList()
                    }).ToListAsync();

                var grades = await _context.GradingPolicies.Where(x => x.Active)
                    .OrderByDescending(x => x.MinValue).Select(x => new { x.Id, x.GradeLetter }).ToListAsync();


                var company = await _context.Configurations.FirstOrDefaultAsync();

                document.Add(PDFManager.GetTextLineElement(company.Name, 14f, alignment: PDFAlignment.Center, isBold: true));

                document.Add(PDFManager.GetTextLineElement(company.Address, 8f, alignment: PDFAlignment.Center));

                document.Add(PDFManager.GetTextLineElement("Grade Summary Report", 14f, alignment: PDFAlignment.Center, isBold: true, leftToRightPadding: new float[] { 4, 20, 4, 4 }));

                document.Add(PDFManager.GetTextLineElement("Course: " + (courseName ?? "All") + " | Division: " + (divisionName ?? "All"), 10f, alignment: PDFAlignment.Center, isBold: true, leftToRightPadding: new float[] { 4, 20, 4, 4 }));
                if (startDate.HasValue && endDate.HasValue)
                    document.Add(PDFManager.GetTextLineElement("Duration: " + startDate.Value.ToString("dd-MMM-yyyy") + " to " + endDate.Value.ToString("dd-MMM-yyyy"), 10f, alignment: PDFAlignment.Center, isBold: true));

                document.Add(PDFManager.GetTextLineElement(" ", 20f, alignment: PDFAlignment.Center));

                if (data.Any())
                {
                    foreach (var division in data)
                    {
                        document.Add(PDFManager.GetTextLineElement("Division: " + division.DivisionName, 12f, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 4, 4, 4, 10 }, isBold: true));

                        foreach (var course in division.Courses)
                        {
                            document.Add(PDFManager.GetImageContent(await GraphImage(grades.Select(x => new ChartModel { X = x.GradeLetter, Y = course.Grades.Find(y => y.Grade == x.GradeLetter)?.NoOfTrainees ?? 0 }).ToList(), course.Title, "Grades", "Trainees receiving the grade"), PDFAlignment.Center, leftToRightPadding: new float[] { 4, 4, 4, 30 }));
                        }
                    }
                }

                document.Close();

                //byte[] bytes = reportStream.ToArray();
                //FileStream fs = new FileStream(@"D:\somepath.pdf", FileMode.OpenOrCreate);
                //fs.Write(bytes, 0, bytes.Length);
                //fs.Close();
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);

                return reportStream.ToArray();
            }
            catch (DocumentException dex)
            {


                document.Add(PDFManager.GetTextLineElement(dex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
            // Catching System.IO.IOException if any
            catch (Exception ioex)
            {


                document.Add(PDFManager.GetTextLineElement(ioex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
        }

        public async Task<byte[]> GetGradeSummaryExcel(Guid? courseId, long? divisionId, DateTime? startDate, DateTime? endDate)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0;

            try
            {
                string divisionName = null, courseName = null;

                var query = _context.TraineeExams.AsQueryable();

                if (courseId.HasValue && courseId != Guid.Empty)
                {
                    courseName = await _context.Courses.Where(x => x.Id == courseId).Select(x => x.Title).FirstOrDefaultAsync();
                    query = query.Where(x => x.Exam.CourseId == courseId);
                }

                if (divisionId.HasValue && divisionId > 0)
                {
                    divisionName = await _context.Divisions.Where(x => x.Id == divisionId).Select(x => x.Name).FirstOrDefaultAsync();
                    query = query.Where(x => x.Trainee.DivisionId == divisionId);
                }

                if (startDate.HasValue && endDate.HasValue)
                {
                    query = query.Where(x => x.CreatedDate >= startDate && x.CreatedDate <= endDate);
                }

                var data = await query
                    .GroupBy(x => new { x.Trainee.Division.Id, x.Trainee.Division.Name })
                    .OrderBy(x => x.Key.Name)
                    .Select(x => new
                    {
                        DivisionId = x.Key.Id,
                        DivisionName = x.Key.Name,
                        Courses = x.GroupBy(y => new { y.Exam.Course.Id, y.Exam.Course.Title })
                        .OrderBy(y => y.Key.Title)
                        .Select(y => new
                        {
                            y.Key.Id,
                            y.Key.Title,
                            Grades = y.GroupBy(z => z.Grade).Select(z => new { Grade = z.Key, NoOfTrainees = z.Count() })
                    .OrderBy(z => z.Grade).ToList()
                        }).ToList()
                    }).ToListAsync();

                var grades = await _context.GradingPolicies.Where(x => x.Active)
                    .OrderByDescending(x => x.MinValue).Select(x => new { x.Id, x.GradeLetter }).ToListAsync();

                var company = await _context.Configurations.FirstOrDefaultAsync();

                ExcelManager.GetTextLineElement(company.Name, ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 5);

                ExcelManager.GetTextLineElement(company.Address + " , " + company.ContactNo, ++rowNo, ws, fontSize: 8, isBold: false, alignment: XLAlignmentHorizontalValues.Center, colspan: 5);

                rowNo++;

                ExcelManager.GetTextLineElement("Course Progress Report", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 5);
                ExcelManager.GetTextLineElement("Division: " + (divisionName ?? "All") + " | Course: " + (courseName ?? "All"), ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 5);
                if (startDate.HasValue && endDate.HasValue)
                    ExcelManager.GetTextLineElement("Duration: " + startDate.Value.ToString("dd-MMM-yyyy") + " to " + endDate.Value.ToString("dd-MMM-yyyy"), ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 5);

                rowNo++;

                if (data.Any())
                {
                    foreach (var division in data)
                    {
                        rowNo++;
                        ExcelManager.GetTextLineElement("Division: " + division.DivisionName, ++rowNo, ws, fontSize: 12, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 5);

                        foreach (var course in division.Courses)
                        {
                            ExcelManager.GetImageElement(await GraphImage(grades.Select(x => new ChartModel { X = x.GradeLetter, Y = course.Grades.Find(y => y.Grade == x.GradeLetter)?.NoOfTrainees ?? 0 }).ToList(), course.Title, "Grades", "Trainees receiving the grade"), ++rowNo, ws, 1, 5, XLAlignmentHorizontalValues.Center);

                            rowNo++;
                        }
                    }
                }
                else
                {
                    ExcelManager.GetTextLineElement("No record found", ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 5);
                }


                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
            catch (Exception ibex)
            {
                ExcelManager.GetTextLineElement("Error: " + ibex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 5);


                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
        }

        private async Task<Bitmap> GraphImage(List<ChartModel> items, string title, string xTitle = null, string yTitle = null)
        {
            var chart = new Chart
            {
                Width = 500,
                Height = 250,
                AntiAliasing = AntiAliasingStyles.All,
                TextAntiAliasingQuality = TextAntiAliasingQuality.High,
                Palette = ChartColorPalette.None,
                PaletteCustomColors = new Color[] { Color.CornflowerBlue }
            };

            chart.Titles.Add(title);
            chart.Titles[0].Font = new Font("Arial", 9f, FontStyle.Bold);

            chart.ChartAreas.Add("");
            chart.ChartAreas[0].AxisX.Title = xTitle;
            chart.ChartAreas[0].AxisY.Title = yTitle;
            chart.ChartAreas[0].AxisX.TitleFont = new Font("Arial", 8f);
            chart.ChartAreas[0].AxisY.TitleFont = new Font("Arial", 8f);
            chart.ChartAreas[0].AxisX.LabelStyle.Font = new Font("Arial", 10f);
            chart.ChartAreas[0].AxisX.MajorGrid.LineWidth = 0;
            chart.ChartAreas[0].AxisY.MajorGrid.LineDashStyle = ChartDashStyle.Dash;
            chart.ChartAreas[0].AxisY.MajorGrid.LineColor = Color.Black;
            chart.ChartAreas[0].AxisY.Interval = 1;
            chart.ChartAreas[0].BackColor = Color.White;
            chart.Series.Add("");
            chart.Series[0].ChartType = SeriesChartType.Column;
            chart.Series[0].CustomProperties = "PointWidth=0.5";


            foreach (var item in items)
            {
                chart.Series[0].Points.AddXY(item.X, item.Y);
            }

            var chartimage = new MemoryStream();
            chart.SaveImage(chartimage, ChartImageFormat.Png);
            await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);

            return new Bitmap(chartimage);
        }

        public async Task<byte[]> GetTraineeCourseHistoryReportExcel(Guid? courseId, long? divisionId, DateTime? startDate, DateTime? endDate)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            var headers = new List<string> { "PIN", "Name", "Division", "Department", "Name of Course", "Enrollment Date", "Enrollment Type", "Status", "Starting Date", "Completion Date", "Days Taken to Complete", "No of Attempt", "Marks 1", "Marks 2", "Marks 3", "Best Score", "Result", "Grade Achieved", "Certificate Expired", "Expiration Date" };

            try
            {
                //var totalTrainees = await _context.Trainees.CountAsync(x => x.Active);
                var query = _context.CourseEnrollments.AsQueryable();

                string division = null, course = null;

                if (courseId.HasValue && courseId != Guid.Empty)
                {
                    course = await _context.Courses.Where(x => x.Id == courseId).Select(x => x.Title).FirstOrDefaultAsync();
                    query = query.Where(x => x.CourseId == courseId);
                }

                if (divisionId.HasValue && divisionId > 0)
                {
                    division = await _context.Divisions.Where(x => x.Id == divisionId).Select(x => x.Name).FirstOrDefaultAsync();
                    query = query.Where(x => x.Trainee.DivisionId == divisionId);
                }

                if (startDate.HasValue && endDate.HasValue)
                {
                    query = query.Where(x => x.EnrollmentDate >= startDate && x.EnrollmentDate <= endDate);
                }

                var data = await query
                    .GroupJoin(_context.TraineeCourseActivities, x => new { x.CourseId, x.TraineeId }, y => new { y.CourseId, y.TraineeId }, (x, y) => new
                    {
                        Enrollment = x,
                        Activities = y
                    }).SelectMany(x => x.Activities.DefaultIfEmpty(), (y, z) => new { y.Enrollment, Activity = z })
                    .GroupJoin(_context.TraineeExams, x => new { x.Enrollment.CourseId, x.Enrollment.TraineeId }, y => new { y.Exam.CourseId, y.TraineeId }, (x, y) => new
                    {
                        x.Enrollment,
                        x.Activity,
                        TraineeExams = y
                    })
                    .GroupJoin(_context.TraineeCertificates.Where(x => !x.Expired), x => new { x.Enrollment.CourseId, x.Enrollment.TraineeId }, y => new { y.CourseId, y.TraineeId }, (x, y) => new
                    {
                        x.Enrollment,
                        x.Activity,
                        x.TraineeExams,
                        Certificates = y
                    }).SelectMany(x => x.Certificates.DefaultIfEmpty(), (y, z) => new { y.Enrollment, y.Activity, y.TraineeExams, Certificate = z })
                    .Select(x => new
                    {
                        x.Enrollment.Trainee.PIN,
                        x.Enrollment.Trainee.Name,
                        Division = x.Enrollment.Trainee.Division.Name,
                        Department = x.Enrollment.Trainee.Department.Name,
                        Course = x.Enrollment.Course.Title,
                        x.Enrollment.EnrollmentDate,
                        x.Activity.FirstStudyDate,
                        EnrollmentType = x.Activity.CreatorId == x.Enrollment.Trainee.UserId ? "Self" : "Assigned",
                        x.Activity.Progress,
                        CertificateDate = x.Certificate != null ? x.Certificate.CertificateDate : default(DateTime?),
                        Attempts = x.Certificate != null ? x.Certificate.TraineeExam.Attempts : 0,
                        ExpiryDate = x.Certificate != null ? x.Certificate.ExpiryDate : default(DateTime?),
                        Expired = x.Certificate != null ? x.Certificate.Expired : false,
                        TraineeExams = x.TraineeExams.OrderBy(y => y.StartDate).Select(y => new
                        {
                            y.GainedMarks,
                            y.StartDate,
                            y.Result,
                            y.Grade,
                            y.GainedPercentage
                        }).Take(3).ToList()
                    }).OrderBy(x => x.Division).ThenBy(x => x.Course).ThenBy(x => x.Name).ToListAsync();

                var company = await _context.Configurations.FirstOrDefaultAsync();

                ExcelManager.GetTextLineElement(company.Name, ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headers.Count);

                ExcelManager.GetTextLineElement(company.Address + " , " + company.ContactNo, ++rowNo, ws, fontSize: 8, isBold: false, alignment: XLAlignmentHorizontalValues.Center, colspan: headers.Count);

                rowNo++;

                ExcelManager.GetTextLineElement("Trainees' Course History Report", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headers.Count);
                ExcelManager.GetTextLineElement("Division: " + (division ?? "All") + " | Course: " + (course ?? "All"), ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headers.Count);
                if (startDate.HasValue && endDate.HasValue)
                    ExcelManager.GetTextLineElement("Duration: " + startDate.Value.ToString("dd-MMM-yyyy") + " to " + endDate.Value.ToString("dd-MMM-yyyy"), ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headers.Count);

                rowNo += 2;



                colNo = 1;
                ExcelManager.GetTableHeaderCell(headers, rowNo, ws, 10, XLAlignmentHorizontalValues.Center);

                if (data.Any())
                {
                    foreach (var item in data)
                    {
                        rowNo++;
                        colNo = 1;
                        //var traineeExamByTraineeIdList = traineeExams.Where(x => x.TraineeId == item.TraineeId).Select(x=>x).ToList();
                        //var traineeExam = traineeExams.Where(x=>x.GainedMarks == traineeExamByTraineeIdList.Max(i=>i.GainedMarks)).FirstOrDefault();
                        ExcelManager.GetTableDataCell(item.PIN, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center, isBold: false);
                        ExcelManager.GetTableDataCell(item.Name, 10, rowNo, colNo++, ws, isBold: false);
                        ExcelManager.GetTableDataCell(item.Division, 10, rowNo, colNo++, ws, isBold: false);
                        ExcelManager.GetTableDataCell(item.Department, 10, rowNo, colNo++, ws, isBold: false);
                        ExcelManager.GetTableDataCell(item.Course, 10, rowNo, colNo++, ws, isBold: false);
                        ExcelManager.GetTableDataCell(item.EnrollmentDate.ToLocalTime().ToString("dd-MMM-yyyy"), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center, isBold: false);
                        ExcelManager.GetTableDataCell(item.EnrollmentType, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center, isBold: false);

                        if (item.Progress == 0)
                            ExcelManager.GetTableDataCell("Not Started Yet", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center, isBold: false);
                        else if (item.Progress > 0 && item.Progress < 100)
                            ExcelManager.GetTableDataCell("In Progress", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center, isBold: false);
                        else
                            ExcelManager.GetTableDataCell("Completed", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center, isBold: false);

                        ExcelManager.GetTableDataCell(item.FirstStudyDate.HasValue ? item.FirstStudyDate.Value.ToLocalTime().ToString("dd-MMM-yyyy") : " ", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center, isBold: false);
                        ExcelManager.GetTableDataCell(item.CertificateDate.HasValue ? item.CertificateDate.Value.ToLocalTime().ToString("dd-MMM-yyyy") : " ", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center, isBold: false);
                        ExcelManager.GetTableDataCell(item.CertificateDate.HasValue && item.FirstStudyDate.HasValue ? item.CertificateDate.Value.ToLocalTime().Subtract(item.FirstStudyDate.Value.ToLocalTime()).TotalDays.ToString("0") : " ", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right, isBold: false);
                        ExcelManager.GetTableDataCell(item.Attempts, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right, isBold: false);
                        for (int i = 0; i < Math.Min(3, item.TraineeExams.Count); i++)
                        {
                            colNo += i;
                            ExcelManager.GetTableDataCell(item.TraineeExams[i].GainedMarks, 10, rowNo, colNo, ws, XLAlignmentHorizontalValues.Right, isBold: false);
                        }
                        colNo = 16;
                        ExcelManager.GetTableDataCell(item.TraineeExams.Any() ? item.TraineeExams.Max(x => x.GainedMarks).ToString("0.0") : " ", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right, isBold: false);
                        ExcelManager.GetTableDataCell(item.TraineeExams.Any(x => x.Result == GradeResult.Passed) ? "Pass" : "Fail", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center, isBold: false);
                        ExcelManager.GetTableDataCell(item.TraineeExams.OrderByDescending(x => x.GainedMarks).Select(x => x.Grade).FirstOrDefault(), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center, isBold: false);
                        ExcelManager.GetTableDataCell(item.Expired ? "Yes" : "No", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center, isBold: false);
                        ExcelManager.GetTableDataCell(item.ExpiryDate.HasValue ? item.ExpiryDate.Value.ToLocalTime().ToString("dd-MMM-yyyy") : " ", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center, isBold: false);
                    }

                    for (int i = 0; i < headers.Count; i++)
                    {
                        if (i == 4) ws.Column(5).Width = 50;
                        else
                            ws.Column(i + 1).AdjustToContents();
                    }
                }
                else
                {
                    ExcelManager.GetTextLineElement("No record found", ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headers.Count);
                }

                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
            catch (Exception ibex)
            {
                ExcelManager.GetTextLineElement("Error: " + ibex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
        }
        public async Task<byte[]> GetTraineeCourseHistoryReportPdf(Guid? courseId,long? divisionId, DateTime? startDate, DateTime? endDate)
        {

            var document = new Document(PageSize.A4.Rotate(), 10f, 10f, 100f, 0f);
            //document.SetPageSize(PageSize.A4);
            var reportStream = new MemoryStream();

            try
            {
                PdfWriter writer = PdfWriter.GetInstance(document, reportStream);
                writer.PageEvent = new PDFWriterEvents(true, true);
                writer.CloseStream = false;

                // Openning the Document
                document.Open();

                //string divisionName = null, courseName = null;

                //var totalTrainees = await _context.Trainees.CountAsync(x => x.Active);
                var query = _context.CourseEnrollments.AsQueryable();

                string division = null, course = null;

                if (courseId.HasValue && courseId != Guid.Empty)
                {
                    course = await _context.Courses.Where(x => x.Id == courseId).Select(x => x.Title).FirstOrDefaultAsync();
                    query = query.Where(x => x.CourseId == courseId);
                }

                if (divisionId.HasValue && divisionId > 0)
                {
                    division = await _context.Divisions.Where(x => x.Id == divisionId).Select(x => x.Name).FirstOrDefaultAsync();
                    query = query.Where(x => x.Trainee.DivisionId == divisionId);
                }

                if (startDate.HasValue && endDate.HasValue)
                {
                    query = query.Where(x => x.EnrollmentDate >= startDate && x.EnrollmentDate <= endDate);
                }

                var data = await query
                    .GroupJoin(_context.TraineeCourseActivities, x => new { x.CourseId, x.TraineeId }, y => new { y.CourseId, y.TraineeId }, (x, y) => new
                    {
                        Enrollment = x,
                        Activities = y
                    }).SelectMany(x => x.Activities.DefaultIfEmpty(), (y, z) => new { y.Enrollment, Activity = z })
                    .GroupJoin(_context.TraineeExams, x => new { x.Enrollment.CourseId, x.Enrollment.TraineeId }, y => new { y.Exam.CourseId, y.TraineeId }, (x, y) => new
                    {
                        x.Enrollment,
                        x.Activity,
                        TraineeExams = y
                    })
                    .GroupJoin(_context.TraineeCertificates.Where(x => !x.Expired), x => new { x.Enrollment.CourseId, x.Enrollment.TraineeId }, y => new { y.CourseId, y.TraineeId }, (x, y) => new
                    {
                        x.Enrollment,
                        x.Activity,
                        x.TraineeExams,
                        Certificates = y
                    }).SelectMany(x => x.Certificates.DefaultIfEmpty(), (y, z) => new { y.Enrollment, y.Activity, y.TraineeExams, Certificate = z })
                    .Select(x => new
                    {
                        x.Enrollment.Trainee.PIN,
                        x.Enrollment.Trainee.Name,
                        Division = x.Enrollment.Trainee.Division.Name,
                        Department = x.Enrollment.Trainee.Department.Name,
                        Course = x.Enrollment.Course.Title,
                        x.Enrollment.EnrollmentDate,
                        x.Activity.FirstStudyDate,
                        EnrollmentType = x.Activity.CreatorId == x.Enrollment.Trainee.UserId ? "Self" : "Assigned",
                        x.Activity.Progress,
                        CertificateDate = x.Certificate != null ? x.Certificate.CertificateDate : default(DateTime?),
                        Attempts = x.Certificate != null ? x.Certificate.TraineeExam.Attempts : 0,
                        ExpiryDate = x.Certificate != null ? x.Certificate.ExpiryDate : default(DateTime?),
                        Expired = x.Certificate != null ? x.Certificate.Expired : false,
                        TraineeExams = x.TraineeExams.OrderBy(y => y.StartDate).Select(y => new
                        {
                            y.GainedMarks,
                            y.StartDate,
                            y.Result,
                            y.Grade,
                            y.GainedPercentage
                        }).Take(3).ToList()
                    }).OrderBy(x => x.Division).ThenBy(x => x.Course).ThenBy(x => x.Name).ToListAsync();

                var company = await _context.Configurations.FirstOrDefaultAsync();

                #region Page Header
                PdfPTable header = new PdfPTable(1);
                header.TotalWidth = 180;
                header.DefaultCell.Border = 0;
                header.AddCell(PDFManager.GetTextLineElement(company.Name, 12f, isBold: true));
                header.AddCell(PDFManager.GetTextLineElement(company.Address + " , " + company.ContactNo, isBold: false, fontSize: 8, leftToRightPadding: new float[] { 4, 2, 4, 2 }));
                header.WriteSelectedRows(0, 3, writer.PageSize.Width - document.LeftMargin - document.RightMargin - 180, writer.PageSize.GetTop(10), writer.DirectContent);
                if (!string.IsNullOrEmpty(company.LogoPath))
                {
                    Image img = Image.GetInstance(HostingEnvironment.MapPath("~") + company.LogoPath);
                    img.SetAbsolutePosition(document.LeftMargin, writer.PageSize.GetTop(70));
                    img.ScaleAbsolute(200f, 60f);
                    document.Add(img);
                }
                PdfPTable line = new PdfPTable(1);
                line.TotalWidth = writer.PageSize.Width;
                line.DefaultCell.Border = 0;
                line.AddCell(new Paragraph(new Chunk(new iTextSharp.text.pdf.draw.LineSeparator(0.0F, 100.0F, BaseColor.BLACK, Element.ALIGN_CENTER, 3))));
                line.WriteSelectedRows(0, 1, 0, writer.PageSize.GetTop(header.TotalHeight + 10), writer.DirectContent);
                #endregion

                document.Add(PDFManager.GetTextLineElement("Trainees' Course History Report", 14f, alignment: PDFAlignment.Center, isBold: true, leftToRightPadding: new float[] { 4, 20, 4, 4 }));

                document.Add(PDFManager.GetTextLineElement("Division: " + (division ?? "All") + " | Course: " + (course ?? "All"), 10f, alignment: PDFAlignment.Center, isBold: true, leftToRightPadding: new float[] { 4, 20, 4, 4 }));
                if (startDate.HasValue && endDate.HasValue)
                    document.Add(PDFManager.GetTextLineElement("Duration: " + startDate.Value.ToString("dd-MMM-yyyy") + " to " + endDate.Value.ToString("dd-MMM-yyyy"), 10f, alignment: PDFAlignment.Center, isBold: true));

                document.Add(PDFManager.GetTextLineElement(" ", 20f, alignment: PDFAlignment.Center));

                if (data.Any())
                {
                    #region Table
                    var table = new PdfPTable(12) { WidthPercentage = 100 };
                    table.SetWidths(new float[] {  5, 10, 15, 7, 7, 5, 5, 5, 5, 5, 5, 5 });
                    table.AddCell(PDFManager.GetTableHeaderCell("PIN", 8f, true, false, PDFAlignment.Center));
                    table.AddCell(PDFManager.GetTableHeaderCell("Name", 8f, true, false, PDFAlignment.Center));
                    //table.AddCell(PDFManager.GetTableHeaderCell("Division", 8f, true, false, PDFAlignment.Center));
                    //table.AddCell(PDFManager.GetTableHeaderCell("Department", 8f, true, false, PDFAlignment.Center));
                    table.AddCell(PDFManager.GetTableHeaderCell("Name of Course", 8f, true, false, PDFAlignment.Center));
                    table.AddCell(PDFManager.GetTableHeaderCell("Enrollment Date", 8f, true, false, PDFAlignment.Center));
                    table.AddCell(PDFManager.GetTableHeaderCell("Enrollment Type", 8f, true, false, PDFAlignment.Center));
                    table.AddCell(PDFManager.GetTableHeaderCell("Status", 8f, true, false, PDFAlignment.Center));
                    table.AddCell(PDFManager.GetTableHeaderCell("Starting Date", 8f, true, false, PDFAlignment.Center));
                    table.AddCell(PDFManager.GetTableHeaderCell("Completion Date", 8f, true, false, PDFAlignment.Center));
                    //table.AddCell(PDFManager.GetTableHeaderCell("Days Taken to Complete", 8f, true, false, PDFAlignment.Center));
                    table.AddCell(PDFManager.GetTableHeaderCell("No of Attempt", 8f, true, false, PDFAlignment.Center));
                    //table.AddCell(PDFManager.GetTableHeaderCell("Marks 1", 8f, true, false, PDFAlignment.Center));
                    //table.AddCell(PDFManager.GetTableHeaderCell("Marks 2", 8f, true, false, PDFAlignment.Center));
                    //table.AddCell(PDFManager.GetTableHeaderCell("Marks 3", 8f, true, false, PDFAlignment.Center));
                    table.AddCell(PDFManager.GetTableHeaderCell("Best Score", 8f, true, false, PDFAlignment.Center));
                    table.AddCell(PDFManager.GetTableHeaderCell("Result", 8f, true, false, PDFAlignment.Center));
                    table.AddCell(PDFManager.GetTableHeaderCell("Grade Achieved", 8f, true, false, PDFAlignment.Center));
                    //table.AddCell(PDFManager.GetTableHeaderCell("Certificate Expired", 8f, true, false, PDFAlignment.Center));
                    //table.AddCell(PDFManager.GetTableHeaderCell("Expiration Date", 8f, true, false, PDFAlignment.Center));



                    foreach (var item in data)
                    {
                        table.AddCell(PDFManager.GetTableDataCell(item.PIN, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(item.Name, PDFAlignment.Center, false, true, 8));
                        //table.AddCell(PDFManager.GetTableDataCell(item.Division, PDFAlignment.Center, false, true, 8));
                        //table.AddCell(PDFManager.GetTableDataCell(item.Department, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(item.Course, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(item.EnrollmentDate.ToLocalTime().ToString("dd-MMM-yyyy"), PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(item.EnrollmentType, PDFAlignment.Center, false, true, 8));
                        if (item.Progress == 0)
                            table.AddCell(PDFManager.GetTableDataCell("Not Started Yet", PDFAlignment.Center, false, true, 8));
                        else if (item.Progress > 0 && item.Progress < 100)
                            table.AddCell(PDFManager.GetTableDataCell("In Progress", PDFAlignment.Center, false, true, 8));
                        else
                            table.AddCell(PDFManager.GetTableDataCell("Completed", PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(item.FirstStudyDate.HasValue ? item.FirstStudyDate.Value.ToLocalTime().ToString("dd-MMM-yyyy") : " ", PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(item.CertificateDate.HasValue ? item.CertificateDate.Value.ToLocalTime().ToString("dd-MMM-yyyy") : " ", PDFAlignment.Center, false, true, 8));
                        //table.AddCell(PDFManager.GetTableDataCell(item.CertificateDate.HasValue ? item.CertificateDate.Value.Subtract(item.FirstStudyDate.Value).TotalDays.ToString("0") : " ", PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(item.Attempts, PDFAlignment.Center, false, true, 8));
                        //for (int i = 0; i < Math.Min(3, item.TraineeExams.Count); i++)
                        //{
                        //    table.AddCell(PDFManager.GetTableDataCell(item.TraineeExams[i].GainedMarks, PDFAlignment.Center, false, true, 8));
                        //}
                        table.AddCell(PDFManager.GetTableDataCell(item.TraineeExams.Any() ? item.TraineeExams.Max(x => x.GainedMarks).ToString("0.0") : " ", PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(item.TraineeExams.Any(x => x.Result == GradeResult.Passed) ? "Pass" : "Fail", PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(item.TraineeExams.OrderByDescending(x => x.GainedMarks).Select(x => x.Grade).FirstOrDefault(), PDFAlignment.Center, false, true, 8));
                        //table.AddCell(PDFManager.GetTableDataCell(item.Expired ? "Yes" : "No", PDFAlignment.Center, false, true, 8));
                        //table.AddCell(PDFManager.GetTableDataCell(item.ExpiryDate.HasValue ? item.ExpiryDate.Value.ToString("dd-MMM-yyyy") : " ", PDFAlignment.Center, false, true, 8));
                    }

                    document.Add(table);
                    #endregion
                }

                document.Close();

                //byte[] bytes = reportStream.ToArray();
                //FileStream fs = new FileStream(@"D:\somepath.pdf", FileMode.OpenOrCreate);
                //fs.Write(bytes, 0, bytes.Length);
                //fs.Close();
                await _auditLogHelper.AddSuccessAudit(audit, JsonConvert.SerializeObject(null), _context);

                return reportStream.ToArray();
            }
            catch (DocumentException dex)
            {


                document.Add(PDFManager.GetTextLineElement(dex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
            // Catching System.IO.IOException if any
            catch (Exception ioex)
            {


                document.Add(PDFManager.GetTextLineElement(ioex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
        }
        public async Task<bool> DeleteNotifications(Guid courseId)
        {
            try
            {
                var notifications = await _context.Notifications.Where(x => x.Payload.Equals(courseId.ToString())).ToListAsync();
                if (notifications.Any())
                {
                    _context.Notifications.RemoveRange(notifications);
                    _context.SaveChanges();
                    return true;
                }
                else
                    return false;
            }
            catch (Exception ex)
            {

                return false;
            }
        }

    }

    public interface ICourseService
    {
        Task<APIResponse> CourseCreateOrUpdate(CourseModel model, ApplicationUser user);
        Task<APIResponse> GetCourseList(string name, long? categoryId, int size, int pageNumber, ApplicationUser user);
        Task<APIResponse> GetEnrolledAndAvailalbleCourse(string name, long? categoryId, int size, int pageNumber,int? courseStatus, ApplicationUser user);
        Task<APIResponse> GetCourseById(Guid id, ApplicationUser user);
        Task<APIResponse> GetCourseDropDownList(ApplicationUser user);
        Task<APIResponse> GetCourseMaterialList(string name, Guid courseId, int size, int pageNumber);
        Task<APIResponse> GetMaterialResources(Guid id);
        Task<APIResponse> GetCourseMaterialsWithExams(Guid id);
        Task<APIResponse> SaveContentDependencies(Guid id, List<CourseContentDependencyModel> contents, IIdentity identity);
        Task<APIResponse> PublishOrUnpublishCourse(Guid id, IIdentity identity);
        Task<APIResponse> GetCourseRatings(Guid id);



        Task<byte[]> GetCourseListExcel(int timeZoneOffset);
        Task<APIResponse> GetCourseEnrollmentCount(int limit, ApplicationUser user);
        Task<byte[]> GetCourseEnrollmentCountExcel(ApplicationUser user);
        Task<byte[]> GetCourseEnrollmentByDateExcel(DateTime fromDate, DateTime toDate, ApplicationUser user);
        Task<byte[]> GetCourseWiseEnrollmentsExcel(Guid? courseId, ApplicationUser user);
        Task<APIResponse> EnrollTraineeToCourse(TraineeEnrollModel model, IIdentity identity);
        Task<APIResponse> CancelCourseEnrollmentOfTrainee(Guid courseId, Guid traineeId);
        Task<APIResponse> GetTraineesByCourseId(Guid courseId, long traineeGroupId);
        Task<APIResponse> GetEnrolledTraineeList(Guid courseId, long? designationId);



        Task<APIResponse> GetMaterialsDropdownList(Guid courseId);
        Task<APIResponse> DeleteMaterialById(Guid id);
        Task<APIResponse> ResourceCreateOrUpdate(MaterialResourceModel model, IIdentity identity);
        Task<APIResponse> DeleteResourceById(Guid id);
        Task<APIResponse> GetEnrolledCourseDropDownList(ApplicationUser user);
        Task<APIResponse> GetMaterialResourcesDropdownList(Guid materialId);
        Task<APIResponse> GetTraineesByCourseIdToNotify(Guid courseId, Guid examId);
        Task<APIResponse> NotifyTrainee(NotifyTraineeModel model, ApplicationUser user);
        Task<APIResponse> GetNotifiedTrainees(Guid courseId, Guid examId);
        Task<bool> DeleteNotifications(Guid courseId);
        Task<byte[]> GetCourseWiseTraineeStudyReportExcel(Guid courseId);
        Task<byte[]> GetCourseWiseTraineeStudyReportPdf(Guid courseId);
        Task<byte[]> GetCourseWiseTestReportExcel(DateTime startDate, DateTime endDate, Guid courseId, ExamType type);
        Task<byte[]> GetTraineeWiseCourseStudyReportExcel(Guid traineeId);
        Task<byte[]> GetTraineeWiseCourseStudyReportPdf(Guid traineeId);
        Task<byte[]> GetTimeWiseCourseStudyReportExcel(DateTime startDate, DateTime endDate);
        Task<byte[]> GetTimeWiseCourseStudyReportPdf(DateTime startDate, DateTime endDate);
        Task<byte[]> GetExamWiseCorrectAnswerRateReportExcel(Guid courseId);
        Task<APIResponse> MaterialCreateOrUpdate(CourseMaterialModel model, IIdentity identity);

        #region Trainee Panel APIs
        Task<APIResponse> GetMyCourseList(int limit, int? courseStatus, ApplicationUser user, DateTime? keyDate = null);
        Task<APIResponse> GetMyCourseList(string name, long? categoryId, int size, int pageNumber, int? courseStatus, ApplicationUser user);
        Task<APIResponse> GetAvailableCourseList(int limit, ApplicationUser user, DateTime? keyDate = null);
        Task<APIResponse> GetAvailableCourseList(string name, long? categoryId, int size, int pageNumber, ApplicationUser user);
        Task<APIResponse> GetLearningHourList(int limit, ApplicationUser user, DateTime? keyDate = null);
        Task<APIResponse> GetLearningHourList(string name, long? categoryId, int size, int pageNumber, ApplicationUser user);
        Task<APIResponse> GetLearningHourGroupByList(string name, long? categoryId, int size, int pageNumber, ApplicationUser user);
        Task<APIResponse> GetMyBookmarkedCourseList(string name, long? categoryId, int size, int pageNumber, ApplicationUser user);

        #region Course Details API
        Task<APIResponse> GetCoursePreview(Guid id, ApplicationUser user);
        Task<APIResponse> GetCourseDetails(Guid id);
        Task<APIResponse> GetMyTraineesToEnrol(Guid id, ApplicationUser user);
        Task<APIResponse> EnrollTeammatesToCourse(Guid id, List<Guid> traineeIds, IIdentity identity);
        Task<APIResponse> GetCourseContents(Guid id, ApplicationUser user);
        Task<APIResponse> GetCourseContentsDropdownList(DiscussionType disscussionType, Guid courseId);
        Task<APIResponse> GetCourseDiscussions(Guid id, ApplicationUser user);
        Task<APIResponse> CourseDiscussionSave(CourseDiscussionModel model, ApplicationUser user);
        Task<APIResponse> DeleteCourseDiscussion(long id);
        Task<APIResponse> EnrollCourse(Guid id, ApplicationUser user);
        Task<APIResponse> BookmarkOrUnbookmarkCourse(Guid id, ApplicationUser user);
        Task<APIResponse> VideoLeftHistory(Guid materialId, ApplicationUser user);
        Task<APIResponse> CourseContentStudied(Guid materialId, int studyTimeSec, ApplicationUser user);
        Task<APIResponse> GetCourseFAQs(Guid id);
        Task<APIResponse> GetFeedbackQuestions(Guid id, ApplicationUser user);
        #endregion

        Task<APIResponse> GetMyCourseProgress(string name, long? categoryId, int size, int pageNumber, ApplicationUser user);
        Task<byte[]> GetCourseCertificate(Guid id, ApplicationUser user);
        #endregion

        Task<APIResponse> HasCertificateExam(Guid id);
        Task<byte[]> GetCourseProgressReportExcel(Guid? courseId, long? divisionId, DateTime? startDate, DateTime? endDate);
        Task<byte[]> GetCourseProgressReportPdf(Guid? courseId, long? divisionId, DateTime? startDate, DateTime? endDate);
        Task<byte[]> GetGradeSummaryPDF(Guid? courseId, long? divisionId, DateTime? startDate, DateTime? endDate);
        Task<byte[]> GetGradeSummaryExcel(Guid? courseId, long? divisionId, DateTime? startDate, DateTime? endDate);
        Task<byte[]> GetTraineeCourseHistoryReportExcel(Guid? courseId, long? divisionId, DateTime? startDate, DateTime? endDate);
        Task<byte[]> GetTraineeCourseHistoryReportPdf(Guid? courseId, long? divisionId, DateTime? startDate, DateTime? endDate);
    }
}


