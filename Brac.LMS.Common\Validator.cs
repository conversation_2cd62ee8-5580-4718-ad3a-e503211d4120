﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace Brac.LMS.Common
{
    public static class Validator
    {
        public delegate Task<List<string>> VerifyFileValueFromDB(IList<string> line, int rowNo);

        public static char[] Columns = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".ToCharArray();

        public static async Task VerifyUploadFileValue(List<IList<string>> rows, Dictionary<int, ColumnType> indexes, VerifyFileValueFromDB dbValidator = null)
        {
            List<string> emptyErrors = new List<string>(), typeErrors = new List<string>(), dbErrors = new List<string>();
            for (int ri = 0; ri < rows.Count; ri++)
            {
                foreach (var i in indexes.Keys)
                {
                    if (rows[ri][i].Trim() == string.Empty) emptyErrors.Add(Columns[i].ToString() + (ri + 2));
                    switch (indexes[i])
                    {
                        case ColumnType.Int:
                            if (!int.TryParse(rows[ri][i], out int vi)) typeErrors.Add(Columns[i].ToString() + (ri + 2) + " => INT");
                            break;
                        case ColumnType.Long:
                            if (!long.TryParse(rows[ri][i], out long vl)) typeErrors.Add(Columns[i].ToString() + (ri + 2) + " => BIG INT");
                            break;
                        case ColumnType.Double:
                            if (!double.TryParse(rows[ri][i], out double dbl)) typeErrors.Add(Columns[i].ToString() + (ri + 2) + " => DOUBLE");
                            break;
                        case ColumnType.DateTime:
                            if (!DateTime.TryParse(rows[ri][i], out DateTime vd))
                                typeErrors.Add(Columns[i].ToString() + (ri + 2) + " => DATE");
                            break;
                        case ColumnType.PhoneNumber:
                            if (rows[ri][i].Length > 11 || !rows[ri][i].StartsWith("01"))
                                typeErrors.Add(Columns[i].ToString() + (ri + 2) + " => Mobile");
                            break;
                        case ColumnType.Email:
                            if (!Utility.IsValidEmail(rows[ri][i]))
                                typeErrors.Add(Columns[i].ToString() + (ri + 2) + " => Email");
                            break;
                        default:
                            break;
                    }
                }
                dbErrors.AddRange(dbValidator != null ? await dbValidator.Invoke(rows[ri], ri) : new List<string>());
            }

            var errormsg = emptyErrors.Any() ? "Empty values for: \n" + string.Join("\n", emptyErrors) : "";
            errormsg += (!string.IsNullOrEmpty(errormsg) ? "\n" : "") + (typeErrors.Any() ? "\nData type error for: \n" + string.Join("\n", typeErrors) : "");
            errormsg += (!string.IsNullOrEmpty(errormsg) ? "\n" : "") + (dbErrors.Any() ? "\n" + string.Join("\n", dbErrors) : "");

            if (!string.IsNullOrEmpty(errormsg)) throw new Exception(errormsg);


        }
        public static async Task VerifyGuestTraineeUploadFileValue(List<IList<string>> rows, Dictionary<int, ColumnType> indexes, VerifyFileValueFromDB dbValidator = null, bool skipEmptyError =false)
        {
            List<string> emptyErrors = new List<string>(), typeErrors = new List<string>(), dbErrors = new List<string>();
            for (int ri = 0; ri < rows.Count; ri++)
            {
                foreach (var i in indexes.Keys)
                {
                    if (rows[ri][i].Trim() == string.Empty && !skipEmptyError) emptyErrors.Add(Columns[i].ToString() + (ri + 2));
                    switch (indexes[i])
                    {
                        case ColumnType.Int:
                            if (!int.TryParse(rows[ri][i], out int vi)) typeErrors.Add(Columns[i].ToString() + (ri + 2) + " => INT");
                            break;
                        case ColumnType.Long:
                            if (!long.TryParse(rows[ri][i], out long vl)) typeErrors.Add(Columns[i].ToString() + (ri + 2) + " => BIG INT");
                            break;
                        case ColumnType.Double:
                            if (!double.TryParse(rows[ri][i], out double dbl)) typeErrors.Add(Columns[i].ToString() + (ri + 2) + " => DOUBLE");
                            break;
                        case ColumnType.DateTime:
                            if (!DateTime.TryParse(rows[ri][i], out DateTime vd))
                                typeErrors.Add(Columns[i].ToString() + (ri + 2) + " => DATE");
                            break;
                        case ColumnType.PhoneNumber:
                            if (rows[ri][i].Length > 11 || !rows[ri][i].StartsWith("01"))
                                typeErrors.Add(Columns[i].ToString() + (ri + 2) + " => Mobile");
                            break;
                        case ColumnType.Email:
                            if (!Utility.IsValidEmail(rows[ri][i]))
                                typeErrors.Add(Columns[i].ToString() + (ri + 2) + " => Email");
                            break;
                        default:
                            break;
                    }
                }
                dbErrors.AddRange(dbValidator != null ? await dbValidator.Invoke(rows[ri], ri) : new List<string>());
            }

            var errormsg = emptyErrors.Any() ? "Empty values for: \n" + string.Join("\n", emptyErrors) : "";
            errormsg += (!string.IsNullOrEmpty(errormsg) ? "\n" : "") + (typeErrors.Any() ? "\nData type error for: \n" + string.Join("\n", typeErrors) : "");
            errormsg += (!string.IsNullOrEmpty(errormsg) ? "\n" : "") + (dbErrors.Any() ? "\n" + string.Join("\n", dbErrors) : "");

            if (!string.IsNullOrEmpty(errormsg)) throw new Exception(errormsg);


        }

        public static string ValidateMobileNo(string mobileNo)
        {
            if (mobileNo.Length >= 10)
            {
                mobileNo = mobileNo.Substring(mobileNo.Length - 10);
                return "+880" + mobileNo;
            }
            return mobileNo;
        }
    }
}
