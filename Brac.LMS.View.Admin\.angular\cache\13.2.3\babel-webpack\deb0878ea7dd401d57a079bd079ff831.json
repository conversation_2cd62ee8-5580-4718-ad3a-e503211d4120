{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { BreadcrumbPagingRoutingModule } from './breadcrumb-paging-routing.module';\nimport { SharedModule } from '../../../../theme/shared/shared.module';\nimport { NgbButtonsModule, NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';\nimport * as i0 from \"@angular/core\";\nexport let BreadcrumbPagingModule = /*#__PURE__*/(() => {\n  class BreadcrumbPagingModule {}\n\n  BreadcrumbPagingModule.ɵfac = function BreadcrumbPagingModule_Factory(t) {\n    return new (t || BreadcrumbPagingModule)();\n  };\n\n  BreadcrumbPagingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: BreadcrumbPagingModule\n  });\n  BreadcrumbPagingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, BreadcrumbPagingRoutingModule, SharedModule, NgbButtonsModule, NgbPaginationModule]]\n  });\n  return BreadcrumbPagingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}