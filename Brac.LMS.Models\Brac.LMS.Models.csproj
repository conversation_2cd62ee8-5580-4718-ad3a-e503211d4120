﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{EC959511-D275-40BB-8E2C-DC414F4C31D5}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Brac.LMS.Models</RootNamespace>
    <AssemblyName>Brac.LMS.Models</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Core.2.2.3\lib\net45\Microsoft.AspNet.Identity.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.EntityFramework, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.EntityFramework.2.2.3\lib\net45\Microsoft.AspNet.Identity.EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AccountModel.cs" />
    <Compile Include="AuditLog.cs" />
    <Compile Include="CertificateConfiguration.cs" />
    <Compile Include="CourseCategory.cs" />
    <Compile Include="CourseSegment.cs" />
    <Compile Include="CourseViewModel.cs" />
    <Compile Include="ExternalCourse.cs" />
    <Compile Include="Feedback.cs" />
    <Compile Include="CourseMockTest.cs" />
    <Compile Include="Enums.cs" />
    <Compile Include="EvaluationExam.cs" />
    <Compile Include="Forum.cs" />
    <Compile Include="GhooriCertificate.cs" />
    <Compile Include="LhCategory.cs" />
    <Compile Include="Library.cs" />
    <Compile Include="LibraryCategory.cs" />
    <Compile Include="NotificationEvent.cs" />
    <Compile Include="OpenMaterial.cs" />
    <Compile Include="Department.cs" />
    <Compile Include="FAQ.cs" />
    <Compile Include="Notification.cs" />
    <Compile Include="NotificationEmail.cs" />
    <Compile Include="PendingNotification.cs" />
    <Compile Include="SubUnit.cs" />
    <Compile Include="TraineeCertificate.cs" />
    <Compile Include="EvaluationActivity.cs" />
    <Compile Include="TraineeCourseExamConfiguration.cs" />
    <Compile Include="TraineeCourseRating.cs" />
    <Compile Include="Configuration.cs" />
    <Compile Include="Course.cs" />
    <Compile Include="CourseBookmark.cs" />
    <Compile Include="CourseDiscussion.cs" />
    <Compile Include="CourseEnrollment.cs" />
    <Compile Include="CourseExam.cs" />
    <Compile Include="CourseMaterial.cs" />
    <Compile Include="ForumCategory.cs" />
    <Compile Include="Division.cs" />
    <Compile Include="Entity.cs" />
    <Compile Include="ExamQuestion.cs" />
    <Compile Include="ForgetPasswordModel.cs" />
    <Compile Include="GradingPolicy.cs" />
    <Compile Include="IdentityModel.cs" />
    <Compile Include="MaterialResource.cs" />
    <Compile Include="Trainee.cs" />
    <Compile Include="TraineeCourseActivity.cs" />
    <Compile Include="TraineeDevice.cs" />
    <Compile Include="TraineeEvaluationExam.cs" />
    <Compile Include="TraineeExam.cs" />
    <Compile Include="TraineeExamAnswer.cs" />
    <Compile Include="TraineeGroup.cs" />
    <Compile Include="TraineeMockTest.cs" />
    <Compile Include="Unit.cs" />
    <Compile Include="UserGroup.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Brac.LMS.Common\Brac.LMS.Common.csproj">
      <Project>{54ADBAFF-7A77-47FB-B7D3-7E335616A868}</Project>
      <Name>Brac.LMS.Common</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" />
</Project>