﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using Brac.LMS.DB;
using Microsoft.AspNet.Identity.Owin;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [RoutePrefix("api/course-category")]
    public class CourseCategoryController : ApplicationController
    {
        private readonly ICourseCategoryService _service;

        public CourseCategoryController()
        {
            _service = new CourseCategoryService();
        }
        public ApplicationUserManager UserManager
        {
            get
            {
                return Request.GetOwinContext().GetUserManager<ApplicationUserManager>();
            }
        }

        [Authorize(Roles = "Admin"), HttpPost, Route("create-or-update")]
        public async Task<IHttpActionResult> CategoryCreateOrUpdate(CategoryModel model)
        {
            var _nservice = new CourseCategoryService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.CategoryCreateOrUpdate(model, CurrentUser));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("list")]
        public async Task<IHttpActionResult> GetCategoryList(int size, int pageNumber)
        {
            return Ok(await _service.GetCategoryList(size, pageNumber));
        }

        [Authorize(Roles = "Admin, Trainee, Guest"), HttpGet, Route("dropdown-list")]
        public async Task<IHttpActionResult> GetCategoryDropDownList()
        {
            return Ok(await _service.GetCategoryDropDownList());
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get/{id}")]
        public async Task<IHttpActionResult> GetCourseById(long id)
        {
            return Ok(await _service.GetCategoryById(id));
        }
    }
}
