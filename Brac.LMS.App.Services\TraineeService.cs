﻿using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;

using ClosedXML.Excel;

using DocumentFormat.OpenXml.Math;

using FastReport;
using FastReport.Export.Pdf;

using iTextSharp.text;
using iTextSharp.text.pdf;

using Microsoft.AspNet.Identity;
using Microsoft.Owin;

using Newtonsoft.Json;
using SendEmailApp;

using System;
using System.Collections.Generic;
//using System.Data;
using System.Data.Entity;
using System.Data.Entity.Validation;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;
using System.Web.Hosting;

using Image = iTextSharp.text.Image;
using Paragraph = iTextSharp.text.Paragraph;

namespace Brac.LMS.App.Services
{
    public class TraineeService : ITraineeService
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditLogHelper _auditLogHelper;
        private readonly AuditLog audit;
        private readonly ApplicationUser _appUser;
        private readonly ApplicationUserManager _usermanager;

        public TraineeService()
        {
            _context = new ApplicationDbContext();
        }
        public TraineeService(string controller, string method, ApplicationUser applicationUser)
        {
            _auditLogHelper = new AuditLogHelper(controller, method);
            audit = _auditLogHelper.auditLog;
            _appUser = applicationUser;
            audit.CreatedBy = _appUser.Id;

            _context = new ApplicationDbContext();
        }
        public async Task<APIResponse> GetTraineeList(string name, long? divisionId, long? departmentId, int size, int pageNumber)
        {
            try
            {
                //var query = _context.Trainees.Where(x => x.TraineeType == TraineeType.Permanent).AsQueryable();
                var query = _context.Trainees.AsQueryable();
                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Name.Contains(name) || x.Email.Contains(name) || x.PhoneNo.Contains(name) || x.PIN.Contains(name));
                if (departmentId.HasValue) query = query.Where(x => x.DepartmentId == departmentId.Value);
                if (divisionId.HasValue) query = query.Where(x => x.DivisionId == divisionId.Value);
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderBy(x => x.Name)
                            .Skip(pageNumber * size).Take(size);
                var data = await filteredQuery
                    .Join(_context.Users, x => x.UserId, y => y.Id, (x, y) => new
                    {
                        Trainee = x,
                        User = y
                    })
                    .Select(x => new { x.Trainee.Id, x.Trainee.PIN, x.Trainee.Name, x.Trainee.Email, x.Trainee.PhoneNo, x.User.ImagePath, x.Trainee.Active, x.User.LastLogOn, Department = x.Trainee.Department != null ? x.Trainee.Department.Name : "", Division = x.Trainee.Division.Name, x.Trainee.Position }).ToListAsync();

                var count = await ((((!string.IsNullOrEmpty(name)) || (divisionId != null) || (departmentId != null)) || ((!string.IsNullOrEmpty(name)) && (departmentId != null && (departmentId != null)))) ? filteredQuery.CountAsync() : _context.Trainees.CountAsync());

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data, Total = count }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }

        }

        public async Task<APIResponse> GetTraineeById(Guid id)
        {
            try
            {
                var data = await _context.Trainees.Where(t => t.Id == id)
                .Select(t => new
                {
                    t.Id,
                    t.Name,
                    t.Email,
                    t.PIN,
                    t.PhoneNo,
                    t.Address,
                    t.WorkLocation,
                    t.LineManagerName,
                    t.LineManagerPIN,
                    t.Active,
                    Gender = t.Gender.ToString(),
                    Division = t.Division.Name,
                    t.DivisionId,
                    Department = t.Department.Name,
                    t.DepartmentId,
                    Unit = t.Unit.Name,
                    t.UnitId,
                    SubUnit = t.SubUnit.Name,
                    t.SubUnitId,
                    t.EmployeeType,
                    t.DateOfJoining,
                    t.Grade,
                    t.Position,
                }).FirstOrDefaultAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> CreateOrUpdateTrainee(TraineeModel model, IIdentity identity, ApplicationUserManager userManager)
        {
            bool isEdit = true;
            Trainee item = null;
            ApplicationUser user = null;
            IdentityResult result = null;
            try
            {
                if (await _context.Trainees.AnyAsync(x => x.Id != model.Id && x.Email == model.Email))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Email already exist"
                    };

                if (!model.Id.HasValue) // blocked trainee creation
                {
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Trainee not found"
                    };
                }

                if (model.Id.HasValue)
                {
                    item = await _context.Trainees.FindAsync(model.Id);
                    if (item == null) return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Trainee not found"
                    };
                }
                else
                {
                    item = new Trainee
                    {
                        Email = model.Email,
                        TraineeType = TraineeType.Permanent
                    };
                    //isEdit = false; // commented out to prevent trainee creation as brac current concern is only about information updation
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Failed on Trainee information updation!"
                    };
                }

                item.Name = model.Name;
                //item.DivisionId = model.DivisionId;
                //item.DepartmentId = model.DepartmentId;
                //item.Position = model.Position;
                item.PhoneNo = model.PhoneNo;
                item.Grade = model.Grade;
                item.Email = model.Email;
                //item.PIN = model.PIN;
                //item.Address = model.Address;
                //item.LineManagerName = model.LineManagerName;
                //item.LineManagerPIN = model.LineManagerPIN;
                //item.WorkLocation = model.WorkLocation;
                //item.Active = model.Active;
                //item.Gender = model.Gender;

                using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    try
                    {
                        if (isEdit)
                        {
                            user = await userManager.FindByIdAsync(item.UserId);
                            if (user == null) return new APIResponse
                            {
                                Status = ResponseStatus.Warning,
                                Message = "User not found for this employee"
                            };

                            user.Email = model.Email;
                            //user.UserName = model.PIN;
                            user.FirstName = item.Name;
                            user.PhoneNumber = item.PhoneNo;
                            if (user.Active && !item.Active) user.DeActivateOn = DateTime.UtcNow;
                            user.Active = item.Active;
                            result = await userManager.UpdateAsync(user);
                            if (!result.Succeeded) return new APIResponse
                            {
                                Status = ResponseStatus.Warning,
                                Message = "Error on user update: " + string.Join(", ", result.Errors)
                            };

                            item.SetAuditTrailEntity(identity);
                            _context.Entry(item).State = EntityState.Modified;
                            await _context.SaveChangesAsync();
                        }
                        else
                        {
                            user = new ApplicationUser() { UserName = item.Email, PhoneNumber = item.PhoneNo, Email = item.Email, FirstName = item.Name, Active = item.Active, UserType = UserType.Trainee };

                            var password = Utility.RandomString(8);
                            userManager.PasswordValidator = new PasswordValidator
                            {
                                RequiredLength = 6,
                                RequireNonLetterOrDigit = false,
                                RequireDigit = false,
                                RequireLowercase = false,
                                RequireUppercase = false,
                            };
                            result = await userManager.CreateAsync(user, password);
                            if (!result.Succeeded) return new APIResponse
                            {
                                Status = ResponseStatus.Warning,
                                Message = @"Error on user creation: - " + string.Join(" - ", result.Errors)
                            };

                            //result = await userManager.AddToRoleAsync(user.Id, "Trainee");
                            //if (!result.Succeeded) throw new Exception("Error on user role assign: <br/>" + string.Join("<br/>", result.Errors));
                            item.UserId = user.Id;
                            item.SetAuditTrailEntity(identity);

                            item.Id = Guid.NewGuid();
                            _context.Trainees.Add(item);
                            await _context.SaveChangesAsync();

                            var institute = await _context.Configurations.Select(x => x.Name).FirstOrDefaultAsync();

                            string appUrl = System.Configuration.ConfigurationManager.AppSettings["TraineeUrl"];
                            var em = new MailSendService();
                            using (StreamReader reader = File.OpenText(HostingEnvironment.MapPath("~/Files/EmailTemplates/account-create.html"))) // Path to your 
                            {                                                         // HTML file
                                StringBuilder sb = new StringBuilder(reader.ReadToEnd());
                                sb = sb.Replace("#institute#", institute);
                                sb = sb.Replace("#account-type#", "E-Learning Solution - Trainee");
                                sb = sb.Replace("#link#", appUrl);
                                sb = sb.Replace("#full-name#", user.FirstName + " " + user.LastName);
                                sb = sb.Replace("#username#", user.UserName);
                                sb = sb.Replace("#password#", password);
                                em.SendMail(user.Email, null, null, "Your E-Learning Solution Trainee account has been created", sb.ToString());
                            }
                        }
                        scope.Complete();
                    }
                    catch (Exception ex)
                    {
                        //await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                        scope.Dispose();
                        return new APIResponse
                        {
                            Status = ResponseStatus.Error,
                            Message = ex.Message
                        };
                    }
                }
                //await _auditLogHelper.AddSuccessAudit(audit, null, _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                };

            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                //await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())
                };
            }
            catch (Exception ex)
            {
                //await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }

        }

        public async Task<APIResponse> GetTraineeDropDownList()
        {
            try
            {
                var data = await _context.Trainees.Where(x => x.Active && x.TraineeType == TraineeType.Permanent).OrderBy(o => o.Name)
                .Select(t => new
                {
                    t.Id,
                    t.Name,
                    t.User.ImagePath,
                    t.UserId,
                    t.PhoneNo,
                    t.PIN
                }).ToListAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data, }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }

        }

        public async Task<byte[]> GetTraineeListExcel(string name, long? divisionId, long? departmentId, TraineeType traineeType = TraineeType.Permanent)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            try
            {
                string division = string.Empty, department = string.Empty;
                IQueryable<Trainee> query = _context.Trainees.Where(x => x.TraineeType == traineeType);
                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Name.Contains(name) || x.Email.Contains(name) || x.PIN.Contains(name) || x.PhoneNo.Contains(name));
                if (divisionId.HasValue)
                {
                    query = query.Where(x => x.DivisionId == divisionId);
                    division = await _context.Divisions.Where(x => x.Id == divisionId).Select(x => x.Name).FirstOrDefaultAsync();
                }
                if (departmentId.HasValue)
                {
                    query = query.Where(x => x.DepartmentId == departmentId);
                    department = await _context.Departments.Where(x => x.Id == departmentId).Select(x => x.Name).FirstOrDefaultAsync();
                }

                var data = await query.Select(x => new { x.Id, x.PIN, x.Name, x.Email, x.PhoneNo, Division = x.Division.Name, Department = x.Department.Name, x.Active }).OrderBy(x => x.Name).ToListAsync();

                var headerColumns = new List<string> { "Trainee No", "Name", "Email", "Phone No.", "Division", "Department", "Active" };

                ExcelManager.GetTextLineElement("Trainee List", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                ExcelManager.GetTextLineElement("Name/Email: " + (name ?? "-") + " &  Division: " + (division ?? "-") + " &  Department: " + (department ?? "-"), ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                ExcelManager.GetTextLineElement("Report Generation Time: " + DateTime.Now.ToString("dd-MMM-yyyy HH:mm:ss tt \"GMT\"zzz"), ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                rowNo++;

                ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);

                foreach (var item in data)
                {
                    rowNo++;
                    colNo = 1;
                    ExcelManager.GetTableDataCell(item.PIN, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                    ExcelManager.GetTableDataCell(item.Name, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                    ExcelManager.GetTableDataCell(item.Email, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                    ExcelManager.GetTableDataCell(item.PhoneNo, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                    ExcelManager.GetTableDataCell(item.Division, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                    ExcelManager.GetTableDataCell(item.Department, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                    ExcelManager.GetTableDataCell(item.Active, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                }

                for (int i = 0; i < headerColumns.Count; i++)
                {
                    ws.Column(i + 1).AdjustToContents();
                }

                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
            catch (Exception ioex)
            {
                ExcelManager.GetTextLineElement("Excel File Generation Error: " + ioex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
        }

        public async Task<APIResponse> QueryTrainee(string query, int limit)
        {
            try
            {
                var data = await _context.Trainees.Where(x => x.Active && (x.Name.Contains(query) || x.PIN.Contains(query)) && (x.TraineeType == TraineeType.Permanent || x.TraineeType == TraineeType.Guest))
                    .OrderBy(o => o.Name).Take(limit)
                    .Select(t => new
                    {
                        t.Id,
                        t.Name,
                        t.PIN,
                        t.User.ImagePath,
                        t.UserId,
                        t.Position,
                        Division = t.Division.Name
                    }).ToListAsync();
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }


        }

        public async Task<APIResponse> QueryTraineeByExcel()
        {
            try
            {
                if (HttpContext.Current.Request.Files.Count > 0)
                {
                    HttpPostedFile file = HttpContext.Current.Request.Files[0];
                    var fileData = ExcelParser.Parse(file);

                    var header = fileData.Item1;
                    var lines = fileData.Item2.ToList();

                    var indexes = new Dictionary<int, ColumnType>
                        {
                            {0, ColumnType.String }
                        };

                    await Validator.VerifyUploadFileValue(lines, indexes);
                    var traineePins = lines.Select(x => x[0]).Distinct().ToList();

                    var data = await _context.Trainees.Where(x => x.Active && traineePins.Contains(x.PIN))
                    .OrderBy(o => o.Name)
                    .Select(t => new
                    {
                        t.Id,
                        t.Name,
                        t.PIN,
                        t.User.ImagePath,
                        t.UserId,
                        t.Position,
                        Division = t.Division.Name
                    }).ToListAsync();

                    string notFoundPINs = null;

                    if (data.Count != traineePins.Count)
                    {
                        var foundPINs = data.Select(x => x.PIN).ToList();
                        notFoundPINs = string.Join(", ", traineePins.Where(x => !foundPINs.Contains(x)));
                    }
                    await _auditLogHelper.AddSuccessAudit(audit, null, _context);
                    return new APIResponse
                    {
                        Status = ResponseStatus.Success,
                        Data = data,
                        Message = notFoundPINs
                    };
                }
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "No excel file provided"
                };
            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }


        }


        //public async Task<APIResponse> CourseRatingSave(RatingSaveModel model, ApplicationUser user)
        //{
        //    try
        //    {
        //        var isEdit = true;

        //        var course = await _context.Courses.FindAsync(model.CourseId);
        //        if (course == null) return new APIResponse
        //        {
        //            Status = ResponseStatus.Warning,
        //            Message = "Course not found"
        //        };

        //        var item = await _context.TraineeCourseRatings.FirstOrDefaultAsync(x => x.TraineeId == user.Trainee.Id && x.CourseId == model.CourseId);

        //        if (item == null)
        //            item = new TraineeCourseRating
        //            {
        //                TraineeId = user.Trainee.Id,
        //                CourseId = model.CourseId
        //            };
        //        else
        //        {
        //            course.TotalRatings -= item.Rating;
        //            course.NoOfRating--;
        //        }

        //        item.Rating = model.Rating;
        //        item.Review = model.Review;

        //        using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
        //        {
        //            try
        //            {
        //                if (item.Id > 0)
        //                {
        //                    item.RatingModifiedDate = DateTime.UtcNow;
        //                    _context.Entry(item).State = EntityState.Modified;
        //                }
        //                else
        //                {
        //                    isEdit = false;
        //                    item.RatingDate = DateTime.UtcNow;
        //                    _context.TraineeCourseRatings.Add(item);
        //                }

        //                course.UpdateRating(item.Rating);
        //                _context.Entry(course).State = EntityState.Modified;

        //                await _context.SaveChangesAsync();

        //                scope.Complete();
        //            }
        //            catch (Exception ex)
        //            {
        //                scope.Dispose();
        //                return new APIResponse
        //                {
        //                    Status = ResponseStatus.Error,
        //                    Message = ex.Message
        //                };
        //            }
        //        }
        //        return new APIResponse
        //        {
        //            Status = ResponseStatus.Success,
        //            Data = new
        //            {
        //                Message = "Your rating is" + (isEdit ? " updated" : " saved")
        //            }
        //        };

        //    }
        //    catch (Exception ex)
        //    {
        //        return new APIResponse
        //        {
        //            Status = ResponseStatus.Error,
        //            Message = ex.Message
        //        };
        //    }
        //}




        public async Task<APIResponse> UpdatePhoto(IIdentity identity, ApplicationUserManager userManager)
        {
            ApplicationUser user = null;
            try
            {
                user = await userManager.FindByNameAsync(identity.Name);
                if (user == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "User info not found"
                };

                var pin = await _context.Trainees.Where(x => x.UserId == user.Id).Select(x => x.PIN).FirstOrDefaultAsync();
                if (pin == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Trainee not found"
                };


                if (HttpContext.Current.Request.Files.Count > 0)
                    user.ImagePath = Utility.SaveImage(pin + "_" + Utility.RandomString(3, false), "/Images/Trainee/", HttpContext.Current.Request.Files[0], user.ImagePath, 230, 230);
                else user.ImagePath = Utility.RemoveImage(user.ImagePath);


                var result = await userManager.UpdateAsync(user);
                if (!result.Succeeded) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Error on user update: " + string.Join(", ", result.Errors)
                };
                //await _auditLogHelper.AddSuccessAudit(audit, null, _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = user.ImagePath != null ? "Image Successfully Uploaded" : "Image Successfully Removed",
                    Data = user.ImagePath
                };

            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                //await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())
                };
            }
            catch (Exception ex)
            {
                //await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }



        public async Task<byte[]> GetAnswerSheetPDF(Guid id)
        {
            var document = new Document(iTextSharp.text.PageSize.A4, 36, 36, 36, 36);
            var reportStream = new System.IO.MemoryStream();

            try
            {
                PdfWriter writer = PdfWriter.GetInstance(document, reportStream);
                writer.PageEvent = new PDFWriterEvents(true, true);
                writer.CloseStream = false;

                // Openning the Document
                document.Open();

                var data = await _context.TraineeExams.Where(x => x.Id == id && (x.Status == ExamStatus.Submitted || x.Status == ExamStatus.Examined))
                    .Select(x => new { x.Id, Course = x.Exam.Course.Title, x.Trainee.PIN, x.Trainee.Name, Division = x.Trainee.Division.Name, x.Trainee.PhoneNo, x.Exam.MCQOnly, x.TotalMarks, x.GainedMarks, x.StartDate, Status = x.Status.ToString() })
                    .FirstOrDefaultAsync();

                var mcqList = await _context.MCQAnswers.Where(x => x.TraineeExamId == data.Id).Select(x => new { x.Id, x.Question.Question, x.Question.Option1, x.Question.Option2, x.Question.Option3, x.Question.Option4, x.Question.Answers, QMark = x.Question.Mark, x.Mark, x.Answered }).ToListAsync();

                var tfqList = await _context.TrueFalseAnswers.Where(x => x.TraineeExamId == data.Id).Select(x => new { x.Id, x.Question.Question, x.Question.Answer, QMark = x.Question.Mark, x.Mark, x.Answered }).ToListAsync();

                var figqList = await _context.FIGAnswers.Where(x => x.TraineeExamId == data.Id).Select(x => new { x.Id, x.Question.Question, x.Question.Answer, QMark = x.Question.Mark, x.Mark, x.Answered }).ToListAsync();

                var matchingqList = await _context.MatchingAnswers.Where(x => x.TraineeExamId == data.Id).Select(x => new { x.Id, x.Question.LeftSide, x.Question.RightSide, QMark = x.Question.Mark, x.Mark, Answered = x.RightSide }).ToListAsync();

                var writtenQList = await _context.WrittenAnswers.Where(x => x.TraineeExamId == data.Id).Select(x => new { x.Id, x.Question.Question, QMark = x.Question.Mark, x.Mark, x.Answered }).ToListAsync();

                var institute = await _context.Configurations.Select(x => new { x.Name, x.Address, x.LogoPath }).FirstOrDefaultAsync();

                #region Header
                var headerTbl = new PdfPTable(2) { WidthPercentage = 100 };
                headerTbl.SetWidths(new[] { 30, 70 });

                Bitmap logo = new Bitmap(HostingEnvironment.MapPath("~") + institute.LogoPath);
                headerTbl.AddCell(PDFManager.GetTableImageCell(logo));
                headerTbl.AddCell(PDFManager.GetPDFDataCellWithContent(new PdfPTable[] {
                    PDFManager.GetTextLineElement(institute.Name, 18f, alignment: PDFAlignment.Right, leftToRightPadding: new float[] { 0, 10, 0, 0 })
                    }));
                document.Add(headerTbl);
                #endregion


                document.Add(PDFManager.GetTextLineElement("Answer Sheet", 18f, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 15, 0, 15 }, isUnderlined: true));
                document.Add(PDFManager.GetTextLineElement(" ", 20));

                #region Header
                var table = new PdfPTable(4) { WidthPercentage = 100 };
                table.SetWidths(new[] { 10, 40, 12, 38 });
                table.AddCell(PDFManager.GetPDFDataCell("Trainee", borderLess: true, isBold: true, fontSize: 10f, leftToRightBorder: new float[] { 0, 0.3f, 0, 0.3f }, borderColor: "#999999", leftToRightPadding: new float[] { 0, 8, 0, 8 }));
                table.AddCell(PDFManager.GetPDFDataCell(": " + data.PIN + " - " + data.Name, borderLess: true, fontSize: 10f, leftToRightBorder: new float[] { 0, 0.3f, 0, 0.3f }, borderColor: "#999999", leftToRightPadding: new float[] { 0, 8, 0, 8 }));
                table.AddCell(PDFManager.GetPDFDataCell("Division", borderLess: true, isBold: true, fontSize: 10f, leftToRightBorder: new float[] { 0, 0.3f, 0, 0.3f }, borderColor: "#999999", leftToRightPadding: new float[] { 0, 8, 0, 8 }));
                table.AddCell(PDFManager.GetPDFDataCell(": " + data.Division, borderLess: true, fontSize: 10f, leftToRightBorder: new float[] { 0, 0.3f, 0, 0.3f }, borderColor: "#999999", leftToRightPadding: new float[] { 0, 8, 0, 8 }));



                table.AddCell(PDFManager.GetPDFDataCell("Course", colspan: 2, borderLess: true, isBold: true, fontSize: 10f, leftToRightPadding: new float[] { 0, 8, 0, 8 }));
                table.AddCell(PDFManager.GetPDFDataCell(": " + data.Course, borderLess: true, fontSize: 10f, isBold: false, leftToRightPadding: new float[] { 0, 8, 0, 8 }));

                table.AddCell(PDFManager.GetPDFDataCell("Marks", borderLess: true, isBold: true, fontSize: 10f, leftToRightPadding: new float[] { 0, 8, 0, 8 }));
                table.AddCell(PDFManager.GetPDFDataCell(": " + data.GainedMarks + " out of " + data.TotalMarks, 3, borderLess: true, fontSize: 10f, isBold: false, leftToRightPadding: new float[] { 0, 8, 0, 8 }));
                document.Add(table);
                #endregion

                int counter = 0;

                PdfPTable tblQs, tblMarks;

                #region MCQ
                if (mcqList.Any())
                {
                    document.Add(PDFManager.GetTextLineElement("MCQ", 14f, alignment: PDFAlignment.Left, isBold: true, leftToRightPadding: new float[] { 0, 10, 0, 10 }, leftToRightBorder: new float[] { 0, 1, 0, 1 }, borderColor: "#999999"));
                    table = new PdfPTable(2) { WidthPercentage = 100 };
                    table.SetWidths(new[] { 95, 5 });

                    Bitmap box = new Bitmap(System.Web.Hosting.HostingEnvironment.MapPath("~/App_Data/box.png"));
                    Bitmap checkedBox = new Bitmap(System.Web.Hosting.HostingEnvironment.MapPath("~/App_Data/checked_box.jpg"));

                    string[] answers;
                    counter = 0;
                    foreach (var item in mcqList)
                    {
                        tblQs = new PdfPTable(4) { WidthPercentage = 100 };
                        tblQs.SetWidths(new[] { 3.5f, 46.5f, 3.5f, 46.5f });

                        tblMarks = new PdfPTable(1) { WidthPercentage = 100 };
                        tblMarks.SetWidths(new[] { 100 });

                        counter++;

                        answers = item.Answered.Split(',');
                        tblQs.AddCell(PDFManager.GetPDFDataCell(counter + ". " + item.Question, 4, borderLess: true, isBold: true, fontSize: 10f));
                        tblQs.AddCell(PDFManager.GetTableImageCell(answers.Contains("1") ? checkedBox : box));
                        tblQs.AddCell(PDFManager.GetPDFDataCell("a. " + item.Option1, borderLess: true, fontSize: 10f));
                        tblQs.AddCell(PDFManager.GetTableImageCell(answers.Contains("2") ? checkedBox : box));
                        tblQs.AddCell(PDFManager.GetPDFDataCell("b. " + item.Option2, borderLess: true, fontSize: 10f));
                        tblQs.AddCell(PDFManager.GetTableImageCell(answers.Contains("3") ? checkedBox : box));
                        tblQs.AddCell(PDFManager.GetPDFDataCell("c. " + item.Option3, borderLess: true, fontSize: 10f));
                        tblQs.AddCell(PDFManager.GetTableImageCell(answers.Contains("4") ? checkedBox : box));
                        tblQs.AddCell(PDFManager.GetPDFDataCell("d. " + item.Option4, borderLess: true, fontSize: 10f));

                        tblMarks.AddCell(PDFManager.GetPDFDataCell("[" + item.QMark + "]", 4, borderLess: true, isBold: true, fontSize: 10f, alignment: PDFAlignment.Right, leftToRightPadding: new float[] { 0, 5f, 0, 10f }));
                        tblMarks.AddCell(PDFManager.GetPDFDataCell(item.Mark, 4, borderLess: false, sideBorderLess: false, isBold: false, fontSize: 10f, alignment: PDFAlignment.Right, leftToRightPadding: new float[] { 3f, 3f, 3f, 3f }));

                        table.AddCell(PDFManager.GetPDFDataCellWithContent(new PdfPTable[] { tblQs }));
                        table.AddCell(PDFManager.GetPDFDataCellWithContent(new PdfPTable[] { tblMarks }));
                    }

                    document.Add(table);
                }
                #endregion

                #region True/False
                if (tfqList.Any())
                {
                    document.Add(PDFManager.GetTextLineElement("True/False Question", 14f, alignment: PDFAlignment.Left, isBold: true, leftToRightPadding: new float[] { 0, 10, 0, 10 }, leftToRightBorder: new float[] { 0, 1, 0, 1 }, borderColor: "#999999"));
                    table = new PdfPTable(2) { WidthPercentage = 100 };
                    table.SetWidths(new[] { 95, 5 });

                    counter = 0;
                    foreach (var item in tfqList)
                    {
                        tblQs = new PdfPTable(2) { WidthPercentage = 100 };
                        tblQs.SetWidths(new[] { 7, 93 });

                        tblMarks = new PdfPTable(1) { WidthPercentage = 100 };
                        tblMarks.SetWidths(new[] { 100 });

                        counter++;

                        tblQs.AddCell(PDFManager.GetPDFDataCell(counter + ". " + item.Question, 2, borderLess: true, isBold: true, fontSize: 10f));
                        tblQs.AddCell(PDFManager.GetPDFDataCell("Ans: ", borderLess: true, isBold: true, fontSize: 10f));
                        tblQs.AddCell(PDFManager.GetPDFDataCell(item.Answered ? "True" : "False", borderLess: true, isBold: false, fontSize: 10f));

                        tblMarks.AddCell(PDFManager.GetPDFDataCell("[" + item.QMark + "]", 2, borderLess: true, isBold: true, fontSize: 10f, alignment: PDFAlignment.Right, leftToRightPadding: new float[] { 0, 5f, 0, 5f }));
                        tblMarks.AddCell(PDFManager.GetPDFDataCell(item.Mark, 2, borderLess: false, sideBorderLess: false, isBold: false, fontSize: 10f, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 3f, 3f, 3f, 3f }));

                        table.AddCell(PDFManager.GetPDFDataCellWithContent(new PdfPTable[] { tblQs }));
                        table.AddCell(PDFManager.GetPDFDataCellWithContent(new PdfPTable[] { tblMarks }));
                    }

                    document.Add(table);
                }
                #endregion

                #region Fill in the gap Questions
                if (figqList.Any())
                {
                    document.Add(PDFManager.GetTextLineElement("Fill in the gap Questions", 14f, alignment: PDFAlignment.Left, isBold: true, leftToRightPadding: new float[] { 0, 15, 0, 10 }, leftToRightBorder: new float[] { 0, 1, 0, 1 }, borderColor: "#999999"));
                    table = new PdfPTable(2) { WidthPercentage = 100 };
                    table.SetWidths(new[] { 95, 5 });

                    counter = 0;
                    foreach (var item in figqList)
                    {
                        tblQs = new PdfPTable(2) { WidthPercentage = 100 };
                        tblQs.SetWidths(new[] { 7, 93 });

                        tblMarks = new PdfPTable(1) { WidthPercentage = 100 };
                        tblMarks.SetWidths(new[] { 100 });

                        counter++;

                        tblQs.AddCell(PDFManager.GetPDFDataCell(counter + ". " + item.Question, 2, borderLess: true, isBold: true, fontSize: 10f));
                        tblQs.AddCell(PDFManager.GetPDFDataCell("Ans: ", borderLess: true, isBold: true, fontSize: 10f));
                        tblQs.AddCell(PDFManager.GetPDFDataCell(item.Answered, borderLess: true, isBold: false, fontSize: 10f));

                        tblMarks.AddCell(PDFManager.GetPDFDataCell("[" + item.QMark + "]", 2, borderLess: true, isBold: true, fontSize: 10f, alignment: PDFAlignment.Right, leftToRightPadding: new float[] { 0, 5f, 0, 5f }));
                        tblMarks.AddCell(PDFManager.GetPDFDataCell(item.Mark, 2, borderLess: false, sideBorderLess: false, isBold: false, fontSize: 10f, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 3f, 3f, 3f, 3f }));

                        table.AddCell(PDFManager.GetPDFDataCellWithContent(new PdfPTable[] { tblQs }));
                        table.AddCell(PDFManager.GetPDFDataCellWithContent(new PdfPTable[] { tblMarks }));
                    }

                    document.Add(table);
                }
                #endregion

                #region Matching Questions
                if (matchingqList.Any())
                {
                    document.Add(PDFManager.GetTextLineElement("Matching Questions", 14f, alignment: PDFAlignment.Left, isBold: true, leftToRightPadding: new float[] { 0, 15, 0, 10 }, leftToRightBorder: new float[] { 0, 1, 0, 1 }, borderColor: "#999999"));
                    table = new PdfPTable(2) { WidthPercentage = 100 };
                    table.SetWidths(new[] { 95, 5 });

                    counter = 0;
                    foreach (var item in matchingqList)
                    {
                        tblQs = new PdfPTable(2) { WidthPercentage = 100 };
                        tblQs.SetWidths(new[] { 7, 93 });

                        tblMarks = new PdfPTable(1) { WidthPercentage = 100 };
                        tblMarks.SetWidths(new[] { 100 });

                        counter++;

                        tblQs.AddCell(PDFManager.GetPDFDataCell(counter + ". " + item.LeftSide, 2, borderLess: true, isBold: true, fontSize: 10f));
                        tblQs.AddCell(PDFManager.GetPDFDataCell("Right Side: ", borderLess: true, isBold: true, fontSize: 10f));
                        tblQs.AddCell(PDFManager.GetPDFDataCell(item.Answered, borderLess: true, isBold: false, fontSize: 10f));

                        tblMarks.AddCell(PDFManager.GetPDFDataCell("[" + item.QMark + "]", 2, borderLess: true, isBold: true, fontSize: 10f, alignment: PDFAlignment.Right, leftToRightPadding: new float[] { 0, 5f, 0, 5f }));
                        tblMarks.AddCell(PDFManager.GetPDFDataCell(item.Mark, 2, borderLess: false, sideBorderLess: false, isBold: false, fontSize: 10f, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 3f, 3f, 3f, 3f }));

                        table.AddCell(PDFManager.GetPDFDataCellWithContent(new PdfPTable[] { tblQs }));
                        table.AddCell(PDFManager.GetPDFDataCellWithContent(new PdfPTable[] { tblMarks }));
                    }

                    document.Add(table);
                }
                #endregion

                #region Written Questions
                if (writtenQList.Any())
                {
                    document.Add(PDFManager.GetTextLineElement("Written Questions", 14f, alignment: PDFAlignment.Left, isBold: true, leftToRightPadding: new float[] { 0, 15, 0, 10 }, leftToRightBorder: new float[] { 0, 1, 0, 1 }, borderColor: "#999999"));
                    table = new PdfPTable(2) { WidthPercentage = 100 };
                    table.SetWidths(new[] { 95, 5 });

                    counter = 0;
                    foreach (var item in writtenQList)
                    {
                        tblQs = new PdfPTable(2) { WidthPercentage = 100 };
                        tblQs.SetWidths(new[] { 7, 93 });

                        tblMarks = new PdfPTable(1) { WidthPercentage = 100 };
                        tblMarks.SetWidths(new[] { 100 });

                        counter++;

                        tblQs.AddCell(PDFManager.GetPDFDataCell(counter + ". " + item.Question, 2, borderLess: true, isBold: true, fontSize: 10f));
                        tblQs.AddCell(PDFManager.GetPDFDataCell("Ans: ", borderLess: true, isBold: true, fontSize: 10f));
                        tblQs.AddCell(PDFManager.GetPDFDataCell(item.Answered, borderLess: true, isBold: false, fontSize: 10f));

                        tblMarks.AddCell(PDFManager.GetPDFDataCell("[" + item.QMark + "]", 2, borderLess: true, isBold: true, fontSize: 10f, alignment: PDFAlignment.Right, leftToRightPadding: new float[] { 0, 5f, 0, 5f }));
                        tblMarks.AddCell(PDFManager.GetPDFDataCell(item.Mark, 2, borderLess: false, sideBorderLess: false, isBold: false, fontSize: 10f, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 3f, 3f, 3f, 3f }));

                        table.AddCell(PDFManager.GetPDFDataCellWithContent(new PdfPTable[] { tblQs }));
                        table.AddCell(PDFManager.GetPDFDataCellWithContent(new PdfPTable[] { tblMarks }));
                    }

                    document.Add(table);
                }
                #endregion

                document.Close();

                return reportStream.ToArray();
            }
            catch (DocumentException dex)
            {
                document.Add(PDFManager.GetTextLineElement(dex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
            // Catching System.IO.IOException if any
            catch (Exception ioex)
            {
                document.Add(PDFManager.GetTextLineElement(ioex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
        }

        public async Task<byte[]> GetEnrolledTraineeListExcel(Guid courseId, long? divisionId)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            try
            {

                string designation = string.Empty, course = string.Empty;

                course = await _context.Courses.Where(x => x.Id == courseId).Select(x => x.Title).FirstOrDefaultAsync();
                IQueryable<CourseEnrollment> query = _context.CourseEnrollments.Where(x => x.CourseId == courseId && x.Trainee.Active && x.Trainee.TraineeType == TraineeType.Permanent);
                if (divisionId.HasValue)
                {
                    query = query.Where(x => x.Trainee.DivisionId == divisionId);
                    //designation = await _context.Divisions.Where(x => x.Id == divisionId).Select(x => x.Name).FirstOrDefaultAsync();
                }


                var data = await query.Select(x => new { x.Id, x.Trainee.PIN, x.Trainee.Name, Department = x.Trainee.Department.Name, Division = x.Trainee.Division.Name, x.Trainee.PhoneNo, x.Trainee.Email }).OrderBy(x => x.Name).ToListAsync();

                var headerColumns = new List<string> { "Trainee No", "Name", "Division", "Email", "Phone No.", "Department" };

                ExcelManager.GetTextLineElement("Enrolled Trainee List", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                ExcelManager.GetTextLineElement("Course: " + course, ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                ExcelManager.GetTextLineElement("Report Generation Time: " + DateTime.Now.ToString("dd-MMM-yyyy HH:mm:ss tt \"GMT\"zzz"), ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                rowNo++;



                if (data.Count <= 0)
                {
                    ExcelManager.GetTextLineElement("No Enrolled Trainee found for this course", ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                }
                else
                {
                    ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);
                    foreach (var item in data)
                    {
                        rowNo++;
                        colNo = 1;
                        ExcelManager.GetTableDataCell(item.PIN, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                        ExcelManager.GetTableDataCell(item.Name, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                        ExcelManager.GetTableDataCell(item.Division, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        ExcelManager.GetTableDataCell(item.Email, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                        ExcelManager.GetTableDataCell(item.PhoneNo, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                        ExcelManager.GetTableDataCell(!string.IsNullOrEmpty(item.Department) ? item.Department : "None", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                    }

                    for (int i = 0; i < headerColumns.Count; i++)
                    {
                        ws.Column(i + 1).AdjustToContents();
                    }
                }


                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
            catch (Exception ioex)
            {
                ExcelManager.GetTextLineElement("Excel File Generation Error: " + ioex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
        }

        #region Guest Trainee
        public async Task<APIResponse>  CreateOrUpdateGuestTrainee(TraineeModel model, IIdentity identity, ApplicationUserManager userManager)
        {
            bool isEdit = true;
            Trainee item = null;
            ApplicationUser user = null;
            IdentityResult result = null;
            try
            {
                if (await _context.Trainees.AnyAsync(x => x.Id != model.Id && x.Email == model.Email))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Email already exist"
                    };

                if (model.Id.HasValue)
                {
                    item = await _context.Trainees.FindAsync(model.Id);
                    if (item == null) return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Trainee not found"
                    };
                }
                else
                {
                    item = new Trainee
                    {
                        Email = model.Email,
                        TraineeType = TraineeType.Guest
                    };
                    isEdit = false;
                }

                item.Name = model.Name;
                item.DivisionId = model.DivisionId;
                item.DepartmentId = model.DepartmentId;
                item.Position = model.Position;
                item.PhoneNo = model.PhoneNo;
                item.PIN = model.PIN;
                item.Address = model.Address;
                item.LineManagerName = model.LineManagerName;
                item.LineManagerPIN = model.LineManagerPIN;
                item.WorkLocation = model.WorkLocation;
                item.Active = model.Active;
                item.Gender = model.Gender;
                item.Grade = model.Grade;

                using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    try
                    {
                        if (isEdit)
                        {
                            user = await userManager.FindByIdAsync(item.UserId);
                            if (user == null) return new APIResponse
                            {
                                Status = ResponseStatus.Warning,
                                Message = "User not found for this trainee"
                            };

                            user.Email = model.Email;
                            user.UserName = model.Email;
                            user.FirstName = item.Name;
                            user.PhoneNumber = item.PhoneNo;
                            if (user.Active && !item.Active) user.DeActivateOn = DateTime.UtcNow;
                            user.Active = item.Active;
                            result = await userManager.UpdateAsync(user);
                            if (!result.Succeeded) return new APIResponse
                            {
                                Status = ResponseStatus.Warning,
                                Message = "Error on user update: " + string.Join(", ", result.Errors)
                            };

                            item.SetAuditTrailEntity(identity);
                            _context.Entry(item).State = EntityState.Modified;
                            await _context.SaveChangesAsync();
                        }
                        else
                        {
                            user = new ApplicationUser() { UserName = item.Email, PhoneNumber = item.PhoneNo, Email = item.Email, FirstName = item.Name, Active = item.Active, UserType = UserType.Guest };

                            userManager.PasswordValidator = new PasswordValidator
                            {
                                RequiredLength = 6,
                                RequireNonLetterOrDigit = false,
                                RequireDigit = false,
                                RequireLowercase = false,
                                RequireUppercase = false,
                            };

                            userManager.MaxFailedAccessAttemptsBeforeLockout = 3;
                            userManager.UserLockoutEnabledByDefault = true;
                            userManager.DefaultAccountLockoutTimeSpan = TimeSpan.FromMinutes(5);

                            result = await userManager.CreateAsync(user, user.PhoneNumber);
                            if (!result.Succeeded) return new APIResponse
                            {
                                Status = ResponseStatus.Warning,
                                Message = @"Error on user creation: - " + string.Join(" - ", result.Errors)
                            };

                            result = await userManager.AddToRoleAsync(user.Id, "Guest");
                            if (!result.Succeeded)
                            {
                                return new APIResponse
                                {
                                    Status = ResponseStatus.Warning,
                                    Message = @"Error on user role assign: - " + string.Join(", ", result.Errors)
                                };
                            }

                            item.UserId = user.Id;
                            item.SetAuditTrailEntity(identity);

                            item.Id = Guid.NewGuid();
                            _context.Trainees.Add(item);
                            await _context.SaveChangesAsync();
                        }
                        scope.Complete();
                    }
                    catch (Exception ex)
                    {
                        //await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                        scope.Dispose();
                        return new APIResponse
                        {
                            Status = ResponseStatus.Error,
                            Message = ex.Message
                        };
                    }
                }
                //await _auditLogHelper.AddSuccessAudit(audit, null, _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                };

            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                //await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())
                };
            }
            catch (Exception ex)
            {
                //await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }

        }

        public async Task<APIResponse> GetGuestTraineeList(string name, long? divisionId, long? departmentId, int size, int pageNumber)
        {
            try
            {
                var query = _context.Trainees.Where(x => x.TraineeType == TraineeType.Guest).AsQueryable();
                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Name.Contains(name) || x.Email.Contains(name) || x.PhoneNo.Contains(name) || x.PIN.Contains(name));
                if (departmentId.HasValue) query = query.Where(x => x.DepartmentId == departmentId.Value);
                if (divisionId.HasValue) query = query.Where(x => x.DivisionId == divisionId.Value);
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderBy(x => x.Name)
                           .Skip(pageNumber * size).Take(size);
                var data = await filteredQuery
                    .Join(_context.Users, x => x.UserId, y => y.Id, (x, y) => new
                    {
                        Trainee = x,
                        User = y
                    })
                    .Select(x => new { x.Trainee.Id, x.Trainee.PIN, x.Trainee.Name, x.Trainee.Email, x.Trainee.PhoneNo, x.User.ImagePath, x.Trainee.Active, x.User.LastLogOn, Department = x.Trainee.Department != null ? x.Trainee.Department.Name : "", Division = x.Trainee.Division.Name, x.Trainee.Position }).OrderBy(x => x.Name).ToListAsync();

                var count = await ((((!string.IsNullOrEmpty(name)) || (divisionId != null) || (departmentId != null)) || ((!string.IsNullOrEmpty(name)) && (departmentId != null && (departmentId != null)))) ? filteredQuery.CountAsync() : _context.Trainees.Where(x => x.TraineeType == TraineeType.Guest).CountAsync());

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data, Total = count }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }

        }

        //public async Task<APIResponse> UploadGuestTrainee(IIdentity identity, IOwinContext owinContext )
        public async Task<APIResponse> UploadGuestTrainee(IIdentity identity, ApplicationUserManager userManager)
        {
            try
            {
                HttpPostedFile file = HttpContext.Current.Request.Files[0];
                var data = ExcelParser.Parse(file);
                var header = data.Item1;
                var lines = data.Item2.ToList();
                string pin, email, phoneNo;

                var indexes = new Dictionary<int, ColumnType>
                {
                    {0, ColumnType.String },
                    {1, ColumnType.String },
                    {2, ColumnType.String },
                    {6, ColumnType.String },
                    {7, ColumnType.String },
                    {8, ColumnType.PhoneNumber }
                };

                await Validator.VerifyGuestTraineeUploadFileValue(lines, indexes, skipEmptyError: true);

                using (var scope = _context.Database.BeginTransaction())
                {
                    try
                    {
                        var divisionDict = new Dictionary<string, Division>();
                        var departmentDict = new Dictionary<string, Department>();

                        // Collect unique division and department names
                        var divisionNames = lines.Select(line => line[3]).Where(name => !string.IsNullOrEmpty(name)).Distinct();
                        var departmentNames = lines.Select(line => line[4]).Where(name => !string.IsNullOrEmpty(name)).Distinct();

                        // Load existing divisions and departments into dictionaries
                        var existingDivisions = await _context.Divisions.Where(d => divisionNames.Contains(d.Name)).ToListAsync();
                        foreach (var division in existingDivisions)
                        {
                            divisionDict[division.Name] = division;
                        }

                        var existingDepartments = await _context.Departments.Where(d => departmentNames.Contains(d.Name)).ToListAsync();
                        foreach (var department in existingDepartments)
                        {
                            departmentDict[department.Name] = department;
                        }

                        // Create missing divisions
                        foreach (var divisionName in divisionNames.Except(divisionDict.Keys))
                        {
                            var newDivision = new Division
                            {
                                Name = divisionName,
                                Active = true,
                                CreatedDate = DateTime.UtcNow
                            };
                            divisionDict[divisionName] = newDivision;
                            _context.Divisions.Add(newDivision);
                        }

                        // Create missing departments
                        foreach (var departmentName in departmentNames.Except(departmentDict.Keys))
                        {
                            var newDepartment = new Department
                            {
                                Name = departmentName,
                                Active = true,
                                CreatedDate = DateTime.UtcNow
                            };
                            departmentDict[departmentName] = newDepartment;
                            _context.Departments.Add(newDepartment);
                        }

                        // Save new divisions and departments
                        await _context.SaveChangesAsync();

                        // Now process trainees
                        foreach (var line in lines)
                        {
                            pin = line[0];
                            var divisionName = line[3];
                            var departmentName = line[4];
                            email = string.IsNullOrWhiteSpace(line[7]) ? $"{Guid.NewGuid()}@sample.com" : line[7];
                            phoneNo = line[8];

                            // Check for existing records
                            if (await _context.Trainees.AnyAsync(x => x.PhoneNo == phoneNo))
                                return new APIResponse { Status = ResponseStatus.Warning, Message = $"Phone '{phoneNo}' already exists" };

                            if (await _context.Trainees.AnyAsync(x => x.Email == email))
                                return new APIResponse { Status = ResponseStatus.Warning, Message = $"Email '{email}' already exists" };

                            if (await _context.Trainees.AnyAsync(x => x.PIN == pin))
                                return new APIResponse { Status = ResponseStatus.Warning, Message = $"PIN '{pin}' already exists" };

                            // Create new trainee
                            var item = new Trainee
                            {
                                Email = email,
                                TraineeType = TraineeType.Guest,
                                Name = line[1],
                                Position = line[2],
                                PhoneNo = phoneNo,
                                PIN = pin,
                                WorkLocation = line[9],
                                Active = true,
                                Gender = !string.IsNullOrWhiteSpace(line[6]) ? (Gender)Enum.Parse(typeof(Gender), line[6]) : Gender.Other,
                                Grade = line[5]
                            };

                            // Assign division and department IDs
                            if (!string.IsNullOrEmpty(divisionName) && divisionDict.ContainsKey(divisionName))
                            {
                                item.DivisionId = divisionDict[divisionName].Id;
                            }

                            if (!string.IsNullOrEmpty(departmentName) && departmentDict.ContainsKey(departmentName))
                            {
                                item.DepartmentId = departmentDict[departmentName].Id;
                            }

                            // Create application user
                            var user = new ApplicationUser
                            {
                                UserName = item.PIN,
                                PhoneNumber = item.PhoneNo,
                                Email = item.Email,
                                FirstName = item.Name,
                                Active = item.Active,
                                UserType = UserType.Guest,
                                LastPasswordChanged = DateTime.Now.ToKindLocal()
                            };

                            userManager.PasswordValidator = new PasswordValidator
                            {
                                RequiredLength = 6,
                                RequireNonLetterOrDigit = false,
                                RequireDigit = false,
                                RequireLowercase = false,
                                RequireUppercase = false
                            };

                            userManager.MaxFailedAccessAttemptsBeforeLockout = 3;
                            userManager.UserLockoutEnabledByDefault = true;
                            userManager.DefaultAccountLockoutTimeSpan = TimeSpan.FromMinutes(5);

                            var result = await userManager.CreateAsync(user, item.PhoneNo);
                            if (!result.Succeeded)
                            {
                                throw new Exception("Error on user creation: " + string.Join(" - ", result.Errors));
                            }

                            result = await userManager.AddToRolesAsync(user.Id, new string[] { "Guest", "Trainee" });
                            if (!result.Succeeded)
                            {
                                throw new Exception("Error on user role assign: " + string.Join(", ", result.Errors));
                            }

                            item.UserId = user.Id;
                            item.SetAuditTrailEntity(identity);
                            item.Id = Guid.NewGuid();
                            _context.Trainees.Add(item);
                        }

                        // Save changes to the context
                        await _context.SaveChangesAsync();
                        scope.Commit();
                    }
                    catch (Exception ex)
                    {
                        scope.Rollback();
                        return new APIResponse { Status = ResponseStatus.Warning, Message = ex.Message };
                    }
                }
                //await _auditLogHelper.AddSuccessAudit(audit, null, _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Uploaded Successfully"
                };
            }
            catch (DbEntityValidationException ex)
            {
                //await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                LogControl.Write("DbEntityValidationException Message catch :" + ex.Message);
                LogControl.Write("DbEntityValidationException stac trace :" + ex.StackTrace);
                LogControl.Write("DbEntityValidationException InnerException" + ex.InnerException.ToString());
                List<string> errorList = new List<string>();
                foreach (var errors in ex.EntityValidationErrors)
                {
                    foreach (var validationError in errors.ValidationErrors)
                    {
                        // get the error message 
                        errorList.Add(validationError.ErrorMessage);
                    }
                }
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" ", errorList)
                };
            }
            catch (Exception ex)
            {
                //await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                LogControl.Write("UploadGuestTrainee Message catch :" + ex.Message);
                LogControl.Write("UploadGuestTrainee stac trace :" + ex.StackTrace);
                LogControl.Write("UploadGuestTrainee InnerException" + ex.InnerException?.ToString());
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }


            //try
            //{
            //    ApplicationUserManager userManager = ApplicationUserManager.CreateWithoutValidation(owinContext);
            //    HttpPostedFile file = HttpContext.Current.Request.Files[0];
            //    Trainee item = null;
            //    var data = ExcelParser.Parse(file);

            //    var header = data.Item1;
            //    var lines = data.Item2.ToList();
            //    string pin, divisionName, departmentName, email, phoneNo;

            //    var indexes = new Dictionary<int, ColumnType>
            //    {
            //        {0, ColumnType.String },
            //        {1, ColumnType.String },
            //        {2, ColumnType.String },
            //        {6, ColumnType.String },
            //        {7, ColumnType.Email },
            //        {8, ColumnType.PhoneNumber }
            //    };

            //    await Validator.VerifyUploadFileValue(lines, indexes);

            //    ApplicationUser user;
            //    IdentityResult result;
            //    Department department = null;
            //    Division division = null;

            //    //userManager.PasswordValidator= new PasswordValidator
            //    //{
            //    //    RequiredLength = 6,
            //    //    RequireNonLetterOrDigit = false,
            //    //    RequireDigit = false,
            //    //    RequireLowercase = false,
            //    //    RequireUppercase = false,
            //    //};



            //    //using (var scope = _context.Database.BeginTransaction())
            //    using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))

            //    {
            //        foreach (var line in lines)
            //        {
            //            pin = line[0];
            //            divisionName = line[3];
            //            departmentName = line[4];
            //            email = line[7];
            //            phoneNo = line[8];

            //            if (await _context.Trainees.AnyAsync(x => x.Email == email))
            //                return new APIResponse
            //                {
            //                    Status = ResponseStatus.Warning,
            //                    Message = "Email already exist"
            //                };

            //            if (await _context.Trainees.AnyAsync(x => x.PIN == pin))
            //                return new APIResponse
            //                {
            //                    Status = ResponseStatus.Warning,
            //                    Message = "PIN already exist"
            //                };

            //            item = new Trainee
            //            {
            //                Email = email,
            //                TraineeType = TraineeType.Guest
            //            };


            //            if (!string.IsNullOrEmpty(divisionName))
            //            {
            //                division = await _context.Divisions.FirstOrDefaultAsync(x => x.Name == divisionName);
            //                if (division == null)
            //                {
            //                    division = new Division
            //                    {
            //                        Name = divisionName,
            //                        Active = true,
            //                        CreatedDate = DateTime.UtcNow
            //                    };
            //                    _context.Divisions.Add(division);
            //                    await _context.SaveChangesAsync();
            //                }
            //                item.DivisionId = division.Id;
            //            }

            //            if (!string.IsNullOrEmpty(departmentName))
            //            {
            //                department = await _context.Departments.FirstOrDefaultAsync(x => x.Name == departmentName);
            //                if (department == null)
            //                {
            //                    department = new Department
            //                    {
            //                        Name = departmentName,
            //                        Active = true,
            //                        CreatedDate = DateTime.UtcNow
            //                    };
            //                    _context.Departments.Add(department);
            //                    await _context.SaveChangesAsync();
            //                }
            //                item.DepartmentId = department.Id;
            //            }

            //            item.Name = line[1];
            //            item.Position = line[2];
            //            item.PhoneNo = phoneNo;
            //            item.PIN = pin;
            //            item.WorkLocation = line[9];
            //            item.Active = true;
            //            item.Gender = (Gender)Enum.Parse(typeof(Gender), line[6]);
            //            item.Grade = line[5];

            //            user = new ApplicationUser() { UserName = item.Email, PhoneNumber = item.PhoneNo, Email = item.Email, FirstName = item.Name, Active = item.Active, UserType = UserType.Guest };
            //            userManager.PasswordValidator = new PasswordValidator
            //            {
            //                RequiredLength = 6,
            //                RequireNonLetterOrDigit = false,
            //                RequireDigit = false,
            //                RequireLowercase = false,
            //                RequireUppercase = false,
            //            };
            //            userManager.MaxFailedAccessAttemptsBeforeLockout = 3;
            //            userManager.UserLockoutEnabledByDefault = true;
            //            userManager.DefaultAccountLockoutTimeSpan = TimeSpan.FromMinutes(5);
            //            result = await userManager.CreateAsync(user, item.PhoneNo);
            //            if (!result.Succeeded)
            //            {
            //                //scope.Rollback();
            //                scope.Dispose();
            //                return new APIResponse
            //                {
            //                    Status = ResponseStatus.Warning,
            //                    Message = @"Error on user creation: - " + string.Join(" - ", result.Errors)
            //                };
            //            }

            //            result = await userManager.AddToRolesAsync(user.Id, new string[] { "Guest", "Trainee" });
            //            if (!result.Succeeded)
            //            {
            //                //scope.Rollback();
            //                scope.Dispose();

            //                return new APIResponse
            //                {
            //                    Status = ResponseStatus.Warning,
            //                    Message = @"Error on user role assign: - " + string.Join(", ", result.Errors)
            //                };
            //            }

            //            item.UserId = user.Id;
            //            item.SetAuditTrailEntity(identity);
            //            item.Id = Guid.NewGuid();
            //            _context.Trainees.Add(item);
            //            await _context.SaveChangesAsync();
            //        }

            //        //scope.Commit();
            //        scope.Complete();

            //    }
            //    await _auditLogHelper.AddSuccessAudit(audit, null, _context);

            //    return new APIResponse
            //    {
            //        Status = ResponseStatus.Success,
            //        Message = "Uploaded Successfully"
            //    };
            //}
            //catch (DbEntityValidationException ex)
            //{
            //    await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
            //    LogControl.Write("DbEntityValidationException Message catch :" + ex.Message);
            //    LogControl.Write("DbEntityValidationException stac trace :" + ex.StackTrace);
            //    LogControl.Write("DbEntityValidationException InnerException" + ex.InnerException.ToString());
            //    List<string> errorList = new List<string>();
            //    foreach (var errors in ex.EntityValidationErrors)
            //    {
            //        foreach (var validationError in errors.ValidationErrors)
            //        {
            //            // get the error message 
            //            errorList.Add(validationError.ErrorMessage);
            //        }
            //    }
            //    return new APIResponse
            //    {
            //        Status = ResponseStatus.Error,
            //        Message = string.Join(" ", errorList)
            //    };
            //}
            //catch (Exception ex)
            //{
            //    await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
            //    LogControl.Write("UploadGuestTrainee Message catch :" + ex.Message);
            //    LogControl.Write("UploadGuestTrainee stac trace :" + ex.StackTrace);
            //    LogControl.Write("UploadGuestTrainee InnerException" + ex.InnerException.ToString());
            //    return new APIResponse
            //    {
            //        Status = ResponseStatus.Error,
            //        Message = ex.Message
            //    };
            //}

        }
        #endregion


        #region Trainee Panel APIs
        public async Task<APIResponse> GetProfile(IIdentity identity)
        {
            try
            {
                var userId = identity.GetUserId();
                var item = await _context.Trainees.Where(x => x.UserId == userId).Select(t => new
                {
                    t.Name,
                    t.Email,
                    t.PIN,
                    t.PhoneNo,
                    t.Address,
                    t.WorkLocation,
                    t.LineManagerName,
                    t.LineManagerPIN,
                    t.Active,
                    Gender = t.Gender.ToString(),
                    Division = t.Division.Name,
                    Department = t.Department.Name,
                    Unit = t.Unit.Name,
                    SubUnit = t.SubUnit.Name,
                    t.EmployeeType,
                    t.DateOfJoining,
                    t.Grade,
                    t.Position,
                    t.User.ImagePath,
                }).FirstOrDefaultAsync();


                if (item == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Profile not found"
                };

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = item
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<byte[]> GetMyCourseStudyReportExcel(ApplicationUser user)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            try
            {
                var company = await _context.Configurations.FirstOrDefaultAsync();

                var query = _context.CourseEnrollments.Where(x => x.TraineeId == user.Trainee.Id).AsQueryable();

                var data = await query
                    .GroupJoin(_context.TraineeCourseActivities, x => new { x.TraineeId, x.CourseId }, y => new { y.TraineeId, y.CourseId }, (x, y) => new
                    {
                        Enrollment = x,
                        Activities = y
                    }).SelectMany(x => x.Activities.DefaultIfEmpty(), (y, z) => new { y.Enrollment, Activity = z })
                    .Select(x => new { x.Enrollment.TraineeId, x.Enrollment.Course.Title, x.Enrollment.CourseId, x.Enrollment.Trainee.Name, x.Enrollment.Trainee.PhoneNo, x.Enrollment.Trainee.PIN, Division = x.Enrollment.Trainee.Division.Name, x.Enrollment.Trainee.Email, FirstStudyDate = x.Activity != null ? x.Activity.FirstStudyDate : default(DateTime?), LastStudyDate = x.Activity.LastStudyDate != null ? x.Activity.LastStudyDate : default(DateTime?) })
                    .OrderBy(x => x.Title)
                    .ToListAsync();

                var traineeCertificates = await _context.TraineeCertificates.Where(x => !x.Expired && x.TraineeId == user.Trainee.Id)
                    .Select(x => new { x.TraineeId, x.CourseId, x.GainedPercentage, x.CertificateDate }).ToListAsync();

                var headerColumns = new List<string> { "Course Title", "Study Start Date", "Test Pass Date", "Final Study Date", "Score" };

                ExcelManager.GetTextLineElement(company.Name, ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                ExcelManager.GetTextLineElement(company.Address + " , " + company.ContactNo, ++rowNo, ws, fontSize: 8, isBold: false, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                rowNo++;

                ExcelManager.GetTextLineElement("My Course's Study Report", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                ExcelManager.GetTextLineElement("Trainee: " + user.Trainee.PIN + " - " + user.Trainee.Name, ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                rowNo++;
                if (data.Count > 0)
                {
                    ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);

                    foreach (var item in data)
                    {
                        rowNo++;
                        colNo = 1;

                        var certificate = traineeCertificates.FirstOrDefault(x => x.CourseId == item.CourseId);

                        ExcelManager.GetTableDataCell(item.Title, 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(item.FirstStudyDate?.ToString("dd-MMM-yyyy"), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        ExcelManager.GetTableDataCell(certificate?.CertificateDate.ToString("dd-MMM-yyyy") ?? "", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        ExcelManager.GetTableDataCell(item.LastStudyDate?.ToString("dd-MMM-yyyy") ?? "", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        ExcelManager.GetTableDataCell(certificate?.GainedPercentage ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                    }

                    for (int i = 0; i < headerColumns.Count; i++)
                    {
                        ws.Column(i + 1).AdjustToContents();
                    }
                }
                else
                {
                    ExcelManager.GetTextLineElement("No record found", ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                }

                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
            catch (Exception ibex)
            {
                ExcelManager.GetTextLineElement("Error: " + ibex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
        }

        public async Task<byte[]> GetMyCourseStudyReportPdf(ApplicationUser user)
        {
            var document = new Document(PageSize.A4, 36, 36, 100, 36);
            document.SetPageSize(PageSize.A4.Rotate());
            var reportStream = new MemoryStream();

            try
            {
                PdfWriter writer = PdfWriter.GetInstance(document, reportStream);
                writer.PageEvent = new PDFWriterEvents(true, true);
                writer.CloseStream = false;

                // Openning the Document
                document.Open();

                var data = await _context.CourseEnrollments.Where(x => x.TraineeId == user.Trainee.Id)
                    .GroupJoin(_context.TraineeCourseActivities, x => new { x.TraineeId, x.CourseId }, y => new { y.TraineeId, y.CourseId }, (x, y) => new
                    {
                        Enrollment = x,
                        Activities = y
                    }).SelectMany(x => x.Activities.DefaultIfEmpty(), (y, z) => new { y.Enrollment, Activity = z })
                    .Select(x => new
                    {
                        x.Enrollment.TraineeId,
                        x.Enrollment.Course.Title,
                        x.Enrollment.CourseId,
                        x.Enrollment.Trainee.Name,
                        x.Enrollment.Trainee.PhoneNo,
                        x.Enrollment.Trainee.PIN,
                        Division = x.Enrollment.Trainee.Division.Name,
                        x.Enrollment.Trainee.Email,
                        FirstStudyDate = x.Activity != null ? x.Activity.FirstStudyDate : default(DateTime?),
                        LastStudyDate = x.Activity.LastStudyDate != null ? x.Activity.LastStudyDate : default(DateTime?)
                    })
                    .OrderBy(x => x.Title)
                    .ToListAsync();

                if (!data.Any()) throw new Exception("No data found");

                var traineeCertificates = await _context.TraineeCertificates.Where(x => !x.Expired && x.TraineeId == user.Trainee.Id)
                    .Select(x => new { x.TraineeId, x.CourseId, x.GainedPercentage, x.CertificateDate }).ToListAsync();


                var company = await _context.Configurations.FirstOrDefaultAsync();



                #region Page Header
                PdfPTable header = new PdfPTable(1);
                header.TotalWidth = 180;
                header.DefaultCell.Border = 0;
                header.AddCell(PDFManager.GetTextLineElement(company.Name, 12f, isBold: true));
                header.AddCell(PDFManager.GetTextLineElement(company.Address + " , " + company.ContactNo, isBold: false, fontSize: 8, leftToRightPadding: new float[] { 4, 2, 4, 2 }));
                header.WriteSelectedRows(0, 3, writer.PageSize.Width - document.LeftMargin - document.RightMargin - 180, writer.PageSize.GetTop(10), writer.DirectContent);

                if (!string.IsNullOrEmpty(company.LogoPath))
                {
                    Image img = Image.GetInstance(HostingEnvironment.MapPath("~") + company.LogoPath);
                    img.SetAbsolutePosition(document.LeftMargin, writer.PageSize.GetTop(70));
                    img.ScaleAbsolute(200f, 40f);
                    document.Add(img);
                }
                PdfPTable line = new PdfPTable(1);
                line.TotalWidth = writer.PageSize.Width;
                line.DefaultCell.Border = 0;
                line.AddCell(new Paragraph(new Chunk(new iTextSharp.text.pdf.draw.LineSeparator(0.0F, 100.0F, BaseColor.BLACK, Element.ALIGN_CENTER, 3))));
                line.WriteSelectedRows(0, 1, 0, writer.PageSize.GetTop(header.TotalHeight + 10), writer.DirectContent);
                #endregion

                document.Add(PDFManager.GetTextLineElement("My Course's Study Report", fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));


                #region Table
                var table = new PdfPTable(5) { WidthPercentage = 100 };
                table.SetWidths(new float[] { 45, 15, 15, 15, 10 });

                table.AddCell(PDFManager.GetTableHeaderCell("Course Title", 10f, true, false, PDFAlignment.Left));
                table.AddCell(PDFManager.GetTableHeaderCell("Study Start Date", 10f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Test Pass Date", 10f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Final Study Date", 10f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Score", 10f, true, false, PDFAlignment.Right));

                foreach (var item in data)
                {
                    var certificate = traineeCertificates.Find(x => x.CourseId == item.CourseId);

                    table.AddCell(PDFManager.GetTableDataCell(item.Title, PDFAlignment.Left, false, true, 10));
                    table.AddCell(PDFManager.GetTableDataCell(item.FirstStudyDate?.ToLocalTime().ToString("dd-MMM-yyyy"), PDFAlignment.Center, false, true, 10));
                    table.AddCell(PDFManager.GetTableDataCell(certificate?.CertificateDate.ToLocalTime().ToString("dd-MMM-yyyy") ?? "", PDFAlignment.Center, false, true, 10));
                    table.AddCell(PDFManager.GetTableDataCell(item.LastStudyDate?.ToLocalTime().ToString("dd-MMM-yyyy") ?? "", PDFAlignment.Center, false, true, 10));
                    table.AddCell(PDFManager.GetTableDataCell(certificate?.GainedPercentage ?? 0, PDFAlignment.Right, false, true, 10));
                }
                document.Add(table);
                #endregion

                // Closing the Document
                document.Close();
                return reportStream.ToArray();
            }
            catch (DocumentException dex)
            {
                document.Add(PDFManager.GetTextLineElement(dex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
            // Catching System.IO.IOException if any
            catch (Exception ioex)
            {
                document.Add(PDFManager.GetTextLineElement(ioex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
        }


        public async Task<APIResponse> GetUserProfile(string userId)
        {
            try
            {
                var item = await _context.Trainees.Where(x => x.UserId == userId).Select(t => new
                {
                    t.Name,
                    t.Email,
                    t.PIN,
                    t.PhoneNo,
                    t.WorkLocation,
                    t.LineManagerName,
                    t.LineManagerPIN,
                    Division = t.Division.Name,
                    Department = t.Department.Name,
                    Unit = t.Unit.Name,
                    SubUnit = t.SubUnit.Name,
                    t.Grade,
                    t.Position,
                    t.UserId,
                    t.User.ImagePath
                }).FirstOrDefaultAsync();


                if (item == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Profile not found"
                };

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = item
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> RemoveInvalidGuestTrainee()
        {
            try
            {
                var InvalidGuestUsers = await _context.Users
                .Where(x => x.UserType == UserType.Guest && !_context.Trainees.Where(t => t.TraineeType == TraineeType.Guest).Select(t => t.UserId).Contains(x.Id))
                .ToListAsync();
                if (InvalidGuestUsers.Count > 0)
                {
                    foreach (var invalidUser in InvalidGuestUsers)
                    {
                        _context.Users.Remove(invalidUser);
                    }

                    await _context.SaveChangesAsync();
                    return new APIResponse
                    {
                        Message="Successfully removed invalid guest users.",
                        Status= ResponseStatus.Success,
                     
                    };
                }
                return new APIResponse
                {
                    Message = "No invalid guest users found.",
                    Status = ResponseStatus.Warning,

                };
            }
            catch (Exception ex)
            {

                return new APIResponse
                {
                    Message = ex.Message,
                    Status = ResponseStatus.Error
                };
            }

        }
        #endregion

    }

    public interface ITraineeService
    {
        // Task<APIResponse> UploadTrainee(IIdentity identity, ApplicationUserManager userManager);
        Task<APIResponse> GetTraineeList(string name, long? departmentId, long? divisionId, int size, int pageNumber);
        Task<APIResponse> GetTraineeById(Guid id);
        Task<APIResponse> CreateOrUpdateTrainee(TraineeModel model, IIdentity identity, ApplicationUserManager userManager);
        Task<byte[]> GetTraineeListExcel(string name, long? divisionId, long? departmentId, TraineeType traineeType = TraineeType.Permanent);
        Task<APIResponse> QueryTrainee(string query, int limit);
        Task<APIResponse> QueryTraineeByExcel();
        Task<APIResponse> GetTraineeDropDownList();
        //Task<APIResponse> CourseRatingSave(RatingSaveModel model, ApplicationUser user);
        Task<APIResponse> UpdatePhoto(IIdentity identity, ApplicationUserManager userManager);
        Task<byte[]> GetAnswerSheetPDF(Guid id);
        Task<byte[]> GetEnrolledTraineeListExcel(Guid courseId, long? divisionId);

        #region Guest Trainee
        Task<APIResponse> CreateOrUpdateGuestTrainee(TraineeModel model, IIdentity identity, ApplicationUserManager userManager);
        Task<APIResponse> GetGuestTraineeList(string name, long? divisionId, long? departmentId, int size, int pageNumber);
        //Task<APIResponse> UploadGuestTrainee(IIdentity identity,  IOwinContext owinContext);
        Task<APIResponse> UploadGuestTrainee(IIdentity identity, ApplicationUserManager userManager);
        #endregion

        #region Trainee Panel APIs
        Task<APIResponse> GetProfile(IIdentity identity);
        Task<byte[]> GetMyCourseStudyReportExcel(ApplicationUser user);
        Task<byte[]> GetMyCourseStudyReportPdf(ApplicationUser user);
        Task<APIResponse> GetUserProfile(string userId);
        Task<APIResponse> RemoveInvalidGuestTrainee();
        #endregion
    }
}
