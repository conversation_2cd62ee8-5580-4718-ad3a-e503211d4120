﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [Authorize(Roles = "Trainee, Guest"), RoutePrefix("api/trainee-device")]
    public class TraineeDeviceController : ApplicationController
    {
        private readonly ITraineeDeviceService _service;

        public TraineeDeviceController()
        {
            _service = new TraineeDeviceService();
        }

        [HttpPost, Route("create-or-update")]
        public async Task<IHttpActionResult> TraineeDeviceCreateOrUpdate(TraineeDeviceModel model)
        {
            var _nservice = new TraineeDeviceService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.TraineeDeviceCreateOrUpdate(model, CurrentUser));
        }
    }
}
