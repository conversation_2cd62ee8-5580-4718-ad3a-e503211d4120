{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ColumnMode } from \"@swimlane/ngx-datatable\";\nimport { Validators } from \"@angular/forms\";\nimport { BlockUI } from \"ng-block-ui\";\nimport { environment } from \"../../environments/environment\";\nimport { UploadDialogComponent } from \"../_helpers/upload-dialog/dialog.component\";\nimport { ResponseStatus } from \"../_models/enum\";\nimport { concat, of, Subject } from \"rxjs\";\nimport { debounceTime, distinctUntilChanged, tap, switchMap, catchError } from \"rxjs/operators\";\nimport { Page } from \"../_models/page\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../app.component\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../_services/common.service\";\nimport * as i4 from \"ngx-toastr\";\nimport * as i5 from \"@angular/material/dialog\";\nimport * as i6 from \"ng-block-ui\";\nimport * as i7 from \"@ng-select/ng-select\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/flex-layout/extended\";\nimport * as i10 from \"@angular/router\";\nimport * as i11 from \"@swimlane/ngx-datatable\";\nimport * as i12 from \"ngx-moment\";\n\nfunction TraineeEvaluationTestListComponent_div_15_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 43);\n    i0.ɵɵtext(1, \"Exam is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtemplate(1, TraineeEvaluationTestListComponent_div_15_span_1_Template, 2, 0, \"span\", 42);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.examId.errors.required);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_27_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 52);\n  }\n\n  if (rf & 2) {\n    const item_r18 = i0.ɵɵnextContext().item;\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r20.baseUrl + item_r18.ImagePath, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_27_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 53);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_27_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 54);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_27_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 55);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_27_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 56);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, TraineeEvaluationTestListComponent_ng_template_27_img_1_Template, 1, 1, \"img\", 45);\n    i0.ɵɵtemplate(2, TraineeEvaluationTestListComponent_ng_template_27_img_2_Template, 1, 0, \"img\", 46);\n    i0.ɵɵtemplate(3, TraineeEvaluationTestListComponent_ng_template_27_img_3_Template, 1, 0, \"img\", 47);\n    i0.ɵɵtemplate(4, TraineeEvaluationTestListComponent_ng_template_27_img_4_Template, 1, 0, \"img\", 48);\n    i0.ɵɵtemplate(5, TraineeEvaluationTestListComponent_ng_template_27_img_5_Template, 1, 0, \"img\", 49);\n    i0.ɵɵelementStart(6, \"div\", 50);\n    i0.ɵɵelementStart(7, \"h5\", 51);\n    i0.ɵɵelementStart(8, \"b\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"br\");\n    i0.ɵɵelementStart(11, \"b\");\n    i0.ɵɵtext(12, \"PIN:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵelement(14, \"br\");\n    i0.ɵɵelementStart(15, \"b\");\n    i0.ɵɵtext(16, \"Division :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r18 = ctx.item;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r18.ImagePath);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r18.ImagePath && !item_r18.Gender);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r18.ImagePath && item_r18.Gender == \"Others\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r18.ImagePath && item_r18.Gender == \"Male\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r18.ImagePath && item_r18.Gender == \"Female\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r18.Name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", item_r18.PIN, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", item_r18.Division, \" \");\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r26 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r26);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r26);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r27 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r27);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r27);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r28 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r28);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r28);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_47_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵpipe(1, \"amDateFormat\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"amDateFormat\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r29 = i0.ɵɵnextContext().value;\n    i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind2(1, 2, value_r29, \"MMM DD, YYYY hh:mm A\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 5, value_r29, \"MMM DD, YYYY hh:mm A\"), \" \");\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TraineeEvaluationTestListComponent_ng_template_47_span_0_Template, 4, 8, \"span\", 58);\n  }\n\n  if (rf & 2) {\n    const value_r29 = ctx.value;\n    i0.ɵɵproperty(\"ngIf\", value_r29);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r32 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r32);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r32);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r33 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r33);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r33);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r34 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r34);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r34);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_55_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r35 = i0.ɵɵnextContext().row;\n    i0.ɵɵpropertyInterpolate(\"title\", row_r35.Checker);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(row_r35.Checker);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_55_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 60);\n    i0.ɵɵtext(1, \"AUTO\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TraineeEvaluationTestListComponent_ng_template_55_span_0_Template, 2, 2, \"span\", 58);\n    i0.ɵɵtemplate(1, TraineeEvaluationTestListComponent_ng_template_55_span_1_Template, 2, 0, \"span\", 59);\n  }\n\n  if (rf & 2) {\n    const row_r35 = ctx.row;\n    i0.ɵɵproperty(\"ngIf\", row_r35.Checker);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", row_r35.Status === \"Examined\" && !row_r35.Checker);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_57_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r39 = i0.ɵɵnextContext().value;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r39);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_57_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r39 = i0.ɵɵnextContext().value;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r39);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TraineeEvaluationTestListComponent_ng_template_57_span_0_Template, 2, 1, \"span\", 61);\n    i0.ɵɵtemplate(1, TraineeEvaluationTestListComponent_ng_template_57_span_1_Template, 2, 1, \"span\", 62);\n  }\n\n  if (rf & 2) {\n    const value_r39 = ctx.value;\n    i0.ɵɵproperty(\"ngIf\", value_r39 === \"Passed\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", value_r39 === \"Failed\");\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_59_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵpipe(1, \"amDateFormat\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"amDateFormat\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r44 = i0.ɵɵnextContext().value;\n    i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind2(1, 2, value_r44, \"MMM DD, YYYY\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 5, value_r44, \"MMM DD, YYYY\"), \" \");\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_59_ng_template_1_Template(rf, ctx) {}\n\nfunction TraineeEvaluationTestListComponent_ng_template_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TraineeEvaluationTestListComponent_ng_template_59_span_0_Template, 4, 8, \"span\", 65);\n    i0.ɵɵtemplate(1, TraineeEvaluationTestListComponent_ng_template_59_ng_template_1_Template, 0, 0, \"ng-template\", null, 66, i0.ɵɵtemplateRefExtractor);\n  }\n\n  if (rf & 2) {\n    const value_r44 = ctx.value;\n\n    const _r46 = i0.ɵɵreference(2);\n\n    i0.ɵɵproperty(\"ngIf\", value_r44 !== \"0001-01-01T00:00:00\")(\"ngIfElse\", _r46);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r49 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r49);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(value_r49);\n  }\n}\n\nfunction TraineeEvaluationTestListComponent_ng_template_63_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function TraineeEvaluationTestListComponent_ng_template_63_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const row_r50 = i0.ɵɵnextContext().row;\n      const ctx_r52 = i0.ɵɵnextContext();\n      return ctx_r52.downloadAnswersheet(row_r50);\n    });\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c0 = function (a1) {\n  return [\"/check-trainee-evaluation-answersheet\", a1];\n};\n\nfunction TraineeEvaluationTestListComponent_ng_template_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TraineeEvaluationTestListComponent_ng_template_63_button_0_Template, 2, 0, \"button\", 67);\n    i0.ɵɵelementStart(1, \"a\", 68);\n    i0.ɵɵelement(2, \"i\", 69);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r50 = ctx.row;\n    i0.ɵɵproperty(\"ngIf\", row_r50.Status !== \"Submitted\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(2, _c0, row_r50.Id));\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nconst _c2 = function () {\n  return [\"/trainee-evaluation-test-publish\"];\n};\n\nexport class TraineeEvaluationTestListComponent {\n  constructor(appComponent, formBuilder, _service, toastr, dialog) {\n    this.appComponent = appComponent;\n    this.formBuilder = formBuilder;\n    this._service = _service;\n    this.toastr = toastr;\n    this.dialog = dialog;\n    this.baseUrl = environment.baseUrl;\n    this.submitted = false;\n    this.courseList = [];\n    this.examList = [];\n    this.examPublishList = [];\n    this.rows = [];\n    this.rowsPublish = [];\n    this.statusList = [{\n      id: \"Submitted\",\n      text: \"Submitted\"\n    }, {\n      id: \"Examined\",\n      text: \"Examined\"\n    }, {\n      id: \"Published\",\n      text: \"Published\"\n    }, {\n      id: \"Attended\",\n      text: \"Attended\"\n    }];\n    this.loadingIndicator = false;\n    this.ColumnMode = ColumnMode;\n    this.traineeLoading = false;\n    this.traineeInput$ = new Subject();\n    this.page = new Page();\n    this.page.pageNumber = 0;\n    this.page.size = 10;\n  }\n\n  handleSelectClick(selectElement) {\n    this.appComponent.handleSelectClick(selectElement);\n  }\n\n  ngOnInit() {\n    this.filterForm = this.formBuilder.group({\n      examId: [null, [Validators.required]],\n      status: [null],\n      trainee: [null]\n    });\n    this.loadTrainee();\n    this.getExamList();\n  }\n\n  get f() {\n    return this.filterForm.controls;\n  }\n\n  loadTrainee() {\n    this.trainee$ = concat(of([]), // default items\n    this.traineeInput$.pipe(debounceTime(500), distinctUntilChanged(), tap(() => this.traineeLoading = true), switchMap(term => {\n      if (term && term.length > 2) return this._service.get(\"trainee/query/10/\" + term).pipe(catchError(() => of([])), tap(() => this.traineeLoading = false), switchMap(res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, \"Warning!\", {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, \"Error!\", {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        return of(res.Data);\n      }));else {\n        this.traineeLoading = false;\n        return of([]);\n      }\n    })));\n  }\n\n  getExamList() {\n    this.examList = [];\n\n    this._service.get(\"evaluation-exam/dropdown-list\").subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.examList = res.Data;\n    }, () => {});\n  }\n\n  setPage(pageInfo) {\n    this.page.pageNumber = pageInfo.offset;\n    this.filterList();\n  }\n\n  filterList() {\n    this.submitted = true;\n    if (this.filterForm.invalid) return;\n    this.loadingIndicator = true;\n    const obj = {\n      examId: this.filterForm.value.examId,\n      examStatus: this.filterForm.value.status,\n      traineeId: this.filterForm.value.trainee ? this.filterForm.value.trainee.Id : null,\n      size: this.page.size,\n      pageNumber: this.page.pageNumber\n    };\n\n    this._service.get(\"evaluation-exam/get-trainee-exam-list\", obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, \"Warning!\", {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, \"Error!\", {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.rows = res.Data.Records;\n        this.page.totalElements = res.Data.Total;\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n        setTimeout(() => {\n          this.loadingIndicator = false;\n        }, 1000);\n      },\n      error: err => {\n        this.toastr.error(err.message || err, \"Error!\", {\n          timeOut: 2000\n        });\n        setTimeout(() => {\n          this.loadingIndicator = false;\n        }, 1000);\n      },\n      complete: () => setTimeout(() => {\n        this.loadingIndicator = false;\n      }, 1000)\n    });\n  }\n\n  downloadExcel() {\n    this.submitted = true;\n    if (this.filterForm.invalid) return;\n    const obj = {\n      examId: this.filterForm.value.examId,\n      examStatus: this.filterForm.value.status,\n      traineeId: this.filterForm.value.trainee ? this.filterForm.value.trainee.Id : null,\n      timeZoneOffset: new Date().getTimezoneOffset()\n    };\n    this.blockUI.start(\"Generating excel file. Please wait ...\");\n    return this._service.downloadFile('evaluation-exam/download-list-in-excel', obj).subscribe(res => {\n      this.blockUI.stop();\n      const url = window.URL.createObjectURL(res);\n      var link = document.createElement(\"a\");\n      link.href = url;\n      link.download = \"Evaluation_Test_Result_List.xlsx\";\n      link.click();\n      link.remove();\n      this.submitted = false;\n    }, error => {\n      this.toastr.error(error.message || error, \"Error!\", {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n      this.submitted = false;\n    });\n  }\n\n  openUploadDialog() {\n    this.dialog.open(UploadDialogComponent, {\n      data: {\n        url: this._service.generateUrl(\"course-exam/upload-marking-file\"),\n        whiteList: [\"xlsx\", \"xls\"],\n        uploadtext: \"Please upload an Excel file\",\n        title: \"Upload Marking File\"\n      },\n      width: \"50%\",\n      height: \"50%\"\n    }).afterClosed().subscribe(response => {\n      if (response) {\n        this.toastr.success(response, \"Success!\", {\n          timeOut: 2000\n        });\n        this.filterList();\n      }\n    });\n  }\n\n  downloadAnswersheet(item) {\n    this.blockUI.start('Generating pdf file. Please wait ...');\n\n    this._service.downloadFile('evaluation-exam/get-answer-sheet-in-pdf/' + item.Id).subscribe({\n      next: res => {\n        const url = window.URL.createObjectURL(res);\n        var link = document.createElement('a');\n        link.href = url;\n        link.download = item.PIN + '_Evaluation_Test_Answersheet.pdf';\n        link.click();\n        link.remove();\n      },\n      error: error => {\n        this.toastr.error(error.message || error, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n}\n\nTraineeEvaluationTestListComponent.ɵfac = function TraineeEvaluationTestListComponent_Factory(t) {\n  return new (t || TraineeEvaluationTestListComponent)(i0.ɵɵdirectiveInject(i1.AppComponent), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.CommonService), i0.ɵɵdirectiveInject(i4.ToastrService), i0.ɵɵdirectiveInject(i5.MatDialog));\n};\n\nTraineeEvaluationTestListComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: TraineeEvaluationTestListComponent,\n  selectors: [[\"app-trainee-evaluation-test-list\"]],\n  decls: 64,\n  vars: 65,\n  consts: [[1, \"col-12\"], [1, \"card\", \"card-border-primary\"], [1, \"card-header\"], [1, \"card-block\"], [1, \"row\"], [\"autocomplete\", \"off\", 1, \"col-lg-12\", \"custom-inline-form\", 3, \"formGroup\"], [1, \"mb-3\", \"col-lg-3\", \"col-12\", \"mb-3\"], [1, \"col-form-label\", \"col-form-label-sm\", \"required\"], [\"formControlName\", \"examId\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Select\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"clearable\", \"clearOnBackspace\", \"items\", \"click\"], [\"selectElement\", \"\"], [\"class\", \"error-text\", 4, \"ngIf\"], [1, \"mb-3\", \"col-lg-2\", \"col-12\"], [1, \"col-form-label\", \"col-form-label-sm\"], [\"formControlName\", \"status\", \"bindLabel\", \"text\", \"bindValue\", \"id\", \"placeholder\", \"All\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\"], [\"selectElementS\", \"\"], [1, \"mb-3\", \"col-lg-3\", \"col-12\"], [1, \"col-form-label\", \"col-form-label-sm\", \"text-right\"], [\"formControlName\", \"trainee\", \"bindLabel\", \"Name\", \"typeToSearchText\", \"Please enter 3 or more characters\", \"placeholder\", \"Type trainee pin/name\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"hideSelected\", \"loading\", \"typeahead\", \"click\"], [\"selectElementT\", \"\"], [\"ng-option-tmp\", \"\"], [1, \"mb-3\", \"col-lg-4\", \"col-12\"], [1, \"btn\", \"btn-theme\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"feather\", \"icon-search\"], [1, \"feather\", \"icon-download\"], [\"target\", \"_blank\", \"rel\", \"noopener\", 1, \"btn\", \"btn-theme\", \"btn-sm\", \"float-end\", \"tetl-style-1\", 3, \"routerLink\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"col-lg-12\"], [\"rowHeight\", \"auto\", 1, \"material\", \"table-bordered\", 3, \"rows\", \"loadingIndicator\", \"externalPaging\", \"columnMode\", \"headerHeight\", \"footerHeight\", \"count\", \"offset\", \"limit\", \"page\"], [\"name\", \"PIN\", \"prop\", \"PIN\", 3, \"maxWidth\", \"draggable\", \"canAutoResize\", \"sortable\"], [\"ngx-datatable-cell-template\", \"\"], [\"name\", \"Name\", \"prop\", \"Name\", 3, \"draggable\", \"canAutoResize\", \"sortable\"], [\"prop\", \"Division\", 3, \"draggable\", \"canAutoResize\", \"sortable\"], [\"name\", \"Attend/Entry On\", \"prop\", \"StartDate\", 3, \"canAutoResize\", \"draggable\", \"sortable\"], [\"name\", \"T. Marks\", \"prop\", \"TotalMarks\", 3, \"draggable\", \"canAutoResize\", \"sortable\"], [\"name\", \"G. Marks\", \"prop\", \"GainedMarks\", 3, \"draggable\", \"canAutoResize\", \"sortable\"], [\"name\", \"Status\", \"prop\", \"Status\", 3, \"draggable\", \"canAutoResize\", \"sortable\"], [\"name\", \"Marked By\", 3, \"draggable\", \"canAutoResize\", \"sortable\"], [\"name\", \"Result\", \"prop\", \"Result\", 3, \"draggable\", \"canAutoResize\", \"sortable\"], [\"name\", \"Marked On\", \"prop\", \"MarkedOn\", 3, \"draggable\", \"canAutoResize\", \"sortable\"], [\"name\", \"Termination\", \"prop\", \"Terminated\", 3, \"minWidth\", \"draggable\", \"sortable\"], [\"name\", \"Action\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"draggable\", \"sortable\", \"canAutoResize\"], [1, \"error-text\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"], [1, \"media\"], [\"class\", \"rounded-circle me-3\", \"width\", \"40\", 3, \"src\", 4, \"ngIf\"], [\"src\", \"assets/images/user/user-avatar-blank.png\", \"class\", \"rounded-circle me-3\", \"width\", \"40\", 4, \"ngIf\"], [\"src\", \"assets/images/user/other.jpg\", \"alt\", \"user image\", \"class\", \"rounded-circle me-3\", \"width\", \"40\", 4, \"ngIf\"], [\"src\", \"assets/images/user/male.jpg\", \"alt\", \"user image\", \"class\", \"rounded-circle me-3\", \"width\", \"40\", 4, \"ngIf\"], [\"src\", \"assets/images/user/female.jpg\", \"alt\", \"user image\", \"class\", \"rounded-circle me-3\", \"width\", \"40\", 4, \"ngIf\"], [1, \"media-body\"], [1, \"mt-0\"], [\"width\", \"40\", 1, \"rounded-circle\", \"me-3\", 3, \"src\"], [\"src\", \"assets/images/user/user-avatar-blank.png\", \"width\", \"40\", 1, \"rounded-circle\", \"me-3\"], [\"src\", \"assets/images/user/other.jpg\", \"alt\", \"user image\", \"width\", \"40\", 1, \"rounded-circle\", \"me-3\"], [\"src\", \"assets/images/user/male.jpg\", \"alt\", \"user image\", \"width\", \"40\", 1, \"rounded-circle\", \"me-3\"], [\"src\", \"assets/images/user/female.jpg\", \"alt\", \"user image\", \"width\", \"40\", 1, \"rounded-circle\", \"me-3\"], [3, \"title\"], [3, \"title\", 4, \"ngIf\"], [\"title\", \"AUTO\", 4, \"ngIf\"], [\"title\", \"AUTO\"], [\"class\", \"badge bg-success\", 4, \"ngIf\"], [\"class\", \"badge bg-danger\", 4, \"ngIf\"], [1, \"badge\", \"bg-success\"], [1, \"badge\", \"bg-danger\"], [3, \"title\", 4, \"ngIf\", \"ngIfElse\"], [\"noDate\", \"\"], [\"class\", \"btn btn-info btn-mini me-1\", \"title\", \"Download PDF\", 3, \"click\", 4, \"ngIf\"], [\"target\", \"blank\", \"rel\", \"noopener\", \"title\", \"Check Answersheet\", 1, \"btn\", \"btn-primary\", \"btn-mini\", 3, \"routerLink\"], [1, \"feather\", \"icon-arrow-right\"], [\"title\", \"Download PDF\", 1, \"btn\", \"btn-info\", \"btn-mini\", \"me-1\", 3, \"click\"]],\n  template: function TraineeEvaluationTestListComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r55 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"h5\");\n      i0.ɵɵtext(5, \"Trainee Evaluation Test List\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"div\", 3);\n      i0.ɵɵelementStart(7, \"div\", 4);\n      i0.ɵɵelementStart(8, \"form\", 5);\n      i0.ɵɵelementStart(9, \"div\", 4);\n      i0.ɵɵelementStart(10, \"div\", 6);\n      i0.ɵɵelementStart(11, \"label\", 7);\n      i0.ɵɵtext(12, \" Exam \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(13, \"ng-select\", 8, 9);\n      i0.ɵɵlistener(\"click\", function TraineeEvaluationTestListComponent_Template_ng_select_click_13_listener() {\n        i0.ɵɵrestoreView(_r55);\n\n        const _r0 = i0.ɵɵreference(14);\n\n        return ctx.handleSelectClick(_r0);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(15, TraineeEvaluationTestListComponent_div_15_Template, 2, 1, \"div\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(16, \"div\", 11);\n      i0.ɵɵelementStart(17, \"label\", 12);\n      i0.ɵɵtext(18, \" Status \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"ng-select\", 13, 14);\n      i0.ɵɵlistener(\"click\", function TraineeEvaluationTestListComponent_Template_ng_select_click_19_listener() {\n        i0.ɵɵrestoreView(_r55);\n\n        const _r2 = i0.ɵɵreference(20);\n\n        return ctx.handleSelectClick(_r2);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(21, \"div\", 15);\n      i0.ɵɵelementStart(22, \"label\", 16);\n      i0.ɵɵtext(23, \" Trainee \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"ng-select\", 17, 18);\n      i0.ɵɵlistener(\"click\", function TraineeEvaluationTestListComponent_Template_ng_select_click_24_listener() {\n        i0.ɵɵrestoreView(_r55);\n\n        const _r3 = i0.ɵɵreference(25);\n\n        return ctx.handleSelectClick(_r3);\n      });\n      i0.ɵɵpipe(26, \"async\");\n      i0.ɵɵtemplate(27, TraineeEvaluationTestListComponent_ng_template_27_Template, 18, 8, \"ng-template\", 19);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(28, \"div\", 20);\n      i0.ɵɵelementStart(29, \"button\", 21);\n      i0.ɵɵlistener(\"click\", function TraineeEvaluationTestListComponent_Template_button_click_29_listener() {\n        return ctx.filterList();\n      });\n      i0.ɵɵelement(30, \"i\", 22);\n      i0.ɵɵtext(31, \" Search \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(32, \"button\", 21);\n      i0.ɵɵlistener(\"click\", function TraineeEvaluationTestListComponent_Template_button_click_32_listener() {\n        return ctx.downloadExcel();\n      });\n      i0.ɵɵelement(33, \"i\", 23);\n      i0.ɵɵtext(34, \" Download \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(35, \"a\", 24);\n      i0.ɵɵtext(36, \"Bulk Result Publish \");\n      i0.ɵɵelement(37, \"i\", 25);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(38, \"div\", 26);\n      i0.ɵɵelementStart(39, \"ngx-datatable\", 27);\n      i0.ɵɵlistener(\"page\", function TraineeEvaluationTestListComponent_Template_ngx_datatable_page_39_listener($event) {\n        return ctx.setPage($event);\n      });\n      i0.ɵɵelementStart(40, \"ngx-datatable-column\", 28);\n      i0.ɵɵtemplate(41, TraineeEvaluationTestListComponent_ng_template_41_Template, 2, 2, \"ng-template\", 29);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(42, \"ngx-datatable-column\", 30);\n      i0.ɵɵtemplate(43, TraineeEvaluationTestListComponent_ng_template_43_Template, 2, 2, \"ng-template\", 29);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(44, \"ngx-datatable-column\", 31);\n      i0.ɵɵtemplate(45, TraineeEvaluationTestListComponent_ng_template_45_Template, 2, 2, \"ng-template\", 29);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(46, \"ngx-datatable-column\", 32);\n      i0.ɵɵtemplate(47, TraineeEvaluationTestListComponent_ng_template_47_Template, 1, 1, \"ng-template\", 29);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(48, \"ngx-datatable-column\", 33);\n      i0.ɵɵtemplate(49, TraineeEvaluationTestListComponent_ng_template_49_Template, 2, 2, \"ng-template\", 29);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(50, \"ngx-datatable-column\", 34);\n      i0.ɵɵtemplate(51, TraineeEvaluationTestListComponent_ng_template_51_Template, 2, 2, \"ng-template\", 29);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(52, \"ngx-datatable-column\", 35);\n      i0.ɵɵtemplate(53, TraineeEvaluationTestListComponent_ng_template_53_Template, 2, 2, \"ng-template\", 29);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(54, \"ngx-datatable-column\", 36);\n      i0.ɵɵtemplate(55, TraineeEvaluationTestListComponent_ng_template_55_Template, 2, 2, \"ng-template\", 29);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(56, \"ngx-datatable-column\", 37);\n      i0.ɵɵtemplate(57, TraineeEvaluationTestListComponent_ng_template_57_Template, 2, 2, \"ng-template\", 29);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(58, \"ngx-datatable-column\", 38);\n      i0.ɵɵtemplate(59, TraineeEvaluationTestListComponent_ng_template_59_Template, 3, 2, \"ng-template\", 29);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(60, \"ngx-datatable-column\", 39);\n      i0.ɵɵtemplate(61, TraineeEvaluationTestListComponent_ng_template_61_Template, 2, 2, \"ng-template\", 29);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(62, \"ngx-datatable-column\", 40);\n      i0.ɵɵtemplate(63, TraineeEvaluationTestListComponent_ng_template_63_Template, 3, 4, \"ng-template\", 29);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(62, _c1, ctx.submitted && ctx.f.examId.errors))(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx.examList);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.examId.errors);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"clearable\", true)(\"clearOnBackspace\", true)(\"items\", ctx.statusList);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(26, 60, ctx.trainee$))(\"hideSelected\", true)(\"loading\", ctx.traineeLoading)(\"typeahead\", ctx.traineeInput$);\n      i0.ɵɵadvance(11);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(64, _c2));\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"rows\", ctx.rows)(\"loadingIndicator\", ctx.loadingIndicator)(\"externalPaging\", true)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"count\", ctx.page.totalElements)(\"offset\", ctx.page.pageNumber)(\"limit\", ctx.page.size);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"maxWidth\", 80)(\"draggable\", false)(\"canAutoResize\", true)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"canAutoResize\", true)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"canAutoResize\", true)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"canAutoResize\", true)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"canAutoResize\", true)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"canAutoResize\", true)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"canAutoResize\", true)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"canAutoResize\", true)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"canAutoResize\", true)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"canAutoResize\", true)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"minWidth\", 10)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false)(\"canAutoResize\", true);\n    }\n  },\n  directives: [i6.BlockUIComponent, i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.FormGroupDirective, i7.NgSelectComponent, i2.NgControlStatus, i2.FormControlName, i8.NgClass, i9.DefaultClassDirective, i8.NgIf, i7.NgOptionTemplateDirective, i10.RouterLinkWithHref, i11.DatatableComponent, i11.DataTableColumnDirective, i11.DataTableColumnCellDirective],\n  pipes: [i8.AsyncPipe, i12.DateFormatPipe],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], TraineeEvaluationTestListComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}