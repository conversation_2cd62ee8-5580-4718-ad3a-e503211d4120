﻿using Brac.LMS.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.ViewModels
{
    public class TraineeModel
    {
        public Guid? Id { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public string WorkLocation { get; set; }
        public string LineManagerName { get; set; }
        public string LineManagerPIN { get; set; }
        public string PIN { get; set; }
        public string PhoneNo { get; set; }
        public string Position { get; set; }
        public string Grade { get; set; }
        public long DivisionId { get; set; }
        public long DepartmentId { get; set; }
        public bool Active { get; set; }
        public Gender Gender { get; set; }
    }

    public class TraineeEnrollModel
    {
        public TraineeEnrollModel()
        {
            Trainees = new List<Guid>();
        }
        public Guid CourseId { get; set; }
        public long? DivisionId { get; set; }
        public List<Guid> Trainees { get; set; }
    }
}
