{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { CoreChartRoutingModule } from './core-chart-routing.module';\nimport * as i0 from \"@angular/core\";\nexport let CoreChartModule = /*#__PURE__*/(() => {\n  class CoreChartModule {}\n\n  CoreChartModule.ɵfac = function CoreChartModule_Factory(t) {\n    return new (t || CoreChartModule)();\n  };\n\n  CoreChartModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CoreChartModule\n  });\n  CoreChartModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, CoreChartRoutingModule]]\n  });\n  return CoreChartModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}