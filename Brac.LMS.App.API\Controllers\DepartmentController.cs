﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [Authorize(Roles = "Admin"), RoutePrefix("api/department")]
    public class DepartmentController : ApplicationController
    {
        private readonly IDepartmentService _service;

        public DepartmentController()
        {
            _service = new DepartmentService();
        }

        [HttpPost, Route("create-or-update")]
        public async Task<IHttpActionResult> DepartmentCreateOrUpdate(DepartmentModel model)
        {
            var _nservice = new DepartmentService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.DepartmentCreateOrUpdate(model, User.Identity));
        }

        [HttpGet, Route("list")]
        public async Task<IHttpActionResult> GetDepartmentList(string name, int size, int pageNumber)
        {
            return Ok(await _service.GetDepartmentList(name, size, pageNumber));
        }

        [HttpGet, Route("dropdown-list")]
        public async Task<IHttpActionResult> GetDepartmentDropDownList()
        {
            return Ok(await _service.GetDepartmentDropDownList());
        }

        [HttpGet, Route("get/{id}")]
        public async Task<IHttpActionResult> GetDepartmentById(long id)
        {
            return Ok(await _service.GetDepartmentById(id));
        }

        [HttpGet, Route("delete/{id}")]
        public async Task<IHttpActionResult> DeleteDepartmentById(long id)
        {
            return Ok(await _service.DeleteDepartmentById(id));
        }
    }
}
