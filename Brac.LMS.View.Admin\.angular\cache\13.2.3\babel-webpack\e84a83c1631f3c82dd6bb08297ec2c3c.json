{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { BasicSpinnerRoutingModule } from './basic-spinner-routing.module';\nimport { SharedModule } from '../../../../theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport let BasicSpinnerModule = /*#__PURE__*/(() => {\n  class BasicSpinnerModule {}\n\n  BasicSpinnerModule.ɵfac = function BasicSpinnerModule_Factory(t) {\n    return new (t || BasicSpinnerModule)();\n  };\n\n  BasicSpinnerModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: BasicSpinnerModule\n  });\n  BasicSpinnerModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, BasicSpinnerRoutingModule, SharedModule]]\n  });\n  return BasicSpinnerModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}