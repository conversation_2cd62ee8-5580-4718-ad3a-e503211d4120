{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CertificateSettingComponent } from './certificate-setting.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: CertificateSettingComponent\n}];\nexport let CertificateSettingRoutingModule = /*#__PURE__*/(() => {\n  class CertificateSettingRoutingModule {}\n\n  CertificateSettingRoutingModule.ɵfac = function CertificateSettingRoutingModule_Factory(t) {\n    return new (t || CertificateSettingRoutingModule)();\n  };\n\n  CertificateSettingRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CertificateSettingRoutingModule\n  });\n  CertificateSettingRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return CertificateSettingRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}