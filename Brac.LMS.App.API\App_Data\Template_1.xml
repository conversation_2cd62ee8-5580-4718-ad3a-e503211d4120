<?xml version="1.0" standalone="yes"?>
<xs:schema id="NewDataSet" xmlns="" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
  <xs:element name="NewDataSet" msdata:IsDataSet="true" msdata:MainDataTable="Certificate" msdata:UseCurrentLocale="true">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Certificate">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="TraineeName" type="xs:string" minOccurs="0" />
              <xs:element name="CourseTitle" type="xs:string" minOccurs="0" />
              <xs:element name="CertificateDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="SignatoryText1" type="xs:string" minOccurs="0" />
              <xs:element name="SignatoryText2" type="xs:string" minOccurs="0" />
              <xs:element name="SignatoryText3" type="xs:string" minOccurs="0" />
              <xs:element name="SignatoryDesignationText1" type="xs:string" minOccurs="0" />
              <xs:element name="SignatoryDesignationText2" type="xs:string" minOccurs="0" />
              <xs:element name="SignatoryDesignationText3" type="xs:string" minOccurs="0" />
              <xs:element name="CourseDescription" type="xs:string" minOccurs="0" />
              <xs:element name="PicCertificate" type="xs:base64Binary" minOccurs="0" />
              <xs:element name="SignaturePicture1" type="xs:base64Binary" minOccurs="0" />
              <xs:element name="SignaturePicture2" type="xs:base64Binary" minOccurs="0" />
              <xs:element name="SignaturePicture3" type="xs:base64Binary" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>