{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { CertificateSettingRoutingModule } from './certificate-setting-routing.module';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport let CertificateSettingModule = /*#__PURE__*/(() => {\n  class CertificateSettingModule {}\n\n  CertificateSettingModule.ɵfac = function CertificateSettingModule_Factory(t) {\n    return new (t || CertificateSettingModule)();\n  };\n\n  CertificateSettingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CertificateSettingModule\n  });\n  CertificateSettingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, CertificateSettingRoutingModule, SharedModule]]\n  });\n  return CertificateSettingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}