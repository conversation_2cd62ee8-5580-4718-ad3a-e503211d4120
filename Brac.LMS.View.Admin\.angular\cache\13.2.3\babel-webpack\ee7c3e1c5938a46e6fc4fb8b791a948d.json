{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { TraineeCertificateTestListRoutingModule } from './trainee-certificate-test-list-routing.module';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport let TraineeCertificateTestListModule = /*#__PURE__*/(() => {\n  class TraineeCertificateTestListModule {}\n\n  TraineeCertificateTestListModule.ɵfac = function TraineeCertificateTestListModule_Factory(t) {\n    return new (t || TraineeCertificateTestListModule)();\n  };\n\n  TraineeCertificateTestListModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TraineeCertificateTestListModule\n  });\n  TraineeCertificateTestListModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, TraineeCertificateTestListRoutingModule, SharedModule]]\n  });\n  return TraineeCertificateTestListModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}