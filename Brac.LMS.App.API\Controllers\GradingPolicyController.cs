﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.IO;
using Brac.LMS.App.Services;
using Brac.LMS.Common;

namespace Brac.LMS.App.API.Controllers
{

    [Authorize(Roles = "Admin"), RoutePrefix("api/grading-policy")]
    public class GradingPolicyController : ApplicationController
    {
        private readonly IGradingPolicyService _service;

        public GradingPolicyController()
        {
            _service = new GradingPolicyService();
        }

        [HttpGet, Route("list")]
        public async Task<IHttpActionResult> GetGradingPolicyList(int size, int pageNumber)
        {
            return Ok(await _service.GetGradingPolicyList(size, pageNumber));
        }


        [HttpGet, Route("download-sample-file")]
        public HttpResponseMessage DownloadSampleFile()
        {
            if (!File.Exists(Generator.UploadSampleGradingPolicy))
            {
                return Request.CreateResponse(HttpStatusCode.NotFound);
            }
            else
            {
                HttpResponseMessage result = Request.CreateResponse(HttpStatusCode.OK);
                result.Content = new StreamContent(File.OpenRead(Generator.UploadSampleGradingPolicy));
                result.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/vnd.ms-excel");
                return result;
            }
        }


        [HttpPost, Route("upload")]
        public async Task<IHttpActionResult> UploadGradingPolicy()
        {
            var _nservice = new GradingPolicyService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.UploadGradingPolicy(User.Identity));
        }


        [HttpGet, Route("delete")]
        public async Task<IHttpActionResult> DeleteGradingPolicy()
        {
            var _nservice = new GradingPolicyService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.DeleteGradingPolicy());
        }
    }
}
