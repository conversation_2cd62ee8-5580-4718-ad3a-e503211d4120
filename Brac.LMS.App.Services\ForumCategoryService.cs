﻿using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;

namespace Brac.LMS.App.Services
{
    public class ForumCategoryService : IForumCategoryService
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditLogHelper _auditLogHelper;
        private readonly AuditLog audit;
        private readonly ApplicationUser _appUser;
        public ForumCategoryService()
        {
            _context = new ApplicationDbContext();
        }
        public ForumCategoryService(string controller, string method, ApplicationUser applicationUser)
        {
            _auditLogHelper = new AuditLogHelper(controller, method);
            audit = _auditLogHelper.auditLog;
            _appUser = applicationUser;
            audit.CreatedBy = _appUser.Id;
            _context = new ApplicationDbContext();
        }
        public async Task<APIResponse> CategoryCreateOrUpdate(CategoryModel model, ApplicationUser user)
        {
            ForumCategory item = null;
            bool isEdit = true;
            try
            {
                if (await _context.ForumCategories.AnyAsync(x => x.Id != model.Id && x.Name == model.Name))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Already exists: " + model.Name
                    };

                if (model.Id.HasValue)
                {
                    item = await _context.ForumCategories.FindAsync(model.Id);
                    if (item == null) return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Category not found"
                    };
                }
                else
                {
                    item = new ForumCategory();
                    isEdit = false;
                }

                item.Name = model.Name;
                item.Active = model.Active;

                item.SetAuditTrailEntity(user.User.Identity);

                if (!isEdit)
                {
                    _context.ForumCategories.Add(item);
                }
                else
                {
                    _context.Entry(item).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                };
            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())

                };
            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> AddOrModifyCategoryTags(ForumCategoryTagModel model, ApplicationUser user)
        {
            ForumCategory item = null;
            ForumTag forumTag = null;
            bool isEdit = true;
            try
            {
                item = await _context.ForumCategories.FindAsync(model.CategoryId);
                if (item == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Category not found"
                };


                if (item.Tags == null) item.Tags = new List<ForumTag>();
                item.Tags.Clear();

                var forumTags = await _context.ForumTags.Where(x => model.Tags.Select(y => y.ToLower()).Contains(x.Name.ToLower())).ToListAsync();

                foreach (var tag in model.Tags.Where(x => !forumTags.Select(y => y.Name.ToLower()).Contains(x.ToLower())))
                {
                    forumTag = new ForumTag
                    {
                        Name = tag
                    };
                    forumTag.SetAuditTrailEntity(user.User.Identity);
                    forumTags.Add(forumTag);
                }

                item.Tags = forumTags;

                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                };
            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetCategoryList(int size, int pagenumber)
        {
            try
            {
                var query = _context.ForumCategories.AsQueryable();


                //if (user.usertype == usertype.trainer) query = query.where(x => x.trainerid == user.trainer.id);


                var data = await query.Select(x => new
                {
                    x.Id,
                    x.Name,
                    x.Active,
                    Tags = x.Tags.Select(y => y.Name),
                }).OrderByDescending(x => x.Name)
                .Skip(pagenumber * size).Take(size).ToListAsync();

                var count = await query.CountAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data, Total = count }
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetCategoryDropDownList()
        {
            try
            {
                var query = _context.ForumCategories.Where(x => x.Active).AsQueryable();

                var data = await query.Select(x => new
                {
                    x.Id,
                    x.Name
                }).OrderByDescending(x => x.Name).ToListAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetCategoryById(long id)
        {
            try
            {
                var query = _context.ForumCategories.Where(t => t.Id == id).AsQueryable();

                var data = await query
                .Select(t => new
                {
                    t.Id,
                    t.Name,
                    t.Active
                }).FirstOrDefaultAsync();

                return new APIResponse
                {
                    Status = data == null ? ResponseStatus.Warning : ResponseStatus.Success,
                    Message = data == null ? "Category not found" : null,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetTagsByCategoryId(long id)
        {
            try
            {
                var query = _context.ForumTags.Where(t => t.CategoryId == id).AsQueryable();

                var data = await query
                .Select(t => new
                {
                    t.Id,
                    t.Name,
                }).ToListAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }
    }

    public interface IForumCategoryService
    {
        Task<APIResponse> CategoryCreateOrUpdate(CategoryModel model, ApplicationUser user);
        Task<APIResponse> AddOrModifyCategoryTags(ForumCategoryTagModel model, ApplicationUser user);
        Task<APIResponse> GetCategoryList(int size, int pageNumber);
        Task<APIResponse> GetCategoryDropDownList();
        Task<APIResponse> GetCategoryById(long id);
        Task<APIResponse> GetTagsByCategoryId(long id);

        //Task<APIResponse> GetCategoryById(Guid id, ApplicationUser user);
        //Task<APIResponse> GetCategoryDropDownList(ApplicationUser user);
        //Task<APIResponse> GetCategoryMaterialList(string name, Guid CategoryId, int size, int pageNumber);
        //Task<APIResponse> GetMaterialResources(Guid id);
    }
}
