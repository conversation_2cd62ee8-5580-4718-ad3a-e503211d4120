﻿using Brac.LMS.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.ViewModels
{
    public class FeedbackQuestionModel
    {
        public long? Id { get; set; }
        public QuestionGroup QuestionGroup { get; set; }
        public string Question { get; set; }
        public QuestionType QuestionType { get; set; }
        public List<string> Options { get; set; }
    }

    public class CourseFeedbackModel
    {
        public Guid CourseId { get; set; }
        public decimal Rating { get; set; }
        public string Comment { get; set; }
        public virtual ICollection<FeedbackAnswerModel> Feedbacks { get; set; }
    }

    public class LearningHourFeedbackModel
    {
        public Guid ExamId { get; set; }
        public virtual ICollection<FeedbackAnswerModel> Feedbacks { get; set; }
    }

    public class FeedbackAnswerModel
    {
        public FeedbackAnswerModel()
        {
            Answers = new List<string>();
        }
        public long QuestionId { get; set; }
        public List<string> Answers { get; set; }
    }
}
