﻿using Brac.LMS.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{

    public enum TraineeType { Permanent, Guest }
    public class Trainee : AuditableEntity
    {
        [Column(TypeName = "VARCHAR"), StringLength(50)]
        public string PIN { get; set; }


        [Required, StringLength(250)]
        public string Name { get; set; }

        public Gender Gender { get; set; }

        [DataType(DataType.EmailAddress), StringLength(250)]
        public string Email { get; set; }

        [StringLength(250)]
        public string PhoneNo { get; set; }

        private DateTime? _DateOfJoining;
        public DateTime? DateOfJoining
        {
            get
            { return _DateOfJoining; }
            set
            { _DateOfJoining = value.HasValue ? value.Value.ToKindUtc() : value; }
        }

        [StringLength(250)]
        public string Position { get; set; }


        [StringLength(250)]
        public string Grade { get; set; }


        [StringLength(500)]
        public string Address { get; set; }

        public long? DivisionId { get; set; }
        public virtual Division Division { get; set; }

        public long? DepartmentId { get; set; }
        public virtual Department Department { get; set; }

        public long? UnitId { get; set; }
        public virtual Unit Unit { get; set; }

        public long? SubUnitId { get; set; }
        public virtual SubUnit SubUnit { get; set; }


        [StringLength(250)]
        public string LineManagerName { get; set; }

        [StringLength(50), Column(TypeName = "VARCHAR")]
        public string LineManagerPIN { get; set; }

        [StringLength(500)]
        public string WorkLocation { get; set; }

        [StringLength(500)]
        public string EmployeeType { get; set; }

        public bool Active { get; set; }
        public TraineeType TraineeType { get; set; }

        [Required, StringLength(128)]
        public string UserId { get; set; }
        public virtual ApplicationUser User { get; set; }

        public virtual ICollection<ForumCategory> ForumCategories { get; set; }
        public virtual ICollection<OpenMaterial> OpenMaterials { get; set; }
        public virtual ICollection<EvaluationExam> EvaluationExams { get; set; }
        public virtual ICollection<TraineeExamAttempt> ExamAttempts { get; set; }
        public virtual ICollection<TraineeEvaluationExamAttempt> EvaluationExamAttempts { get; set; }
        public virtual ICollection<TraineeMockTestAttempt> MockTestAttempts { get; set; }
        public virtual ICollection<TraineeDevice> Devices { get; set; }

    }
}
