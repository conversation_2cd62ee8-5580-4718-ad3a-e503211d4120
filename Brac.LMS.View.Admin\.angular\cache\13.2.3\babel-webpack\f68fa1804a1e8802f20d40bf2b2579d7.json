{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\n\nfunction AlertComponent_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 3);\n    i0.ɵɵlistener(\"click\", function AlertComponent_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n\n      const _r0 = i0.ɵɵreference(1);\n\n      return ctx_r2.dismissAlert(_r0);\n    });\n    i0.ɵɵelementStart(1, \"span\", 4);\n    i0.ɵɵtext(2, \"\\u00D7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"alert-dismissible\": a0\n  };\n};\n\nconst _c1 = [\"*\"];\nexport let AlertComponent = /*#__PURE__*/(() => {\n  class AlertComponent {\n    constructor() {}\n\n    dismissAlert(element) {\n      element.parentElement.removeChild(element);\n    }\n\n    ngOnInit() {}\n\n  }\n\n  AlertComponent.ɵfac = function AlertComponent_Factory(t) {\n    return new (t || AlertComponent)();\n  };\n\n  AlertComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AlertComponent,\n    selectors: [[\"app-alert\"]],\n    inputs: {\n      type: \"type\",\n      dismiss: \"dismiss\"\n    },\n    ngContentSelectors: _c1,\n    decls: 4,\n    vars: 7,\n    consts: [[\"role\", \"alert\", 3, \"ngClass\"], [\"alert\", \"\"], [\"type\", \"button\", \"class\", \"close\", \"data-dismiss\", \"alert\", \"aria-label\", \"Close\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"data-dismiss\", \"alert\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"]],\n    template: function AlertComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0, 1);\n        i0.ɵɵprojection(2);\n        i0.ɵɵtemplate(3, AlertComponent_button_3_Template, 3, 0, \"button\", 2);\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵclassMapInterpolate1(\"alert alert-\", ctx.type, \"\");\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, ctx.dismiss));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.dismiss);\n      }\n    },\n    directives: [i1.NgClass, i1.NgIf],\n    styles: [\"\"]\n  });\n  return AlertComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}