﻿using System;

namespace Brac.LMS.Models
{
    public class CourseViewModel
    {
       // [Required, Column(TypeName = "VARCHAR"), StringLength(15)]
        public Guid Id { get; set; }
        public string Code { get; set; }
        //[Required, StringLength(250)]
        public string Title { get; set; }
        public string Description { get; set; }
        //[Column(TypeName = "VARCHAR"), StringLength(250)]
        public string ImagePath { get; set; }
        public bool Active { get; set; }
        public bool SelfEnrollment { get; set; }
        public bool Published { get; set; }
        public bool DependencySet { get; set; }
        public int NoOfRating { get; set; }
        public DateTime? CreatedDate { get; set; }
        //public int NoOfContents { get; set; }
        public decimal TotalRatings { get; set; }
        public decimal Rating { get; set; }
        public int? ExpiryMonth { get; set; }
        public int? CertificateExpiryMonth { get; set; }
        public long? CategoryId { get; set; }
        
        public  string CategoryName { get; set; }
        //public virtual CourseCategory Category { get; set; }
        public string TraineeId { get; set; }
        //public virtual Trainee Trainee { get; set; }
        public string CourseId { get; set; }
        //public virtual Course Course { get; set; }
        public int? NoOfContents { get; set; }
        public int? NoOfContentsStudied { get; set; }
    }
}
