{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { CourseMaterialRoutingModule } from './course-material-routing.module';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';\nimport * as i0 from \"@angular/core\";\nexport let CourseMaterialModule = /*#__PURE__*/(() => {\n  class CourseMaterialModule {}\n\n  CourseMaterialModule.ɵfac = function CourseMaterialModule_Factory(t) {\n    return new (t || CourseMaterialModule)();\n  };\n\n  CourseMaterialModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CourseMaterialModule\n  });\n  CourseMaterialModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, CourseMaterialRoutingModule, SharedModule, NgbTooltipModule]]\n  });\n  return CourseMaterialModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}