﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class AuditLog 
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Required]
        public int Id { get; set; }
        [Required, StringLength(30)]
        public string Controller { get; set; }
        [Required, StringLength(30)]
        public string Method { get; set; }
        [Required]
        public  string CreatedBy { get; set; }
        [Required]
        public DateTime CreatedOn { get; set; }
        [Required] 
        public DateTime EndTime { get; set;}

        public string Request { get; set;}
      
        public string Response { get; set;}
        [Required]
        public bool Status { get; set; } = false;
        [Required]
        public string StatusCode { get; set; }
        [Required]
        public bool IsDelete { get; set;}

    }
}
