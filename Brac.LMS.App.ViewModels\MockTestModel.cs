﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.ViewModels
{
    public class MockTestModel
    {
        public MockTestModel()
        {
            MCQs = new List<MCQQuestionModel>();
        }
        public Guid? Id { get; set; }
        public Guid CourseId { get; set; }
        public string ExamName { get; set; }
        public string ExamInstructions { get; set; }
        public int Quota { get; set; }
        //public int Marks { get; set; }
        public int DurationMnt { get; set; }
        public int MCQNo { get; set; }
        public List<MCQQuestionModel> MCQs { get; set; }
    }
}
