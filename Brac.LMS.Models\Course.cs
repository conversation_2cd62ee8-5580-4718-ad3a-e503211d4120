﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class Course : AuditableEntity
    {
        [Required, Column(TypeName = "VARCHAR"), StringLength(15)]
        public string Code { get; set; }


        [Required, StringLength(250)]
        public string Title { get; set; }
        [Required, StringLength(250)]
        public string ShortTitle { get; set; }
        

        public string Description { get; set; }


        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string ImagePath { get; set; }

        public bool Active { get; set; }
        public bool SelfEnrollment { get; set; }
        public bool Published { get; set; }
        public bool DependencySet { get; set; }
        public int NoOfRating { get; set; }
        public int NoOfContents { get; set; }
        public decimal TotalRatings { get; set; }
        public decimal Rating { get; set; }
        public int? ExpiryMonth { get; set; }
        public int? CertificateExpiryMonth { get; set; }

        public long? CategoryId { get; set; }
        public virtual CourseCategory Category { get; set; }

        public virtual ICollection<CourseMaterial> Materials { get; set; }
        public virtual ICollection<CourseMockTest> MockTests { get; set; }
        public virtual ICollection<CourseContentDependency> Dependencies { get; set; }
    


        public void UpdateRating(int rating)
        {
            NoOfRating++;
            TotalRatings += rating;
            Rating = NoOfRating > 0 ? TotalRatings / NoOfRating : 0;
        }
    }


    public enum CourseContentType { Material, Exam }

    public class CourseContentDependency : NumberAuditableEntity
    {
        public Guid CourseId { get; set; }
        public virtual Course Course { get; set; }

        public Guid ContentId { get; set; }
        public CourseContentType Type { get; set; }
        public int Sequence { get; set; }

        [NotMapped]
        public bool Modified { get; set; }

    }
}
