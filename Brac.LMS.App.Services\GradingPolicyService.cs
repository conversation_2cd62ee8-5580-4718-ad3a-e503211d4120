﻿using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Brac.LMS.App.Services
{
    public class GradingPolicyService : IGradingPolicyService
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditLogHelper _auditLogHelper;
        private readonly AuditLog audit;
        private readonly ApplicationUser _appUser;
        public GradingPolicyService()
        {
            _context = new ApplicationDbContext();
        }
        public GradingPolicyService(string controller, string method, ApplicationUser applicationUser)
        {
            _auditLogHelper = new AuditLogHelper(controller, method);
            audit = _auditLogHelper.auditLog;
            _appUser = applicationUser;
            audit.CreatedBy = _appUser.Id;
            _context = new ApplicationDbContext();
        }
        //public async Task<APIResponse> GradingPolicyUpdate(GradingPolicyModel model, IIdentity identity)
        //{
        //    try
        //    {
        //        var item = await _context.GradingPolicies.FindAsync(model.Id);
        //        if (item == null) throw new Exception("Grading policy not found");

        //        item.GradeLetter = model.GradeLetter;
        //        item.MinValue = model.MinValue;
        //        item.Range = model.Range;
        //        item.Range = model.Range;

        //        item.SetAuditTrailEntity(identity);

        //        _context.Entry(item).State = EntityState.Modified;
        //        await _context.SaveChangesAsync();

        //        return new
        //        {
        //            Success = true,
        //            Message = "Successfully Updated"
        //        };
        //    }
        //    catch (Exception ex)
        //    {
        //        return new
        //        {
        //            Success = false,
        //            ex.Message
        //        };
        //    }
        //}

        public async Task<APIResponse> GetGradingPolicyList(int size, int pageNumber)
        {
            try
            {
                var data = await _context.GradingPolicies.Where(x => x.Active).Select(x => new { x.Id, x.GradeLetter, x.MinValue, x.Range, Result = x.Result.ToString() }).OrderByDescending(x => x.MinValue)
                            .ToListAsync();
                var count = await _context.GradingPolicies.Where(x => x.Active).CountAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Records = data,
                        Total = count
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        //public async Task<APIResponse> GetGradingPolicyById(long id)
        //{
        //    try
        //    {
        //        var data = await _context.GradingPolicies.Where(t => t.Id == id)
        //        .Select(t => new
        //        {
        //            t.Id,
        //            t.Grade,
        //            t.LowerNumber,
        //            t.Description
        //        }).FirstOrDefaultAsync();
        //        return new
        //        {
        //            Success = true,
        //            Record = data
        //        };
        //    }
        //    catch (Exception ex)
        //    {
        //        return new
        //        {
        //            Success = false,
        //            ex.Message
        //        };
        //    }
        //}

        public async Task<APIResponse> UploadGradingPolicy(IIdentity identity)
        {
            try
            {
                HttpPostedFile file = HttpContext.Current.Request.Files[0];
                var data = ExcelParser.Parse(file);

                var header = data.Item1;
                var lines = data.Item2.ToList();

                var indexes = new Dictionary<int, ColumnType>
                {
                    {0, ColumnType.String },
                    {1, ColumnType.Int },
                    {2, ColumnType.String },
                    {3, ColumnType.Int }
                };

                if (lines.Count() != lines.Select(x => x[1]).Distinct().Count())
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Duplicate min value found. Please fix them and then try to upload again."
                    };

                if (lines.Count() != lines.Select(x => x[2]).Distinct().Count())
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Duplicate grade letter found. Please fix them and then try to upload again."
                    };

                if (lines.All(x => x[3] != "0"))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Failed grade not found. Please fix it and then try to upload again."
                    };

                if (lines.Count(x => x[3] == "0") > 1)
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Failed grade found more than 1. Please fix it and then try to upload again."
                    };

                await Validator.VerifyUploadFileValue(lines, indexes);

                var policyList = await _context.GradingPolicies.Where(x => x.Active).ToListAsync();
                var groupCode = policyList.Any() ? policyList.FirstOrDefault().GroupCode++ : 1;

                foreach (var item in policyList)
                {
                    item.Active = false;
                    _context.Entry(item).State = EntityState.Modified;
                }

                GradingPolicy gradingPolicy;
                foreach (var line in lines)
                {
                    gradingPolicy = new GradingPolicy
                    {
                        GradeLetter = line[2],
                        MinValue = int.Parse(line[1]),
                        Range = line[0],
                        Result = line[3] == "0" ? GradeResult.Failed : GradeResult.Passed,
                        GroupCode = groupCode,
                        Active = true
                    };
                    gradingPolicy.SetAuditTrailEntity(identity);
                    gradingPolicy.Id = Guid.NewGuid();
                    _context.GradingPolicies.Add(gradingPolicy);

                }
                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Uploaded"
                    
                };

            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())

                };
            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }

        }

        public async Task<APIResponse> DeleteGradingPolicy()
        {
            try
            {
                var items = await _context.GradingPolicies.Where(t => t.Active).ToListAsync();

                var groupCode = items.Select(x => x.GroupCode).FirstOrDefault();

                if (await _context.TraineeExams.AnyAsync(x => x.GradingGroup == groupCode))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Grading policy can not be deleted now, This policy has already been used for one or more result-processing."
                    };

                foreach (var item in items)
                {
                    _context.Entry(item).State = EntityState.Deleted;
                }

                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Message = "Successfully Deleted"
                    }
                };

            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

    }

    public interface IGradingPolicyService
    {
        //Task<APIResponse> GradingPolicyUpdate(GradingPolicy model, IIdentity identity);
        Task<APIResponse> GetGradingPolicyList(int size, int pageNumber);
        //Task<APIResponse> GetGradingPolicyById(long id);
        Task<APIResponse> UploadGradingPolicy(IIdentity identity);
        Task<APIResponse> DeleteGradingPolicy();
    }
}
