﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class TraineeMockTest : AuditableEntity
    {
        public TraineeMockTest()
        {
            this.MCQMockTestAnswers = new HashSet<MCQMockTestAnswer>();
        }

        public Guid TraineeId { get; set; }
        public virtual Trainee Trainee { get; set; }

        public Guid ExamId { get; set; }
        public virtual CourseMockTest Exam { get; set; }

        public ExamStatus Status { get; set; }
        public bool AutoSubmission { get; set; }
        public int TotalMarks { get; set; }
        public float GainedMarks { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int ObtainedParcentage { get; set; }
        public int NoOfQuestion { get; set; }
        public int NoOfCorrectAnsweredQs { get; set; }

        public virtual ICollection<MCQMockTestAnswer> MCQMockTestAnswers { get; set; }
    }

    public class TraineeMockTestAttempt
    {
        [Key, Column(Order = 1)]
        public Guid TraineeId { get; set; }
        public virtual Trainee Trainee { get; set; }

        [Key, Column(Order = 2)]
        public Guid ExamId { get; set; }
        public virtual CourseMockTest Exam { get; set; }

        public int Attempt { get; set; }
    }
}
