{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NotificationSetupRoutingModule } from './notification-setup-routing.module';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport let NotificationSetupModule = /*#__PURE__*/(() => {\n  class NotificationSetupModule {}\n\n  NotificationSetupModule.ɵfac = function NotificationSetupModule_Factory(t) {\n    return new (t || NotificationSetupModule)();\n  };\n\n  NotificationSetupModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: NotificationSetupModule\n  });\n  NotificationSetupModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, NotificationSetupRoutingModule, SharedModule]]\n  });\n  return NotificationSetupModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}