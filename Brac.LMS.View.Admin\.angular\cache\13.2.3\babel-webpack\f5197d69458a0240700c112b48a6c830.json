{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { BasicTypographyComponent } from './basic-typography.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: BasicTypographyComponent\n}];\nexport let BasicTypographyRoutingModule = /*#__PURE__*/(() => {\n  class BasicTypographyRoutingModule {}\n\n  BasicTypographyRoutingModule.ɵfac = function BasicTypographyRoutingModule_Factory(t) {\n    return new (t || BasicTypographyRoutingModule)();\n  };\n\n  BasicTypographyRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: BasicTypographyRoutingModule\n  });\n  BasicTypographyRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return BasicTypographyRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}