﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using Brac.LMS.DB;
using Microsoft.AspNet.Identity.Owin;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [RoutePrefix("api/library")]
    public class LibraryController : ApplicationController
    {
        private readonly ILibraryService _service;

        public LibraryController()
        {
            _service = new LibraryService();
        }
        public ApplicationUserManager UserManager
        {
            get
            {
                return Request.GetOwinContext().GetUserManager<ApplicationUserManager>();
            }
        }


        [Authorize(Roles = "Admin"), HttpPost, Route("create-or-update")]
        public async Task<IHttpActionResult> LibraryContentCreateOrUpdate()
        {
            try
            {
                var _nservice = new LibraryService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
                var model = JsonConvert.DeserializeObject<LibraryModel>(System.Web.HttpContext.Current.Request.Form["Model"]);
                return Ok(await _nservice.LibraryContentCreateOrUpdate(model, User.Identity));
            }
            catch (Exception ex)
            {
                return Ok(new { Success = false, ex.Message });
            }

        }


        [Authorize(Roles = "Admin"), HttpGet, Route("get/{id}")]
        public async Task<IHttpActionResult> GetLibraryContentById(long id)
        {
            return Ok(await _service.GetLibraryContentById(id));
        }


        [Authorize(Roles = "Admin,Trainee"), HttpGet, Route("list")]
        public async Task<IHttpActionResult> GetLibraryContentList(string name, long? categoryId, int size, int pageNumber)
        {
            return Ok(await _service.GetLibraryContentList(name, categoryId, size, pageNumber));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("delete/{id}")]
        public async Task<IHttpActionResult> DeleteLibraryContentById(long id)
        {
            return Ok(await _service.DeleteLibraryContentById(id));
        }
    }
}
