﻿using Brac.LMS.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public enum DiscussionType { Video, Document, MockTest, CertificationTest, Course }
    public class CourseDiscussion : NumberEntityField
    {
        public Guid CourseId { get; set; }
        public virtual Course Course { get; set; }

        public DiscussionType DiscussionType { get; set; }

        public Guid? MaterialId { get; set; }
        public virtual CourseMaterial Material { get; set; }


        public Guid? ExamId { get; set; }
        public virtual CourseExam Exam { get; set; }


        public Guid? MockTestId { get; set; }
        public virtual CourseMockTest MockTest { get; set; }


        [StringLength(2000)]
        public string Comment { get; set; }


        private DateTime _CommentTime;
        public DateTime CommentTime {
            get
            { return _CommentTime; }
            set
            { _CommentTime = value.ToKindUtc(); }
        }

        public UserType CommentatorType { get; set; }

        [Required]
        public string CommentatorId { get; set; }
        public virtual ApplicationUser Commentator { get; set; }


    }
}
