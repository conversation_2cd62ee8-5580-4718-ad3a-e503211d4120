﻿using ClosedXML.Excel;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Common
{
    public static class ExcelManager
    {
        public static void GetTableHeaderCell(List<string> headers, int rowNo, IXLWorksheet ws, float fontSize = 10f, XLAlignmentHorizontalValues alignment = XLAlignmentHorizontalValues.Left, string fontName = "Tahoma")
        {
            try
            {
                var cellNo = 1;
                foreach (var item in headers)
                {
                    ws.Cell(rowNo, cellNo).Value = item;
                    ws.Cell(rowNo, cellNo).Style.Font.Bold = true;
                    ws.Cell(rowNo, cellNo).Style.Font.FontSize = fontSize;
                    ws.Cell(rowNo, cellNo).Style.Alignment.SetHorizontal(alignment);
                    ws.Cell(rowNo, cellNo).Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center);
                    ws.Cell(rowNo, cellNo).Style.Font.FontName = fontName;
                    cellNo++;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Error on GetTableHeaderCell: " + ex.Message);
            }
        }

        public static void GetTableDataCell(object cellValue, float fontSize, int rowNo, int cellNo, IXLWorksheet ws, XLAlignmentHorizontalValues alignment = XLAlignmentHorizontalValues.Left, string fontName = "Tahoma", bool isBold = false, XLDataType dataType = XLDataType.TimeSpan)
        {
            try
            {

                ws.Cell(rowNo, cellNo).Style.Font.Bold = isBold;
                ws.Cell(rowNo, cellNo).Style.Font.FontSize = fontSize;
                ws.Cell(rowNo, cellNo).Style.Alignment.SetHorizontal(alignment);
                ws.Cell(rowNo, cellNo).Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center);
                ws.Cell(rowNo, cellNo).Style.Font.FontName = fontName;
                if (dataType == XLDataType.Text)
                {
                    ws.Cell(rowNo, cellNo).SetValue(Convert.ToString(cellValue));
                    ws.Cell(rowNo, cellNo).DataType = dataType;
                }
                else if (dataType == XLDataType.TimeSpan)
                {
                    ws.Cell(rowNo, cellNo).Value = Convert.ToString(cellValue);
                }
                else
                {
                    ws.Cell(rowNo, cellNo).Value = Convert.ToString(cellValue);
                    ws.Cell(rowNo, cellNo).DataType = dataType;
                }
                //switch (dataType)
                //{
                //    case XLDataType.Text:
                //        ws.Cell(rowNo, cellNo).SetValue(Convert.ToString(cellValue));
                //        ws.Cell(rowNo, cellNo).DataType = dataType;
                //        break;
                //    case XLDataType.Number:
                //    case XLDataType.Boolean:
                //    case XLDataType.DateTime:
                //        ws.Cell(rowNo, cellNo).Value = Convert.ToString(cellValue);
                //        ws.Cell(rowNo, cellNo).DataType = dataType;
                //        break;
                //    case XLDataType.TimeSpan:
                //        break;
                //    default:
                //        break;
                //}
            }
            catch (Exception ex)
            {
                throw new Exception("Error on GetTableDataCell: " + ex.Message);
            }

        }

        public static void GetDataCell(object txt, IXLWorksheet ws, int rowNo, int colNo, int colspan = 1, int rowspan = 1, string bgColor = null, float fontSize = 10f, string fontName = "Tahoma", string fontColor = null, XLAlignmentHorizontalValues alignment = XLAlignmentHorizontalValues.Left, bool isBold = false, bool hasUnderline = false)
        {
            try
            {
                ws.Cell(rowNo, colNo).Value = txt.ToString();
                ws.Cell(rowNo, colNo).Style.Font.Bold = isBold;
                ws.Cell(rowNo, colNo).Style.Font.FontSize = fontSize;
                ws.Cell(rowNo, colNo).Style.Font.Underline = hasUnderline ? XLFontUnderlineValues.Single : XLFontUnderlineValues.None;
                ws.Cell(rowNo, colNo).Style.Alignment.SetHorizontal(alignment);
                ws.Cell(rowNo, colNo).Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center);
                if (fontColor != null) ws.Cell(rowNo, colNo).Style.Font.FontColor = XLColor.FromHtml(fontColor);
                if (bgColor != null) ws.Cell(rowNo, colNo).Style.Fill.BackgroundColor = XLColor.FromHtml(bgColor);
                ws.Cell(rowNo, colNo).Style.Font.FontName = fontName;
                if (colspan > 1) ws.Range(ws.Cell(rowNo, colNo), ws.Cell(rowNo, (colNo + colspan - 1))).Merge();
                if (rowspan > 1) ws.Range(ws.Cell(rowNo, colNo), ws.Cell(rowNo + rowspan - 1, colNo)).Merge();
            }
            catch (Exception ex)
            {
                throw new Exception("Error on GetDataCell: " + ex.Message);
            }

        }


        public static void GetTextLineElement(string txt, int rowNo, IXLWorksheet ws, int colNo = 1, int colspan = 1, float fontSize = 10f, string fontName = "Tahoma", string fontColor = "#000000", string bgColor = null, bool isBold = false, bool isUnderlined = false, XLAlignmentHorizontalValues alignment = XLAlignmentHorizontalValues.Left)
        {
            try
            {
                ws.Cell(rowNo, colNo).Value = txt;
                ws.Cell(rowNo, colNo).Style.Font.Bold = isBold;
                ws.Cell(rowNo, colNo).Style.Font.FontSize = fontSize;
                ws.Cell(rowNo, colNo).Style.Alignment.SetHorizontal(alignment);
                ws.Cell(rowNo, colNo).Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center);
                ws.Cell(rowNo, colNo).Style.Font.FontColor = XLColor.FromHtml(fontColor);
                if (bgColor != null) ws.Cell(rowNo, colNo).Style.Fill.BackgroundColor = XLColor.FromHtml(bgColor);
                ws.Cell(rowNo, colNo).Style.Font.Underline = isUnderlined ? XLFontUnderlineValues.Single : XLFontUnderlineValues.None;
                ws.Cell(rowNo, colNo).Style.Font.FontName = fontName;
                if (colspan > 1) ws.Range(ws.Cell(rowNo, colNo), ws.Cell(rowNo, (colNo + colspan - 1))).Merge();
            }
            catch (Exception ex)
            {
                throw new Exception("Error on GetTextLineElement: " + ex.Message);
            }

        }


        public static void GetImageElement(Bitmap img, int rowNo, IXLWorksheet ws, int colNo = 1, int colspan = 1, XLAlignmentHorizontalValues alignment = XLAlignmentHorizontalValues.Center)
        {
            try
            {
                ws.AddPicture(img).MoveTo(ws.Cell(rowNo, colNo));
                ws.Cell(rowNo, colNo).Style.Alignment.SetHorizontal(alignment);
                ws.Cell(rowNo, colNo).Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center);
                if (colspan > 1) ws.Range(ws.Cell(rowNo, colNo), ws.Cell(rowNo, (colNo + colspan - 1))).Merge();
            }
            catch (Exception ex)
            {
                throw new Exception("Error on GetImageElement: " + ex.Message);
            }

        }
    }

}
