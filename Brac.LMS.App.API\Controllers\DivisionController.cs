﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [Authorize(Roles = "Admin"), RoutePrefix("api/division")]
    public class DivisionController : ApplicationController
    {
        private readonly IDivisionService _service;

        public DivisionController()
        {
            _service = new DivisionService();
        }

        [HttpPost, Route("create-or-update")]
        public async Task<IHttpActionResult> DivisionCreateOrUpdate(DivisionModel model)
        {
            var _nservice = new DivisionService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.DivisionCreateOrUpdate(model, User.Identity));
        }

        [HttpGet, Route("list")]
        public async Task<IHttpActionResult> GetDivisionList(string name, int size, int pageNumber)
        {
            return Ok(await _service.GetDivisionList(name, size, pageNumber));
        }

        [HttpGet, Route("dropdown-list")]
        public async Task<IHttpActionResult> GetDivisionDropDownList()
        {
            return Ok(await _service.GetDivisionDropDownList());
        }

        [HttpGet, Route("get/{id}")]
        public async Task<IHttpActionResult> GetDivisionById(long id)
        {
            return Ok(await _service.GetDivisionById(id));
        }

        [HttpGet, Route("delete/{id}")]
        public async Task<IHttpActionResult> DeleteDivisionById(long id)
        {
            var _nservice = new DivisionService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.DeleteDivisionById(id));
        }
    }
}
