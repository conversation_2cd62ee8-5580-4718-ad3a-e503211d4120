﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using Brac.LMS.DB;
using Microsoft.AspNet.Identity.Owin;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [RoutePrefix("api/course-segment")]
    public class CourseSegmentController : ApplicationController
    {
        private readonly ICourseSegmentService _service;

        public CourseSegmentController()
        {
            _service = new CourseSegmentService();
        }
        public ApplicationUserManager UserManager
        {
            get
            {
                return Request.GetOwinContext().GetUserManager<ApplicationUserManager>();
            }
        }

        [Authorize(Roles = "Admin"), HttpPost, Route("create-or-update")]
        public async Task<IHttpActionResult> SegmentCreateOrUpdate(SegmentModel model)
        {
            var _nservice = new CourseSegmentService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.SegmentCreateOrUpdate(model, CurrentUser));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("list")]
        public async Task<IHttpActionResult> GetSegmentList(int size, int pageNumber)
        {
            return Ok(await _service.GetSegmentList(size, pageNumber));
        }

        [Authorize(Roles = "Admin,Trainee"), HttpGet, Route("dropdown-list")]
        public async Task<IHttpActionResult> GetSegmentDropDownList()
        {
            return Ok(await _service.GetSegmentDropDownList());
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get/{id}")]
        public async Task<IHttpActionResult> GetCourseById(long id)
        {
            return Ok(await _service.GetSegmentById(id));
        }
    }
}
