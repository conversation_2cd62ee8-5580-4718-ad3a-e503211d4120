﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class ForumCategory : NumberAuditableEntity
    {
        [Required, StringLength(250)]
        public string Name { get; set; }
        public bool Active { get; set; }

        public virtual ICollection<ForumTag> Tags { get; set; }
        public virtual ICollection<Trainee> Trainees { get; set; }
        public virtual ICollection<ForumTopic> ForumTopics { get; set; }
    }

    public class ForumTag : NumberAuditableEntity
    {
        public long CategoryId { get; set; }
        public virtual ForumCategory Category { get; set; }

        [Required, StringLength(250)]
        public string Name { get; set; }

        public virtual ICollection<ForumTopic> Topics { get; set; }
    }
}
