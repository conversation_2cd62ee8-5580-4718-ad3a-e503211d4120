{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Validators } from '@angular/forms';\nimport { BlockUI } from 'ng-block-ui';\nimport { ColumnMode } from '@swimlane/ngx-datatable';\nimport { Page } from '../_models/page';\nimport { debounceTime } from 'rxjs/operators';\nimport { ResponseStatus } from '../_models/enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"ngx-toastr\";\nimport * as i3 from \"../_services/common.service\";\nimport * as i4 from \"../_helpers/confirm-dialog/confirm.service\";\nimport * as i5 from \"ng-block-ui\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/flex-layout/extended\";\nimport * as i8 from \"@swimlane/ngx-datatable\";\n\nfunction SpecialityComponent_div_12_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 35);\n    i0.ɵɵtext(1, \" Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SpecialityComponent_div_12_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 35);\n    i0.ɵɵtext(1, \"Name must be atleast 1 character\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SpecialityComponent_div_12_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 35);\n    i0.ɵɵtext(1, \"Name must be not than 250 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SpecialityComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, SpecialityComponent_div_12_span_1_Template, 2, 0, \"span\", 34);\n    i0.ɵɵtemplate(2, SpecialityComponent_div_12_span_2_Template, 2, 0, \"span\", 34);\n    i0.ɵɵtemplate(3, SpecialityComponent_div_12_span_3_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.name.errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.name.errors.minlength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.name.errors.maxlength);\n  }\n}\n\nfunction SpecialityComponent_ng_template_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const rowIndex_r10 = ctx.rowIndex;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.page.pageNumber * ctx_r1.page.size + rowIndex_r10 + 1, \" \");\n  }\n}\n\nfunction SpecialityComponent_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r11 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r11);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r11, \" \");\n  }\n}\n\nfunction SpecialityComponent_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r12 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r12);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r12, \" \");\n  }\n}\n\nfunction SpecialityComponent_ng_template_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r13 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r13 === true ? \"Yes\" : \"No\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r13 === true ? \"Yes\" : \"No\", \" \");\n  }\n}\n\nfunction SpecialityComponent_ng_template_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function SpecialityComponent_ng_template_48_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const row_r14 = restoredCtx.row;\n      const ctx_r15 = i0.ɵɵnextContext();\n      return ctx_r15.deleteItem(row_r14.Id);\n    });\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵtext(2, \" Delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function SpecialityComponent_ng_template_48_Template_button_click_3_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const row_r14 = restoredCtx.row;\n      const ctx_r17 = i0.ɵɵnextContext();\n      return ctx_r17.getItem(row_r14.Id);\n    });\n    i0.ɵɵelement(4, \"i\", 40);\n    i0.ɵɵtext(5, \" Edit\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nexport class SpecialityComponent {\n  constructor(formBuilder, toastr, _service, confirmService) {\n    this.formBuilder = formBuilder;\n    this.toastr = toastr;\n    this._service = _service;\n    this.confirmService = confirmService;\n    this.submitted = false;\n    this.formTitle = 'Create Speciality';\n    this.btnSaveText = 'Save';\n    this.modalLgConfig = {\n      class: 'gray modal-md',\n      backdrop: 'static'\n    };\n    this.page = new Page();\n    this.rows = [];\n    this.loadingIndicator = false;\n    this.ColumnMode = ColumnMode;\n    this.scrollBarHorizontal = false;\n    this.page.pageNumber = 0;\n    this.page.size = 10;\n\n    window.onresize = () => {\n      this.scrollBarHorizontal = window.innerWidth < 1200;\n    };\n  }\n\n  ngOnInit() {\n    this.entryForm = this.formBuilder.group({\n      id: [null],\n      name: [null, [Validators.required, Validators.minLength(1), Validators.maxLength(250)]],\n      active: [true]\n    });\n    this.filterForm = this.formBuilder.group({\n      name: [null]\n    });\n    this.filterForm.get(\"name\").valueChanges.pipe(debounceTime(700)).subscribe(() => {\n      this.getList();\n    });\n    this.getList();\n  }\n\n  get f() {\n    return this.entryForm.controls;\n  }\n\n  setPage(pageInfo) {\n    this.page.pageNumber = pageInfo.offset;\n    this.getList();\n  }\n\n  getList() {\n    this.loadingIndicator = true;\n    const obj = {\n      name: this.filterForm.value.name ? this.filterForm.value.name.trim() : null,\n      size: this.page.size,\n      pageNumber: this.page.pageNumber\n    };\n\n    this._service.get('speciality/list', obj).subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.rows = res.Data.Records;\n      this.page.totalElements = res.Data.Total;\n      this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n      setTimeout(() => {\n        this.loadingIndicator = false;\n      }, 1000);\n    }, err => {\n      this.toastr.warning(err.Messaage || err, 'Warning!', {\n        closeButton: true,\n        disableTimeOut: false\n      });\n      setTimeout(() => {\n        this.loadingIndicator = false;\n      }, 1000);\n    });\n  }\n\n  getItem(id) {\n    this.blockUI.start('Getting data...');\n\n    this._service.get('speciality/get/' + id).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.formTitle = 'Update Speciality';\n      this.btnSaveText = 'Update';\n      this.entryForm.controls['id'].setValue(res.Data.Record.Id);\n      this.entryForm.controls['name'].setValue(res.Data.Record.Name);\n      this.entryForm.controls['active'].setValue(res.Data.Record.Active);\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.warning(err.Message || err, 'Warning!', {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  deleteItem(id) {\n    this.confirmService.confirm('Are you sure?', 'You are deleting this speciality.').subscribe(result => {\n      if (result) {\n        this.blockUI.start('Deleting data...');\n\n        this._service.get('speciality/delete/' + id).subscribe(res => {\n          this.blockUI.stop();\n\n          if (res.Status === ResponseStatus.Warning) {\n            this.toastr.warning(res.Message, 'Warning!', {\n              timeOut: 2000\n            });\n            return;\n          } else if (res.Status === ResponseStatus.Error) {\n            this.toastr.error(res.Message, 'Error!', {\n              closeButton: true,\n              disableTimeOut: false,\n              enableHtml: true\n            });\n            return;\n          }\n\n          this.toastr.success(res.Message, 'SUCCESS!', {\n            progressBar: true\n          });\n          this.getList();\n        });\n      }\n    });\n  }\n\n  onFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid) {\n      return;\n    }\n\n    this.blockUI.start('Saving...');\n    const obj = {\n      Id: this.entryForm.value.id,\n      Name: this.entryForm.value.name.trim(),\n      Active: this.entryForm.value.active\n    };\n\n    const request = this._service.post('speciality/create-or-update', obj);\n\n    request.subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, 'Success!', {\n        timeOut: 2000\n      });\n      this.getList();\n      this.clearForm();\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.warning(err.Message || err, 'Warning!', {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  clearForm() {\n    this.entryForm.reset();\n    this.submitted = false;\n    this.formTitle = 'Create Speciality';\n    this.btnSaveText = 'Save';\n    this.entryForm.controls['active'].setValue(true);\n  }\n\n}\n\nSpecialityComponent.ɵfac = function SpecialityComponent_Factory(t) {\n  return new (t || SpecialityComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ToastrService), i0.ɵɵdirectiveInject(i3.CommonService), i0.ɵɵdirectiveInject(i4.ConfirmService));\n};\n\nSpecialityComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: SpecialityComponent,\n  selectors: [[\"app-speciality\"]],\n  decls: 49,\n  vars: 31,\n  consts: [[1, \"row\"], [1, \"col-lg-3\", \"col-md-5\", \"col-sm-12\"], [1, \"card\", \"card-border-default\"], [1, \"card-header\"], [3, \"innerHtml\"], [1, \"card-body\"], [\"autocomplete\", \"off\", 3, \"formGroup\"], [1, \"mb-3\"], [1, \"col-form-label\", \"col-form-label-sm\", \"required\"], [\"type\", \"text\", \"formControlName\", \"name\", \"type\", \"text\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"class\", \"error-text\", 4, \"ngIf\"], [1, \"custom-control\", \"custom-checkbox\"], [\"formControlName\", \"active\", \"id\", \"active\", \"type\", \"checkbox\", 1, \"custom-control-input\"], [\"for\", \"active\", 1, \"custom-control-label\"], [1, \"btn\", \"btn-theme\", \"btn-testz\", \"btn-sm\", 3, \"click\"], [1, \"feather\", \"icon-save\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"ms-2\", 3, \"click\"], [1, \"feather\", \"icon-refresh-cw\"], [1, \"col-lg-9\", \"col-md-7\", \"col-sm-12\"], [1, \"card-block\"], [1, \"col-12\"], [\"autocomplete\", \"off\", 1, \"row\", 3, \"formGroup\"], [1, \"col-lg-8\", \"col-md-10\", \"col-12\"], [1, \"mb-3\", \"row\"], [\"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Search By name/code\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-lg-12\"], [\"rowHeight\", \"auto\", 1, \"material\", \"table-bordered\", 3, \"rows\", \"loadingIndicator\", \"externalPaging\", \"columnMode\", \"headerHeight\", \"footerHeight\", \"count\", \"offset\", \"limit\", \"page\"], [\"name\", \"SL#\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"ngx-datatable-cell-template\", \"\"], [\"prop\", \"Code\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"prop\", \"Name\", 3, \"draggable\", \"sortable\"], [\"prop\", \"Active\", \"headerClass\", \"text-center\", \"cellClass\", \"text-center\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"name\", \"Action\", \"prop\", \"Id\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [1, \"error-text\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"], [3, \"title\"], [1, \"btn\", \"btn-outline-danger\", \"btn-mini\", \"me-1\", 3, \"click\"], [1, \"feather\", \"icon-trash-2\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-mini\", 3, \"click\"], [1, \"feather\", \"icon-edit\"]],\n  template: function SpecialityComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelement(5, \"h5\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"div\", 5);\n      i0.ɵɵelementStart(7, \"form\", 6);\n      i0.ɵɵelementStart(8, \"div\", 7);\n      i0.ɵɵelementStart(9, \"label\", 8);\n      i0.ɵɵtext(10, \"Name \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(11, \"input\", 9);\n      i0.ɵɵtemplate(12, SpecialityComponent_div_12_Template, 4, 3, \"div\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(13, \"div\", 7);\n      i0.ɵɵelementStart(14, \"div\", 11);\n      i0.ɵɵelement(15, \"input\", 12);\n      i0.ɵɵelementStart(16, \"label\", 13);\n      i0.ɵɵtext(17, \"Active\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"button\", 14);\n      i0.ɵɵlistener(\"click\", function SpecialityComponent_Template_button_click_18_listener() {\n        return ctx.onFormSubmit();\n      });\n      i0.ɵɵelement(19, \"i\", 15);\n      i0.ɵɵtext(20);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(21, \"button\", 16);\n      i0.ɵɵlistener(\"click\", function SpecialityComponent_Template_button_click_21_listener() {\n        return ctx.clearForm();\n      });\n      i0.ɵɵelement(22, \"i\", 17);\n      i0.ɵɵtext(23, \" Clear\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"div\", 18);\n      i0.ɵɵelementStart(25, \"div\", 2);\n      i0.ɵɵelementStart(26, \"div\", 3);\n      i0.ɵɵelementStart(27, \"h5\");\n      i0.ɵɵtext(28, \"Speciality List \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(29, \"div\", 19);\n      i0.ɵɵelementStart(30, \"div\", 0);\n      i0.ɵɵelementStart(31, \"div\", 20);\n      i0.ɵɵelementStart(32, \"form\", 21);\n      i0.ɵɵelementStart(33, \"div\", 22);\n      i0.ɵɵelementStart(34, \"div\", 23);\n      i0.ɵɵelementStart(35, \"div\", 20);\n      i0.ɵɵelement(36, \"input\", 24);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(37, \"div\", 25);\n      i0.ɵɵelementStart(38, \"ngx-datatable\", 26);\n      i0.ɵɵlistener(\"page\", function SpecialityComponent_Template_ngx_datatable_page_38_listener($event) {\n        return ctx.setPage($event);\n      });\n      i0.ɵɵelementStart(39, \"ngx-datatable-column\", 27);\n      i0.ɵɵtemplate(40, SpecialityComponent_ng_template_40_Template, 2, 1, \"ng-template\", 28);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(41, \"ngx-datatable-column\", 29);\n      i0.ɵɵtemplate(42, SpecialityComponent_ng_template_42_Template, 2, 2, \"ng-template\", 28);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(43, \"ngx-datatable-column\", 30);\n      i0.ɵɵtemplate(44, SpecialityComponent_ng_template_44_Template, 2, 2, \"ng-template\", 28);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(45, \"ngx-datatable-column\", 31);\n      i0.ɵɵtemplate(46, SpecialityComponent_ng_template_46_Template, 2, 2, \"ng-template\", 28);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(47, \"ngx-datatable-column\", 32);\n      i0.ɵɵtemplate(48, SpecialityComponent_ng_template_48_Template, 6, 0, \"ng-template\", 28);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"innerHtml\", ctx.formTitle, i0.ɵɵsanitizeHtml);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.entryForm);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(29, _c0, ctx.submitted && ctx.f.name.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.name.errors);\n      i0.ɵɵadvance(8);\n      i0.ɵɵtextInterpolate1(\" \", ctx.btnSaveText, \"\");\n      i0.ɵɵadvance(12);\n      i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"rows\", ctx.rows)(\"loadingIndicator\", ctx.loadingIndicator)(\"externalPaging\", true)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"count\", ctx.page.totalElements)(\"offset\", ctx.page.pageNumber)(\"limit\", ctx.page.size);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"maxWidth\", 50)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 150)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 90)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 170)(\"draggable\", false)(\"sortable\", false);\n    }\n  },\n  directives: [i5.BlockUIComponent, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.DefaultValueAccessor, i1.NgControlStatus, i1.FormControlName, i6.NgClass, i7.DefaultClassDirective, i6.NgIf, i1.CheckboxControlValueAccessor, i8.DatatableComponent, i8.DataTableColumnDirective, i8.DataTableColumnCellDirective],\n  styles: [\"\"]\n});\n\n__decorate([BlockUI()], SpecialityComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}