﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using Microsoft.AspNet.Identity.Owin;
using Microsoft.Owin.Security;
using Microsoft.Owin.Security.Cookies;
using Microsoft.Owin.Security.OAuth;
using Brac.LMS.App.API.Models;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Newtonsoft.Json;
using System.Data.Entity;
using System.DirectoryServices;

namespace Brac.LMS.App.API.Providers
{
    public class ApplicationOAuthProvider : OAuthAuthorizationServerProvider
    {
        private readonly string _publicClientId;

        public ApplicationOAuthProvider(string publicClientId)
        {
            _publicClientId = publicClientId ?? throw new ArgumentNullException("publicClientId");
        }

        public override async Task GrantResourceOwnerCredentials(OAuthGrantResourceOwnerCredentialsContext context)
        {
            try
            {
                var userManager = context.OwinContext.GetUserManager<ApplicationUserManager>();
                ApplicationUser user = await userManager.FindAsync(context.UserName, context.Password);

                if (user == null || user.LoginType == LoginType.External)
                {
                    var pin = VerifyADUser(context.UserName, context.Password);
                    if (pin == null)
                    {
                        context.SetError("invalid_grant", "The user name or password is incorrect.");
                        return;
                    }

                    user = await userManager.Users.FirstOrDefaultAsync(x => x.UserName == pin);
                    if(user == null)
                    {
                        context.SetError("invalid_grant", "No information found about you on this system. Please contact with System Administrator.");
                        return;
                    }
                }
                
                var data = await context.Request.ReadFormAsync();
                if (user.UserType.ToString() != data["usertype"] && user.UserType != UserType.Both)
                {
                    context.SetError("invalid_grant", "The user name or password is incorrect.");
                    return;
                }

                if (!user.Active)
                {
                    context.SetError("invalid_grant", "This user has been de-activated by the Administration. Please contact with the Administration");
                    return;
                }

                var dbContext = context.OwinContext.Get<ApplicationDbContext>();

                var userGroupName = "";

                if (user.UserGroupId.HasValue)
                {
                    userGroupName = await dbContext.UserGroups.Where(x => x.Id == user.UserGroupId.Value).Select(x => x.Name).FirstOrDefaultAsync();
                }

                user.LastLogOn = DateTime.UtcNow;
                await userManager.UpdateAsync(user);

                var position = await dbContext.Trainees.Where(x => x.UserId == user.Id).Select(x => x.Position).FirstOrDefaultAsync();

                ClaimsIdentity oAuthIdentity = await user.GenerateUserIdentityAsync(userManager,
                   OAuthDefaults.AuthenticationType);
                ClaimsIdentity cookiesIdentity = await user.GenerateUserIdentityAsync(userManager,
                   context.Options.AuthenticationType);

                var roles = await userManager.GetRolesAsync(user.Id);

                AuthenticationProperties properties = CreateProperties(user, roles, userGroupName, position);
                AuthenticationTicket ticket = new AuthenticationTicket(oAuthIdentity, properties);
                context.Validated(ticket);

                context.Request.Context.Authentication.SignIn(cookiesIdentity);
            }
            catch (Exception ex)
            {
                context.SetError("Autorization Error", ex.Message);
            }
        }

        private string VerifyADUser(string username, string password)
        {
            string pin = null;
            try
            {
                string ldapPath = System.Configuration.ConfigurationManager.AppSettings["LDAPPath"];
                string ldapDn = System.Configuration.ConfigurationManager.AppSettings["LDAPDn"];
                string uidProperty = System.Configuration.ConfigurationManager.AppSettings["UidProperty"];
                string pinProperty = System.Configuration.ConfigurationManager.AppSettings["PinProperty"];


                DirectoryEntry root = new DirectoryEntry(ldapPath, username, password, AuthenticationTypes.SecureSocketsLayer | AuthenticationTypes.Secure);
                //DirectoryEntry root = new DirectoryEntry(ldapPath, string.Format("{0}\\{1}", ldapDn, username), password, AuthenticationTypes.SecureSocketsLayer | AuthenticationTypes.Secure);

                DirectorySearcher ds = new DirectorySearcher(root);

                // set options
                ds.Filter = string.Format("({0}={1})", uidProperty, username);
                // do we find anyone by that name??
                SearchResult result = ds.FindOne();

                if (result != null && result.Properties[pinProperty].Count > 0)
                    pin = result.Properties[pinProperty][0].ToString();

                return pin;
            }
            catch (Exception ex)
            {
                throw new Exception("AD Authentication: " + ex.Message);
            }
        }

        public override Task TokenEndpoint(OAuthTokenEndpointContext context)
        {
            foreach (KeyValuePair<string, string> property in context.Properties.Dictionary)
            {
                context.AdditionalResponseParameters.Add(property.Key, property.Value);
            }

            return Task.FromResult<object>(null);
        }

        public override Task ValidateClientAuthentication(OAuthValidateClientAuthenticationContext context)
        {
            // Resource owner password credentials does not provide a client ID.
            if (context.ClientId == null)
            {
                context.Validated();
            }

            return Task.FromResult<object>(null);
        }

        public override Task ValidateClientRedirectUri(OAuthValidateClientRedirectUriContext context)
        {
            if (context.ClientId == _publicClientId)
            {
                Uri expectedRootUri = new Uri(context.Request.Uri, "/");

                if (expectedRootUri.AbsoluteUri == context.RedirectUri)
                {
                    context.Validated();
                }
            }

            return Task.FromResult<object>(null);
        }

        public static AuthenticationProperties CreateProperties(string userName)
        {
            IDictionary<string, string> data = new Dictionary<string, string>
            {
                { "userName", userName }
            };
            return new AuthenticationProperties(data);
        }

        public static AuthenticationProperties CreateProperties(ApplicationUser user, IList<string> roles, string userGroupName, string position)
        {

            IDictionary<string, string> data = new Dictionary<string, string>
            {
                {
                    "Id",user.Id
                },
                {
                    "UserName", user.UserName
                },
                {
                    "Email", user.Email
                },
                {
                    "FirstName", user.FirstName ?? ""
                },
                {
                    "LastName", user.LastName ?? ""
                },
                {
                    "Position", position ?? ""
                },
                {
                    "ImagePath", user.ImagePath ?? ""
                },
                {
                    "UserType", user.UserType.ToString()
                },
                {
                    "Gender", user.Gender.ToString()
                },
                {
                    "Roles", JsonConvert.SerializeObject(roles)
                },
                {
                    "UserGroup", userGroupName ?? "Super Admin"
                }
            };
            return new AuthenticationProperties(data);
        }
    }
}