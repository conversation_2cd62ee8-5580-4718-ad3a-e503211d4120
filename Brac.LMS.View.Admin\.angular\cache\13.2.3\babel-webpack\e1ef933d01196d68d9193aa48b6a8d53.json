{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Injector, ElementRef, TemplateRef, Injectable } from '@angular/core';\nimport { listenToTriggersV2, registerOutsideClick, registerEscClick } from 'ngx-bootstrap/utils';\nimport * as i1 from 'ngx-bootstrap/positioning';\n\nclass BsComponentRef {}\n/**\n * @copyright Valor Software\n * @copyright Angular ng-bootstrap team\n */\n\n\nclass ContentRef {\n  constructor( // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  nodes, viewRef, // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  componentRef) {\n    this.nodes = nodes;\n    this.viewRef = viewRef;\n    this.componentRef = componentRef;\n  }\n\n} // todo: add delay support\n\n\nclass ComponentLoader {\n  /**\n   * Do not use this directly, it should be instanced via\n   * `ComponentLoadFactory.attach`\n   * @internal\n   */\n  constructor(_viewContainerRef, _renderer, _elementRef, _injector, _componentFactoryResolver, _ngZone, _applicationRef, _posService) {\n    this._viewContainerRef = _viewContainerRef;\n    this._renderer = _renderer;\n    this._elementRef = _elementRef;\n    this._injector = _injector;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._ngZone = _ngZone;\n    this._applicationRef = _applicationRef;\n    this._posService = _posService;\n    this.onBeforeShow = new EventEmitter();\n    this.onShown = new EventEmitter();\n    this.onBeforeHide = new EventEmitter();\n    this.onHidden = new EventEmitter();\n    this._providers = [];\n    this._isHiding = false;\n    /**\n     * A selector used if container element was not found\n     */\n\n    this.containerDefaultSelector = 'body';\n    this._listenOpts = {};\n    this._globalListener = Function.prototype;\n  }\n\n  get isShown() {\n    if (this._isHiding) {\n      return false;\n    }\n\n    return !!this._componentRef;\n  }\n\n  attach(compType) {\n    this._componentFactory = this._componentFactoryResolver.resolveComponentFactory(compType);\n    return this;\n  } // todo: add behaviour: to target element, `body`, custom element\n\n\n  to(container) {\n    this.container = container || this.container;\n    return this;\n  }\n\n  position(opts) {\n    if (!opts) {\n      return this;\n    }\n\n    this.attachment = opts.attachment || this.attachment;\n    this._elementRef = opts.target || this._elementRef;\n    return this;\n  }\n\n  provide(provider) {\n    this._providers.push(provider);\n\n    return this;\n  } // todo: appendChild to element or document.querySelector(this.container)\n\n\n  show(opts = {}) {\n    this._subscribePositioning();\n\n    this._innerComponent = void 0;\n\n    if (!this._componentRef) {\n      this.onBeforeShow.emit();\n      this._contentRef = this._getContentRef(opts.content, opts.context, opts.initialState);\n      const injector = Injector.create({\n        providers: this._providers,\n        parent: this._injector\n      });\n\n      if (!this._componentFactory) {\n        return;\n      }\n\n      this._componentRef = this._componentFactory.create(injector, this._contentRef.nodes);\n\n      this._applicationRef.attachView(this._componentRef.hostView); // this._componentRef = this._viewContainerRef\n      //   .createComponent(this._componentFactory, 0, injector, this._contentRef.nodes);\n\n\n      this.instance = this._componentRef.instance;\n      Object.assign(this._componentRef.instance, opts);\n\n      if (this.container instanceof ElementRef) {\n        this.container.nativeElement.appendChild(this._componentRef.location.nativeElement);\n      }\n\n      if (typeof this.container === 'string' && typeof document !== 'undefined') {\n        const selectedElement = document.querySelector(this.container) || document.querySelector(this.containerDefaultSelector);\n\n        if (!selectedElement) {\n          return;\n        }\n\n        selectedElement.appendChild(this._componentRef.location.nativeElement);\n      }\n\n      if (!this.container && this._elementRef && this._elementRef.nativeElement.parentElement) {\n        this._elementRef.nativeElement.parentElement.appendChild(this._componentRef.location.nativeElement);\n      } // we need to manually invoke change detection since events registered\n      // via\n      // Renderer::listen() are not picked up by change detection with the\n      // OnPush strategy\n\n\n      if (this._contentRef.componentRef) {\n        this._innerComponent = this._contentRef.componentRef.instance;\n\n        this._contentRef.componentRef.changeDetectorRef.markForCheck();\n\n        this._contentRef.componentRef.changeDetectorRef.detectChanges();\n      }\n\n      this._componentRef.changeDetectorRef.markForCheck();\n\n      this._componentRef.changeDetectorRef.detectChanges();\n\n      this.onShown.emit(opts.id ? {\n        id: opts.id\n      } : this._componentRef.instance);\n    }\n\n    this._registerOutsideClick();\n\n    return this._componentRef;\n  }\n\n  hide(id) {\n    var _a, _b, _c, _d, _e, _f;\n\n    if (!this._componentRef) {\n      return this;\n    }\n\n    this._posService.deletePositionElement(this._componentRef.location);\n\n    this.onBeforeHide.emit(this._componentRef.instance);\n    const componentEl = this._componentRef.location.nativeElement;\n    (_a = componentEl.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(componentEl);\n    (_c = (_b = this._contentRef) === null || _b === void 0 ? void 0 : _b.componentRef) === null || _c === void 0 ? void 0 : _c.destroy();\n\n    if (this._viewContainerRef && ((_d = this._contentRef) === null || _d === void 0 ? void 0 : _d.viewRef)) {\n      this._viewContainerRef.remove(this._viewContainerRef.indexOf(this._contentRef.viewRef));\n    }\n\n    (_f = (_e = this._contentRef) === null || _e === void 0 ? void 0 : _e.viewRef) === null || _f === void 0 ? void 0 : _f.destroy();\n    this._contentRef = void 0;\n    this._componentRef = void 0;\n\n    this._removeGlobalListener();\n\n    this.onHidden.emit(id ? {\n      id\n    } : null);\n    return this;\n  }\n\n  toggle() {\n    if (this.isShown) {\n      this.hide();\n      return;\n    }\n\n    this.show();\n  }\n\n  dispose() {\n    if (this.isShown) {\n      this.hide();\n    }\n\n    this._unsubscribePositioning();\n\n    if (this._unregisterListenersFn) {\n      this._unregisterListenersFn();\n    }\n  }\n\n  listen(listenOpts) {\n    var _a;\n\n    this.triggers = listenOpts.triggers || this.triggers;\n    this._listenOpts.outsideClick = listenOpts.outsideClick;\n    this._listenOpts.outsideEsc = listenOpts.outsideEsc;\n    listenOpts.target = listenOpts.target || ((_a = this._elementRef) === null || _a === void 0 ? void 0 : _a.nativeElement);\n\n    const hide = this._listenOpts.hide = () => listenOpts.hide ? listenOpts.hide() : void this.hide();\n\n    const show = this._listenOpts.show = registerHide => {\n      listenOpts.show ? listenOpts.show(registerHide) : this.show(registerHide);\n      registerHide();\n    }; // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n\n    const toggle = registerHide => {\n      this.isShown ? hide() : show(registerHide);\n    };\n\n    if (this._renderer) {\n      this._unregisterListenersFn = listenToTriggersV2(this._renderer, {\n        target: listenOpts.target,\n        triggers: listenOpts.triggers,\n        show,\n        hide,\n        toggle\n      });\n    }\n\n    return this;\n  }\n\n  _removeGlobalListener() {\n    if (this._globalListener) {\n      this._globalListener();\n\n      this._globalListener = Function.prototype;\n    }\n  }\n\n  attachInline(vRef, // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  template) {\n    if (vRef && template) {\n      this._inlineViewRef = vRef.createEmbeddedView(template);\n    }\n\n    return this;\n  }\n\n  _registerOutsideClick() {\n    if (!this._componentRef || !this._componentRef.location) {\n      return;\n    } // why: should run after first event bubble\n\n\n    if (this._listenOpts.outsideClick) {\n      const target = this._componentRef.location.nativeElement;\n      setTimeout(() => {\n        if (this._renderer && this._elementRef) {\n          this._globalListener = registerOutsideClick(this._renderer, {\n            targets: [target, this._elementRef.nativeElement],\n            outsideClick: this._listenOpts.outsideClick,\n            hide: () => this._listenOpts.hide && this._listenOpts.hide()\n          });\n        }\n      });\n    }\n\n    if (this._listenOpts.outsideEsc && this._renderer && this._elementRef) {\n      const target = this._componentRef.location.nativeElement;\n      this._globalListener = registerEscClick(this._renderer, {\n        targets: [target, this._elementRef.nativeElement],\n        outsideEsc: this._listenOpts.outsideEsc,\n        hide: () => this._listenOpts.hide && this._listenOpts.hide()\n      });\n    }\n  }\n\n  getInnerComponent() {\n    return this._innerComponent;\n  }\n\n  _subscribePositioning() {\n    if (this._zoneSubscription || !this.attachment) {\n      return;\n    }\n\n    this.onShown.subscribe(() => {\n      var _a;\n\n      this._posService.position({\n        element: (_a = this._componentRef) === null || _a === void 0 ? void 0 : _a.location,\n        target: this._elementRef,\n        attachment: this.attachment,\n        appendToBody: this.container === 'body'\n      });\n    });\n    this._zoneSubscription = this._ngZone.onStable.subscribe(() => {\n      if (!this._componentRef) {\n        return;\n      }\n\n      this._posService.calcPosition();\n    });\n  }\n\n  _unsubscribePositioning() {\n    if (!this._zoneSubscription) {\n      return;\n    }\n\n    this._zoneSubscription.unsubscribe();\n\n    this._zoneSubscription = void 0;\n  }\n\n  _getContentRef( // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  content, // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  context, // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  initialState) {\n    if (!content) {\n      return new ContentRef([]);\n    }\n\n    if (content instanceof TemplateRef) {\n      if (this._viewContainerRef) {\n        const _viewRef = this._viewContainerRef.createEmbeddedView(content, context);\n\n        _viewRef.markForCheck();\n\n        return new ContentRef([_viewRef.rootNodes], _viewRef);\n      }\n\n      const viewRef = content.createEmbeddedView({});\n\n      this._applicationRef.attachView(viewRef);\n\n      return new ContentRef([viewRef.rootNodes], viewRef);\n    }\n\n    if (typeof content === 'function') {\n      const contentCmptFactory = this._componentFactoryResolver.resolveComponentFactory(content);\n\n      const modalContentInjector = Injector.create({\n        providers: this._providers,\n        parent: this._injector\n      });\n      const componentRef = contentCmptFactory.create(modalContentInjector);\n      Object.assign(componentRef.instance, initialState);\n\n      this._applicationRef.attachView(componentRef.hostView);\n\n      return new ContentRef([[componentRef.location.nativeElement]], componentRef.hostView, componentRef);\n    }\n\n    const nodes = this._renderer ? [this._renderer.createText(`${content}`)] : [];\n    return new ContentRef([nodes]);\n  }\n\n}\n\nlet ComponentLoaderFactory = /*#__PURE__*/(() => {\n  class ComponentLoaderFactory {\n    constructor(_componentFactoryResolver, _ngZone, _injector, _posService, _applicationRef) {\n      this._componentFactoryResolver = _componentFactoryResolver;\n      this._ngZone = _ngZone;\n      this._injector = _injector;\n      this._posService = _posService;\n      this._applicationRef = _applicationRef;\n    }\n    /**\n     *\n     * @param _elementRef\n     * @param _viewContainerRef\n     * @param _renderer\n     */\n\n\n    createLoader(_elementRef, _viewContainerRef, _renderer) {\n      return new ComponentLoader(_viewContainerRef, _renderer, _elementRef, this._injector, this._componentFactoryResolver, this._ngZone, this._applicationRef, this._posService);\n    }\n\n  }\n\n  ComponentLoaderFactory.ɵfac = function ComponentLoaderFactory_Factory(t) {\n    return new (t || ComponentLoaderFactory)(i0.ɵɵinject(i0.ComponentFactoryResolver), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i1.PositioningService), i0.ɵɵinject(i0.ApplicationRef));\n  };\n\n  ComponentLoaderFactory.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ComponentLoaderFactory,\n    factory: ComponentLoaderFactory.ɵfac,\n    providedIn: 'root'\n  });\n  return ComponentLoaderFactory;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { BsComponentRef, ComponentLoader, ComponentLoaderFactory, ContentRef }; //# sourceMappingURL=ngx-bootstrap-component-loader.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}