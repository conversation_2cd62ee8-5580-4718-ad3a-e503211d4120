﻿using Brac.LMS.Common;
using Microsoft.AspNet.Identity;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class EntityField
    {
        [Key]
        public Guid Id { get; set; }
    }

    public class NumberEntityField
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long Id { get; set; }
    }

    public class AuditableEntity : EntityField
    {
        private DateTime _CreatedDate;
        public DateTime CreatedDate
        {
            get
            { return _CreatedDate; }
            set
            { _CreatedDate = value.ToKindUtc(); }
        }

        [Required, StringLength(128)]
        public string CreatorId { get; set; }


        private DateTime? _ModifiedDate;
        public DateTime? ModifiedDate {
            get
            { return _ModifiedDate; }
            set
            { _ModifiedDate = value.HasValue ? value.Value.ToKindUtc() : value; }
        }

        [StringLength(128)]
        public string ModifierId { get; set; }

        public void SetAuditTrailEntity(IIdentity identity)
        {
            if (this.Id == Guid.Empty)
            {
                this.CreatedDate = DateTime.UtcNow.ToKindLocal();
                this.CreatorId = identity.GetUserId();
            }
            else
            {
                this.ModifiedDate = DateTime.UtcNow.ToKindLocal();
                this.ModifierId = identity.GetUserId();
            }
        }
    }

    public class NumberAuditableEntity : NumberEntityField
    {
        private DateTime _CreatedDate;
        public DateTime CreatedDate
        {
            get
            { return _CreatedDate; }
            set
            { _CreatedDate = value.ToKindUtc(); }
        }

        [Required, StringLength(128)]
        public string CreatorId { get; set; }


        private DateTime? _ModifiedDate;
        public DateTime? ModifiedDate
        {
            get
            { return _ModifiedDate; }
            set
            { _ModifiedDate = value.HasValue ? value.Value.ToKindUtc() : value; }
        }

        [StringLength(128)]
        public string ModifierId { get; set; }
        public void SetAuditTrailEntity(IIdentity identity)
        {
            if (this.Id == 0)
            {
                this.CreatedDate = DateTime.UtcNow.ToKindLocal();
                this.CreatorId = identity.GetUserId();
            }
            else
            {
                this.ModifiedDate = DateTime.UtcNow.ToKindLocal();
                this.ModifierId = identity.GetUserId();
            }
        }
    }
}
