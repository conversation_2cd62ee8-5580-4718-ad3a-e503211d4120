﻿using Brac.LMS.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.ViewModels
{
    public class EvaluationExamModel
    {
        public Guid? Id { get; set; }
        public string ExamName { get; set; }
        public string ExamInstructions { get; set; }
        public int Quota { get; set; }
        public int DurationMnt { get; set; }
        public bool MCQOnly { get; set; }
        public bool Random { get; set; }
        public bool Publish { get; set; }
        public int ExamMCQNo { get; set; }
        public int ExamTrueFalseNo { get; set; }
        public int ExamFIGNo { get; set; }
        public int ExamMatchingNo { get; set; }
        public int ExamWritingNo { get; set; }
        public bool Active { get; set; }
        public int? Order { get; set; }
        public QuesType QuesType { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public List<MCQQuestionModel> MCQs { get; set; }
        public List<TrueFalseQuestionModel> TruFalseQs { get; set; }
        public List<FIGQuestionModel> FIGQs { get; set; }
        public List<MatchingQuestionModel> MatchingQs { get; set; }
        public List<WrittenQuestionModel> WrittenQs { get; set; }
        public long CategoryId { get; set; }
        public OMAllowFor AllowFor { get; set; }
        public long? DivisionId { get; set; }
        public long? DepartmentId { get; set; }
        public long? UnitId { get; set; }
        public List<Guid> Trainees { get; set; }
    }
   
}
