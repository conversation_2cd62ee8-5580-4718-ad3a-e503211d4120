﻿using Newtonsoft.Json;

using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Common
{
    public static class LogControl
    {
        private static bool DEBUG = false;
        private static string logPath = ConfigurationManager.AppSettings["LogPath"].ToString();

        public static void Write(string msg)
        {
            var logFilePath = Path.Combine(logPath ?? @"C:\AppLog\e-Learning", DateTime.Today.ToString("yyyy_MM_dd") + ".txt");
            if (!File.Exists(logFilePath) || !Directory.Exists(Path.GetDirectoryName(logFilePath)))
            {
                Directory.CreateDirectory(Path.GetDirectoryName(logFilePath));
                using (File.Create(logFilePath))
                {
                    // close streams
                }
            }
            try
            {
                using (StreamWriter w = File.AppendText(logFilePath))
                {
                    Log(msg, w);
                }
                if (DEBUG)
                    Console.WriteLine(msg);
            }
            catch (Exception)
            {
                // ignore
            }
        }

        static private void Log(string msg, TextWriter w)
        {
            try
            {
                w.Write(Environment.NewLine);
                w.Write("[{0}]", DateTime.Now.ToLongTimeString());
                w.Write("\t");
                w.WriteLine(" {0}", msg);
            }
            catch (Exception ex)
            {
                throw (ex);
            }
        }

        public static void LogException(Exception exception)
        {
            Write(JsonConvert.SerializeObject(exception));
        }

    }
}
