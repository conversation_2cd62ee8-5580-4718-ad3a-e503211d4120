﻿
using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Hosting;

namespace Brac.LMS.App.Services
{
    public class CertificateConfigurationService : ICertificateConfigurationService
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditLogHelper _auditLogHelper;
        private readonly AuditLog audit;
        private readonly ApplicationUser _appUser;
        public CertificateConfigurationService()
        {
            _context = new ApplicationDbContext();
        }
        public CertificateConfigurationService(string controller, string method, ApplicationUser applicationUser)
        {
            _auditLogHelper = new AuditLogHelper(controller, method);
            audit = _auditLogHelper.auditLog;
            _appUser = applicationUser;
            audit.CreatedBy = _appUser.Id;
            _context = new ApplicationDbContext();
        }

        public async Task<APIResponse> CertificateConfigurationCreateOrUpdate(CertificateConfigurationModel model, IIdentity identity)
        {
            CertificateConfiguration item = null;
            bool isEdit = true;
            bool isNewVersion = false;
            int newVersion = 1;

            try
            {
                // Get the latest version for this course
                var latestConfig = await _context.CertificateConfigurations
                    .Where(x => x.CourseId == model.CourseId)
                    .OrderByDescending(x => x.Version)
                    .FirstOrDefaultAsync();

                if (latestConfig == null)
                {
                    // First time creating configuration for this course
                    item = new CertificateConfiguration { CourseId = model.CourseId, Version = 1 };
                    isEdit = false;
                    newVersion = 1;
                }
                else if (model.CreateNewVersion)
                {
                    // Create new version
                    newVersion = latestConfig.Version + 1;
                    item = new CertificateConfiguration
                    {
                        CourseId = model.CourseId,
                        Version = newVersion,
                        ChangeReason = model.ChangeReason
                    };
                    isEdit = false;
                    isNewVersion = true;

                    // Copy data from latest version
                    item.CourseDescription = latestConfig.CourseDescription;
                    item.Template = latestConfig.Template;
                    item.Designation1 = latestConfig.Designation1;
                    item.Designation2 = latestConfig.Designation2;
                    item.Designation3 = latestConfig.Designation3;
                    item.Person1Name = latestConfig.Person1Name;
                    item.Person2Name = latestConfig.Person2Name;
                    item.Person3Name = latestConfig.Person3Name;
                }
                else
                {
                    // Modify existing latest version
                    item = latestConfig;
                    newVersion = item.Version;
                }

                // Update model data (this will override copied data for new versions)
                item.CourseDescription = model.CourseDescription;
                item.Template = model.Template;
                item.Designation1 = model.Designation1;
                item.Designation2 = model.Designation2;
                item.Designation3 = model.Designation3;
                item.Person1Name = model.Person1Name;
                item.Person2Name = model.Person2Name;
                item.Person3Name = model.Person3Name;

                // Create version-specific folder path
                string versionFolderPath = $"/Images/CertificateConfiguration/{model.CourseId}/v{newVersion}/";

                // If creating new version, copy images from previous version first
                if (isNewVersion && latestConfig != null)
                {
                    await CopyImagesFromPreviousVersion(latestConfig, item, versionFolderPath);
                }

                // Handle image removal when person names are cleared
                if (item.Person1Name == null && item.Person1SignPath != null)
                    item.Person1SignPath = Utility.RemoveImage(item.Person1SignPath);
                if (item.Person2Name == null && item.Person2SignPath != null)
                    item.Person2SignPath = Utility.RemoveImage(item.Person2SignPath);
                if (item.Person3Name == null && item.Person3SignPath != null)
                    item.Person3SignPath = Utility.RemoveImage(item.Person3SignPath);

                // Handle file uploads with version-specific paths
                foreach (var fileKey in HttpContext.Current.Request.Files.AllKeys)
                {
                    switch (fileKey)
                    {
                        case "TemplatePath":
                            item.TemplatePath = Utility.SaveImage("TemplatePath", versionFolderPath, HttpContext.Current.Request.Files.Get(fileKey), item.TemplatePath);
                            break;
                        case "Person1Sign":
                            item.Person1SignPath = Utility.SaveImage("Person1Sign", versionFolderPath, HttpContext.Current.Request.Files.Get(fileKey), item.Person1SignPath);
                            break;
                        case "Person2Sign":
                            item.Person2SignPath = Utility.SaveImage("Person2Sign", versionFolderPath, HttpContext.Current.Request.Files.Get(fileKey), item.Person2SignPath);
                            break;
                        case "Person3Sign":
                            item.Person3SignPath = Utility.SaveImage("Person3Sign", versionFolderPath, HttpContext.Current.Request.Files.Get(fileKey), item.Person3SignPath);
                            break;
                        default:
                            return new APIResponse
                            {
                                Status = ResponseStatus.Warning,
                                Message = "Key not match"
                            };
                    };
                }

                item.SetAuditTrailEntity(identity);

                if (!isEdit)
                {
                    _context.CertificateConfigurations.Add(item);
                }
                else
                {
                    _context.Entry(item).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Message = isNewVersion ? $"Successfully created new version v{newVersion}" :
                                 isEdit ? "Successfully updated" : "Successfully saved",
                        Version = newVersion,
                        IsNewVersion = isNewVersion
                    }
                };

            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                LogControl.Write($"Error on Cert Config create/update: {JsonConvert.SerializeObject(e)}");
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())
                };
            }
             catch (Exception ex)
            {
                LogControl.Write($"Error on Cert Config create/update: {JsonConvert.SerializeObject(ex)}");
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }
        }

        public async Task<APIResponse> GetCertificateConfiguration(Guid courseId)
        {
            try
            {
                // Get the latest version for this course
                var data = await _context.CertificateConfigurations
                    .Where(x => x.CourseId == courseId)
                    .OrderByDescending(x => x.Version)
                    .Select(t => new
                    {
                        t.CourseDescription,
                        t.Designation1,
                        t.Designation2,
                        t.Designation3,
                        t.Person1Name,
                        t.Person2Name,
                        t.Person3Name,
                        t.Person1SignPath,
                        t.Person2SignPath,
                        t.Person3SignPath,
                        Template = t.Template.ToString(),
                        TemplatePath = t.TemplatePath,
                        t.Version,
                        t.ChangeReason,
                        t.CreatedDate
                    }).FirstOrDefaultAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }
        }

        private async Task CopyImagesFromPreviousVersion(CertificateConfiguration previousConfig, CertificateConfiguration newConfig, string newVersionPath)
        {
            try
            {
                string serverPath = HostingEnvironment.MapPath("~");
                string newVersionServerPath = serverPath + newVersionPath;

                // Create new version directory if it doesn't exist
                if (!Directory.Exists(newVersionServerPath))
                {
                    Directory.CreateDirectory(newVersionServerPath);
                }

                // Copy Person1Sign if exists
                if (!string.IsNullOrEmpty(previousConfig.Person1SignPath) && File.Exists(serverPath + previousConfig.Person1SignPath))
                {
                    string fileName = Path.GetFileName(previousConfig.Person1SignPath);
                    string newPath = newVersionPath + fileName;
                    File.Copy(serverPath + previousConfig.Person1SignPath, serverPath + newPath, true);
                    newConfig.Person1SignPath = newPath;
                }

                // Copy Person2Sign if exists
                if (!string.IsNullOrEmpty(previousConfig.Person2SignPath) && File.Exists(serverPath + previousConfig.Person2SignPath))
                {
                    string fileName = Path.GetFileName(previousConfig.Person2SignPath);
                    string newPath = newVersionPath + fileName;
                    File.Copy(serverPath + previousConfig.Person2SignPath, serverPath + newPath, true);
                    newConfig.Person2SignPath = newPath;
                }

                // Copy Person3Sign if exists
                if (!string.IsNullOrEmpty(previousConfig.Person3SignPath) && File.Exists(serverPath + previousConfig.Person3SignPath))
                {
                    string fileName = Path.GetFileName(previousConfig.Person3SignPath);
                    string newPath = newVersionPath + fileName;
                    File.Copy(serverPath + previousConfig.Person3SignPath, serverPath + newPath, true);
                    newConfig.Person3SignPath = newPath;
                }

                // Copy TemplatePath if exists
                if (!string.IsNullOrEmpty(previousConfig.TemplatePath) && File.Exists(serverPath + previousConfig.TemplatePath))
                {
                    string fileName = Path.GetFileName(previousConfig.TemplatePath);
                    string newPath = newVersionPath + fileName;
                    File.Copy(serverPath + previousConfig.TemplatePath, serverPath + newPath, true);
                    newConfig.TemplatePath = newPath;
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the entire operation
                LogControl.Write($"Error on copying files: {JsonConvert.SerializeObject(ex)}");
            }
        }

        public async Task<APIResponse> GetCertificateConfigurationVersions(Guid courseId)
        {
            try
            {
                // Get all versions for this course, ordered by version descending (latest first)
                var versions = await _context.CertificateConfigurations
                    .Where(x => x.CourseId == courseId)
                    .OrderByDescending(x => x.Version)
                    .Select(t => new
                    {
                        t.Id,
                        t.Version,
                        t.ChangeReason,
                        t.CreatedDate,
                        t.CreatorId,
                        t.Person1Name,
                        t.Person2Name,
                        t.Person3Name,
                        IsLatest = t.Version == _context.CertificateConfigurations
                            .Where(c => c.CourseId == courseId)
                            .Max(c => c.Version)
                    }).ToListAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = versions
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }
        }
    }

    public interface ICertificateConfigurationService
    {
        Task<APIResponse> CertificateConfigurationCreateOrUpdate(CertificateConfigurationModel model, IIdentity identity);
        Task<APIResponse> GetCertificateConfiguration(Guid courseId);
        Task<APIResponse> GetCertificateConfigurationVersions(Guid courseId);
    }
}
