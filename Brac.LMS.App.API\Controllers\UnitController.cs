﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [Authorize(Roles = "Admin"), RoutePrefix("api/unit")]
    public class UnitController : ApplicationController
    {
        private readonly IUnitService _service;

        public UnitController()
        {
            _service = new UnitService();
        }

        [HttpPost, Route("create-or-update")]
        public async Task<IHttpActionResult> UnitCreateOrUpdate(UnitModel model)
        {
            return Ok(await _service.UnitCreateOrUpdate(model, User.Identity));
        }

        [HttpGet, Route("list")]
        public async Task<IHttpActionResult> GetUnitList(string name, int size, int pageNumber)
        {
            return Ok(await _service.GetUnitList(name, size, pageNumber));
        }

        [HttpGet, Route("dropdown-list")]
        public async Task<IHttpActionResult> GetUnitDropDownList()
        {
            return Ok(await _service.GetUnitDropDownList());
        }

        [HttpGet, Route("get/{id}")]
        public async Task<IHttpActionResult> GetUnitById(long id)
        {
            return Ok(await _service.GetUnitById(id));
        }

        [HttpGet, Route("delete/{id}")]
        public async Task<IHttpActionResult> DeleteUnitById(long id)
        {
            return Ok(await _service.DeleteUnitById(id));
        }
    }
}
