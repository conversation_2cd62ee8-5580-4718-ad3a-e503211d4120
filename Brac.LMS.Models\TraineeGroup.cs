﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class TraineeGroup : NumberAuditableEntity
    {
        [Required, Column(TypeName = "VARCHAR"), StringLength(10)]
        public string Code { get; set; }

        [Required, StringLength(250)]
        public string Name { get; set; }
        public bool Active { get; set; }
    }
}
