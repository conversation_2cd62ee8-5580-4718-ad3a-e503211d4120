{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ColumnMode } from \"@swimlane/ngx-datatable\";\nimport { Validators } from \"@angular/forms\";\nimport { BlockUI } from \"ng-block-ui\";\nimport { Page } from \"../_models/page\";\nimport { environment } from \"../../environments/environment\";\nimport { Subject } from \"rxjs\";\nimport { debounceTime } from \"rxjs/operators\";\nimport { ResponseStatus } from \"../_models/enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../app.component\";\nimport * as i2 from \"ngx-bootstrap/modal\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../_services/common.service\";\nimport * as i5 from \"ngx-toastr\";\nimport * as i6 from \"../_helpers/confirm-dialog/confirm.service\";\nimport * as i7 from \"ng-block-ui\";\nimport * as i8 from \"@ng-select/ng-select\";\nimport * as i9 from \"@swimlane/ngx-datatable\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"ngx-bootstrap/tooltip\";\nimport * as i12 from \"@angular/flex-layout/extended\";\nconst _c0 = [\"dataTable\"];\n\nfunction OpenMaterialComponent_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const rowIndex_r16 = ctx.rowIndex;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.page.pageNumber * ctx_r1.page.size + rowIndex_r16 + 1, \" \");\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_31_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 34);\n  }\n\n  if (rf & 2) {\n    const row_r17 = i0.ɵɵnextContext().row;\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"alt\", row_r17.Title);\n    i0.ɵɵproperty(\"src\", ctx_r18.baseUrl + row_r17.ImagePath, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_31_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 35);\n  }\n\n  if (rf & 2) {\n    const row_r17 = i0.ɵɵnextContext().row;\n    i0.ɵɵpropertyInterpolate(\"alt\", row_r17.Title);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, OpenMaterialComponent_ng_template_31_img_1_Template, 1, 2, \"img\", 30);\n    i0.ɵɵtemplate(2, OpenMaterialComponent_ng_template_31_img_2_Template, 1, 1, \"img\", 31);\n    i0.ɵɵelementStart(3, \"div\", 32);\n    i0.ɵɵelementStart(4, \"span\", 33);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r17 = ctx.row;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", row_r17.ImagePath);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !row_r17.ImagePath);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"title\", row_r17.Title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", row_r17.Title, \" \");\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_33_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵelement(1, \"img\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r22 = i0.ɵɵnextContext().row;\n    i0.ɵɵpropertyInterpolate(\"title\", row_r22.MaterialType);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", row_r22.MaterialType, \" \");\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_33_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵelement(1, \"img\", 39);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r22 = i0.ɵɵnextContext().row;\n    i0.ɵɵpropertyInterpolate(\"title\", row_r22.MaterialType);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", row_r22.MaterialType, \" \");\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, OpenMaterialComponent_ng_template_33_span_0_Template, 3, 2, \"span\", 36);\n    i0.ɵɵtemplate(1, OpenMaterialComponent_ng_template_33_span_1_Template, 3, 2, \"span\", 36);\n  }\n\n  if (rf & 2) {\n    const row_r22 = ctx.row;\n    i0.ɵɵproperty(\"ngIf\", row_r22.MaterialType == \"Document\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", row_r22.MaterialType == \"Video\");\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_35_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r27 = i0.ɵɵnextContext().value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r27);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r27, \" \");\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, OpenMaterialComponent_ng_template_35_span_0_Template, 2, 2, \"span\", 36);\n  }\n\n  if (rf & 2) {\n    const value_r27 = ctx.value;\n    i0.ɵɵproperty(\"ngIf\", value_r27);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_37_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r30 = i0.ɵɵnextContext().row;\n    i0.ɵɵpropertyInterpolate(\"title\", row_r30.AllowFor);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", row_r30.AllowFor, \" \");\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_37_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r30 = i0.ɵɵnextContext().row;\n    i0.ɵɵpropertyInterpolate(\"title\", row_r30.AllowFor);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", row_r30.AllowFor, \" - \", row_r30.Division, \" \");\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_37_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r30 = i0.ɵɵnextContext().row;\n    i0.ɵɵpropertyInterpolate(\"title\", row_r30.AllowFor);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", row_r30.AllowFor, \" - \", row_r30.Department, \" \");\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_37_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r30 = i0.ɵɵnextContext().row;\n    i0.ɵɵpropertyInterpolate(\"title\", row_r30.AllowFor);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", row_r30.AllowFor, \" - \", row_r30.Unit, \" \");\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_37_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r30 = i0.ɵɵnextContext().row;\n    i0.ɵɵpropertyInterpolate(\"title\", row_r30.AllowFor);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", row_r30.Trainees + \" trainee\" + (row_r30.Trainees > 1 ? \"s\" : \"\"), \" \");\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, OpenMaterialComponent_ng_template_37_span_0_Template, 2, 2, \"span\", 36);\n    i0.ɵɵtemplate(1, OpenMaterialComponent_ng_template_37_span_1_Template, 2, 3, \"span\", 36);\n    i0.ɵɵtemplate(2, OpenMaterialComponent_ng_template_37_span_2_Template, 2, 3, \"span\", 36);\n    i0.ɵɵtemplate(3, OpenMaterialComponent_ng_template_37_span_3_Template, 2, 3, \"span\", 36);\n    i0.ɵɵtemplate(4, OpenMaterialComponent_ng_template_37_span_4_Template, 2, 2, \"span\", 36);\n  }\n\n  if (rf & 2) {\n    const row_r30 = ctx.row;\n    i0.ɵɵproperty(\"ngIf\", row_r30.AllowFor === \"All\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", row_r30.AllowFor === \"Division\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", row_r30.AllowFor === \"Department\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", row_r30.AllowFor === \"Unit\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", row_r30.AllowFor === \"Trainee\");\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_39_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelementStart(1, \"span\", 41);\n    i0.ɵɵtext(2, \"Active\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_39_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelementStart(1, \"span\", 42);\n    i0.ɵɵtext(2, \"In-Active\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, OpenMaterialComponent_ng_template_39_span_0_Template, 3, 0, \"span\", 40);\n    i0.ɵɵtemplate(1, OpenMaterialComponent_ng_template_39_span_1_Template, 3, 0, \"span\", 40);\n  }\n\n  if (rf & 2) {\n    const value_r41 = ctx.value;\n    i0.ɵɵproperty(\"ngIf\", value_r41 == true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", value_r41 == false);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_41_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r44 = i0.ɵɵnextContext().value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r44);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r44, \" \");\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, OpenMaterialComponent_ng_template_41_span_0_Template, 2, 2, \"span\", 36);\n  }\n\n  if (rf & 2) {\n    const value_r44 = ctx.value;\n    i0.ɵɵproperty(\"ngIf\", value_r44);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_43_a_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 49);\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r47 = i0.ɵɵnextContext().row;\n    const ctx_r48 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"href\", ctx_r48.baseUrl + row_r47.FilePath, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_43_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function OpenMaterialComponent_ng_template_43_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const row_r47 = i0.ɵɵnextContext().row;\n      const ctx_r51 = i0.ɵɵnextContext();\n\n      const _r13 = i0.ɵɵreference(49);\n\n      return ctx_r51.openModalVideoPreview(_r13, row_r47);\n    });\n    i0.ɵɵelement(1, \"i\", 52);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵtemplate(0, OpenMaterialComponent_ng_template_43_a_0_Template, 2, 1, \"a\", 43);\n    i0.ɵɵtemplate(1, OpenMaterialComponent_ng_template_43_button_1_Template, 2, 0, \"button\", 44);\n    i0.ɵɵelementStart(2, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function OpenMaterialComponent_ng_template_43_Template_button_click_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const row_r47 = restoredCtx.row;\n      const ctx_r54 = i0.ɵɵnextContext();\n      return ctx_r54.deleteItem(row_r47.Id);\n    });\n    i0.ɵɵelement(3, \"i\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function OpenMaterialComponent_ng_template_43_Template_button_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const row_r47 = restoredCtx.row;\n      const ctx_r56 = i0.ɵɵnextContext();\n\n      const _r9 = i0.ɵɵreference(45);\n\n      const _r11 = i0.ɵɵreference(47);\n\n      return ctx_r56.getItem(row_r47, _r9, _r11);\n    });\n    i0.ɵɵelement(5, \"i\", 48);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const row_r47 = ctx.row;\n    i0.ɵɵproperty(\"ngIf\", row_r47.MaterialType == \"Document\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", row_r47.MaterialType == \"Video\");\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_44_div_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 90);\n    i0.ɵɵtext(1, \"Category is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_44_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, OpenMaterialComponent_ng_template_44_div_13_span_1_Template, 2, 0, \"span\", 89);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r57 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r57.f.categoryId.errors.required);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_44_div_19_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 90);\n    i0.ɵɵtext(1, \" Title is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_44_div_19_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 90);\n    i0.ɵɵtext(1, \"Title must be not then 250 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_44_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, OpenMaterialComponent_ng_template_44_div_19_span_1_Template, 2, 0, \"span\", 89);\n    i0.ɵɵtemplate(2, OpenMaterialComponent_ng_template_44_div_19_span_2_Template, 2, 0, \"span\", 89);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r58 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r58.f.title.errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r58.f.title.errors.maxlength);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_44_div_26_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 90);\n    i0.ɵɵtext(1, \" File is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_44_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, OpenMaterialComponent_ng_template_44_div_26_span_1_Template, 2, 0, \"span\", 89);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r60 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r60.f.attachmentFile.errors.required);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_44_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵelementStart(1, \"label\", 62);\n    i0.ɵɵtext(2, \"Division \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 63);\n    i0.ɵɵelement(4, \"ng-select\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r61 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r61.divisionList);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_44_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵelementStart(1, \"label\", 62);\n    i0.ɵɵtext(2, \"Department \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 63);\n    i0.ɵɵelement(4, \"ng-select\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r62 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r62.departmentList);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_44_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵelementStart(1, \"label\", 62);\n    i0.ɵɵtext(2, \"Unit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 63);\n    i0.ɵɵelement(4, \"ng-select\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r63 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r63.unitList);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_44_img_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r73 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"img\", 94);\n    i0.ɵɵlistener(\"click\", function OpenMaterialComponent_ng_template_44_img_50_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r73);\n      i0.ɵɵnextContext();\n\n      const _r66 = i0.ɵɵreference(53);\n\n      return _r66.click();\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_44_img_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r75 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"img\", 95);\n    i0.ɵɵlistener(\"click\", function OpenMaterialComponent_ng_template_44_img_51_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r75);\n      i0.ɵɵnextContext();\n\n      const _r66 = i0.ɵɵreference(53);\n\n      return _r66.click();\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r65 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r65.imageUrl, i0.ɵɵsanitizeUrl);\n  }\n}\n\nconst _c1 = function () {\n  return {\n    standalone: true\n  };\n};\n\nfunction OpenMaterialComponent_ng_template_44_div_54_div_14_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r82 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 113);\n    i0.ɵɵelementStart(1, \"div\", 114);\n    i0.ɵɵelementStart(2, \"input\", 115);\n    i0.ɵɵlistener(\"ngModelChange\", function OpenMaterialComponent_ng_template_44_div_54_div_14_li_2_Template_input_ngModelChange_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r82);\n      const item_r80 = restoredCtx.$implicit;\n      return item_r80.selected = $event;\n    })(\"change\", function OpenMaterialComponent_ng_template_44_div_54_div_14_li_2_Template_input_change_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r82);\n      const item_r80 = restoredCtx.$implicit;\n      const ctx_r83 = i0.ɵɵnextContext(4);\n      return ctx_r83.getSelected(item_r80);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 116);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 117);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r80 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"name\", item_r80.Id);\n    i0.ɵɵpropertyInterpolate(\"id\", item_r80.Id);\n    i0.ɵɵpropertyInterpolate(\"value\", item_r80.Id);\n    i0.ɵɵproperty(\"ngModel\", item_r80.selected)(\"ngModelOptions\", i0.ɵɵpureFunction0(9, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"for\", item_r80.Id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", item_r80.Name, \" - \", item_r80.PIN, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r80.Position);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_44_div_54_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵelementStart(1, \"ul\", 111);\n    i0.ɵɵtemplate(2, OpenMaterialComponent_ng_template_44_div_54_div_14_li_2_Template, 7, 10, \"li\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r76 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r76.traineeList);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_44_div_54_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵelementStart(1, \"h5\", 99);\n    i0.ɵɵtext(2, \"Selected Trainees\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_44_div_54_div_17_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r87 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 113);\n    i0.ɵɵelementStart(1, \"div\", 114);\n    i0.ɵɵelementStart(2, \"input\", 119);\n    i0.ɵɵlistener(\"change\", function OpenMaterialComponent_ng_template_44_div_54_div_17_li_2_Template_input_change_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r87);\n      const item_r85 = restoredCtx.$implicit;\n      const ctx_r86 = i0.ɵɵnextContext(4);\n      return ctx_r86.removeSelected(item_r85);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 116);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 117);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r85 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"name\", item_r85.Id);\n    i0.ɵɵpropertyInterpolate(\"id\", item_r85.Id);\n    i0.ɵɵpropertyInterpolate(\"value\", item_r85.Id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"for\", item_r85.Id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", item_r85.Name, \" - \", item_r85.PIN, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r85.Position);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_44_div_54_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 118);\n    i0.ɵɵelementStart(1, \"ul\", 111);\n    i0.ɵɵtemplate(2, OpenMaterialComponent_ng_template_44_div_54_div_17_li_2_Template, 7, 7, \"li\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r78 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r78.selectedTraineeList);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_44_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r89 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelementStart(1, \"div\", 0);\n    i0.ɵɵelementStart(2, \"div\", 97);\n    i0.ɵɵelementStart(3, \"div\", 98);\n    i0.ɵɵelementStart(4, \"h5\", 99);\n    i0.ɵɵtext(5, \"Select Trainees\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 0);\n    i0.ɵɵelementStart(7, \"div\", 100);\n    i0.ɵɵelement(8, \"input\", 101);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 102);\n    i0.ɵɵelementStart(10, \"div\", 103);\n    i0.ɵɵelementStart(11, \"input\", 104);\n    i0.ɵɵlistener(\"ngModelChange\", function OpenMaterialComponent_ng_template_44_div_54_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r89);\n      const ctx_r88 = i0.ɵɵnextContext(2);\n      return ctx_r88.select_All = $event;\n    })(\"change\", function OpenMaterialComponent_ng_template_44_div_54_Template_input_change_11_listener($event) {\n      i0.ɵɵrestoreView(_r89);\n      const ctx_r90 = i0.ɵɵnextContext(2);\n      return ctx_r90.selectAll($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"label\", 105);\n    i0.ɵɵtext(13, \" Select All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, OpenMaterialComponent_ng_template_44_div_54_div_14_Template, 3, 1, \"div\", 106);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 107);\n    i0.ɵɵtemplate(16, OpenMaterialComponent_ng_template_44_div_54_div_16_Template, 3, 0, \"div\", 108);\n    i0.ɵɵtemplate(17, OpenMaterialComponent_ng_template_44_div_54_div_17_Template, 3, 1, \"div\", 109);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r67 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngModel\", ctx_r67.select_All)(\"ngModelOptions\", i0.ɵɵpureFunction0(5, _c1));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r67.traineeList.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r67.selectedTraineeList.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r67.selectedTraineeList.length > 0);\n  }\n}\n\nconst _c2 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nfunction OpenMaterialComponent_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r92 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"i\", 54);\n    i0.ɵɵelement(2, \"h4\", 55);\n    i0.ɵɵelementStart(3, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function OpenMaterialComponent_ng_template_44_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r92);\n      const ctx_r91 = i0.ɵɵnextContext();\n      return ctx_r91.modalHide();\n    });\n    i0.ɵɵelement(4, \"i\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 58);\n    i0.ɵɵelementStart(6, \"form\", 59);\n    i0.ɵɵelementStart(7, \"div\", 60);\n    i0.ɵɵelementStart(8, \"div\", 61);\n    i0.ɵɵelementStart(9, \"label\", 62);\n    i0.ɵɵtext(10, \" Category \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 63);\n    i0.ɵɵelement(12, \"ng-select\", 64);\n    i0.ɵɵtemplate(13, OpenMaterialComponent_ng_template_44_div_13_Template, 2, 1, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 61);\n    i0.ɵɵelementStart(15, \"label\", 62);\n    i0.ɵɵtext(16, \"Doc Title \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 63);\n    i0.ɵɵelement(18, \"textarea\", 66);\n    i0.ɵɵtemplate(19, OpenMaterialComponent_ng_template_44_div_19_Template, 3, 2, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 61);\n    i0.ɵɵelementStart(21, \"label\", 62);\n    i0.ɵɵtext(22, \"Doc File \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 63);\n    i0.ɵɵelementStart(24, \"input\", 67, 68);\n    i0.ɵɵlistener(\"change\", function OpenMaterialComponent_ng_template_44_Template_input_change_24_listener() {\n      i0.ɵɵrestoreView(_r92);\n\n      const _r59 = i0.ɵɵreference(25);\n\n      const ctx_r93 = i0.ɵɵnextContext();\n      return ctx_r93.loadAttachment(_r59.files);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, OpenMaterialComponent_ng_template_44_div_26_Template, 2, 1, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 61);\n    i0.ɵɵelementStart(28, \"label\", 62);\n    i0.ɵɵtext(29, \"Allow For \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 63);\n    i0.ɵɵelementStart(31, \"ng-select\", 69);\n    i0.ɵɵlistener(\"change\", function OpenMaterialComponent_ng_template_44_Template_ng_select_change_31_listener($event) {\n      i0.ɵɵrestoreView(_r92);\n      const ctx_r94 = i0.ɵɵnextContext();\n      return ctx_r94.changeAllowedFor($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 61);\n    i0.ɵɵelementStart(33, \"label\", 62);\n    i0.ɵɵtext(34, \"Active \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 63);\n    i0.ɵɵelement(36, \"input\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 61);\n    i0.ɵɵelementStart(38, \"label\", 71);\n    i0.ɵɵtext(39, \"Order \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 63);\n    i0.ɵɵelement(41, \"input\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, OpenMaterialComponent_ng_template_44_div_42_Template, 5, 3, \"div\", 73);\n    i0.ɵɵtemplate(43, OpenMaterialComponent_ng_template_44_div_43_Template, 5, 3, \"div\", 73);\n    i0.ɵɵtemplate(44, OpenMaterialComponent_ng_template_44_div_44_Template, 5, 3, \"div\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 74);\n    i0.ɵɵelementStart(46, \"label\", 75);\n    i0.ɵɵtext(47, \"Cover Photo (400x210)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 76);\n    i0.ɵɵelementStart(49, \"div\", 77);\n    i0.ɵɵtemplate(50, OpenMaterialComponent_ng_template_44_img_50_Template, 1, 0, \"img\", 78);\n    i0.ɵɵtemplate(51, OpenMaterialComponent_ng_template_44_img_51_Template, 1, 1, \"img\", 79);\n    i0.ɵɵelementStart(52, \"input\", 80, 81);\n    i0.ɵɵlistener(\"change\", function OpenMaterialComponent_ng_template_44_Template_input_change_52_listener() {\n      i0.ɵɵrestoreView(_r92);\n\n      const _r66 = i0.ɵɵreference(53);\n\n      const ctx_r95 = i0.ɵɵnextContext();\n      return ctx_r95.readURL(_r66);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(54, OpenMaterialComponent_ng_template_44_div_54_Template, 18, 6, \"div\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 83);\n    i0.ɵɵelementStart(56, \"div\", 84);\n    i0.ɵɵelementStart(57, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function OpenMaterialComponent_ng_template_44_Template_button_click_57_listener() {\n      i0.ɵɵrestoreView(_r92);\n      const ctx_r96 = i0.ɵɵnextContext();\n      return ctx_r96.modalHide();\n    });\n    i0.ɵɵelement(58, \"i\", 57);\n    i0.ɵɵtext(59, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function OpenMaterialComponent_ng_template_44_Template_button_click_60_listener() {\n      i0.ɵɵrestoreView(_r92);\n      const ctx_r97 = i0.ɵɵnextContext();\n      return ctx_r97.onFormSubmit(\"attachment\");\n    });\n    i0.ɵɵelement(61, \"i\", 87);\n    i0.ɵɵtext(62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerText\", ctx_r10.modalTitle);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"formGroup\", ctx_r10.entryForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"clearable\", true)(\"clearOnBackspace\", true)(\"items\", ctx_r10.categoryList)(\"ngClass\", i0.ɵɵpureFunction1(20, _c2, ctx_r10.submitted && ctx_r10.f.categoryId.errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.submitted && ctx_r10.f.categoryId.errors);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(22, _c2, ctx_r10.submitted && ctx_r10.f.title.errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.submitted && ctx_r10.f.title.errors);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.submitted && ctx_r10.f.attachmentFile.errors);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r10.allowList);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.divisionList.length > 0 && ctx_r10.divisionSelected);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.departmentList.length > 0 && ctx_r10.departmentSelected);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.unitList.length > 0 && ctx_r10.unitSelected);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.imageUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.imageUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.traineeSelected);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.btnSaveText, \" \");\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_div_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 90);\n    i0.ɵɵtext(1, \"Category is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, OpenMaterialComponent_ng_template_46_div_13_span_1_Template, 2, 0, \"span\", 89);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r98 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r98.fVideo.categoryId.errors.required);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_div_19_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 90);\n    i0.ɵɵtext(1, \" Title is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_div_19_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 90);\n    i0.ɵɵtext(1, \"Title must be not then 250 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, OpenMaterialComponent_ng_template_46_div_19_span_1_Template, 2, 0, \"span\", 89);\n    i0.ɵɵtemplate(2, OpenMaterialComponent_ng_template_46_div_19_span_2_Template, 2, 0, \"span\", 89);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r99 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r99.fVideo.title.errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r99.fVideo.title.errors.maxlength);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_div_26_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 90);\n    i0.ɵɵtext(1, \" File is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, OpenMaterialComponent_ng_template_46_div_26_span_1_Template, 2, 0, \"span\", 89);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r101 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r101.fVideo.videoFile.errors.required);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵelementStart(1, \"label\", 71);\n    i0.ɵɵtext(2, \"Division \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 63);\n    i0.ɵɵelement(4, \"ng-select\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r102 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r102.divisionList);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵelementStart(1, \"label\", 71);\n    i0.ɵɵtext(2, \"Department \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 63);\n    i0.ɵɵelement(4, \"ng-select\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r103 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r103.departmentList);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵelementStart(1, \"label\", 71);\n    i0.ɵɵtext(2, \"Unit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 63);\n    i0.ɵɵelement(4, \"ng-select\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r104 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r104.unitList);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_img_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r114 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"img\", 94);\n    i0.ɵɵlistener(\"click\", function OpenMaterialComponent_ng_template_46_img_50_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r114);\n      i0.ɵɵnextContext();\n\n      const _r107 = i0.ɵɵreference(53);\n\n      return _r107.click();\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_img_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r116 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"img\", 95);\n    i0.ɵɵlistener(\"click\", function OpenMaterialComponent_ng_template_46_img_51_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r116);\n      i0.ɵɵnextContext();\n\n      const _r107 = i0.ɵɵreference(53);\n\n      return _r107.click();\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r106 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r106.imageUrl, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_div_54_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r122 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵelementStart(1, \"h5\", 99);\n    i0.ɵɵtext(2, \"Select Trainees\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 103);\n    i0.ɵɵelementStart(4, \"input\", 104);\n    i0.ɵɵlistener(\"ngModelChange\", function OpenMaterialComponent_ng_template_46_div_54_div_7_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r122);\n      const ctx_r121 = i0.ɵɵnextContext(3);\n      return ctx_r121.select_All = $event;\n    })(\"change\", function OpenMaterialComponent_ng_template_46_div_54_div_7_Template_input_change_4_listener($event) {\n      i0.ɵɵrestoreView(_r122);\n      const ctx_r123 = i0.ɵɵnextContext(3);\n      return ctx_r123.selectAll($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"label\", 105);\n    i0.ɵɵtext(6, \" Select All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r117 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r117.select_All)(\"ngModelOptions\", i0.ɵɵpureFunction0(2, _c1));\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_div_54_div_8_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r127 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 113);\n    i0.ɵɵelementStart(1, \"div\", 114);\n    i0.ɵɵelementStart(2, \"input\", 115);\n    i0.ɵɵlistener(\"ngModelChange\", function OpenMaterialComponent_ng_template_46_div_54_div_8_li_2_Template_input_ngModelChange_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r127);\n      const item_r125 = restoredCtx.$implicit;\n      return item_r125.selected = $event;\n    })(\"change\", function OpenMaterialComponent_ng_template_46_div_54_div_8_li_2_Template_input_change_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r127);\n      const item_r125 = restoredCtx.$implicit;\n      const ctx_r128 = i0.ɵɵnextContext(4);\n      return ctx_r128.getSelected(item_r125);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 116);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 117);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r125 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"name\", item_r125.Id);\n    i0.ɵɵpropertyInterpolate(\"id\", item_r125.Id);\n    i0.ɵɵpropertyInterpolate(\"value\", item_r125.Id);\n    i0.ɵɵproperty(\"ngModel\", item_r125.selected)(\"ngModelOptions\", i0.ɵɵpureFunction0(9, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"for\", item_r125.Id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", item_r125.Name, \" - \", item_r125.PIN, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r125.Position);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_div_54_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵelementStart(1, \"ul\", 111);\n    i0.ɵɵtemplate(2, OpenMaterialComponent_ng_template_46_div_54_div_8_li_2_Template, 7, 10, \"li\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r118 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r118.traineeList);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_div_54_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵelementStart(1, \"h5\", 99);\n    i0.ɵɵtext(2, \"Selected Trainees\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_div_54_div_11_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r132 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 113);\n    i0.ɵɵelementStart(1, \"div\", 114);\n    i0.ɵɵelementStart(2, \"input\", 115);\n    i0.ɵɵlistener(\"ngModelChange\", function OpenMaterialComponent_ng_template_46_div_54_div_11_li_2_Template_input_ngModelChange_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r132);\n      const item_r130 = restoredCtx.$implicit;\n      return item_r130.selected = $event;\n    })(\"change\", function OpenMaterialComponent_ng_template_46_div_54_div_11_li_2_Template_input_change_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r132);\n      const item_r130 = restoredCtx.$implicit;\n      const ctx_r133 = i0.ɵɵnextContext(4);\n      return ctx_r133.removeSelected(item_r130);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 116);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 117);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r130 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"name\", item_r130.Id);\n    i0.ɵɵpropertyInterpolate(\"id\", item_r130.Id);\n    i0.ɵɵpropertyInterpolate(\"value\", item_r130.Id);\n    i0.ɵɵproperty(\"ngModel\", item_r130.selected)(\"ngModelOptions\", i0.ɵɵpureFunction0(9, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"for\", item_r130.Id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", item_r130.Name, \" - \", item_r130.PIN, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r130.Position);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_div_54_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 118);\n    i0.ɵɵelementStart(1, \"ul\", 111);\n    i0.ɵɵtemplate(2, OpenMaterialComponent_ng_template_46_div_54_div_11_li_2_Template, 7, 10, \"li\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r120 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r120.selectedTraineeList);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelementStart(1, \"div\", 125);\n    i0.ɵɵelementStart(2, \"label\", 126);\n    i0.ɵɵtext(3, \" Trainee User\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 127);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 0);\n    i0.ɵɵelementStart(6, \"div\", 107);\n    i0.ɵɵtemplate(7, OpenMaterialComponent_ng_template_46_div_54_div_7_Template, 7, 3, \"div\", 108);\n    i0.ɵɵtemplate(8, OpenMaterialComponent_ng_template_46_div_54_div_8_Template, 3, 1, \"div\", 106);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 107);\n    i0.ɵɵtemplate(10, OpenMaterialComponent_ng_template_46_div_54_div_10_Template, 3, 0, \"div\", 108);\n    i0.ɵɵtemplate(11, OpenMaterialComponent_ng_template_46_div_54_div_11_Template, 3, 1, \"div\", 109);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r108 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r108.traineeList.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r108.traineeList.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r108.selectedTraineeList.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r108.selectedTraineeList.length > 0);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r135 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"i\", 120);\n    i0.ɵɵelement(2, \"h4\", 121);\n    i0.ɵɵelementStart(3, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function OpenMaterialComponent_ng_template_46_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r135);\n      const ctx_r134 = i0.ɵɵnextContext();\n      return ctx_r134.modalHideVideo();\n    });\n    i0.ɵɵelement(4, \"i\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 58);\n    i0.ɵɵelementStart(6, \"form\", 59);\n    i0.ɵɵelementStart(7, \"div\", 60);\n    i0.ɵɵelementStart(8, \"div\", 61);\n    i0.ɵɵelementStart(9, \"label\", 62);\n    i0.ɵɵtext(10, \" Category \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 63);\n    i0.ɵɵelement(12, \"ng-select\", 64);\n    i0.ɵɵtemplate(13, OpenMaterialComponent_ng_template_46_div_13_Template, 2, 1, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 61);\n    i0.ɵɵelementStart(15, \"label\", 62);\n    i0.ɵɵtext(16, \"Video Title \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 63);\n    i0.ɵɵelement(18, \"textarea\", 66);\n    i0.ɵɵtemplate(19, OpenMaterialComponent_ng_template_46_div_19_Template, 3, 2, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 61);\n    i0.ɵɵelementStart(21, \"label\", 62);\n    i0.ɵɵtext(22, \"Video File \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 63);\n    i0.ɵɵelementStart(24, \"input\", 122, 123);\n    i0.ɵɵlistener(\"change\", function OpenMaterialComponent_ng_template_46_Template_input_change_24_listener() {\n      i0.ɵɵrestoreView(_r135);\n\n      const _r100 = i0.ɵɵreference(25);\n\n      const ctx_r136 = i0.ɵɵnextContext();\n      return ctx_r136.loadVideo(_r100.files);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, OpenMaterialComponent_ng_template_46_div_26_Template, 2, 1, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 61);\n    i0.ɵɵelementStart(28, \"label\", 62);\n    i0.ɵɵtext(29, \"Allow For \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 63);\n    i0.ɵɵelementStart(31, \"ng-select\", 69);\n    i0.ɵɵlistener(\"change\", function OpenMaterialComponent_ng_template_46_Template_ng_select_change_31_listener($event) {\n      i0.ɵɵrestoreView(_r135);\n      const ctx_r137 = i0.ɵɵnextContext();\n      return ctx_r137.changeAllowedFor($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 61);\n    i0.ɵɵelementStart(33, \"label\", 62);\n    i0.ɵɵtext(34, \"Active \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 63);\n    i0.ɵɵelement(36, \"input\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 61);\n    i0.ɵɵelementStart(38, \"label\", 71);\n    i0.ɵɵtext(39, \"Order \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 63);\n    i0.ɵɵelement(41, \"input\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, OpenMaterialComponent_ng_template_46_div_42_Template, 5, 3, \"div\", 73);\n    i0.ɵɵtemplate(43, OpenMaterialComponent_ng_template_46_div_43_Template, 5, 3, \"div\", 73);\n    i0.ɵɵtemplate(44, OpenMaterialComponent_ng_template_46_div_44_Template, 5, 3, \"div\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 74);\n    i0.ɵɵelementStart(46, \"label\", 75);\n    i0.ɵɵtext(47, \"Cover Photo (400x210)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 76);\n    i0.ɵɵelementStart(49, \"div\", 77);\n    i0.ɵɵtemplate(50, OpenMaterialComponent_ng_template_46_img_50_Template, 1, 0, \"img\", 78);\n    i0.ɵɵtemplate(51, OpenMaterialComponent_ng_template_46_img_51_Template, 1, 1, \"img\", 79);\n    i0.ɵɵelementStart(52, \"input\", 80, 81);\n    i0.ɵɵlistener(\"change\", function OpenMaterialComponent_ng_template_46_Template_input_change_52_listener() {\n      i0.ɵɵrestoreView(_r135);\n\n      const _r107 = i0.ɵɵreference(53);\n\n      const ctx_r138 = i0.ɵɵnextContext();\n      return ctx_r138.readURL(_r107);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(54, OpenMaterialComponent_ng_template_46_div_54_Template, 12, 4, \"div\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 83);\n    i0.ɵɵelementStart(56, \"button\", 124);\n    i0.ɵɵlistener(\"click\", function OpenMaterialComponent_ng_template_46_Template_button_click_56_listener() {\n      i0.ɵɵrestoreView(_r135);\n      const ctx_r139 = i0.ɵɵnextContext();\n      return ctx_r139.modalHideVideo();\n    });\n    i0.ɵɵelement(57, \"i\", 57);\n    i0.ɵɵtext(58, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function OpenMaterialComponent_ng_template_46_Template_button_click_59_listener() {\n      i0.ɵɵrestoreView(_r135);\n      const ctx_r140 = i0.ɵɵnextContext();\n      return ctx_r140.onFormSubmit(\"video\");\n    });\n    i0.ɵɵelement(60, \"i\", 87);\n    i0.ɵɵtext(61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerText\", ctx_r12.modalTitle);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"formGroup\", ctx_r12.entryFormVideo);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"clearable\", true)(\"clearOnBackspace\", true)(\"items\", ctx_r12.categoryList)(\"ngClass\", i0.ɵɵpureFunction1(20, _c2, ctx_r12.submitted && ctx_r12.fVideo.categoryId.errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.submitted && ctx_r12.fVideo.categoryId.errors);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(22, _c2, ctx_r12.submitted && ctx_r12.fVideo.title.errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.submitted && ctx_r12.fVideo.title.errors);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.submitted && ctx_r12.fVideo.videoFile.errors);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r12.allowList);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.divisionList.length > 0 && ctx_r12.divisionSelected);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.departmentList.length > 0 && ctx_r12.departmentSelected);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.unitList.length > 0 && ctx_r12.unitSelected);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r12.imageUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.imageUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.traineeSelected);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.btnSaveText, \" \");\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_48_source_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"source\", 135);\n  }\n\n  if (rf & 2) {\n    const ctx_r141 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r141.videoItem.path, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction OpenMaterialComponent_ng_template_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r143 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 128);\n    i0.ɵɵelementStart(1, \"h4\", 129);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function OpenMaterialComponent_ng_template_48_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r143);\n      const ctx_r142 = i0.ɵɵnextContext();\n      return ctx_r142.modalHideVideoPreview();\n    });\n    i0.ɵɵelement(4, \"i\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 130);\n    i0.ɵɵelementStart(6, \"video\", 131);\n    i0.ɵɵtemplate(7, OpenMaterialComponent_ng_template_48_source_7_Template, 1, 1, \"source\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 133);\n    i0.ɵɵelementStart(9, \"button\", 134);\n    i0.ɵɵlistener(\"click\", function OpenMaterialComponent_ng_template_48_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r143);\n      const ctx_r144 = i0.ɵɵnextContext();\n      return ctx_r144.modalHideVideoPreview();\n    });\n    i0.ɵɵelement(10, \"i\", 57);\n    i0.ɵɵtext(11, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r14.videoItem.title);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.videoItem);\n  }\n}\n\nexport class OpenMaterialComponent {\n  constructor(appComponent, modalService, formBuilder, _service, toastr, confirmService) {\n    this.appComponent = appComponent;\n    this.modalService = modalService;\n    this.formBuilder = formBuilder;\n    this._service = _service;\n    this.toastr = toastr;\n    this.confirmService = confirmService;\n    this.submitted = false;\n    this.modalTitle = \"Upload Learning Hour Document\";\n    this.btnSaveText = \"Save\";\n    this.isEdit = false;\n    this.select_All = false;\n    this.selected_count = 0;\n    this.selected_items = [];\n    this.modalConfig = {\n      class: \"gray modal-xl\",\n      backdrop: \"static\"\n    };\n    this.modalConfigVideo = {\n      class: \"gray modal-xl\",\n      backdrop: \"static\"\n    };\n    this.baseUrl = environment.baseUrl;\n    this.page = new Page();\n    this.categoryList = [];\n    this.rows = [];\n    this.loadingIndicator = false;\n    this.ColumnMode = ColumnMode;\n    this.scrollBarHorizontal = window.innerWidth < 1200;\n    this.allowList = [\"All\", \"Division\", \"Department\", \"Unit\", \"Trainee\"];\n    this.materialId = null;\n    this.materialType = null;\n    this.materialTitle = null;\n    this.materialList = [];\n    this.videoItem = null;\n    this.editing = {};\n    this.departmentList = [];\n    this.departmentSelected = false;\n    this.divisionList = [];\n    this.divisionSelected = false;\n    this.unitList = [];\n    this.unitSelected = false;\n    this.traineeList = [];\n    this.selectedTraineeList = [];\n    this.traineeSelected = false;\n    this.traineeLoading = false;\n    this.traineeInput$ = new Subject();\n    this.traineeDisabled = false;\n    this.page.pageNumber = 0;\n    this.page.size = 10; // this.baseUrl = environment.mediaBaseUrl;\n\n    this.baseUrl = environment.baseUrl;\n\n    window.onresize = () => {\n      this.scrollBarHorizontal = window.innerWidth < 1200;\n    };\n  }\n\n  handleSelectClick(selectElement) {\n    this.appComponent.handleSelectClick(selectElement);\n  }\n\n  ngOnInit() {\n    this.entryForm = this.formBuilder.group({\n      id: [null],\n      title: [null, [Validators.required, Validators.maxLength(250)]],\n      categoryId: [true, [Validators.required]],\n      attachmentFile: [null],\n      allowFor: [null],\n      division: [null],\n      department: [null],\n      unit: [null],\n      pin: [null],\n      active: true,\n      order: [null]\n    });\n    this.entryFormVideo = this.formBuilder.group({\n      id: [null],\n      title: [null, [Validators.required, Validators.maxLength(250)]],\n      categoryId: [true, [Validators.required]],\n      videoFile: [null],\n      allowFor: [null],\n      division: [null],\n      department: [null],\n      unit: [null],\n      preparator: [null],\n      pin: [null],\n      active: true,\n      order: [null]\n    });\n    this.entryFormMaterial = this.formBuilder.group({\n      id: [null],\n      title: [null, [Validators.required, Validators.maxLength(250)]],\n      attachmentFile: [null]\n    });\n    this.filterForm = this.formBuilder.group({\n      name: [null],\n      categoryId: [null]\n    });\n    this.filterForm.get(\"name\").valueChanges.pipe(debounceTime(700)).subscribe(() => {\n      this.getMaterialList();\n    }); //this.loaddocEmployeeLoading();\n    //this.loadVideoEmployeeLoading();\n\n    this.getMaterialList();\n    this.getCategoryList();\n    this.entryForm.get(\"pin\").valueChanges.pipe(debounceTime(700)).subscribe(value => {\n      this.getTraineelList(value);\n    });\n    this.entryFormVideo.get(\"pin\").valueChanges.pipe(debounceTime(700)).subscribe(value => {\n      this.getTraineelList(value);\n    });\n  }\n\n  get f() {\n    return this.entryForm.controls;\n  }\n\n  get fVideo() {\n    return this.entryFormVideo.controls;\n  }\n\n  get m() {\n    return this.entryFormMaterial.controls;\n  }\n\n  getCategoryList() {\n    this._service.get('learning-hour-category/dropdown-list').subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.categoryList = res.Data;\n    }, err => {});\n  }\n\n  setPage(pageInfo) {\n    this.page.pageNumber = pageInfo.offset;\n    this.getMaterialList();\n  }\n\n  updateValue(event, cell, rowIndex, row) {\n    const obj = {\n      Id: row.Id,\n      Title: event.target.value,\n      materialType: row.materialType\n    };\n\n    this._service.post(\"course/material/create-or-update\", obj).subscribe(res => {\n      if (!res.Success) {\n        this.toastr.error(res.Message, \"Error!\", {\n          timeOut: 2000\n        });\n        return;\n      }\n\n      this.editing[rowIndex + \"-\" + cell] = false;\n      this.rows[rowIndex][cell] = event.target.value;\n      this.rows = [...this.rows];\n      this.toastr.success(res.Message, \"Success!\", {\n        timeOut: 2000\n      });\n    }, err => {\n      this.toastr.error(\"Error\", \"Error!\", {\n        timeOut: 2000\n      });\n    });\n  }\n\n  loadAttachment(files) {\n    if (files.length === 0) return;\n    this.attachmentFile = files[0];\n  }\n\n  loadVideo(files) {\n    if (files.length === 0) return;\n    this.videoFile = files[0];\n  }\n\n  getMaterialList() {\n    this.loadingIndicator = true;\n    const obj = {\n      name: this.filterForm.value.name ? this.filterForm.value.name.trim() : this.filterForm.value.name,\n      categoryId: this.filterForm.value.categoryId,\n      size: this.page.size,\n      pageNumber: this.page.pageNumber\n    };\n\n    this._service.get(\"open-material/list\", obj).subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      console.log('res.Data.Records', res.Data.Records);\n      this.rows = res.Data.Records;\n      this.page.totalElements = res.Data.Total;\n      this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n      setTimeout(() => {\n        this.loadingIndicator = false;\n      }, 1000);\n    }, err => {\n      this.toastr.error(err.message || err, \"Error!\", {\n        timeOut: 2000\n      });\n      setTimeout(() => {\n        this.loadingIndicator = false;\n      }, 1000);\n    });\n  }\n\n  deleteItem(id) {\n    this.confirmService.confirm(\"Are you sure?\", \"You are deleting this Material.\").subscribe(result => {\n      if (result) {\n        this.blockUI.start(\"Deleting...\");\n\n        this._service.get(\"open-material/delete/\" + id).subscribe(res => {\n          this.blockUI.stop();\n\n          if (res.Status === ResponseStatus.Warning) {\n            this.toastr.warning(res.Message, \"Warning!\", {\n              timeOut: 2000\n            });\n            return;\n          } else if (res.Status === ResponseStatus.Error) {\n            this.toastr.error(res.Message, \"Error!\", {\n              closeButton: true,\n              disableTimeOut: false,\n              enableHtml: true\n            });\n            return;\n          }\n\n          this.getMaterialList();\n          this.toastr.success(res.Message, \"Success!\", {\n            timeOut: 2000\n          });\n        }, err => {\n          this.blockUI.stop();\n          this.toastr.error(err.message || err, \"Error!\", {\n            timeOut: 2000\n          });\n        });\n      }\n    });\n  }\n\n  getItem(item, template, templateVideo) {\n    this.blockUI.start(\"Getting data...\");\n\n    this._service.get(\"open-material/get/\" + item.Id).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, \"Warning!\", {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, \"Error!\", {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.btnSaveText = \"Update\";\n        this.isEdit = true;\n        if (res.Data.ImagePath) this.imageUrl = this.baseUrl + res.Data.ImagePath;\n\n        if (res.Data.MaterialType === \"Video\") {\n          this.modalTitle = \"Update Learning Hour  Video\";\n          this.entryFormVideo.controls[\"id\"].setValue(res.Data.Id);\n          this.entryFormVideo.controls[\"title\"].setValue(res.Data.Title);\n          this.entryFormVideo.controls[\"categoryId\"].setValue(res.Data.CategoryId);\n          this.entryFormVideo.controls[\"allowFor\"].setValue(res.Data.AllowFor);\n          this.entryFormVideo.controls[\"active\"].setValue(res.Data.Active);\n          this.entryFormVideo.controls[\"order\"].setValue(res.Data.Order);\n          this.editChangeAllowedFor(res.Data.AllowFor, res.Data.DivisionId, res.Data.DepartmentId, res.Data.UnitId, res.Data.Trainees);\n          this.modalRef = this.modalService.show(templateVideo, this.modalConfig);\n        } else {\n          this.modalTitle = \"Update Learning Hour Document\";\n          this.entryForm.controls[\"id\"].setValue(res.Data.Id);\n          this.entryForm.controls[\"title\"].setValue(res.Data.Title);\n          this.entryForm.controls[\"allowFor\"].setValue(res.Data.AllowFor);\n          this.entryForm.controls[\"categoryId\"].setValue(res.Data.CategoryId);\n          this.entryForm.controls[\"active\"].setValue(res.Data.Active);\n          this.entryForm.controls[\"order\"].setValue(res.Data.Order);\n          this.editChangeAllowedFor(res.Data.AllowFor, res.Data.DivisionId, res.Data.DepartmentId, res.Data.UnitId, res.Data.Trainees);\n          this.modalRef = this.modalService.show(template, this.modalConfig);\n        }\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, \"Warning!\", {\n          closeButton: true,\n          disableTimeOut: false\n        });\n        this.blockUI.stop();\n      },\n      complete: () => {\n        this.blockUI.stop();\n      }\n    });\n  }\n\n  onFormSubmit(type) {\n    this.submitted = true;\n    const formdata = new FormData();\n\n    if (type === \"attachment\") {\n      if (this.entryForm.invalid) {\n        return;\n      }\n\n      if (!this.entryForm.value.id && !this.entryForm.value.attachmentFile) {\n        this.toastr.warning(\"Please choose a file\", \"Warning!\");\n        return;\n      }\n\n      if (!this.entryForm.value.id && !this.imageFile) {\n        this.toastr.warning(\"Please upload a cover image\", \"Warning!\");\n        return;\n      }\n\n      if (this.entryForm.value.allowFor === \"Trainee\") {\n        if (this.selectedTraineeList.length <= 0) {\n          this.toastr.warning(\"Please select at least one trainee\", \"Warning!\");\n          return;\n        }\n      }\n\n      const obj = {\n        Id: this.entryForm.value.id ? this.entryForm.value.id : \"00000000-0000-0000-0000-000000000000\",\n        Title: this.entryForm.value.title.trim(),\n        CategoryId: this.entryForm.value.categoryId,\n        AllowFor: this.entryForm.value.allowFor,\n        DivisionId: this.entryForm.value.division,\n        DepartmentId: this.entryForm.value.department,\n        UnitId: this.entryForm.value.unit,\n        Active: this.entryForm.value.active,\n        Order: this.entryForm.value.order,\n        Trainees: this.selectedTraineeList.map(function (x) {\n          return x.Id;\n        }),\n        materialType: \"Document\"\n      };\n      formdata.append(\"Model\", JSON.stringify(obj));\n      formdata.append(\"File\", this.attachmentFile);\n      formdata.append(\"Image\", this.imageFile);\n    }\n\n    if (type === \"video\") {\n      if (this.entryFormVideo.invalid) {\n        return;\n      }\n\n      if (!this.entryFormVideo.value.id && !this.entryFormVideo.value.videoFile) {\n        this.toastr.warning(\"Please choose a file\", \"Warning!\");\n        return;\n      }\n\n      if (this.entryFormVideo.value.allowFor === \"Trainee\") {\n        if (this.selectedTraineeList.length <= 0) {\n          this.toastr.warning(\"Please select at least one trainee \", \"Warning!\");\n          return;\n        }\n      }\n\n      const obj = {\n        Id: this.entryFormVideo.value.id ? this.entryFormVideo.value.id : \"00000000-0000-0000-0000-000000000000\",\n        Title: this.entryFormVideo.value.title.trim(),\n        CategoryId: this.entryFormVideo.value.categoryId,\n        AllowFor: this.entryFormVideo.value.allowFor,\n        DivisionId: this.entryFormVideo.value.division,\n        DepartmentId: this.entryFormVideo.value.department,\n        UnitId: this.entryFormVideo.value.unit,\n        Active: this.entryFormVideo.value.active,\n        Order: this.entryFormVideo.value.order,\n        Trainees: this.selectedTraineeList.map(function (x) {\n          return x.Id;\n        }),\n        materialType: \"Video\"\n      };\n      formdata.append(\"Model\", JSON.stringify(obj));\n      formdata.append(\"File\", this.videoFile);\n      formdata.append(\"Image\", this.imageFile);\n    }\n\n    this.blockUI.start(\"Saving...\");\n\n    this._service.post(\"open-material/create-or-update\", formdata).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, \"Success!\", {\n        timeOut: 2000\n      });\n      this.getMaterialList();\n\n      if (type === \"video\") {\n        this.modalHideVideo();\n      } else {\n        this.modalHide();\n      }\n\n      this.getMaterialList();\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, \"Error!\", {\n        timeOut: 2000\n      });\n    });\n  }\n\n  modalHide() {\n    this.clearLists();\n    this.entryForm.reset();\n    this.modalRef.hide();\n    this.submitted = false;\n    this.btnSaveText = \"Save\";\n    this.attachmentFile = null;\n    this.imageUrl = null;\n  }\n\n  modalHideVideo() {\n    this.clearLists();\n    this.entryFormVideo.reset();\n    this.modalRef.hide();\n    this.submitted = false;\n    this.btnSaveText = \"Save\";\n    this.videoFile = null;\n    this.imageUrl = null;\n  }\n\n  openModal(template, type) {\n    this.isEdit = false;\n    this.btnSaveText = \"Save\";\n    this.modalTitle = \"Upload Learning Hour \" + type;\n    this.modalRef = this.modalService.show(template, this.modalConfig);\n  }\n\n  modalHideVideoPreview() {\n    this.modalRef.hide(); // this.getVideoCurrentTime();\n  }\n\n  openModalVideoPreview(template, item) {\n    // console.log(item);\n    this.videoItem = null;\n    this.modalRef = this.modalService.show(template, this.modalConfigVideo);\n    this.videoItem = {\n      id: item.Id,\n      path: this.baseUrl + item.FilePath,\n      title: item.Title\n    };\n  }\n\n  changeAllowedFor(event) {\n    if (event === \"Division\") {\n      if (this.divisionList.length <= 0) {\n        this.getDivisionList();\n      }\n\n      this.divisionSelected = true;\n      this.departmentSelected = false;\n      this.unitSelected = false;\n      this.traineeSelected = false;\n      return;\n    }\n\n    if (event === \"Department\") {\n      if (this.departmentList.length <= 0) {\n        this.getDepartmentList();\n      }\n\n      this.divisionSelected = false;\n      this.departmentSelected = true;\n      this.unitSelected = false;\n      this.traineeSelected = false;\n      return;\n    }\n\n    if (event === \"Unit\") {\n      if (this.unitList.length <= 0) {\n        this.getUnitList();\n      }\n\n      this.divisionSelected = false;\n      this.departmentSelected = false;\n      this.unitSelected = true;\n      this.traineeSelected = false;\n      return;\n    }\n\n    if (event === \"Trainee\") {\n      // if (this.traineeList.length <= 0) {\n      //   this.getTraineelList();\n      // }\n      this.divisionSelected = false;\n      this.departmentSelected = false;\n      this.unitSelected = false;\n      this.traineeSelected = true;\n      return;\n    }\n\n    if (event === \"All\") {\n      this.divisionSelected = false;\n      this.departmentSelected = false;\n      this.unitSelected = false;\n      this.traineeSelected = false;\n      return;\n    }\n  }\n\n  editChangeAllowedFor(event, divisionId, departmentId, unitId, trainees) {\n    this.selectedTraineeList = trainees;\n\n    if (event === \"Division\") {\n      if (this.divisionList.length <= 0) {\n        this.getDivisionList();\n      }\n\n      this.entryForm.controls[\"division\"].setValue(divisionId);\n      this.divisionSelected = true;\n      this.departmentSelected = false;\n      this.unitSelected = false;\n      this.traineeSelected = false;\n      return;\n    }\n\n    if (event === \"Department\") {\n      if (this.departmentList.length <= 0) {\n        this.getDepartmentList();\n      }\n\n      this.entryForm.controls[\"department\"].setValue(departmentId);\n      this.divisionSelected = false;\n      this.departmentSelected = true;\n      this.unitSelected = false;\n      this.traineeSelected = false;\n      return;\n    }\n\n    if (event === \"Unit\") {\n      if (this.unitList.length <= 0) {\n        this.getUnitList();\n      }\n\n      this.entryForm.controls[\"unit\"].setValue(unitId);\n      this.divisionSelected = false;\n      this.departmentSelected = false;\n      this.unitSelected = true;\n      this.traineeSelected = false;\n      return;\n    }\n\n    if (event === \"Trainee\") {\n      this.divisionSelected = false;\n      this.departmentSelected = false;\n      this.unitSelected = false;\n      this.traineeSelected = true;\n      return;\n    }\n\n    if (event === \"All\") {\n      this.divisionSelected = false;\n      this.departmentSelected = false;\n      this.unitSelected = false;\n      this.traineeSelected = false;\n      return;\n    }\n  }\n\n  getDivisionList() {\n    this._service.get(\"division/dropdown-list\").subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.divisionList = res.Data;\n    }, err => {\n      this.toastr.warning(err.message || err, \"warning!\", {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  getDepartmentList() {\n    this._service.get(\"department/dropdown-list\").subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.departmentList = res.Data;\n    }, err => {\n      this.toastr.warning(err.message || err, \"warning!\", {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  getUnitList() {\n    this._service.get(\"unit/dropdown-list\").subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.unitList = res.Data;\n    }, err => {\n      this.toastr.warning(err.message || err, \"warning!\", {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  selectAll(event) {\n    this.traineeList.forEach(element => {\n      element.selected = event.target.checked;\n    });\n    this.selected_items = this.traineeList.filter(x => x.selected);\n    this.selected_count = this.selected_items.length;\n\n    if (event.target.checked) {\n      this.selected_items.forEach(elem => {\n        if (!this.selectedTraineeList.find(x => x.Id === elem.Id)) this.selectedTraineeList.push(elem);\n      });\n    } else {\n      this.traineeList.forEach(element => {\n        element.selected = event.target.checked;\n\n        if (!element.selected) {\n          this.selectedTraineeList = this.selectedTraineeList.filter(obj => obj !== element);\n        }\n      });\n    }\n  }\n\n  getTraineelList(name) {\n    this.select_All = false;\n    if (!name) return;\n\n    this._service.get(\"trainee/query/10/\" + name).subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.traineeList = res.Data;\n      this.traineeList.forEach(element => {\n        if (this.selectedTraineeList.find(x => x.Id === element.Id)) element.selected = true;\n      });\n    }, () => {});\n  }\n\n  getSelected(item) {\n    this.selected_items = this.traineeList.filter(s => {\n      return s.selected;\n    });\n    this.selected_count = this.selected_items.length;\n\n    if (item.selected) {\n      if (!this.selectedTraineeList.find(x => x.Id === item.Id)) this.selectedTraineeList.push(item);\n    } else this.selectedTraineeList = this.selectedTraineeList.filter(obj => obj.Id !== item.Id);\n  }\n\n  setSelected(ids) {\n    this.selected_items = [];\n    this.traineeList.forEach(s => {\n      if (ids.indexOf(s.Id) >= 0) {\n        s.selected = true;\n        this.selected_items.push(s);\n      }\n    });\n    this.selected_count = this.selected_items.length;\n  }\n\n  removeSelected(item) {\n    this.selectedTraineeList = this.selectedTraineeList.filter(obj => obj !== item);\n  }\n\n  clearLists() {\n    this.divisionList = [];\n    this.departmentList = [];\n    this.unitList = [];\n    this.traineeList = [];\n    this.selectedTraineeList = [];\n    this.divisionSelected = false;\n    this.departmentSelected = false;\n    this.unitSelected = false;\n    this.traineeSelected = false;\n    this.select_All = false;\n  }\n\n  readURL(input) {\n    if (!input || input.files.length === 0) return;\n    var mimeType = input.files[0].type;\n\n    if (mimeType.match(/image\\/*/) == null) {\n      this.toastr.warning(\"Only images are supported\", \"Warning!!\");\n      return;\n    }\n\n    var reader = new FileReader();\n    this.imageFile = input.files[0];\n    reader.readAsDataURL(input.files[0]);\n\n    reader.onload = _event => {\n      this.imageUrl = reader.result;\n    };\n  }\n\n}\n\nOpenMaterialComponent.ɵfac = function OpenMaterialComponent_Factory(t) {\n  return new (t || OpenMaterialComponent)(i0.ɵɵdirectiveInject(i1.AppComponent), i0.ɵɵdirectiveInject(i2.BsModalService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.CommonService), i0.ɵɵdirectiveInject(i5.ToastrService), i0.ɵɵdirectiveInject(i6.ConfirmService));\n};\n\nOpenMaterialComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: OpenMaterialComponent,\n  selectors: [[\"app-open-material\"]],\n  viewQuery: function OpenMaterialComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.table = _t.first);\n    }\n  },\n  decls: 50,\n  vars: 36,\n  consts: [[1, \"row\"], [1, \"col-lg-12\"], [1, \"card\", \"card-border-primary\"], [1, \"card-block\"], [\"autocomplete\", \"off\", 1, \"col-lg-8\", \"col-12\", 3, \"formGroup\"], [1, \"col-lg-8\", \"col-12\", \"mb-3\"], [\"for\", \"name\", 1, \"visually-hidden\"], [\"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"type name to search...\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-lg-4\", \"col-12\", \"mb-3\"], [\"for\", \"categoryId\", 1, \"visually-hidden\"], [\"formControlName\", \"categoryId\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Select Category\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"click\", \"change\"], [\"selectElement\", \"\"], [1, \"btn\", \"btn-theme\", \"btn-sm\", \"float-end\", 3, \"click\"], [1, \"fas\", \"fa-file-upload\"], [1, \"btn\", \"btn-theme\", \"btn-sm\", \"float-end\", \"me-2\", 3, \"click\"], [\"rowHeight\", \"auto\", 1, \"material\", \"table-bordered\", \"mb-3\", 3, \"rows\", \"loadingIndicator\", \"externalPaging\", \"columnMode\", \"headerHeight\", \"footerHeight\", \"count\", \"offset\", \"limit\", \"scrollbarH\", \"page\"], [\"name\", \"SL#\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"ngx-datatable-cell-template\", \"\"], [\"name\", \"Title\", 3, \"draggable\", \"sortable\"], [\"prop\", \"MaterialType\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"prop\", \"Category\", \"name\", \"Category\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"width\", \"draggable\", \"sortable\"], [\"prop\", \"AllowFor\", \"name\", \"Allow For\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"draggable\", \"sortable\"], [\"prop\", \"Active\", \"name\", \"Active\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"width\", \"draggable\", \"sortable\"], [\"prop\", \"Order\", \"name\", \"Order\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"width\", \"draggable\", \"sortable\"], [\"name\", \"Action\", \"cellClass\", \"text-center\", \"headerClass\", \"text-center\", 3, \"minWidth\", \"draggable\", \"sortable\"], [\"template\", \"\"], [\"templateVideo\", \"\"], [\"templateVideoModal\", \"\"], [1, \"fw-bold\"], [1, \"media\"], [\"class\", \"me-2 mw-50\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"src\", \"https://via.placeholder.com/50x25?text=No+Image\", \"class\", \"me-2 mw-50\", 3, \"alt\", 4, \"ngIf\"], [1, \"media-body\", \"p-l-10\"], [1, \"text-wrap\", 3, \"title\"], [1, \"me-2\", \"mw-50\", 3, \"src\", \"alt\"], [\"src\", \"https://via.placeholder.com/50x25?text=No+Image\", 1, \"me-2\", \"mw-50\", 3, \"alt\"], [3, \"title\", 4, \"ngIf\"], [3, \"title\"], [\"src\", \"assets/images/file.png\", 1, \"wid-25\"], [\"src\", \"assets/images/play-button.png\", 1, \"wid-25\"], [4, \"ngIf\"], [1, \"admin-color-green\"], [1, \"admin-color-red\"], [\"target\", \"blank\", \"rel\", \"noopener\", \"class\", \"btn btn-icon btn-outline-info btn-mini me-1\", \"tooltip\", \"Download Document\", 3, \"href\", 4, \"ngIf\"], [\"tooltip\", \"Play Video\", \"container\", \"body\", \"class\", \"btn btn-icon btn-outline-info btn-mini me-1\", 3, \"click\", 4, \"ngIf\"], [\"container\", \"body\", \"tooltip\", \"Delete Item\", 1, \"btn\", \"btn-icon\", \"btn-outline-danger\", \"btn-mini\", \"me-1\", 3, \"click\"], [1, \"feather\", \"icon-trash\", \"m-0\"], [\"container\", \"body\", \"tooltip\", \"Edit Item\", 1, \"btn\", \"btn-icon\", \"btn-outline-primary\", \"btn-mini\", \"me-1\", 3, \"click\"], [1, \"feather\", \"icon-edit\"], [\"target\", \"blank\", \"rel\", \"noopener\", \"tooltip\", \"Download Document\", 1, \"btn\", \"btn-icon\", \"btn-outline-info\", \"btn-mini\", \"me-1\", 3, \"href\"], [1, \"feather\", \"icon-download\", \"m-0\"], [\"tooltip\", \"Play Video\", \"container\", \"body\", 1, \"btn\", \"btn-icon\", \"btn-outline-info\", \"btn-mini\", \"me-1\", 3, \"click\"], [1, \"feather\", \"icon-play\", \"m-0\"], [1, \"modal-header\"], [1, \"feather\", \"icon-file\", \"pe-1\"], [\"id\", \"modalTitle\", 1, \"modal-title\", 3, \"innerText\"], [\"type\", \"button \", \"aria-label\", \"Close \", 1, \"close\", \"float-end\", \"btn\", \"btn-mini\", \"btn-danger\", \"btn\", \"btn-mini\", \"btn-outline-danger\", 3, \"click\"], [1, \"feather\", \"icon-x\"], [1, \"card-body\"], [\"autocomplete\", \"off\", 1, \"row\", 3, \"formGroup\"], [1, \"col-lg-8\", \"col-md-7\", \"col-12\"], [1, \"mb-3\", \"row\"], [1, \"col-lg-2\", \"col-md-3\", \"col-12\", \"col-form-label\", \"col-form-label-sm\", \"required\"], [1, \"col-lg-10\", \"col-md-9\", \"col-12\"], [\"formControlName\", \"categoryId\", \"placeholder\", \"Select Category\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"ngClass\"], [\"class\", \"error-text\", 4, \"ngIf\"], [\"formControlName\", \"title\", \"rows\", \"2\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"file\", \"formControlName\", \"attachmentFile\", \"accept\", \".doc,.docx,.pdf,.xls,.xlsx, .ppt, .pptx\", 3, \"change\"], [\"attachment\", \"\"], [\"formControlName\", \"allowFor\", \"placeholder\", \"Allow for\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"change\"], [\"type\", \"checkbox\", \"formControlName\", \"active\", 1, \"form-check-input\"], [1, \"col-lg-2\", \"col-md-3\", \"col-12\", \"col-form-label\", \"col-form-label-sm\"], [\"type\", \"number\", \"formControlName\", \"order\", 1, \"form-control\", \"form-control-sm\"], [\"class\", \"mb-3 row\", 4, \"ngIf\"], [1, \"col-lg-4\", \"col-md-5\", \"col-12\"], [1, \"required\"], [1, \"card\", \"bg-light\", \"pic-upload\", \"mb-0\"], [1, \"card-body\", \"bg-white\"], [\"class\", \"omc-200-140\", \"src\", \"https://via.placeholder.com/200x104?text=No+Image\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"omc-200-140\", 3, \"src\", \"click\", 4, \"ngIf\"], [\"accept\", \"image/png,image/jpeg\", \"type\", \"file\", 1, \"btn-img-upload\", \"mt-2\", 3, \"change\"], [\"docImageInput\", \"\"], [\"class\", \"card mb-0\", 4, \"ngIf\"], [1, \"modal-footer\"], [1, \"pe-4\"], [1, \"btn\", \"btn-outline-danger\", \"me-2\", \"btn-sm\", 3, \"click\"], [1, \"btn\", \"btn-theme\", \"btn-sm\", 3, \"click\"], [1, \"feather\", \"icon-check\"], [1, \"error-text\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"], [\"formControlName\", \"division\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Select a division\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\"], [\"formControlName\", \"department\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Select a department\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\"], [\"formControlName\", \"unit\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Select a unit\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\"], [\"src\", \"https://via.placeholder.com/200x104?text=No+Image\", 1, \"omc-200-140\", 3, \"click\"], [1, \"omc-200-140\", 3, \"src\", \"click\"], [1, \"card\", \"mb-0\"], [1, \"col-6\", \"border-end\"], [1, \"card-header\", \"p-2\"], [1, \"card-title\"], [1, \"col-lg-9\", \"col-12\", \"mt-2\"], [\"type\", \"text\", \"formControlName\", \"pin\", \"placeholder\", \"Please enter 3 or more characters of PIN/Name\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-lg-3\", \"col-12\", \"mt-2\"], [1, \"custom-control\", \"custom-checkbox\", \"float-end\"], [\"name\", \"selectAll\", \"id\", \"selectAll\", \"type\", \"checkbox\", 1, \"custom-control-input\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\", \"change\"], [\"for\", \"selectAll\", 1, \"custom-control-label\"], [\"class\", \"card-body p-2 omc-style-1\", 4, \"ngIf\"], [1, \"col-6\"], [\"class\", \"card-header p-2\", 4, \"ngIf\"], [\"class\", \"card-body p-2 omc-style-2\", 4, \"ngIf\"], [1, \"card-body\", \"p-2\", \"omc-style-1\"], [1, \"list-group\"], [\"class\", \"list-group-item d-flex justify-content-between align-items-center list-group-item-action\", 4, \"ngFor\", \"ngForOf\"], [1, \"list-group-item\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"list-group-item-action\"], [1, \"custom-control\", \"custom-checkbox\"], [\"type\", \"checkbox\", 1, \"custom-control-input\", 3, \"name\", \"id\", \"ngModel\", \"ngModelOptions\", \"value\", \"ngModelChange\", \"change\"], [1, \"custom-control-label\", 3, \"for\"], [1, \"text-muted\", \"mb-0\"], [1, \"card-body\", \"p-2\", \"omc-style-2\"], [\"type\", \"checkbox\", \"checked\", \"\", 1, \"custom-control-input\", 3, \"name\", \"id\", \"value\", \"change\"], [1, \"feather\", \"icon-play\", \"pt-1\"], [\"id\", \"modalTitleVideo\", 1, \"modal-title\", \"float-start\", 3, \"innerText\"], [\"type\", \"file\", \"formControlName\", \"videoFile\", \"accept\", \".mp4\", 3, \"change\"], [\"video\", \"\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"col-12\"], [1, \"col-form-label\", \"col-form-label-sm\", \"required\"], [\"type\", \"text\", \"formControlName\", \"pin\", \"placeholder\", \"Please enter 3 or more characters\", 1, \"form-control\", \"form-control-sm\"], [1, \"modal-header\", \"pt-2\", \"pb-2\"], [1, \"modal-title\", \"float-start\"], [1, \"modal-body\"], [\"id\", \"videoPlayer\", \"disablepictureinpicture\", \"\", \"controlslist\", \"nodownload\", \"controls\", \"\", \"autoplay\", \"\", 1, \"admin-width-100-pc\"], [\"type\", \"video/mp4\", 3, \"src\", 4, \"ngIf\"], [1, \"modal-footer\", \"pt-1\", \"pb-1\"], [1, \"btn\", \"btn-outline-danger\", \"float-end\", 3, \"click\"], [\"type\", \"video/mp4\", 3, \"src\"]],\n  template: function OpenMaterialComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r145 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"div\", 0);\n      i0.ɵɵelementStart(6, \"form\", 4);\n      i0.ɵɵelementStart(7, \"div\", 0);\n      i0.ɵɵelementStart(8, \"div\", 5);\n      i0.ɵɵelementStart(9, \"label\", 6);\n      i0.ɵɵtext(10, \"Name\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(11, \"input\", 7);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(12, \"div\", 8);\n      i0.ɵɵelementStart(13, \"label\", 9);\n      i0.ɵɵtext(14, \"Category\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"ng-select\", 10, 11);\n      i0.ɵɵlistener(\"click\", function OpenMaterialComponent_Template_ng_select_click_15_listener() {\n        i0.ɵɵrestoreView(_r145);\n\n        const _r0 = i0.ɵɵreference(16);\n\n        return ctx.handleSelectClick(_r0);\n      })(\"change\", function OpenMaterialComponent_Template_ng_select_change_15_listener() {\n        return ctx.getMaterialList();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(17, \"div\", 8);\n      i0.ɵɵelementStart(18, \"button\", 12);\n      i0.ɵɵlistener(\"click\", function OpenMaterialComponent_Template_button_click_18_listener() {\n        i0.ɵɵrestoreView(_r145);\n\n        const _r11 = i0.ɵɵreference(47);\n\n        return ctx.openModal(_r11, \"Video\");\n      });\n      i0.ɵɵelement(19, \"i\", 13);\n      i0.ɵɵtext(20, \" Upload Video \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(21, \"button\", 14);\n      i0.ɵɵlistener(\"click\", function OpenMaterialComponent_Template_button_click_21_listener() {\n        i0.ɵɵrestoreView(_r145);\n\n        const _r9 = i0.ɵɵreference(45);\n\n        return ctx.openModal(_r9, \"Document\");\n      });\n      i0.ɵɵelement(22, \"i\", 13);\n      i0.ɵɵtext(23, \" Upload Document \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"div\", 0);\n      i0.ɵɵelementStart(25, \"div\", 1);\n      i0.ɵɵelementStart(26, \"ngx-datatable\", 15);\n      i0.ɵɵlistener(\"page\", function OpenMaterialComponent_Template_ngx_datatable_page_26_listener($event) {\n        return ctx.setPage($event);\n      });\n      i0.ɵɵtext(27, \" > \");\n      i0.ɵɵelementStart(28, \"ngx-datatable-column\", 16);\n      i0.ɵɵtemplate(29, OpenMaterialComponent_ng_template_29_Template, 2, 1, \"ng-template\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(30, \"ngx-datatable-column\", 18);\n      i0.ɵɵtemplate(31, OpenMaterialComponent_ng_template_31_Template, 6, 4, \"ng-template\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(32, \"ngx-datatable-column\", 19);\n      i0.ɵɵtemplate(33, OpenMaterialComponent_ng_template_33_Template, 2, 2, \"ng-template\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(34, \"ngx-datatable-column\", 20);\n      i0.ɵɵtemplate(35, OpenMaterialComponent_ng_template_35_Template, 1, 1, \"ng-template\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(36, \"ngx-datatable-column\", 21);\n      i0.ɵɵtemplate(37, OpenMaterialComponent_ng_template_37_Template, 5, 5, \"ng-template\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(38, \"ngx-datatable-column\", 22);\n      i0.ɵɵtemplate(39, OpenMaterialComponent_ng_template_39_Template, 2, 2, \"ng-template\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(40, \"ngx-datatable-column\", 23);\n      i0.ɵɵtemplate(41, OpenMaterialComponent_ng_template_41_Template, 1, 1, \"ng-template\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(42, \"ngx-datatable-column\", 24);\n      i0.ɵɵtemplate(43, OpenMaterialComponent_ng_template_43_Template, 6, 2, \"ng-template\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(44, OpenMaterialComponent_ng_template_44_Template, 63, 24, \"ng-template\", null, 25, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(46, OpenMaterialComponent_ng_template_46_Template, 62, 24, \"ng-template\", null, 26, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(48, OpenMaterialComponent_ng_template_48_Template, 12, 2, \"ng-template\", null, 27, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"clearable\", true)(\"clearOnBackspace\", true)(\"items\", ctx.categoryList);\n      i0.ɵɵadvance(11);\n      i0.ɵɵproperty(\"rows\", ctx.rows)(\"loadingIndicator\", ctx.loadingIndicator)(\"externalPaging\", true)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"count\", ctx.page.totalElements)(\"offset\", ctx.page.pageNumber)(\"limit\", ctx.page.size)(\"scrollbarH\", true);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 60)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 160)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 150)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 150)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 150)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"minWidth\", 10)(\"draggable\", false)(\"sortable\", false);\n    }\n  },\n  directives: [i7.BlockUIComponent, i3.ɵNgNoValidate, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.DefaultValueAccessor, i3.NgControlStatus, i3.FormControlName, i8.NgSelectComponent, i9.DatatableComponent, i9.DataTableColumnDirective, i9.DataTableColumnCellDirective, i10.NgIf, i11.TooltipDirective, i10.NgClass, i12.DefaultClassDirective, i3.CheckboxControlValueAccessor, i3.NumberValueAccessor, i3.NgModel, i10.NgForOf],\n  styles: [\".btn.btn-icon[_ngcontent-%COMP%]{height:30px;width:30px;font-size:small}.omc-200-140[_ngcontent-%COMP%]{width:200px;height:104px}.omc-style-1[_ngcontent-%COMP%]{max-height:300px;overflow-y:scroll}.omc-style-2[_ngcontent-%COMP%]{max-height:350px;overflow-y:scroll}\"]\n});\n\n__decorate([BlockUI()], OpenMaterialComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}