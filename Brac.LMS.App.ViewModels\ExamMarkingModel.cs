﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.ViewModels
{
    public class ExamMarkingModel
    {
        public Guid ExamId { get; set; }
        public float TotalMark { get; set; }
        public List<MarkingQuestionModel> MCQList { get; set; }
        public List<MarkingQuestionModel> TFQList { get; set; }
        public List<MarkingQuestionModel> FIGQList { get; set; }
        public List<MarkingQuestionModel> MatchingQList { get; set; }
        public List<MarkingQuestionModel> WQList { get; set; }
        public string Comments { get; set; }
    }

    public class MarkingQuestionModel
    {
        public long QuestionId { get; set; }
        public float Mark { get; set; }
    }
}
