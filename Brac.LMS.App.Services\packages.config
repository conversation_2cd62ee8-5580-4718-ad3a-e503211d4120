﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="BouncyCastle" version="1.8.9" targetFramework="net472" />
  <package id="ClosedXML" version="0.95.4" targetFramework="net472" />
  <package id="DocumentFormat.OpenXml" version="2.15.0" targetFramework="net472" />
  <package id="EntityFramework" version="6.4.4" targetFramework="net472" />
  <package id="ExcelNumberFormat" version="1.1.0" targetFramework="net472" />
  <package id="Google.Api.Gax" version="3.2.0" targetFramework="net472" />
  <package id="Google.Api.Gax.Rest" version="3.2.0" targetFramework="net472" />
  <package id="Google.Apis" version="1.49.0" targetFramework="net472" />
  <package id="Google.Apis.Auth" version="1.49.0" targetFramework="net472" />
  <package id="Google.Apis.Core" version="1.49.0" targetFramework="net472" />
  <package id="iTextSharp" version="5.5.13.2" targetFramework="net472" />
  <package id="Microsoft.AspNet.Identity.Core" version="2.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.Identity.EntityFramework" version="2.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="1.0.0" targetFramework="net472" />
  <package id="Microsoft.CSharp" version="4.7.0" targetFramework="net472" />
  <package id="Microsoft.Owin" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net472" />
  <package id="Owin" version="1.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Collections.Immutable" version="1.7.1" targetFramework="net472" />
  <package id="System.Configuration.ConfigurationManager" version="9.0.5" targetFramework="net472" />
  <package id="System.IO.FileSystem.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.Packaging" version="6.0.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.4" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.Caching" version="9.0.5" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.2" targetFramework="net472" />
  <package id="WebActivatorEx" version="2.2.0" targetFramework="net472" />
  <package id="WMPLib" version="1.0.0" targetFramework="net472" developmentDependency="true" />
</packages>