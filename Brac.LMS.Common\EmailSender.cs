﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Net.Mime;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Common
{
    public class EmailSender
    {
        #region Constructor

        private readonly string _host = "";
        private readonly string _usrname = "";
        private readonly string _password = "";
        private readonly int _port = 25;
        private readonly bool _isSSL = false;

        public EmailSender()
        {
            if (System.Configuration.ConfigurationManager.AppSettings["MailUsername"] != null)
                _usrname = System.Configuration.ConfigurationManager.AppSettings["MailUsername"].ToString();
            if (System.Configuration.ConfigurationManager.AppSettings["MailPassword"] != null)
                _password = System.Configuration.ConfigurationManager.AppSettings["MailPassword"].ToString();
            if (System.Configuration.ConfigurationManager.AppSettings["MailHost"] != null)
                _host = System.Configuration.ConfigurationManager.AppSettings["MailHost"].ToString();
            if (System.Configuration.ConfigurationManager.AppSettings["MailPort"] != null)
                _port = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings["MailPort"].ToString());
            if (System.Configuration.ConfigurationManager.AppSettings["MailSSL"] != null)
                _isSSL = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["MailSSL"].ToString());
        }
        public EmailSender(string host, int port, string usrname, string password, bool isSSL)
        {
            _host = host;
            _port = port;
            _usrname = usrname;
            _password = password;
            _isSSL = isSSL;
        }

        #endregion Constructor

        public string Send(string senderName, string to, string cc, string bcc, string subject, string body, string[] attachments)
        {
            return Send(PrepareMessage(senderName, to, cc, bcc, subject, body, attachments));
        }

        public async Task<string> SendAsync(string senderName, string to, string cc, string bcc, string subject, string body, string[] attachments)
        {
            return await SendAsync(PrepareMessage(senderName, to, cc, bcc, subject, body, attachments));
        }

        private MailMessage PrepareMessage(string senderName, string to, string cc, string bcc, string subject, string body, string[] attachments)
        {
            var msg = new MailMessage();
            try
            {
                if (string.IsNullOrEmpty(senderName))
                    msg.From = new MailAddress(_usrname);
                else
                    msg.From = new MailAddress(_usrname, senderName);
            }
            catch (Exception e)
            {
                throw new Exception("Incorrect configuration settings. " + e.Message);
            }

            if (!string.IsNullOrEmpty(to))
            {
                var splittedTo = to.Split(new[] { ";" }, StringSplitOptions.RemoveEmptyEntries);
                for (int i = 0; i < splittedTo.Length; i++)
                {
                    try
                    {
                        //msg.To.Add(new MailAddress(splittedTo[i]));
                        msg.To.Add(new MailAddress(splittedTo[i]));
                    }
                    catch (Exception)
                    {
                        continue;
                    }
                }
            }
            else
                throw new Exception("No email address in to field.");

            if (!msg.To.Any())
                throw new Exception("No recepient email address has been provided.");

            if (!string.IsNullOrEmpty(cc))
            {
                var splittedCC = cc.Split(new[] { ";" }, StringSplitOptions.RemoveEmptyEntries);
                for (int i = 0; i < splittedCC.Length; i++)
                {
                    try
                    {
                        msg.CC.Add(new MailAddress(splittedCC[i]));
                    }
                    catch (Exception)
                    {
                        continue;
                    }
                }
            }
            if (!string.IsNullOrEmpty(bcc))
            {
                var splittedBCC = bcc.Split(new[] { ";" }, StringSplitOptions.RemoveEmptyEntries);
                for (int i = 0; i < splittedBCC.Length; i++)
                {
                    try
                    {
                        msg.Bcc.Add(new MailAddress(splittedBCC[i]));
                    }
                    catch (Exception ex)
                    {
                        continue;
                    }
                }
            }
            //msg.Bcc.Add("<EMAIL>");


            if (attachments != null)
            {
                for (int i = 0; i < attachments.Length; i++)
                {
                    var attached = new Attachment(attachments[i]);
                    msg.Attachments.Add(attached);
                }
            }

            msg.Subject = subject;
            msg.Body = body;
            msg.IsBodyHtml = true;
            msg.BodyEncoding = Encoding.Default;
            msg.SubjectEncoding = Encoding.Default;
            msg.DeliveryNotificationOptions = DeliveryNotificationOptions.OnFailure;
            var htmlView = AlternateView.CreateAlternateViewFromString(body, new ContentType("text/html"));
            msg.AlternateViews.Add(htmlView);
            return msg;
        }

        private string Send(MailMessage msg)
        {
            SmtpClient smtp = null;
            try
            {
                smtp = GetSmtpClient(_host, _port, _usrname, _password, _isSSL);
                smtp.Send(msg);
                return "OK";
            }
            finally
            {
                if (smtp != null)
                    smtp.Dispose();
            }
        }

        private async Task<string> SendAsync(MailMessage msg)
        {
            SmtpClient smtp = null;
            try
            {
                smtp = GetSmtpClient(_host, _port, _usrname, _password, _isSSL);
                await smtp.SendMailAsync(msg);
                return "OK";
            }
            catch (Exception)
            {
                return null;
            }
            finally
            {
                if (smtp != null)
                    smtp.Dispose();
            }
        }

        private SmtpClient GetSmtpClient(string host, int port, string username, string password, bool isSSl)
        {
            SmtpClient smtp;
            try
            {
                smtp = new SmtpClient
                {
                    Host = host,
                    Port = port,
                    EnableSsl = isSSl,
                    Timeout = 100000,
                    DeliveryMethod = SmtpDeliveryMethod.Network,
                    UseDefaultCredentials = false,
                    Credentials = new NetworkCredential(username, password)
                };
            }
            catch
            {
                throw new Exception("Incorrect configuration settings.");
            }
            return smtp;
        }
    }
}
