﻿using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;

namespace Brac.LMS.App.Services
{
    public class CourseSegmentService : ICourseSegmentService
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditLogHelper _auditLogHelper;
        private readonly AuditLog audit;
        private readonly ApplicationUser _appUser;
        public CourseSegmentService()
        {
            _context = new ApplicationDbContext();
        }
        public CourseSegmentService(string controller, string method, ApplicationUser applicationUser)
        {
            _auditLogHelper = new AuditLogHelper(controller, method);
            audit = _auditLogHelper.auditLog;
            _appUser = applicationUser;
            audit.CreatedBy = _appUser.Id;
            _context = new ApplicationDbContext();
        }
        public async Task<APIResponse> SegmentCreateOrUpdate(SegmentModel model, ApplicationUser user)
        {
            CourseSegment item = null;
            bool isEdit = true;
            try
            {
                if (await _context.CourseSegments.AnyAsync(x => x.Id != model.Id && x.Name == model.Name))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Already exists: " + model.Name
                    };

                if (model.Id.HasValue)
                {
                    item = await _context.CourseSegments.FindAsync(model.Id);
                    if (item == null) return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Segment not found"
                    };
                }
                else
                {
                    item = new CourseSegment();
                    isEdit = false;
                }

                item.Name = model.Name;
                item.Active = model.Active;

                item.SetAuditTrailEntity(user.User.Identity);

                if (!isEdit) _context.CourseSegments.Add(item);
                else _context.Entry(item).State = EntityState.Modified;

                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                };
            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())
                };
            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetSegmentList(int size, int pagenumber)
        {
            try
            {
                var data = await _context.CourseSegments.Select(x => new
                {
                    x.Id,
                    x.Name,
                    x.Active
                }).OrderBy(x => x.Name)
                .Skip(pagenumber * size).Take(size).ToListAsync();

                var count = await _context.CourseSegments.CountAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data, Total = count }
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetSegmentDropDownList()
        {
            try
            {
                var query = _context.CourseSegments.Where(x => x.Active).AsQueryable();

                var data = await query.Select(x => new
                {
                    x.Id,
                    x.Name
                }).OrderBy(x => x.Name).ToListAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetSegmentById(long id)
        {
            try
            {
                var query = _context.CourseSegments.Where(t => t.Id == id).AsQueryable();

                var data = await query
                .Select(t => new
                {
                    t.Id,
                    t.Name,
                    t.Active
                }).FirstOrDefaultAsync();

                return new APIResponse
                {
                    Status = data == null ? ResponseStatus.Warning : ResponseStatus.Success,
                    Message = data == null ? "Segment not found" : null,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }
    }

    public interface ICourseSegmentService
    {
        Task<APIResponse> SegmentCreateOrUpdate(SegmentModel model, ApplicationUser user);
        Task<APIResponse> GetSegmentList(int size, int pageNumber);
        Task<APIResponse> GetSegmentDropDownList();
        Task<APIResponse> GetSegmentById(long id);
    }
}
