﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class TraineeCourseActivity : NumberAuditableEntity
    {
        //public TraineeCourseActivity()
        //{
        //    MaterialStudies = new HashSet<CourseMaterialStudy>();
        //}
        public Guid TraineeId { get; set; }
        public virtual Trainee Trainee { get; set; }

        public Guid CourseId { get; set; }
        public virtual Course Course { get; set; }

        public int NoOfContents { get; set; }
        public int NoOfContentsStudied { get; set; }

        public bool HasCertification { get; set; }
        public bool CertificateAchieved { get; set; }
        public int Progress { get; set; }
        public DateTime? FirstStudyDate { get; set; }
        public DateTime? LastStudyDate { get; set; }
        public virtual ICollection<CourseMaterialStudy> MaterialStudies { get; set; }

        public void GenerateProgress()
        {
            Progress = HasCertification && CertificateAchieved ? 100 : (NoOfContents + (HasCertification ? 1 : 0)) * 100 / (NoOfContentsStudied == 0 ? 1 : NoOfContentsStudied);
        }
    }

    public class CourseMaterialStudy : NumberEntityField
    {
        public long ActivityId { get; set; }
        public virtual TraineeCourseActivity Activity { get; set; }

        public Guid MaterialId { get; set; }
        public virtual CourseMaterial Material { get; set; }

        public DateTime StudyDate { get; set; }
        public int StudyTimeSec { get; set; }
        public bool Downloaded { get; set; }
        public bool Completed { get; set; }

    }
}
