﻿using Brac.LMS.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.ViewModels
{
    public class CourseDiscussionModel
    {
        public long? Id { get; set; }
        public Guid CourseId { get; set; }
        public DiscussionType? DiscussionType { get; set; }
        public Guid? MaterialId { get; set; }
        public Guid? ExamId { get; set; }
        public Guid? MockTestId { get; set; }
        public string Comment { get; set; }

    }
}
