﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace BRAC.LMS.Background.Processor
{
    public class FirebaseMessagingClient : IFirebaseMessagingClient
    {
        private readonly string _serverkey = "AAAA5Cnh22E:APA91bHoxWU-vlDS51lsXoZyCn94EenWdvCD1ZPK7wakukU0-st3pUiSsHfY1Z5Lq0bIsB-2tlVewmvcmQPFu-6zWk94vBrWe2jG9wR21oEQ5Y9ZF2AbuhSDen5W-h8CYDbtIoMRyNu4";
        private readonly string _senderId = "979955211105";

        public FirebaseMessagingClient()
        {
            if (System.Configuration.ConfigurationManager.AppSettings["FCMServerKey"] == null) throw new Exception("FCMServerKey not configured");
            _serverkey = System.Configuration.ConfigurationManager.AppSettings["FCMServerKey"].ToString();
            if (System.Configuration.ConfigurationManager.AppSettings["FCMSenderID"] == null) throw new Exception("FCMSenderID not configured");
            _senderId = System.Configuration.ConfigurationManager.AppSettings["FCMSenderID"].ToString();
        }

        //public async Task SendNotification(string token, string title, string body)
        //{
        //    var result = await messaging.SendAsync(CreateNotification(title, body, token));
        //    //do something with result
        //}

        //public async Task SendNotification(string token, string title, object body)
        //{
        //    var message = new Message()
        //    {
        //        Data = new Dictionary<string, string>()
        //            {
        //                { "score", "850" },
        //                { "time", "2:45" },
        //            },
        //        Token = token,
        //    };

        //    // Send a message to the device corresponding to the provided
        //    // registration token.
        //    string response = await messaging.SendAsync(message);
        //    //do something with result
        //}


        public async Task SendNotifications(string[] tokens, string title, object body)
        {
            WebRequest tRequest = WebRequest.Create("https://fcm.googleapis.com/fcm/send");
            tRequest.Method = "post";
            tRequest.ContentType = "application/json";
            var objNotification = new
            {
                registration_ids = tokens,
                priority = "high",
                data = new
                {
                    title = title,
                    body = body
                }
            };
            string jsonNotificationFormat = Newtonsoft.Json.JsonConvert.SerializeObject(objNotification);

            byte[] byteArray = Encoding.UTF8.GetBytes(jsonNotificationFormat);
            tRequest.Headers.Add(string.Format("Authorization: key={0}", _serverkey));
            tRequest.Headers.Add(string.Format("Sender: id={0}", _senderId));
            tRequest.ContentLength = byteArray.Length;
            tRequest.ContentType = "application/json";
            using (Stream dataStream = await tRequest.GetRequestStreamAsync())
            {
                dataStream.Write(byteArray, 0, byteArray.Length);

                using (WebResponse tResponse = await tRequest.GetResponseAsync())
                {
                    using (Stream dataStreamResponse = tResponse.GetResponseStream())
                    {
                        using (StreamReader tReader = new StreamReader(dataStreamResponse))
                        {
                            string responseFromFirebaseServer = await tReader.ReadToEndAsync();

                            FCMResponse response = Newtonsoft.Json.JsonConvert.DeserializeObject<FCMResponse>(responseFromFirebaseServer);
                            if (response.failure == 1)
                            {
                                throw new Exception($"#ERROR => Push Notification : Error sent from FCM server, after sending request : {responseFromFirebaseServer} , for following device info: {jsonNotificationFormat}");
                            }

                        }
                    }

                }
            }
        }

        public async Task SendNotifications(string[] tokens, string title, string body, object data)
        {
            WebRequest tRequest = WebRequest.Create("https://fcm.googleapis.com/fcm/send");
            tRequest.Method = "post";
            tRequest.ContentType = "application/json";
            var objNotification = new
            {
                registration_ids = tokens,
                data,
                //data = new
                //{
                //    navigateTo = "profile",
                //    payload = "userId"
                //},
                notification = new
                {
                    title,
                    body
                },
                content_available = true,
                priority = "high"
            };
            string jsonNotificationFormat = Newtonsoft.Json.JsonConvert.SerializeObject(objNotification);

            byte[] byteArray = Encoding.UTF8.GetBytes(jsonNotificationFormat);
            tRequest.Headers.Add(string.Format("Authorization: key={0}", _serverkey));
            tRequest.Headers.Add(string.Format("Sender: id={0}", _senderId));
            tRequest.Headers.Add("apns-priority", "10");
            tRequest.Headers.Add("apns-push-type", "background"); // for iOS 13 required

            tRequest.ContentLength = byteArray.Length;
            tRequest.ContentType = "application/json";
            using (Stream dataStream = await tRequest.GetRequestStreamAsync())
            {
                dataStream.Write(byteArray, 0, byteArray.Length);

                using (WebResponse tResponse = await tRequest.GetResponseAsync())
                {
                    using (Stream dataStreamResponse = tResponse.GetResponseStream())
                    {
                        using (StreamReader tReader = new StreamReader(dataStreamResponse))
                        {
                            string responseFromFirebaseServer = await tReader.ReadToEndAsync();

                            FCMResponse response = Newtonsoft.Json.JsonConvert.DeserializeObject<FCMResponse>(responseFromFirebaseServer);
                            if (response.failure == 1)
                            {
                                throw new Exception($"#ERROR => Push Notification : Error sent from FCM server, after sending request : {responseFromFirebaseServer} , for following device info: {jsonNotificationFormat}");
                            }

                        }
                    }

                }
            }
        }
    }

    public interface IFirebaseMessagingClient
    {
        Task SendNotifications(string[] tokens, string title, string body, object data);
        Task SendNotifications(string[] tokens, string title, object body);
    }

    public class FCMResponse
    {
        public long multicast_id { get; set; }
        public int success { get; set; }
        public int failure { get; set; }
        public int canonical_ids { get; set; }
        public List<FCMResult> results { get; set; }
    }
    public class FCMResult
    {
        public string message_id { get; set; }
    }
}
