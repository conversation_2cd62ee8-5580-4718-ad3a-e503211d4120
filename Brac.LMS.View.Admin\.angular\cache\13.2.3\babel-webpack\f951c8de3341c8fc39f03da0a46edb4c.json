{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, EventEmitter, Component, Inject, Optional, Input, Output, HostListener, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nfunction UiSwitchComponent_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 2);\n    i0.ɵɵelementStart(1, \"span\", 3);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 4);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"color\", ctx_r0.getColor(\"checkedTextColor\"));\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.checkedLabel);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.checkedLabel);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"color\", ctx_r0.getColor(\"uncheckedTextColor\"));\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.uncheckedLabel);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.uncheckedLabel);\n  }\n}\n\nconst _c0 = [\"*\"];\nconst UI_SWITCH_OPTIONS = new InjectionToken('UI_SWITCH_OPTIONS');\nconst UI_SWITCH_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => UiSwitchComponent),\n  multi: true\n};\nlet UiSwitchComponent = /*#__PURE__*/(() => {\n  class UiSwitchComponent {\n    constructor(config = {}, cdr) {\n      this.cdr = cdr;\n      /**\n       * Emits changed value\n       */\n      // eslint-disable-next-line @angular-eslint/no-output-native\n\n      this.change = new EventEmitter();\n      /**\n       * Emits DOM event\n       */\n\n      this.changeEvent = new EventEmitter();\n      /**\n       * Emits changed value\n       */\n\n      this.valueChange = new EventEmitter();\n\n      this.onTouchedCallback = v => {};\n\n      this.onChangeCallback = v => {};\n\n      this.size = config && config.size || 'medium';\n      this.color = config && config.color;\n      this.switchOffColor = config && config.switchOffColor;\n      this.switchColor = config && config.switchColor;\n      this.defaultBgColor = config && config.defaultBgColor;\n      this.defaultBoColor = config && config.defaultBoColor;\n      this.checkedLabel = config && config.checkedLabel;\n      this.uncheckedLabel = config && config.uncheckedLabel;\n      this.checkedTextColor = config && config.checkedTextColor;\n      this.uncheckedTextColor = config && config.uncheckedTextColor;\n    }\n\n    set checked(v) {\n      this._checked = v !== false;\n    }\n\n    get checked() {\n      return this._checked;\n    }\n\n    set disabled(v) {\n      this._disabled = v !== false;\n    }\n\n    get disabled() {\n      return this._disabled;\n    }\n\n    set reverse(v) {\n      this._reverse = v !== false;\n    }\n\n    get reverse() {\n      return this._reverse;\n    }\n\n    set loading(v) {\n      this._loading = v !== false;\n    }\n\n    get loading() {\n      return this._loading;\n    }\n\n    getColor(flag = '') {\n      if (flag === 'borderColor') {\n        return this.defaultBoColor;\n      }\n\n      if (flag === 'switchColor') {\n        if (this.reverse) {\n          return !this.checked ? this.switchColor : this.switchOffColor || this.switchColor;\n        }\n\n        return this.checked ? this.switchColor : this.switchOffColor || this.switchColor;\n      }\n\n      if (flag === 'checkedTextColor') {\n        return this.reverse ? this.uncheckedTextColor : this.checkedTextColor;\n      }\n\n      if (flag === 'uncheckedTextColor') {\n        return this.reverse ? this.checkedTextColor : this.uncheckedTextColor;\n      }\n\n      if (this.reverse) {\n        return !this.checked ? this.color : this.defaultBgColor;\n      }\n\n      return this.checked ? this.color : this.defaultBgColor;\n    }\n\n    onClick(event) {\n      if (this.disabled) {\n        return;\n      }\n\n      this.checked = !this.checked; // Component events\n\n      this.change.emit(this.checked);\n      this.valueChange.emit(this.checked);\n      this.changeEvent.emit(event); // value accessor callbacks\n\n      this.onChangeCallback(this.checked);\n      this.onTouchedCallback(this.checked);\n      this.cdr.markForCheck();\n    }\n\n    onToggle(event) {\n      if (this.disabled) {\n        return;\n      }\n\n      if (this.beforeChange) {\n        this._beforeChange = this.beforeChange.subscribe(confirm => {\n          if (confirm) {\n            this.onClick(event);\n          }\n        });\n      } else {\n        this.onClick(event);\n      }\n    }\n\n    writeValue(obj) {\n      if (obj !== this.checked) {\n        this.checked = !!obj;\n      }\n\n      this.onChangeCallback(this.checked);\n\n      if (this.cdr) {\n        this.cdr.markForCheck();\n      }\n    }\n\n    registerOnChange(fn) {\n      this.onChangeCallback = fn;\n    }\n\n    registerOnTouched(fn) {\n      this.onTouchedCallback = fn;\n    }\n\n    setDisabledState(isDisabled) {\n      this.disabled = isDisabled;\n    }\n\n    ngOnDestroy() {\n      if (this._beforeChange) {\n        this._beforeChange.unsubscribe();\n      }\n    }\n\n  }\n\n  UiSwitchComponent.ɵfac = function UiSwitchComponent_Factory(t) {\n    return new (t || UiSwitchComponent)(i0.ɵɵdirectiveInject(UI_SWITCH_OPTIONS, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n\n  UiSwitchComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: UiSwitchComponent,\n    selectors: [[\"ui-switch\"]],\n    hostBindings: function UiSwitchComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function UiSwitchComponent_click_HostBindingHandler($event) {\n          return ctx.onToggle($event);\n        });\n      }\n    },\n    inputs: {\n      size: \"size\",\n      color: \"color\",\n      switchOffColor: \"switchOffColor\",\n      switchColor: \"switchColor\",\n      defaultBgColor: \"defaultBgColor\",\n      defaultBoColor: \"defaultBoColor\",\n      checkedLabel: \"checkedLabel\",\n      uncheckedLabel: \"uncheckedLabel\",\n      checkedTextColor: \"checkedTextColor\",\n      uncheckedTextColor: \"uncheckedTextColor\",\n      beforeChange: \"beforeChange\",\n      checked: \"checked\",\n      disabled: \"disabled\",\n      reverse: \"reverse\",\n      loading: \"loading\"\n    },\n    outputs: {\n      change: \"change\",\n      changeEvent: \"changeEvent\",\n      valueChange: \"valueChange\"\n    },\n    features: [i0.ɵɵProvidersFeature([UI_SWITCH_CONTROL_VALUE_ACCESSOR])],\n    ngContentSelectors: _c0,\n    decls: 4,\n    vars: 20,\n    consts: [[\"type\", \"button\", \"role\", \"switch\", 1, \"switch\"], [\"class\", \"switch-pane\", 4, \"ngIf\"], [1, \"switch-pane\"], [1, \"switch-label-checked\"], [1, \"switch-label-unchecked\"]],\n    template: function UiSwitchComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"button\", 0);\n        i0.ɵɵtemplate(1, UiSwitchComponent_label_1_Template, 5, 8, \"label\", 1);\n        i0.ɵɵelementStart(2, \"small\");\n        i0.ɵɵprojection(3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n      }\n\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"background-color\", ctx.getColor())(\"border-color\", ctx.getColor(\"borderColor\"));\n        i0.ɵɵclassProp(\"checked\", ctx.checked)(\"disabled\", ctx.disabled)(\"loading\", ctx.loading)(\"switch-large\", ctx.size === \"large\")(\"switch-medium\", ctx.size === \"medium\")(\"switch-small\", ctx.size === \"small\");\n        i0.ɵɵattribute(\"aria-checked\", ctx.checked);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.checkedLabel || ctx.uncheckedLabel);\n        i0.ɵɵadvance(1);\n        i0.ɵɵstyleProp(\"background\", ctx.getColor(\"switchColor\"));\n      }\n    },\n    directives: [i1.NgIf],\n    encapsulation: 2\n  });\n  return UiSwitchComponent;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\nlet UiSwitchModule = /*#__PURE__*/(() => {\n  class UiSwitchModule {\n    static forRoot(config) {\n      return {\n        ngModule: UiSwitchModule,\n        providers: [{\n          provide: UI_SWITCH_OPTIONS,\n          useValue: config || {}\n        }]\n      };\n    }\n\n  }\n\n  UiSwitchModule.ɵfac = function UiSwitchModule_Factory(t) {\n    return new (t || UiSwitchModule)();\n  };\n\n  UiSwitchModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: UiSwitchModule\n  });\n  UiSwitchModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [[CommonModule, FormsModule], FormsModule]\n  });\n  return UiSwitchModule;\n})();\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { UiSwitchComponent, UiSwitchModule }; //# sourceMappingURL=ngx-ui-switch.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}