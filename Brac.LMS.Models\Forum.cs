﻿using Brac.LMS.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public enum ForumTopicStatus { Pending, Open, Closed }
    public class ForumTopic : EntityField
    {
        [Required, StringLength(500)]
        public string Title { get; set; }

        [Required, StringLength(2000)]
        public string Description { get; set; }

        public ForumTopicStatus Status { get; set; }

        public long CategoryId { get; set; }
        public virtual ForumCategory Category { get; set; }

        public virtual ICollection<ForumTag> Tags { get; set; }


        [Required, StringLength(128)]
        public string CreatorId { get; set; }
        public virtual ApplicationUser Creator { get; set; }

        private DateTime _CreatedDate;
        public DateTime CreatedDate
        {
            get
            { return _CreatedDate; }
            set
            { _CreatedDate = value.ToKindUtc(); }
        }

        private DateTime? _ModifiedDate;
        public DateTime? ModifiedDate
        {
            get
            { return _ModifiedDate; }
            set
            { _ModifiedDate = value.HasValue ? value.Value.ToKindUtc() : value; }
        }


        [StringLength(128)]
        public string ClosedById { get; set; }
        public virtual ApplicationUser ClosedBy { get; set; }

        private DateTime? _ClosedDate;
        public DateTime? ClosedDate
        {
            get
            { return _ClosedDate; }
            set
            { _ClosedDate = value.HasValue ? value.Value.ToKindUtc() : value; }
        }

        [StringLength(128)]
        public string ApprovedById { get; set; }
        public virtual ApplicationUser ApprovedBy { get; set; }

        private DateTime? _ApprovedDate;
        public DateTime? ApprovedDate
        {
            get
            { return _ApprovedDate; }
            set
            { _ApprovedDate = value.HasValue ? value.Value.ToKindUtc() : value; }
        }

        public virtual ICollection<ForumPost> Posts { get; set; }
    }


    public class ForumPost : NumberEntityField
    {
        public Guid TopicId { get; set; }
        public virtual ForumTopic Topic { get; set; }

        public string Comment { get; set; }

        [Required, StringLength(128)]
        public string CreatorId { get; set; }
        public virtual ApplicationUser Creator { get; set; }

        private DateTime _CreatedDate;
        public DateTime CreatedDate
        {
            get
            { return _CreatedDate; }
            set
            { _CreatedDate = value.ToKindUtc(); }
        }

        public long? ParentPostId { get; set; }
        public virtual ForumPost ParentPost { get; set; }

        public virtual ICollection<ForumPostLike> Likes { get; set; }
        public virtual ICollection<ForumPost> Replies { get; set; }
    }

    public class ForumPostLike
    {
        [Key, Column(Order = 1)]
        public long PostId { get; set; }
        public virtual ForumPost Post { get; set; }

        [Key, Column(Order = 2), StringLength(128)]
        public string UserId { get; set; }
        public virtual ApplicationUser User { get; set; }

        private DateTime _LikedOn;
        public DateTime LikedOn {
            get
            { return _LikedOn; }
            set
            { _LikedOn = value.ToKindUtc(); }
        }
    }
}
