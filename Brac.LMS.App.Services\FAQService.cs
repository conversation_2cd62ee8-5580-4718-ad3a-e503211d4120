﻿
using Brac.LMS.App.ViewModels;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.Services
{
    public class FAQService : IFAQService
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditLogHelper _auditLogHelper;
        private readonly AuditLog audit;
        private readonly ApplicationUser _appUser;
        public FAQService()
        {
            _context = new ApplicationDbContext();
        }
        public FAQService(string controller, string method, ApplicationUser applicationUser)
        {
            _auditLogHelper = new AuditLogHelper(controller, method);
            audit = _auditLogHelper.auditLog;
            _appUser = applicationUser;
            audit.CreatedBy = _appUser.Id;
            _context = new ApplicationDbContext();
        }
        public async Task<APIResponse> FAQCreateOrUpdate(FAQModel model, IIdentity identity)
        {
            FAQ item = null;
            bool isEdit = true;
            try
            {
                if (await _context.FAQs.AnyAsync(x => x.Id != model.Id && x.Question == model.Question && x.CourseId == model.CourseId))
                        return new APIResponse
                        {
                            Status = ResponseStatus.Warning,
                            Message = "Already exists: " + model.Question
                        }; 

                if (model.Id.HasValue && model.Id > 0)
                {
                    item = await _context.FAQs.FindAsync(model.Id);
                    if (item == null) return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "FAQ not found"
                    }; 
                }
                else
                {
                    if (await _context.FAQs.AnyAsync(x => x.CourseId == model.CourseId))
                        item = new FAQ { Sequence = await _context.FAQs.Where(x => x.CourseId == model.CourseId).MaxAsync(x => x.Sequence) + 1 };
                    else
                        item = new FAQ { Sequence = 1 };
                    isEdit = false;
                }

                item.Question = model.Question;
                item.Answer = model.Answer;
                item.CourseId = model.CourseId;

                item.SetAuditTrailEntity(identity);

                if (!isEdit)
                {
                    _context.FAQs.Add(item);
                }
                else
                {
                    _context.Entry(item).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                    }
                };

            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())

                };
            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetFAQList(int size, int pageNumber)
        {
            try
            {
                var data = await _context.FAQs.Where(x => x.CourseId == null).Select(x => new
                {
                    x.Id,
                    x.Question,
                    x.Answer,
                    x.Sequence
                }).OrderBy(x => x.Sequence).ThenBy(x => x.Question)
                            .Skip(pageNumber * size).Take(size).ToListAsync();

                var count = await _context.FAQs.CountAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Records = data,
                        Total = count
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException != null ? ex.InnerException.InnerException?.Message ?? ex.InnerException.Message : ex.Message
                };
            }

        }

        public async Task<APIResponse> GetCourseFAQList(int size, int pageNumber, Guid? courseId)
        {
            try
            {
                var query = _context.FAQs.Where(x => x.CourseId != null).AsQueryable();
                if (courseId.HasValue) query = query.Where(x => x.CourseId == courseId);
                var data = await query.Select(x => new
                {
                    x.Id,
                    x.Course.Title,
                    x.Question,
                    x.Answer,
                    x.Sequence
                }).OrderBy(x => x.Sequence).ThenBy(x => x.Question)
                            .Skip(pageNumber * size).Take(size).ToListAsync();

                var count = query.Count();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Records = data,
                        Total = count
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetFAQById(long id)
        {
            try
            {
                var data = await _context.FAQs.Where(t => t.Id == id)
                .Select(t => new
                {
                    t.Id,
                    t.Question,
                    t.CourseId,
                    t.Answer
                }).FirstOrDefaultAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Record = data
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> DeleteFAQById(long id)
        {
            try
            {
                var item = await _context.FAQs.FindAsync(id);
                if (item == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "FAQ not found"
                }; 
                _context.Entry(item).State = EntityState.Deleted;
                await _context.SaveChangesAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Message = "Successfully Deleted"
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> MoveFAQById(long id, Movement movement)
        {
            try
            {
                var item = await _context.FAQs.FindAsync(id);
                if (item == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "FAQ not found"
                };

                FAQ movableFaq = null;

                switch (movement)
                {
                    case Movement.UP:
                        movableFaq = await _context.FAQs.Where(x => x.Sequence < item.Sequence && x.CourseId == item.CourseId).OrderByDescending(x => x.Sequence).Take(1).FirstOrDefaultAsync();
                        if (movableFaq != null) movableFaq.Sequence = item.Sequence;
                        item.Sequence--;
                        break;
                    case Movement.DOWN:
                        movableFaq = await _context.FAQs.Where(x => x.Sequence > item.Sequence && x.CourseId == item.CourseId).OrderBy(x => x.Sequence).Take(1).FirstOrDefaultAsync();
                        if (movableFaq != null) movableFaq.Sequence = item.Sequence;
                        item.Sequence++;
                        break;
                }

                _context.Entry(movableFaq).State = EntityState.Modified;
                _context.Entry(item).State = EntityState.Modified;

                await _context.SaveChangesAsync();


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Message = "Moved Successfully"
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }
    }

    public interface IFAQService
    {
        Task<APIResponse> FAQCreateOrUpdate(FAQModel model, IIdentity identity);
        Task<APIResponse> GetFAQList(int size, int pageNumber);
        Task<APIResponse> GetCourseFAQList(int size, int pageNumber, Guid? courseId);
        Task<APIResponse> GetFAQById(long id);
        Task<APIResponse> DeleteFAQById(long id);
        Task<APIResponse> MoveFAQById(long id, Movement movement);
    }
}
