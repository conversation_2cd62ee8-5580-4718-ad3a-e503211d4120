{"ast": null, "code": "var GOOD_LEAF_SIZE = 200; // :: class<T> A rope sequence is a persistent sequence data structure\n// that supports appending, prepending, and slicing without doing a\n// full copy. It is represented as a mostly-balanced tree.\n\nvar RopeSequence = function RopeSequence() {};\n\nRopeSequence.prototype.append = function append(other) {\n  if (!other.length) {\n    return this;\n  }\n\n  other = RopeSequence.from(other);\n  return !this.length && other || other.length < GOOD_LEAF_SIZE && this.leafAppend(other) || this.length < GOOD_LEAF_SIZE && other.leafPrepend(this) || this.appendInner(other);\n}; // :: (union<[T], RopeSequence<T>>) → RopeSequence<T>\n// Prepend an array or other rope to this one, returning a new rope.\n\n\nRopeSequence.prototype.prepend = function prepend(other) {\n  if (!other.length) {\n    return this;\n  }\n\n  return RopeSequence.from(other).append(this);\n};\n\nRopeSequence.prototype.appendInner = function appendInner(other) {\n  return new Append(this, other);\n}; // :: (?number, ?number) → RopeSequence<T>\n// Create a rope repesenting a sub-sequence of this rope.\n\n\nRopeSequence.prototype.slice = function slice(from, to) {\n  if (from === void 0) from = 0;\n  if (to === void 0) to = this.length;\n\n  if (from >= to) {\n    return RopeSequence.empty;\n  }\n\n  return this.sliceInner(Math.max(0, from), Math.min(this.length, to));\n}; // :: (number) → T\n// Retrieve the element at the given position from this rope.\n\n\nRopeSequence.prototype.get = function get(i) {\n  if (i < 0 || i >= this.length) {\n    return undefined;\n  }\n\n  return this.getInner(i);\n}; // :: ((element: T, index: number) → ?bool, ?number, ?number)\n// Call the given function for each element between the given\n// indices. This tends to be more efficient than looping over the\n// indices and calling `get`, because it doesn't have to descend the\n// tree for every element.\n\n\nRopeSequence.prototype.forEach = function forEach(f, from, to) {\n  if (from === void 0) from = 0;\n  if (to === void 0) to = this.length;\n\n  if (from <= to) {\n    this.forEachInner(f, from, to, 0);\n  } else {\n    this.forEachInvertedInner(f, from, to, 0);\n  }\n}; // :: ((element: T, index: number) → U, ?number, ?number) → [U]\n// Map the given functions over the elements of the rope, producing\n// a flat array.\n\n\nRopeSequence.prototype.map = function map(f, from, to) {\n  if (from === void 0) from = 0;\n  if (to === void 0) to = this.length;\n  var result = [];\n  this.forEach(function (elt, i) {\n    return result.push(f(elt, i));\n  }, from, to);\n  return result;\n}; // :: (?union<[T], RopeSequence<T>>) → RopeSequence<T>\n// Create a rope representing the given array, or return the rope\n// itself if a rope was given.\n\n\nRopeSequence.from = function from(values) {\n  if (values instanceof RopeSequence) {\n    return values;\n  }\n\n  return values && values.length ? new Leaf(values) : RopeSequence.empty;\n};\n\nvar Leaf = /*@__PURE__*/function (RopeSequence) {\n  function Leaf(values) {\n    RopeSequence.call(this);\n    this.values = values;\n  }\n\n  if (RopeSequence) Leaf.__proto__ = RopeSequence;\n  Leaf.prototype = Object.create(RopeSequence && RopeSequence.prototype);\n  Leaf.prototype.constructor = Leaf;\n  var prototypeAccessors = {\n    length: {\n      configurable: true\n    },\n    depth: {\n      configurable: true\n    }\n  };\n\n  Leaf.prototype.flatten = function flatten() {\n    return this.values;\n  };\n\n  Leaf.prototype.sliceInner = function sliceInner(from, to) {\n    if (from == 0 && to == this.length) {\n      return this;\n    }\n\n    return new Leaf(this.values.slice(from, to));\n  };\n\n  Leaf.prototype.getInner = function getInner(i) {\n    return this.values[i];\n  };\n\n  Leaf.prototype.forEachInner = function forEachInner(f, from, to, start) {\n    for (var i = from; i < to; i++) {\n      if (f(this.values[i], start + i) === false) {\n        return false;\n      }\n    }\n  };\n\n  Leaf.prototype.forEachInvertedInner = function forEachInvertedInner(f, from, to, start) {\n    for (var i = from - 1; i >= to; i--) {\n      if (f(this.values[i], start + i) === false) {\n        return false;\n      }\n    }\n  };\n\n  Leaf.prototype.leafAppend = function leafAppend(other) {\n    if (this.length + other.length <= GOOD_LEAF_SIZE) {\n      return new Leaf(this.values.concat(other.flatten()));\n    }\n  };\n\n  Leaf.prototype.leafPrepend = function leafPrepend(other) {\n    if (this.length + other.length <= GOOD_LEAF_SIZE) {\n      return new Leaf(other.flatten().concat(this.values));\n    }\n  };\n\n  prototypeAccessors.length.get = function () {\n    return this.values.length;\n  };\n\n  prototypeAccessors.depth.get = function () {\n    return 0;\n  };\n\n  Object.defineProperties(Leaf.prototype, prototypeAccessors);\n  return Leaf;\n}(RopeSequence); // :: RopeSequence\n// The empty rope sequence.\n\n\nRopeSequence.empty = new Leaf([]);\n\nvar Append = /*@__PURE__*/function (RopeSequence) {\n  function Append(left, right) {\n    RopeSequence.call(this);\n    this.left = left;\n    this.right = right;\n    this.length = left.length + right.length;\n    this.depth = Math.max(left.depth, right.depth) + 1;\n  }\n\n  if (RopeSequence) Append.__proto__ = RopeSequence;\n  Append.prototype = Object.create(RopeSequence && RopeSequence.prototype);\n  Append.prototype.constructor = Append;\n\n  Append.prototype.flatten = function flatten() {\n    return this.left.flatten().concat(this.right.flatten());\n  };\n\n  Append.prototype.getInner = function getInner(i) {\n    return i < this.left.length ? this.left.get(i) : this.right.get(i - this.left.length);\n  };\n\n  Append.prototype.forEachInner = function forEachInner(f, from, to, start) {\n    var leftLen = this.left.length;\n\n    if (from < leftLen && this.left.forEachInner(f, from, Math.min(to, leftLen), start) === false) {\n      return false;\n    }\n\n    if (to > leftLen && this.right.forEachInner(f, Math.max(from - leftLen, 0), Math.min(this.length, to) - leftLen, start + leftLen) === false) {\n      return false;\n    }\n  };\n\n  Append.prototype.forEachInvertedInner = function forEachInvertedInner(f, from, to, start) {\n    var leftLen = this.left.length;\n\n    if (from > leftLen && this.right.forEachInvertedInner(f, from - leftLen, Math.max(to, leftLen) - leftLen, start + leftLen) === false) {\n      return false;\n    }\n\n    if (to < leftLen && this.left.forEachInvertedInner(f, Math.min(from, leftLen), to, start) === false) {\n      return false;\n    }\n  };\n\n  Append.prototype.sliceInner = function sliceInner(from, to) {\n    if (from == 0 && to == this.length) {\n      return this;\n    }\n\n    var leftLen = this.left.length;\n\n    if (to <= leftLen) {\n      return this.left.slice(from, to);\n    }\n\n    if (from >= leftLen) {\n      return this.right.slice(from - leftLen, to - leftLen);\n    }\n\n    return this.left.slice(from, leftLen).append(this.right.slice(0, to - leftLen));\n  };\n\n  Append.prototype.leafAppend = function leafAppend(other) {\n    var inner = this.right.leafAppend(other);\n\n    if (inner) {\n      return new Append(this.left, inner);\n    }\n  };\n\n  Append.prototype.leafPrepend = function leafPrepend(other) {\n    var inner = this.left.leafPrepend(other);\n\n    if (inner) {\n      return new Append(inner, this.right);\n    }\n  };\n\n  Append.prototype.appendInner = function appendInner(other) {\n    if (this.left.depth >= Math.max(this.right.depth, other.depth) + 1) {\n      return new Append(this.left, new Append(this.right, other));\n    }\n\n    return new Append(this, other);\n  };\n\n  return Append;\n}(RopeSequence);\n\nvar ropeSequence = RopeSequence;\nexport default ropeSequence;", "map": null, "metadata": {}, "sourceType": "module"}