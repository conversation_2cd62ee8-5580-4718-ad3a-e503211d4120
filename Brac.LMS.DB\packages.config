﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="EntityFramework" version="6.4.4" targetFramework="net472" />
  <package id="Microsoft.AspNet.Identity.Core" version="2.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.Identity.EntityFramework" version="2.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.Identity.Owin" version="2.2.3" targetFramework="net472" />
  <package id="Microsoft.Owin" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security.Cookies" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security.OAuth" version="4.2.0" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net472" />
  <package id="Owin" version="1.0" targetFramework="net472" />
</packages>