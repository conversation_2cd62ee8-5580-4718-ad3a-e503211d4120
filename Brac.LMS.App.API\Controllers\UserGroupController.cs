﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using Brac.LMS.DB;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.Owin;

namespace Brac.LMS.App.API.Controllers
{
    [Authorize(Roles = "Admin"), RoutePrefix("api/user-group")]
    public class UserGroupController : ApplicationController
    {
        private readonly IUserGroupService _service;

        public UserGroupController()
        {
            _service = new UserGroupService();
        }

        public ApplicationUserManager UserManager => Request.GetOwinContext().GetUserManager<ApplicationUserManager>();
        public ApplicationRoleManager RoleManager => Request.GetOwinContext().Get<ApplicationRoleManager>();

        [HttpPost, Route("create-or-update")]
        public async Task<IHttpActionResult> UserGroupCreateOrUpdate(UserGroupModel model)
        {
            return Ok(await _service.UserGroupCreateOrUpdate(model, User.Identity, RoleManager));
        }

        [HttpGet, Route("list")]
        public async Task<IHttpActionResult> GetUserGroupList(int size, int pageNumber)
        {
            return Ok(await _service.GetUserGroupList(size, pageNumber));
        }

        [HttpGet, Route("dropdown-list")]
        public async Task<IHttpActionResult> GetUserGroupDropDownList()
        {
            return Ok(await _service.GetUserGroupDropDownList());
        }

        [HttpGet, Route("get/{id}")]
        public async Task<IHttpActionResult> GetUserGroupById(long id)
        {
            return Ok(await _service.GetUserGroupById(id));
        }

        [HttpGet, Route("permission/list")]
        public async Task<IHttpActionResult> GetAllIdentityRoles()
        {
            return Ok(await _service.GetAllIdentityRoles(User.Identity.GetUserId(), UserManager, RoleManager));
        }

        [HttpGet, Route("{id}/permissions")]
        public async Task<IHttpActionResult> GetIdentityRolesByGroupId(long id)
        {
            return Ok(await _service.GetIdentityRolesByGroupId(id));
        }
    }
}
