﻿using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using ClosedXML.Excel;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

namespace Brac.LMS.App.Services
{
    public class ExternalCourseService : IExternalCourseService
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditLogHelper _auditLogHelper;
        private readonly AuditLog audit;
        private readonly ApplicationUser _appUser;

        public ExternalCourseService()
        {
            _context = new ApplicationDbContext();
        }
        public ExternalCourseService(string controller, string method, ApplicationUser applicationUser)
        {
            _auditLogHelper = new AuditLogHelper(controller, method);
            audit = _auditLogHelper.auditLog;
            _appUser = applicationUser;
            audit.CreatedBy = _appUser.Id;
            _context = new ApplicationDbContext();
        }
        public async Task<APIResponse> ExternalCourseCreateOrUpdate(ExternalCourseModel model, ApplicationUser user)
        {
            ExternalCourse item = null;
            bool isEdit = true;
            try
            {


                if (await _context.ExternalCourses.AnyAsync(x => x.Id != model.Id && x.Title == model.Title))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Already exists: " + model.Title
                    };

                if (model.Id.HasValue)
                {
                    item = await _context.ExternalCourses.FindAsync(model.Id);
                    if (item == null) return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "External course not found"
                    };
                }
                else
                {
                    item = new ExternalCourse
                    {
                        Code = Utility.GenerateCode(await _context.ExternalCourses.OrderByDescending(x => x.CreatedDate).Select(x => x.Code).FirstOrDefaultAsync(), "C", 4)
                    };
                    isEdit = false;
                }

                item.Title = model.Title;
                item.Description = model.Description;
                item.Active = model.Active;
                item.CategoryId = model.CategoryId;
                item.ExternalUrl = model.ExternalUrl;

                if (HttpContext.Current.Request.Files.Count > 0)
                    item.ImagePath = Utility.SaveImage(item.Code + Utility.RandomString(3, false), "/Images/ExternalCourse/", HttpContext.Current.Request.Files[0], item.ImagePath, 400, 210);

                item.SetAuditTrailEntity(user.User.Identity);

                if (!isEdit)
                {
                    item.Id = Guid.NewGuid();
                    _context.ExternalCourses.Add(item);
                }
                else
                {
                    _context.Entry(item).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                };
            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())

                };
            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }
        }

        public async Task<APIResponse> GetExternalCourseList(string name, long? categoryId, int size, int pageNumber, ApplicationUser user)
        {
            try
            {
                var query = _context.ExternalCourses.AsQueryable();


                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Title.Contains(name) || x.Code.Contains(name));
                if (categoryId.HasValue) query = query.Where(x => x.CategoryId == categoryId);
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderByDescending(x => x.CreatedDate)
                .Skip(pageNumber * size).Take(size);
                var data = await filteredQuery.Select(x => new
                {
                    x.Id,
                    x.Code,
                    x.Title,
                    x.Description,
                    x.Active,
                    x.ExternalUrl,
                    x.ImagePath,
                    Category = x.Category != null ? x.Category.Name : null,
                    x.CreatedDate
                }).ToListAsync();

                var count = await ((((!string.IsNullOrEmpty(name)) || (categoryId.HasValue)) || ((!string.IsNullOrEmpty(name)) && (categoryId.HasValue))) ? filteredQuery.CountAsync() : _context.ExternalCourses.CountAsync());
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data, Total = count }
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<byte[]> GetExternalCourseListExcel(int timeZoneOffset)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            try
            {
                var data = await _context.ExternalCourses.Where(x => x.Active).OrderBy(x => x.Code).ThenBy(x => x.CreatedDate)
                .Select(x => new
                {
                    x.Code,
                    x.Title,
                    Category = x.Category != null ? x.Category.Name : null,
                    x.Active,
                }).ToListAsync();
                if (!data.Any()) throw new Exception("No course found");

                var headerColumns = new List<string> { "SL#", "Course Name", "Category", "Active" };

                ExcelManager.GetTextLineElement("ExternalCourse List (Active Only)", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                ExcelManager.GetTextLineElement("Report Generation Time: " + Utility.UTCToLocal(DateTime.UtcNow, timeZoneOffset).ToString("dd-MMM-yyyy HH:mm:ss tt"), ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                rowNo++;

                ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);
                int counter = 0;
                foreach (var item in data)
                {
                    counter++;
                    rowNo++;
                    colNo = 1;
                    ExcelManager.GetTableDataCell(counter, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center, isBold: true);
                    ExcelManager.GetTableDataCell(item.Title, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                    ExcelManager.GetTableDataCell(item.Category, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Left);
                    ExcelManager.GetTableDataCell(item.Active ? "YES" : "NO", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                }

                for (int i = 1; i <= headerColumns.Count; i++)
                    ws.Column(i).AdjustToContents();

                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
            catch (Exception ioex)
            {
                ExcelManager.GetTextLineElement("Excel File Generation Error: " + ioex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
        }

        public async Task<APIResponse> GetExternalCourseById(Guid id, ApplicationUser user)
        {
            try
            {
                var query = _context.ExternalCourses.Where(t => t.Id == id).AsQueryable();

                var data = await query
                .Select(t => new
                {
                    t.Id,
                    t.Title,
                    t.Description,
                    t.ImagePath,
                    t.CategoryId,
                    t.ExternalUrl,
                    t.Active
                }).FirstOrDefaultAsync();

                if (data == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "External course not found"
                };

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetExternalCourseDropDownList(ApplicationUser user)
        {
            try
            {
                var query = _context.ExternalCourses.Where(x => x.Active).OrderBy(o => o.Title).AsQueryable();

                var data = await query
                .Select(t => new
                {
                    t.Id,
                    t.Title
                }).ToListAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        #region Trainee Panel APIs
        public async Task<APIResponse> GetCourseList(int limit, DateTime? keyDate = null)
        {
            try
            {
                var query = _context.ExternalCourses.AsQueryable();

                if (keyDate.HasValue) query = query.Where(x => x.CreatedDate < keyDate);

                var data = await query.Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.Description,
                    x.ImagePath,
                    x.CreatedDate,
                    x.Active
                }).OrderByDescending(x => x.CreatedDate)
                .Take(limit).ToListAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Data = data, Key = data.Any() ? data.Min(x => x.CreatedDate) : default(DateTime?) }
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException != null ? ex.InnerException.InnerException?.Message ?? ex.InnerException.Message : ex.Message
                };
            }
        }

        public async Task<APIResponse> GetCourseList(string name, long? categoryId, int size, int pageNumber)
        {
            try
            {
                var query = _context.ExternalCourses.Where(x=>x.Active==true).AsQueryable();

                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Title.Contains(name));
                if (categoryId.HasValue) query = query.Where(x => x.CategoryId == categoryId);
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderByDescending(x => x.CreatedDate)
                .Skip(pageNumber * size).Take(size);
                var data = await filteredQuery.Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.Description,
                    x.ImagePath,
                    x.CreatedDate,
                    x.Active
                }).ToListAsync();

                var count = await ((((!string.IsNullOrEmpty(name)) || (categoryId.HasValue)) || ((!string.IsNullOrEmpty(name)) && (categoryId.HasValue))) ? filteredQuery.CountAsync() : _context.ExternalCourses.CountAsync());


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Records = data,
                        Total = count
                    }
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException != null ? ex.InnerException.InnerException?.Message ?? ex.InnerException.Message : ex.Message
                };
            }
        }

        public async Task<APIResponse> GetCoursePreview(Guid id)
        {
            try
            {
                var data = await _context.ExternalCourses.Where(x => x.Id == id && x.Active)
                    .Select(x => new
                    {
                        x.Id,
                        x.Title,
                        x.Description,
                        x.ImagePath,
                        x.ExternalUrl
                    }).FirstOrDefaultAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }

        }
        #endregion
    }

    public interface IExternalCourseService
    {
        Task<APIResponse> ExternalCourseCreateOrUpdate(ExternalCourseModel model, ApplicationUser user);
        Task<APIResponse> GetExternalCourseList(string name, long? categoryId, int size, int pageNumber, ApplicationUser user);
        Task<byte[]> GetExternalCourseListExcel(int timeZoneOffset);
        Task<APIResponse> GetExternalCourseById(Guid id, ApplicationUser user);
        Task<APIResponse> GetExternalCourseDropDownList(ApplicationUser user);

        #region Trainee Panel APIs
        Task<APIResponse> GetCourseList(int limit, DateTime? keyDate = null);
        Task<APIResponse> GetCourseList(string name, long? categoryId, int size, int pageNumber);
        Task<APIResponse> GetCoursePreview(Guid id);
        #endregion
    }
}


