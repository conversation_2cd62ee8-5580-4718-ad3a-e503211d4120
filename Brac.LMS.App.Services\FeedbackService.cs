﻿
using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using ClosedXML.Excel;
using iTextSharp.text.pdf;
using iTextSharp.text;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;
using System.Web.Hosting;

namespace Brac.LMS.App.Services
{
    public class FeedbackService : IFeedbackService
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditLogHelper _auditLogHelper;
        private readonly AuditLog audit;
        private readonly ApplicationUser _appUser;
        public FeedbackService()
        {
            _context = new ApplicationDbContext();
        }
        public FeedbackService(string controller, string method, ApplicationUser applicationUser)
        {
            _auditLogHelper = new AuditLogHelper(controller, method);
            audit = _auditLogHelper.auditLog;
            _appUser = applicationUser;
            audit.CreatedBy = _appUser.Id;
            _context = new ApplicationDbContext();
        }
        public async Task<APIResponse> FeedbackQuestionCreateOrUpdate(FeedbackQuestionModel model, EvaluationType type, IIdentity identity)
        {
            FeedbackQuestion item = null;
            bool isEdit = true;
            try
            {
                if (await _context.FeedbackQuestions.AnyAsync(x => x.Id != model.Id && x.Question == model.Question && x.EvaluationType == type))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Already exists: " + model.Question
                    };

                if (model.Id.HasValue && model.Id > 0)
                {
                    item = await _context.FeedbackQuestions.FindAsync(model.Id);
                    if (item == null) return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Feedback not found"
                    };
                }
                else
                {
                    item = new FeedbackQuestion { EvaluationType = type };
                    isEdit = false;
                }

                item.QuestionGroup = model.QuestionGroup;
                item.Question = model.Question;
                item.QuestionType = model.QuestionType;
                item.Options = model.Options.Any() ? JsonConvert.SerializeObject(model.Options) : null;

                item.SetAuditTrailEntity(identity);

                if (!isEdit)
                {
                    _context.FeedbackQuestions.Add(item);
                }
                else
                {
                    _context.Entry(item).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);


                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                };

            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())

                };
            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetFeedbackQuestionList(EvaluationType type, QuestionGroup? group, QuestionType? qType, int size, int pageNumber)
        {
            try
            {
                var query = _context.FeedbackQuestions.Where(x => x.EvaluationType == type);

                if (group.HasValue) query = query.Where(x => x.QuestionGroup == group);
                if (qType.HasValue) query = query.Where(x => x.QuestionType == qType);
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderBy(x => x.QuestionGroup).ThenBy(x => x.QuestionType).ThenBy(x => x.Question)
                .Skip(pageNumber * size).Take(size);

                var list = await filteredQuery.Select(x => new
                {
                    x.Id,
                    x.QuestionGroup,
                    x.Question,
                    x.QuestionType,
                    x.Options
                }).ToListAsync();

                var data = list.Select(x => new
                {
                    x.Id,
                    QuestionGroup = x.QuestionGroup.ToString(),
                    x.Question,
                    QuestionType = x.QuestionType.ToString(),
                    Options = !string.IsNullOrEmpty(x.Options) ? JsonConvert.DeserializeObject<List<string>>(x.Options) : new List<string>()
                }).ToList();

                var count = await ((((group.HasValue || qType.HasValue) || (group.HasValue && qType.HasValue))) ? query.CountAsync() : _context.FeedbackQuestions.Where(x => x.EvaluationType == type).CountAsync());
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Records = data,
                        Total = count
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException != null ? ex.InnerException.InnerException?.Message ?? ex.InnerException.Message : ex.Message
                };
            }

        }

        public async Task<APIResponse> GetFeedbackQuestionById(long id)
        {
            try
            {
                var item = await _context.FeedbackQuestions.Where(t => t.Id == id)
                .Select(x => new
                {
                    x.Id,
                    x.QuestionGroup,
                    x.Question,
                    x.QuestionType,
                    x.Options
                }).FirstOrDefaultAsync();

                var data = new
                {
                    item.Id,
                    item.QuestionGroup,
                    item.Question,
                    item.QuestionType,
                    Options = !string.IsNullOrEmpty(item.Options) ? JsonConvert.DeserializeObject<List<string>>(item.Options) : new List<string>()
                };

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<byte[]> GetTraineeCourseFeedbacks(Guid courseId, int timeZoneOffset)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            try
            {
                var course = await _context.Courses.Where(x => x.Id == courseId).Select(x => x.Title).FirstOrDefaultAsync();

                var data = await _context.CourseFeedbackAnswers.Where(t => t.Feedback.CourseId == courseId)
                    .GroupBy(x => new
                    {
                        x.Feedback.TraineeId,
                        x.Feedback.Trainee.PIN,
                        x.Feedback.Trainee.Name
                    })
                .Select(x => new
                {
                    x.Key.TraineeId,
                    x.Key.PIN,
                    x.Key.Name,
                    Feedbacks = x.Select(y => new
                    {
                        y.Question.Question,
                        y.Answer,
                        y.Question.QuestionGroup,
                        y.Question.QuestionType
                    }).OrderBy(y => y.QuestionGroup).ToList()
                }).ToListAsync();

                var headerColumns = new List<string> { "Trainee PIN", "Trainee Name", "Group", "Question", "Answer" };

                ExcelManager.GetTextLineElement("Trainee Wise Course's Feedbacks", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                ExcelManager.GetTextLineElement("Course: " + course, ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                ExcelManager.GetTextLineElement("Report Generation Time: " + Utility.UTCToLocal(DateTime.UtcNow, timeZoneOffset).ToString("dd-MMM-yyyy HH:mm:ss tt"), ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                rowNo++;

                ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);

                int counter;

                foreach (var trainee in data)
                {
                    rowNo++;
                    colNo = 1;
                    ExcelManager.GetTableDataCell(trainee.PIN, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                    ExcelManager.GetTableDataCell(trainee.Name, 10, rowNo, colNo++, ws);
                    counter = 0;
                    foreach (var item in trainee.Feedbacks)
                    {
                        colNo = 3;
                        if (counter != 0) rowNo++;
                        ExcelManager.GetTableDataCell(item.QuestionGroup.ToString(), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        ExcelManager.GetTableDataCell(item.Question, 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(!string.IsNullOrEmpty(item.Answer) && item.Answer.Contains("&*") ? item.Answer.Replace("&*", " || ") : item.Answer, 10, rowNo, colNo++, ws);
                        counter++;
                    }
                }

                for (int i = 0; i < headerColumns.Count - 2; i++)
                {
                    ws.Column(i + 1).AdjustToContents();
                }
                ws.Column(4).Width = 50;
                ws.Column(4).Style.Alignment.WrapText = true;
                ws.Column(5).Width = 50;
                ws.Column(5).Style.Alignment.WrapText = true;

                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
            catch (Exception ibex)
            {
                ExcelManager.GetTextLineElement("Error: " + ibex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
        }

        public async Task<byte[]> GetTraineeCourseFeedbacksPdf(Guid courseId, int timeZoneOffset)
        {
            var document = new iTextSharp.text.Document(iTextSharp.text.PageSize.A4, 36, 36, 100, 36);
            document.SetPageSize(iTextSharp.text.PageSize.A4.Rotate());
            var reportStream = new MemoryStream();

            try
            {
                PdfWriter writer = PdfWriter.GetInstance(document, reportStream);
                writer.PageEvent = new PDFWriterEvents(true, true);
                writer.CloseStream = false;

                // Openning the Document
                document.Open();
                var company = await _context.Configurations.FirstOrDefaultAsync();
                var course = await _context.Courses.Where(x => x.Id == courseId).Select(x => x.Title).FirstOrDefaultAsync();

                var data = await _context.CourseFeedbackAnswers.Where(t => t.Feedback.CourseId == courseId)
                    .GroupBy(x => new
                    {
                        x.Feedback.TraineeId,
                        x.Feedback.Trainee.PIN,
                        x.Feedback.Trainee.Name
                    })
                .Select(x => new
                {
                    x.Key.TraineeId,
                    x.Key.PIN,
                    x.Key.Name,
                    Feedbacks = x.Select(y => new
                    {
                        y.Question.Question,
                        y.Answer,
                        y.Question.QuestionGroup,
                        y.Question.QuestionType
                    }).OrderBy(y => y.QuestionGroup).ToList()
                }).ToListAsync();

                #region Page Header
                PdfPTable header = new PdfPTable(1);
                header.TotalWidth = 180;
                header.DefaultCell.Border = 0;
                header.AddCell(PDFManager.GetTextLineElement(company.Name, 12f, isBold: true));
                header.AddCell(PDFManager.GetTextLineElement(company.Address + " , " + company.ContactNo, isBold: false, fontSize: 8, leftToRightPadding: new float[] { 4, 2, 4, 2 }));
                header.WriteSelectedRows(0, 3, writer.PageSize.Width - document.LeftMargin - document.RightMargin - 180, writer.PageSize.GetTop(10), writer.DirectContent);
                if (!string.IsNullOrEmpty(company.LogoPath))
                {
                    Image img = Image.GetInstance(HostingEnvironment.MapPath("~") + company.LogoPath);
                    img.SetAbsolutePosition(document.LeftMargin, writer.PageSize.GetTop(70));
                    img.ScaleAbsolute(200f, 60f);
                    document.Add(img);
                }
                PdfPTable line = new PdfPTable(1);
                line.TotalWidth = writer.PageSize.Width;
                line.DefaultCell.Border = 0;
                line.AddCell(new Paragraph(new Chunk(new iTextSharp.text.pdf.draw.LineSeparator(0.0F, 100.0F, BaseColor.BLACK, Element.ALIGN_CENTER, 3))));
                line.WriteSelectedRows(0, 1, 0, writer.PageSize.GetTop(header.TotalHeight + 10), writer.DirectContent);
                #endregion

                document.Add(PDFManager.GetTextLineElement("Trainee Wise Course's Feedbacks", fontSize: 16, isBold: true, isUnderlined: false, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Add(PDFManager.GetTextLineElement("Course: " + course, fontSize: 12, isBold: true, isUnderlined: false, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                #region Table
                var table = new PdfPTable(5) { WidthPercentage = 100 };
                table.SetWidths(new float[] { 10, 25, 25, 20, 10 });
                table.AddCell(PDFManager.GetTableHeaderCell("Pin", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Name", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Group", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Question", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Answer", 8f, true, false, PDFAlignment.Center));

                foreach (var item in data)
                {
                    table.AddCell(PDFManager.GetTableDataCell(item.PIN, PDFAlignment.Left, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(item.Name, PDFAlignment.Center, false, true, 8));
                    int column = 0;
                    foreach (var subItem in item.Feedbacks)
                    {
                        column++;

                        table.AddCell(PDFManager.GetTableDataCell(subItem.QuestionGroup.ToString(), PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(subItem.Question, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(!string.IsNullOrEmpty(subItem.Answer) && subItem.Answer.Contains("&*") ? subItem.Answer.Replace("&*", " || ") : subItem.Answer, PDFAlignment.Center, false, true, 8));
                        if (item.Feedbacks.Count() > column)
                        {
                            table.AddCell(PDFManager.GetTableDataCell("", PDFAlignment.Center, false, true, 8));
                            table.AddCell(PDFManager.GetTableDataCell("", PDFAlignment.Center, false, true, 8));
                        }
                    }

                }
                document.Add(table);
                #endregion

                // Closing the Document
                document.Close();
                return reportStream.ToArray();
            }
            catch (DocumentException dex)
            {
                document.Add(PDFManager.GetTextLineElement(dex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
            // Catching System.IO.IOException if any
            catch (Exception ioex)
            {
                document.Add(PDFManager.GetTextLineElement(ioex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }

        }

        public async Task<byte[]> GetTraineeLearningHourFeedbacks(Guid examId, int timeZoneOffset)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            try
            {
                var company = await _context.Configurations.FirstOrDefaultAsync();
                var exam = await _context.EvaluationExams.Where(x => x.Id == examId).Select(x => x.ExamName).FirstOrDefaultAsync();

                var data = await _context.LearningHourFeedbacks.Where(t => t.ExamId == examId)
                    .GroupBy(x => new
                    {
                        x.TraineeId,
                        x.Trainee.PIN,
                        x.Trainee.Name
                    })
                .Select(x => new
                {
                    x.Key.TraineeId,
                    x.Key.PIN,
                    x.Key.Name,
                    Feedbacks = x.Select(y => new
                    {
                        y.Question.Question,
                        y.Answer,
                        y.Question.QuestionGroup,
                        y.Question.QuestionType
                    }).OrderBy(y => y.QuestionGroup).ToList()
                }).ToListAsync();

                var headerColumns = new List<string> { "Trainee PIN", "Trainee Name", "Group", "Question", "Answer" };

                ExcelManager.GetTextLineElement("Trainee Wise Evaluation Test's Feedbacks", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                ExcelManager.GetTextLineElement("Evaluation Test: " + exam, ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                ExcelManager.GetTextLineElement("Report Generation Time: " + Utility.UTCToLocal(DateTime.UtcNow, timeZoneOffset).ToString("dd-MMM-yyyy HH:mm:ss tt"), ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                ExcelManager.GetTextLineElement(company.Name, ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                ExcelManager.GetTextLineElement(company.Address + " , " + company.ContactNo, ++rowNo, ws, fontSize: 8, isBold: false, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                rowNo++;

                ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);

                int counter;

                foreach (var trainee in data)
                {
                    rowNo++;
                    colNo = 1;
                    ExcelManager.GetTableDataCell(trainee.PIN, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                    ExcelManager.GetTableDataCell(trainee.Name, 10, rowNo, colNo++, ws);
                    counter = 0;
                    foreach (var item in trainee.Feedbacks)
                    {
                        colNo = 3;
                        if (counter != 0) rowNo++;
                        ExcelManager.GetTableDataCell(item.QuestionGroup.ToString(), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        ExcelManager.GetTableDataCell(item.Question, 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(!string.IsNullOrEmpty(item.Answer) && item.Answer.Contains("&*") ? item.Answer.Replace("&*", " || ") : item.Answer, 10, rowNo, colNo++, ws);
                        counter++;
                    }
                }

                for (int i = 0; i < headerColumns.Count - 2; i++)
                {
                    ws.Column(i + 1).AdjustToContents();
                }
                ws.Column(4).Width = 50;
                ws.Column(4).Style.Alignment.WrapText = true;
                ws.Column(5).Width = 50;
                ws.Column(5).Style.Alignment.WrapText = true;

                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
            catch (Exception ibex)
            {
                ExcelManager.GetTextLineElement("Error: " + ibex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
        }

        public async Task<byte[]> GetTraineeLearningHourFeedbacksPdf(Guid examId)
        {
            var document = new iTextSharp.text.Document(iTextSharp.text.PageSize.A4, 36, 36, 100, 36);
            document.SetPageSize(iTextSharp.text.PageSize.A4.Rotate());
            var reportStream = new MemoryStream();
            int rowNo = 0, colNo;
            try
            {
                PdfWriter writer = PdfWriter.GetInstance(document, reportStream);
                writer.PageEvent = new PDFWriterEvents(true, true);
                writer.CloseStream = false;

                // Openning the Document
                document.Open();
                var company = await _context.Configurations.FirstOrDefaultAsync();
                var exam = await _context.EvaluationExams.Where(x => x.Id == examId).Select(x => x.ExamName).FirstOrDefaultAsync();

                var data = await _context.LearningHourFeedbacks.Where(t => t.ExamId == examId)
                    .GroupBy(x => new
                    {
                        x.TraineeId,
                        x.Trainee.PIN,
                        x.Trainee.Name
                    })
                .Select(x => new
                {
                    x.Key.TraineeId,
                    x.Key.PIN,
                    x.Key.Name,
                    Feedbacks = x.Select(y => new
                    {
                        y.Question.Question,
                        y.Answer,
                        y.Question.QuestionGroup,
                        y.Question.QuestionType
                    }).OrderBy(y => y.QuestionGroup).ToList()
                }).ToListAsync();


                #region Page Header
                PdfPTable header = new PdfPTable(1);
                header.TotalWidth = 180;
                header.DefaultCell.Border = 0;
                header.AddCell(PDFManager.GetTextLineElement(company.Name, 12f, isBold: true));
                header.AddCell(PDFManager.GetTextLineElement(company.Address + " , " + company.ContactNo, isBold: false, fontSize: 8, leftToRightPadding: new float[] { 4, 2, 4, 2 }));
                header.WriteSelectedRows(0, 3, writer.PageSize.Width - document.LeftMargin - document.RightMargin - 180, writer.PageSize.GetTop(10), writer.DirectContent);
                if (!string.IsNullOrEmpty(company.LogoPath))
                {
                    Image img = Image.GetInstance(HostingEnvironment.MapPath("~") + company.LogoPath);
                    img.SetAbsolutePosition(document.LeftMargin, writer.PageSize.GetTop(70));
                    img.ScaleAbsolute(200f, 60f);
                    document.Add(img);
                }
                PdfPTable line = new PdfPTable(1);
                line.TotalWidth = writer.PageSize.Width;
                line.DefaultCell.Border = 0;
                line.AddCell(new Paragraph(new Chunk(new iTextSharp.text.pdf.draw.LineSeparator(0.0F, 100.0F, BaseColor.BLACK, Element.ALIGN_CENTER, 3))));
                line.WriteSelectedRows(0, 1, 0, writer.PageSize.GetTop(header.TotalHeight + 10), writer.DirectContent);
                #endregion

                document.Add(PDFManager.GetTextLineElement("Trainee Wise Evaluation Test's Feedbacks", fontSize: 16, isBold: true, isUnderlined: false, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Add(PDFManager.GetTextLineElement("Evaluation Test: " + exam, fontSize: 12, isBold: true, isUnderlined: false, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                #region Table
                var table = new PdfPTable(5) { WidthPercentage = 100 };
                table.SetWidths(new float[] { 10, 25, 25, 20, 10 });
                table.AddCell(PDFManager.GetTableHeaderCell("Pin", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Name", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Group", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Question", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Answer", 8f, true, false, PDFAlignment.Center));
                rowNo++;
                int column;
                foreach (var item in data)
                {
                    rowNo++;
                    colNo = 1;
                    table.AddCell(PDFManager.GetTableDataCell(item.PIN, PDFAlignment.Left, false, true, 8));
                    table.AddCell(PDFManager.GetTableDataCell(item.Name, PDFAlignment.Center, false, true, 8));
                    column = 0;
                    foreach (var subItem in item.Feedbacks)
                    {
                        column++;
                        table.AddCell(PDFManager.GetTableDataCell(subItem.QuestionGroup.ToString(), PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(subItem.Question, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(!string.IsNullOrEmpty(subItem.Answer) && subItem.Answer.Contains("&*") ? subItem.Answer.Replace("&*", " || ") : subItem.Answer, PDFAlignment.Center, false, true, 8));
                        if (item.Feedbacks.Count() > column)
                        {
                            table.AddCell(PDFManager.GetTableDataCell("", PDFAlignment.Center, false, true, 8));
                            table.AddCell(PDFManager.GetTableDataCell("", PDFAlignment.Center, false, true, 8));
                        }
                    }

                }
                document.Add(table);
                #endregion

                // Closing the Document
                document.Close();
                return reportStream.ToArray();
            }

            catch (DocumentException dex)
            {
                document.Add(PDFManager.GetTextLineElement(dex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
            // Catching System.IO.IOException if any
            catch (Exception ioex)
            {
                document.Add(PDFManager.GetTextLineElement(ioex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
        }

        public async Task<APIResponse> DeleteFeedbackQuestionById(long id)
        {
            try
            {
                var item = await _context.FeedbackQuestions.FindAsync(id);
                if (item == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Feedback not found"
                };
                _context.Entry(item).State = EntityState.Deleted;
                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Deleted"
                };

            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }


        #region Trainee Panel APIs
        public async Task<APIResponse> CourseFeedbackSaveOrUpdate(CourseFeedbackModel model, ApplicationUser user)
        {
            bool isEdit = true;
            try
            {
                var course = await _context.Courses.FindAsync(model.CourseId);
                if (course == null) throw new Exception("No course found");

                var courseFeedback = await _context.CourseFeedbacks.FirstOrDefaultAsync(x => x.CourseId == model.CourseId && x.TraineeId == user.Trainee.Id);
                if (courseFeedback == null) courseFeedback = new CourseFeedback
                {
                    CourseId = model.CourseId,
                    TraineeId = user.Trainee.Id
                };
                else course.TotalRatings -= courseFeedback.Rating;

                courseFeedback.Rating = model.Rating;
                courseFeedback.Comment = model.Comment;
                if (courseFeedback.Feedbacks == null) courseFeedback.Feedbacks = new List<CourseFeedbackAnswer>();

                CourseFeedbackAnswer feedback = null;
                foreach (var item in model.Feedbacks)
                {
                    feedback = courseFeedback.Feedbacks.FirstOrDefault(x => x.QuestionId == item.QuestionId);
                    if (feedback == null) feedback = new CourseFeedbackAnswer { QuestionId = item.QuestionId };

                    feedback.Answer = item.Answers.Any() ? string.Join("&*", item.Answers) : null;

                    if (feedback.Id == 0) courseFeedback.Feedbacks.Add(feedback);
                }

                courseFeedback.SetAuditTrailEntity(user.User.Identity);
                if (courseFeedback.Id == Guid.Empty)
                {
                    courseFeedback.Id = Guid.NewGuid();
                    _context.CourseFeedbacks.Add(courseFeedback);

                    course.NoOfRating++;
                    isEdit = false;
                }
                else _context.Entry(courseFeedback).State = EntityState.Modified;

                course.TotalRatings += courseFeedback.Rating;
                course.Rating = decimal.Round((course.TotalRatings / course.NoOfRating), 2, MidpointRounding.AwayFromZero);
                _context.Entry(course).State = EntityState.Modified;

                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfull " + (isEdit ? "Updated" : "Saved")
                };

            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())

                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException != null ? ex.InnerException.InnerException?.Message ?? ex.InnerException.Message : ex.Message
                };
            }

        }

        public async Task<APIResponse> LearningHourFeedbackSaveOrUpdate(LearningHourFeedbackModel model, ApplicationUser user)
        {
            try
            {
                var learningHourFeedbacks = await _context.LearningHourFeedbacks.Where(x => x.ExamId == model.ExamId && x.TraineeId == user.Trainee.Id).ToListAsync();

                LearningHourFeedback feedback = null;
                foreach (var item in model.Feedbacks)
                {
                    feedback = learningHourFeedbacks.FirstOrDefault(x => x.QuestionId == item.QuestionId);
                    if (feedback == null) feedback = new LearningHourFeedback
                    {
                        ExamId = model.ExamId,
                        TraineeId = user.Trainee.Id,
                        QuestionId = item.QuestionId
                    };

                    feedback.Answer = item.Answers.Any() ? string.Join("&*", item.Answers) : null;
                    feedback.SetAuditTrailEntity(user.User.Identity);
                    if (feedback.Id == 0) _context.LearningHourFeedbacks.Add(feedback);
                    else _context.Entry(feedback).State = EntityState.Modified;
                }

                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfull " + (learningHourFeedbacks.Any() ? "Updated" : "Saved")
                };

            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())

                };
            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException != null ? ex.InnerException.InnerException?.Message ?? ex.InnerException.Message : ex.Message
                };
            }

        }
        #endregion

    }

    public interface IFeedbackService
    {
        Task<APIResponse> FeedbackQuestionCreateOrUpdate(FeedbackQuestionModel model, EvaluationType type, IIdentity identity);
        Task<APIResponse> GetFeedbackQuestionList(EvaluationType type, QuestionGroup? group, QuestionType? qType, int size, int pageNumber);
        Task<APIResponse> GetFeedbackQuestionById(long id);
        Task<APIResponse> DeleteFeedbackQuestionById(long id);
        Task<byte[]> GetTraineeCourseFeedbacks(Guid courseId, int timeZoneOffset);
        Task<byte[]> GetTraineeCourseFeedbacksPdf(Guid courseId, int timeZoneOffset);
        Task<byte[]> GetTraineeLearningHourFeedbacks(Guid examId, int timeZoneOffset);
        Task<byte[]> GetTraineeLearningHourFeedbacksPdf(Guid examId);

        #region Trainee Panel APIs
        Task<APIResponse> CourseFeedbackSaveOrUpdate(CourseFeedbackModel model, ApplicationUser user);
        Task<APIResponse> LearningHourFeedbackSaveOrUpdate(LearningHourFeedbackModel model, ApplicationUser user);
        #endregion
    }
}
