﻿using Microsoft.Owin;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Brac.LMS.App.API
{
    public class AntiXssMiddleware : OwinMiddleware
    {
        public AntiXssMiddleware(OwinMiddleware next)
            : base(next)
        {
        }

        public override Task Invoke(IOwinContext context)
        {
            Regex regex = new Regex(@"(%3C|<)|(%3E|>)|(%22)");

            if (context.Request.QueryString.Value != string.Empty && regex.Match(context.Request.QueryString.Value).Success || regex.Match(context.Request.Path.Value).Success)
            {
                context.Response.StatusCode = 400;
                context.Response.WriteAsync("A potentially dangerous request query was detected from the client");
            }

            return Next.Invoke(context);
        }

    }
}