﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class CourseMaterial : AuditableEntity
    {
        public Guid CourseId { get; set; }
        public virtual Course Course { get; set; }

        public MaterialType MaterialType { get; set; }


        [Required, StringLength(500)]
        public string Title { get; set; }


        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string FilePath { get; set; }

        public long FileSizeKb { get; set; }

        public int VideoDurationSecond { get; set; }


        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string S3Path { get; set; }


        [Column(TypeName = "VARCHAR"), StringLength(50)]
        public string YoutubeID { get; set; }

        public string ExternalLink { get; set; }
        public int RequiredStudyTimeSec { get; set; }

        public FileType FileType { get; set; }
        public bool CanDownload { get; set; }

        public virtual ICollection<MaterialResource> Resources { get; set; }
    }
}
