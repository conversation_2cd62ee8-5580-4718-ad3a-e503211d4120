﻿using Brac.LMS.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class CourseEnrollment : NumberEntityField
    {
        public Guid TraineeId { get; set; }
        public virtual Trainee Trainee { get; set; }

        public Guid CourseId { get; set; }
        public virtual Course Course { get; set; }

        private DateTime _EnrollmentDate;
        public DateTime EnrollmentDate
        {
            get
            { return _EnrollmentDate; }
            set
            { _EnrollmentDate = value.ToKindUtc(); }
        }


        private DateTime? _ExpireDate;
        public DateTime? ExpireDate
        {
            get
            { return _ExpireDate; }
            set
            { _ExpireDate = value.HasValue ? value.Value.ToKindUtc() : value; }
        }

        public Notification OnNewEnrolmentByAdmin(string courseName)
        {
            return new Notification()
            {
                Id = Guid.NewGuid(),
                CreatedOn = DateTime.UtcNow,
                NotificationType = NotificationType.CourseEnrolmentByAdmin,
                TargetUserType = UserType.Trainee,
                TargetTraineeId = TraineeId,
                Title = "New Course Enrolment by Admin",
                Details = $"The admin has enrolled you in a new course: {courseName}",
                Payload = CourseId.ToString(),
                NavigateTo = Navigation.CourseDetails
            };
        }

        public Notification OnNewEnrolmentByManager(string courseName)
        {
            return new Notification()
            {
                Id = Guid.NewGuid(),
                CreatedOn = DateTime.UtcNow,
                NotificationType = NotificationType.CourseEnrolmentByAdmin,
                TargetUserType = UserType.Trainee,
                TargetTraineeId = TraineeId,
                Title = "New Course Enrolment by Line Manager",
                Details = $"Your line manager has enrolled you in a new course: {courseName}",
                Payload = CourseId.ToString(),
                NavigateTo = Navigation.CourseDetails
            };
        }

        public Notification OnSelfEnrolment(string courseName)
        {
            return new Notification()
            {
                Id = Guid.NewGuid(),
                CreatedOn = DateTime.UtcNow,
                NotificationType = NotificationType.SelfEnrolmentInCourse,
                TargetUserType = UserType.Trainee,
                TargetTraineeId = TraineeId,
                Title = "New Course Enrolment",
                Details = $"You have successfully enrolled in a course: {courseName}",
                Payload = CourseId.ToString(),
                NavigateTo = Navigation.CourseDetails
            };
        }
    }
}
