{"ast": null, "code": "export var ResponseStatus = /*#__PURE__*/(() => {\n  (function (ResponseStatus) {\n    ResponseStatus[ResponseStatus[\"Error\"] = 0] = \"Error\";\n    ResponseStatus[ResponseStatus[\"Success\"] = 1] = \"Success\";\n    ResponseStatus[ResponseStatus[\"Warning\"] = 2] = \"Warning\";\n  })(ResponseStatus || (ResponseStatus = {}));\n\n  return ResponseStatus;\n})();", "map": null, "metadata": {}, "sourceType": "module"}