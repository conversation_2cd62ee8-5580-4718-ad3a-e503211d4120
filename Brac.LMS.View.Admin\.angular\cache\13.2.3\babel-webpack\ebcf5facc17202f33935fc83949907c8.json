{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SamplePageRoutingModule } from './sample-page-routing.module';\nimport { SharedModule } from '../../../theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport let SamplePageModule = /*#__PURE__*/(() => {\n  class SamplePageModule {}\n\n  SamplePageModule.ɵfac = function SamplePageModule_Factory(t) {\n    return new (t || SamplePageModule)();\n  };\n\n  SamplePageModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SamplePageModule\n  });\n  SamplePageModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, SamplePageRoutingModule, SharedModule]]\n  });\n  return SamplePageModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}