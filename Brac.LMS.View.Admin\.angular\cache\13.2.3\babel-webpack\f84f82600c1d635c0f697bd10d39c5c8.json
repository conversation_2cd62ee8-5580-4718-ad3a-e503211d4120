{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nexport let ApexChartService = /*#__PURE__*/(() => {\n  class ApexChartService {\n    constructor() {\n      this.changeTimeRange = new EventEmitter();\n      this.changeSeriesData = new EventEmitter();\n    }\n\n    eventChangeTimeRange() {\n      this.changeTimeRange.emit();\n    }\n\n    eventChangeSeriesData() {\n      this.changeSeriesData.emit();\n    }\n\n  }\n\n  ApexChartService.ɵfac = function ApexChartService_Factory(t) {\n    return new (t || ApexChartService)();\n  };\n\n  ApexChartService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ApexChartService,\n    factory: ApexChartService.ɵfac\n  });\n  return ApexChartService;\n})();", "map": null, "metadata": {}, "sourceType": "module"}