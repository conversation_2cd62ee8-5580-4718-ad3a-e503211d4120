﻿using Brac.LMS.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class CourseBookmark : NumberEntityField
    {
        public Guid TraineeId { get; set; }
        public virtual Trainee Trainee { get; set; }

        public Guid CourseId { get; set; }
        public virtual Course Course { get; set; }

        private DateTime _BookmarkDate;
        public DateTime BookmarkDate
        {
            get
            { return _BookmarkDate; }
            set
            { _BookmarkDate = value.ToKindUtc(); }
        }

        private DateTime? _EnrollmentDate;
        public DateTime? EnrollmentDate
        {
            get
            { return _EnrollmentDate; }
            set
            { _EnrollmentDate = value.HasValue ? value.Value.ToKindUtc() : value; }
        }
    }
}
