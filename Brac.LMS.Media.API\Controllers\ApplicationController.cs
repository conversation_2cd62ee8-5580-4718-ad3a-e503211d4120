﻿using Brac.LMS.Models;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.Owin;
using System;
using System.Linq;
using System.Net.Http;
using System.Web;
using System.Web.Http;
using Brac.LMS.DB;

namespace Brac.LMS.Media.API.Controllers
{
    public class ApplicationController : ApiController
    {
        public ApplicationUser CurrentUser { get { return GetUser(); } }

        private ApplicationUser GetUser()
        {
            if (User.Identity.IsAuthenticated)
            {
                if (!(HttpContext.Current.Items[User.Identity.Name] is ApplicationUser user))
                {
                    try
                    {
                        using (var userManager = Request.GetOwinContext().GetUserManager<ApplicationUserManager>())
                        {
                            user = userManager.FindByName(User.Identity.Name);
                            user.User = User;

                            switch (user.UserType)
                            {
                                case UserType.Trainee:
                                    using (var dbContext = Request.GetOwinContext().Get<ApplicationDbContext>())
                                    {
                                        user.Trainee = dbContext.Trainees.FirstOrDefault(x => x.UserId == user.Id);
                                    }
                                    break;
                            }
                        }
                        HttpContext.Current.Items[User.Identity.Name] = user;
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Error on app identity: " + ex.Message);
                    }
                }
                return user;
            }
            return null;
        }
    }
}
