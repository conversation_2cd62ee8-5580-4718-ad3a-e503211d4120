{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CertificationTestListComponent } from './certification-test-list.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: CertificationTestListComponent\n}];\nexport let CertificationTestListRoutingModule = /*#__PURE__*/(() => {\n  class CertificationTestListRoutingModule {}\n\n  CertificationTestListRoutingModule.ɵfac = function CertificationTestListRoutingModule_Factory(t) {\n    return new (t || CertificationTestListRoutingModule)();\n  };\n\n  CertificationTestListRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CertificationTestListRoutingModule\n  });\n  CertificationTestListRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return CertificationTestListRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}