-- <PERSON><PERSON> Script to update CertificateConfiguration paths to v1 version folders
-- IMPORTANT: Only updates course-specific paths, NOT direct paths
-- Run this script AFTER moving the physical files with PowerShell script

-- Enable SQLCMD mode for better output (optional)
-- :setvar DatabaseName "YourDatabaseName"

PRINT '=========================================='
PRINT 'Certificate Configuration Path Migration'
PRINT 'Moving course-specific paths to v1 folders'
PRINT '=========================================='

-- Create backup table first
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CertificateConfiguration_Backup]') AND type in (N'U'))
BEGIN
    SELECT * INTO CertificateConfiguration_Backup 
    FROM CertificateConfiguration
    PRINT 'Backup table created: CertificateConfiguration_Backup'
END
ELSE
BEGIN
    PRINT 'Backup table already exists: CertificateConfiguration_Backup'
END

-- Show current state before migration
PRINT ''
PRINT 'BEFORE MIGRATION - Current path patterns:'
SELECT 
    'Person1SignPath' as PathType,
    COUNT(*) as Total,
    COUNT(CASE WHEN Person1SignPath LIKE '/Images/CertificateConfiguration/%/%' AND Person1SignPath NOT LIKE '/Images/CertificateConfiguration/%/%/%' THEN 1 END) as CourseSpecific,
    COUNT(CASE WHEN Person1SignPath LIKE '/Images/CertificateConfiguration/%' AND Person1SignPath NOT LIKE '/Images/CertificateConfiguration/%/%' THEN 1 END) as DirectPaths,
    COUNT(CASE WHEN Person1SignPath IS NULL THEN 1 END) as NullPaths
FROM CertificateConfiguration WHERE Person1SignPath IS NOT NULL

UNION ALL

SELECT 
    'Person2SignPath' as PathType,
    COUNT(*) as Total,
    COUNT(CASE WHEN Person2SignPath LIKE '/Images/CertificateConfiguration/%/%' AND Person2SignPath NOT LIKE '/Images/CertificateConfiguration/%/%/%' THEN 1 END) as CourseSpecific,
    COUNT(CASE WHEN Person2SignPath LIKE '/Images/CertificateConfiguration/%' AND Person2SignPath NOT LIKE '/Images/CertificateConfiguration/%/%' THEN 1 END) as DirectPaths,
    COUNT(CASE WHEN Person2SignPath IS NULL THEN 1 END) as NullPaths
FROM CertificateConfiguration WHERE Person2SignPath IS NOT NULL

UNION ALL

SELECT 
    'Person3SignPath' as PathType,
    COUNT(*) as Total,
    COUNT(CASE WHEN Person3SignPath LIKE '/Images/CertificateConfiguration/%/%' AND Person3SignPath NOT LIKE '/Images/CertificateConfiguration/%/%/%' THEN 1 END) as CourseSpecific,
    COUNT(CASE WHEN Person3SignPath LIKE '/Images/CertificateConfiguration/%' AND Person3SignPath NOT LIKE '/Images/CertificateConfiguration/%/%' THEN 1 END) as DirectPaths,
    COUNT(CASE WHEN Person3SignPath IS NULL THEN 1 END) as NullPaths
FROM CertificateConfiguration WHERE Person3SignPath IS NOT NULL

UNION ALL

SELECT 
    'TemplatePath' as PathType,
    COUNT(*) as Total,
    COUNT(CASE WHEN TemplatePath LIKE '/Images/CertificateConfiguration/%/%' AND TemplatePath NOT LIKE '/Images/CertificateConfiguration/%/%/%' THEN 1 END) as CourseSpecific,
    COUNT(CASE WHEN TemplatePath LIKE '/Images/CertificateConfiguration/%' AND TemplatePath NOT LIKE '/Images/CertificateConfiguration/%/%' THEN 1 END) as DirectPaths,
    COUNT(CASE WHEN TemplatePath IS NULL THEN 1 END) as NullPaths
FROM CertificateConfiguration WHERE TemplatePath IS NOT NULL

PRINT ''
PRINT 'Starting path migration...'

-- Update Person1SignPath (only course-specific paths)
UPDATE CertificateConfiguration 
SET Person1SignPath = REPLACE(Person1SignPath, 
    SUBSTRING(Person1SignPath, 1, LEN('/Images/CertificateConfiguration/') + 36), 
    SUBSTRING(Person1SignPath, 1, LEN('/Images/CertificateConfiguration/') + 36) + '/v1'
)
WHERE Person1SignPath IS NOT NULL
  AND Person1SignPath LIKE '/Images/CertificateConfiguration/%/%'  -- Has course folder
  AND Person1SignPath NOT LIKE '/Images/CertificateConfiguration/%/%/%'  -- Not already versioned
  AND LEN(SUBSTRING(Person1SignPath, LEN('/Images/CertificateConfiguration/') + 2, 36)) = 36  -- GUID length check
  AND SUBSTRING(Person1SignPath, LEN('/Images/CertificateConfiguration/') + 2, 36) LIKE '%-%-%-%-%'  -- GUID pattern

PRINT 'Updated Person1SignPath: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' records'

-- Update Person2SignPath (only course-specific paths)
UPDATE CertificateConfiguration 
SET Person2SignPath = REPLACE(Person2SignPath, 
    SUBSTRING(Person2SignPath, 1, LEN('/Images/CertificateConfiguration/') + 36), 
    SUBSTRING(Person2SignPath, 1, LEN('/Images/CertificateConfiguration/') + 36) + '/v1'
)
WHERE Person2SignPath IS NOT NULL
  AND Person2SignPath LIKE '/Images/CertificateConfiguration/%/%'  -- Has course folder
  AND Person2SignPath NOT LIKE '/Images/CertificateConfiguration/%/%/%'  -- Not already versioned
  AND LEN(SUBSTRING(Person2SignPath, LEN('/Images/CertificateConfiguration/') + 2, 36)) = 36  -- GUID length check
  AND SUBSTRING(Person2SignPath, LEN('/Images/CertificateConfiguration/') + 2, 36) LIKE '%-%-%-%-%'  -- GUID pattern

PRINT 'Updated Person2SignPath: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' records'

-- Update Person3SignPath (only course-specific paths)
UPDATE CertificateConfiguration 
SET Person3SignPath = REPLACE(Person3SignPath, 
    SUBSTRING(Person3SignPath, 1, LEN('/Images/CertificateConfiguration/') + 36), 
    SUBSTRING(Person3SignPath, 1, LEN('/Images/CertificateConfiguration/') + 36) + '/v1'
)
WHERE Person3SignPath IS NOT NULL
  AND Person3SignPath LIKE '/Images/CertificateConfiguration/%/%'  -- Has course folder
  AND Person3SignPath NOT LIKE '/Images/CertificateConfiguration/%/%/%'  -- Not already versioned
  AND LEN(SUBSTRING(Person3SignPath, LEN('/Images/CertificateConfiguration/') + 2, 36)) = 36  -- GUID length check
  AND SUBSTRING(Person3SignPath, LEN('/Images/CertificateConfiguration/') + 2, 36) LIKE '%-%-%-%-%'  -- GUID pattern

PRINT 'Updated Person3SignPath: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' records'

-- Update TemplatePath (only course-specific paths)
UPDATE CertificateConfiguration 
SET TemplatePath = REPLACE(TemplatePath, 
    SUBSTRING(TemplatePath, 1, LEN('/Images/CertificateConfiguration/') + 36), 
    SUBSTRING(TemplatePath, 1, LEN('/Images/CertificateConfiguration/') + 36) + '/v1'
)
WHERE TemplatePath IS NOT NULL
  AND TemplatePath LIKE '/Images/CertificateConfiguration/%/%'  -- Has course folder
  AND TemplatePath NOT LIKE '/Images/CertificateConfiguration/%/%/%'  -- Not already versioned
  AND LEN(SUBSTRING(TemplatePath, LEN('/Images/CertificateConfiguration/') + 2, 36)) = 36  -- GUID length check
  AND SUBSTRING(TemplatePath, LEN('/Images/CertificateConfiguration/') + 2, 36) LIKE '%-%-%-%-%'  -- GUID pattern

PRINT 'Updated TemplatePath: ' + CAST(@@ROWCOUNT AS VARCHAR(10)) + ' records'

PRINT ''
PRINT 'AFTER MIGRATION - Updated path patterns:'
-- Show results after migration (same query as before)
SELECT 
    'Person1SignPath' as PathType,
    COUNT(*) as Total,
    COUNT(CASE WHEN Person1SignPath LIKE '/Images/CertificateConfiguration/%/v1/%' THEN 1 END) as V1Paths,
    COUNT(CASE WHEN Person1SignPath LIKE '/Images/CertificateConfiguration/%' AND Person1SignPath NOT LIKE '/Images/CertificateConfiguration/%/%' THEN 1 END) as DirectPaths,
    COUNT(CASE WHEN Person1SignPath IS NULL THEN 1 END) as NullPaths
FROM CertificateConfiguration WHERE Person1SignPath IS NOT NULL

UNION ALL

SELECT 
    'Person2SignPath' as PathType,
    COUNT(*) as Total,
    COUNT(CASE WHEN Person2SignPath LIKE '/Images/CertificateConfiguration/%/v1/%' THEN 1 END) as V1Paths,
    COUNT(CASE WHEN Person2SignPath LIKE '/Images/CertificateConfiguration/%' AND Person2SignPath NOT LIKE '/Images/CertificateConfiguration/%/%' THEN 1 END) as DirectPaths,
    COUNT(CASE WHEN Person2SignPath IS NULL THEN 1 END) as NullPaths
FROM CertificateConfiguration WHERE Person2SignPath IS NOT NULL

UNION ALL

SELECT 
    'Person3SignPath' as PathType,
    COUNT(*) as Total,
    COUNT(CASE WHEN Person3SignPath LIKE '/Images/CertificateConfiguration/%/v1/%' THEN 1 END) as V1Paths,
    COUNT(CASE WHEN Person3SignPath LIKE '/Images/CertificateConfiguration/%' AND Person3SignPath NOT LIKE '/Images/CertificateConfiguration/%/%' THEN 1 END) as DirectPaths,
    COUNT(CASE WHEN Person3SignPath IS NULL THEN 1 END) as NullPaths
FROM CertificateConfiguration WHERE Person3SignPath IS NOT NULL

UNION ALL

SELECT 
    'TemplatePath' as PathType,
    COUNT(*) as Total,
    COUNT(CASE WHEN TemplatePath LIKE '/Images/CertificateConfiguration/%/v1/%' THEN 1 END) as V1Paths,
    COUNT(CASE WHEN TemplatePath LIKE '/Images/CertificateConfiguration/%' AND TemplatePath NOT LIKE '/Images/CertificateConfiguration/%/%' THEN 1 END) as DirectPaths,
    COUNT(CASE WHEN TemplatePath IS NULL THEN 1 END) as NullPaths
FROM CertificateConfiguration WHERE TemplatePath IS NOT NULL

PRINT ''
PRINT '=========================================='
PRINT 'MIGRATION COMPLETED!'
PRINT '=========================================='
PRINT 'IMPORTANT NOTES:'
PRINT '1. Direct paths (like /Images/CertificateConfiguration/TemplatePath.jpg) were NOT changed'
PRINT '2. NULL paths were left as NULL'
PRINT '3. Only course-specific paths were moved to v1 folders'
PRINT '4. Backup table created: CertificateConfiguration_Backup'
PRINT ''
PRINT 'NEXT STEPS:'
PRINT '1. Test certificate generation with existing data'
PRINT '2. Verify images load correctly in admin panel'
PRINT '3. Check that new certificates use latest configuration'
