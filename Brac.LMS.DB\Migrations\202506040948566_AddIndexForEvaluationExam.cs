﻿namespace Brac.LMS.DB.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddIndexForEvaluationExam : DbMigration
    {
        public override void Up()
        {
            // 1. Index for TraineeEvaluationExam lookups by <PERSON>eeId and ExamId
            // Used in: Concurrency checks and exam validation
            Sql("CREATE NONCLUSTERED INDEX IX_TraineeEvaluationExam_TraineeId_ExamId ON TraineeEvaluationExam (TraineeId, ExamId) INCLUDE (Id, Status, GainedMarks, TotalMarks)");

            // 2. Index for TraineeEvaluationExam by Id (primary key should already exist, but ensuring optimal structure)
            // Used in: Main trainee exam record retrieval
            Sql("CREATE NONCLUSTERED INDEX IX_TraineeEvaluationExam_Id_Status ON TraineeEvaluationExam (Id) INCLUDE (Status, TraineeId, ExamId)");

            // 3. Indexes for answer tables by TraineeExamId (foreign key optimization)
            // Used in: Checking existing answers and bulk inserts
            Sql("CREATE NONCLUSTERED INDEX IX_MCQEvaluationAnswer_TraineeExamId ON MCQEvaluationAnswer (TraineeExamId) INCLUDE (QuestionId, Mark)");
            Sql("CREATE NONCLUSTERED INDEX IX_TrueFalseEvaluationAnswer_TraineeExamId ON TrueFalseEvaluationAnswer (TraineeExamId) INCLUDE (QuestionId, Mark)");
            Sql("CREATE NONCLUSTERED INDEX IX_FIGEvaluationAnswer_TraineeExamId ON FIGEvaluationAnswer (TraineeExamId) INCLUDE (QuestionId, Mark)");
            Sql("CREATE NONCLUSTERED INDEX IX_MatchingEvaluationAnswer_TraineeExamId ON MatchingEvaluationAnswer (TraineeExamId) INCLUDE (QuestionId, Mark)");
            Sql("CREATE NONCLUSTERED INDEX IX_WrittenEvaluationAnswer_TraineeExamId ON WrittenEvaluationAnswer (TraineeExamId) INCLUDE (QuestionId, Mark)");

            // 4. Indexes for question tables by Id (should exist as primary key, but optimizing for bulk lookups)
            // Used in: Parallel question data loading
            Sql("CREATE NONCLUSTERED INDEX IX_MCQEvaluationQuestion_Id_ExamId ON MCQEvaluationQuestion (Id) INCLUDE (Answers, Mark, ExamId)");
            Sql("CREATE NONCLUSTERED INDEX IX_TrueFalseEvaluationQuestion_Id_ExamId ON TrueFalseEvaluationQuestion (Id) INCLUDE (Answer, Mark, ExamId)");
            Sql("CREATE NONCLUSTERED INDEX IX_FIGEvaluationQuestion_Id_ExamId ON FIGEvaluationQuestion (Id) INCLUDE (Answer, Mark, ExamId)");
            Sql("CREATE NONCLUSTERED INDEX IX_MatchingEvaluationQuestion_Id_ExamId ON MatchingEvaluationQuestion (Id) INCLUDE (RightSide, Mark, ExamId)");
            Sql("CREATE NONCLUSTERED INDEX IX_WrittenEvaluationQuestion_Id_ExamId ON WrittenEvaluationQuestion (Id) INCLUDE (Mark, ExamId)");

            // 5. Index for EvaluationExam by Id with included columns for exam details
            // Used in: Exam information retrieval
            Sql("CREATE NONCLUSTERED INDEX IX_EvaluationExam_Id_Details ON EvaluationExam (Id) INCLUDE (Marks, MCQOnly, Publish, ExamName)");

            // 6. Index for TraineeEvaluationExamAttempt for attempt tracking
            // Used in: Exam attempt validation
            Sql("CREATE NONCLUSTERED INDEX IX_TraineeEvaluationExamAttempt_TraineeId_ExamId ON TraineeEvaluationExamAttempt (TraineeId, ExamId) INCLUDE (Attempt)");

            // 7. Index for GradingPolicy for grade calculation
            // Used in: Grade assignment for MCQ-only exams
            Sql("CREATE NONCLUSTERED INDEX IX_GradingPolicy_Active_MinValue ON GradingPolicy (Active, MinValue DESC) INCLUDE (Id, GradeLetter, Result, GroupCode)");

            // 8. Index for TraineeDevices for Firebase notifications
            // Used in: Notification token retrieval
            Sql("CREATE NONCLUSTERED INDEX IX_TraineeDevices_TraineeId_CreatedDate ON TraineeDevice (TraineeId, CreatedDate DESC) INCLUDE (Token)");

            // 9. Composite index for exam questions by ExamId (for future optimizations)
            // Used in: Question retrieval by exam
            Sql("CREATE NONCLUSTERED INDEX IX_MCQEvaluationQuestion_ExamId ON MCQEvaluationQuestion (ExamId) INCLUDE (Id, Answers, Mark)");
            Sql("CREATE NONCLUSTERED INDEX IX_TrueFalseEvaluationQuestion_ExamId ON TrueFalseEvaluationQuestion (ExamId) INCLUDE (Id, Answer, Mark)");
            Sql("CREATE NONCLUSTERED INDEX IX_FIGEvaluationQuestion_ExamId ON FIGEvaluationQuestion (ExamId) INCLUDE (Id, Answer, Mark)");
            Sql("CREATE NONCLUSTERED INDEX IX_MatchingEvaluationQuestion_ExamId ON MatchingEvaluationQuestion (ExamId) INCLUDE (Id, RightSide, Mark)");
            Sql("CREATE NONCLUSTERED INDEX IX_WrittenEvaluationQuestion_ExamId ON WrittenEvaluationQuestion (ExamId) INCLUDE (Id, Mark)");
        }
        
        public override void Down()
        {
            // Drop all performance indexes
            Sql("DROP INDEX IF EXISTS IX_TraineeEvaluationExam_TraineeId_ExamId ON TraineeEvaluationExam");
            Sql("DROP INDEX IF EXISTS IX_TraineeEvaluationExam_Id_Status ON TraineeEvaluationExam");

            Sql("DROP INDEX IF EXISTS IX_MCQEvaluationAnswer_TraineeExamId ON MCQEvaluationAnswer");
            Sql("DROP INDEX IF EXISTS IX_TrueFalseEvaluationAnswer_TraineeExamId ON TrueFalseEvaluationAnswer");
            Sql("DROP INDEX IF EXISTS IX_FIGEvaluationAnswer_TraineeExamId ON FIGEvaluationAnswer");
            Sql("DROP INDEX IF EXISTS IX_MatchingEvaluationAnswer_TraineeExamId ON MatchingEvaluationAnswer");
            Sql("DROP INDEX IF EXISTS IX_WrittenEvaluationAnswer_TraineeExamId ON WrittenEvaluationAnswer");

            Sql("DROP INDEX IF EXISTS IX_MCQEvaluationQuestion_Id_ExamId ON MCQEvaluationQuestion");
            Sql("DROP INDEX IF EXISTS IX_TrueFalseEvaluationQuestion_Id_ExamId ON TrueFalseEvaluationQuestion");
            Sql("DROP INDEX IF EXISTS IX_FIGEvaluationQuestion_Id_ExamId ON FIGEvaluationQuestion");
            Sql("DROP INDEX IF EXISTS IX_MatchingEvaluationQuestion_Id_ExamId ON MatchingEvaluationQuestion");
            Sql("DROP INDEX IF EXISTS IX_WrittenEvaluationQuestion_Id_ExamId ON WrittenEvaluationQuestion");

            Sql("DROP INDEX IF EXISTS IX_EvaluationExam_Id_Details ON EvaluationExam");
            Sql("DROP INDEX IF EXISTS IX_TraineeEvaluationExamAttempt_TraineeId_ExamId ON TraineeEvaluationExamAttempt");
            Sql("DROP INDEX IF EXISTS IX_GradingPolicy_Active_MinValue ON GradingPolicy");
            Sql("DROP INDEX IF EXISTS IX_TraineeDevices_TraineeId_CreatedDate ON TraineeDevice");

            Sql("DROP INDEX IF EXISTS IX_MCQEvaluationQuestion_ExamId ON MCQEvaluationQuestion");
            Sql("DROP INDEX IF EXISTS IX_TrueFalseEvaluationQuestion_ExamId ON TrueFalseEvaluationQuestion");
            Sql("DROP INDEX IF EXISTS IX_FIGEvaluationQuestion_ExamId ON FIGEvaluationQuestion");
            Sql("DROP INDEX IF EXISTS IX_MatchingEvaluationQuestion_ExamId ON MatchingEvaluationQuestion");
            Sql("DROP INDEX IF EXISTS IX_WrittenEvaluationQuestion_ExamId ON WrittenEvaluationQuestion");
        }
    }
}
