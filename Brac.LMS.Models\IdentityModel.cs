﻿using Brac.LMS.Common;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Security.Claims;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public enum Gender { Male, Female, Other }
    public enum UserType { Admin, Trainee, Both, Guest }
    public enum LoginType { Regular, External }


    // You can add profile data for the user by adding more properties to your ApplicationUser class, please visit https://go.microsoft.com/fwlink/?LinkID=317594 to learn more.
    public class ApplicationUser : IdentityUser
    {
        [Required, StringLength(250)]
        public string FirstName { get; set; }

        [StringLength(250)]
        public string LastName { get; set; }

        //private DateTime _LastPasswordChanged;
        public DateTime LastPasswordChanged { get; set; }
        //{
        //    get
        //    { return _LastPasswordChanged; }
        //    set
        //    { 
        //        _LastPasswordChanged = string.IsNullOrWhiteSpace(value.ToString()) ? value.ToKindLocal() : DateTime.Now.ToKindLocal(); 
        //    }
        //}

        private DateTime? _LastLogOn;
        public DateTime? LastLogOn {
            get
            { return _LastLogOn; }
            set
            { _LastLogOn = value.HasValue ? value.ToKindLocal() : value; }
        }
        public bool Active { get; set; }

        private DateTime? _DeActivateOn;
        public DateTime? DeActivateOn {
            get
            { return _DeActivateOn; }
            set
            { _DeActivateOn = value.HasValue ? value.ToKindLocal() : value; }
        }

        public Gender? Gender { get; set; }
        public UserType UserType { get; set; }
        public LoginType LoginType { get; set; }
        public long? UserGroupId { get; set; }
        //public Guid? EmployeeId { get; set; }

        public virtual UserGroup UserGroup { get; set; }


        [StringLength(250)]
        public string ImagePath { get; set; }

        [NotMapped]
        public virtual IPrincipal User { get; set; }

        [NotMapped]
        public virtual Trainee Trainee { get; set; }

        [NotMapped]
        public virtual string[] UserRoles { get; set; }

        public virtual ICollection<ForumPostLike> PostLikes { get; set; }



        public override string ToString()
        {
            return FirstName + (!string.IsNullOrEmpty(LastName) ? " " + LastName : "");
        }

        public async Task<ClaimsIdentity> GenerateUserIdentityAsync(UserManager<ApplicationUser, string> manager, string authenticationType)
        {
            // Note the authenticationType must match the one defined in CookieAuthenticationOptions.AuthenticationType
            var userIdentity = await manager.CreateIdentityAsync(this, authenticationType);
            // Add custom user claims here
            return userIdentity;
        }
    }

    public class ApplicationRole : IdentityRole
    {
        public string NormalizeName { get; set; }
        public string Description { get; set; }
        public bool Restricted { get; set; }
        public virtual ICollection<UserGroupRole> UserGroupRoles { get; set; }
    }

    public class UserOtp : EntityField
    {
        [Required, StringLength(128)]
        public string UserId { get; set; }
        public virtual ApplicationUser User { get; set; }

        public string Otp { get; set; }
        public DateTime ExpiredTime { get; set; }
        public DateTime GeneratedTime { get; set; }
    }
}
