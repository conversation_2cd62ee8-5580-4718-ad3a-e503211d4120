{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { DivisionRoutingModule } from './division-routing.module';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport let DivisionModule = /*#__PURE__*/(() => {\n  class DivisionModule {}\n\n  DivisionModule.ɵfac = function DivisionModule_Factory(t) {\n    return new (t || DivisionModule)();\n  };\n\n  DivisionModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: DivisionModule\n  });\n  DivisionModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, DivisionRoutingModule, SharedModule]]\n  });\n  return DivisionModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}