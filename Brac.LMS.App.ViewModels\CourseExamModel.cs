﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.ViewModels
{
    public enum QuesType { MCQ, TrueFalse, FIG, Matching, Written, NoQues }
    public class CourseExamModel
    {
        public Guid? Id { get; set; }
        public Guid CourseId { get; set; }
        public string ExamInstructions { get; set; }
        public int Quota { get; set; }
        //public int Marks { get; set; }
        public int DurationMnt { get; set; }
        public bool MCQOnly { get; set; }
        public bool Random { get; set; }
        public bool Publish { get; set; }
        public int ExamMCQNo { get; set; }
        public int ExamTrueFalseNo { get; set; }
        public int ExamFIGNo { get; set; }
        public int ExamMatchingNo { get; set; }
        public int ExamWritingNo { get; set; }
        public QuesType QuesType { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public long? SegmentId { get; set; }
        public List<MCQQuestionModel> MCQs { get; set; }
        public List<TrueFalseQuestionModel> TruFalseQs { get; set; }
        public List<FIGQuestionModel> FIGQs { get; set; }
        public List<MatchingQuestionModel> MatchingQs { get; set; }
        public List<WrittenQuestionModel> WrittenQs { get; set; }
        public List<SegmentWiseSetupModel> SegmentWiseSetups { get; set; }
    }

    public class MCQQuestionModel
    {
        public long? Id { get; set; }
        public string Question { get; set; }
        public string Option1 { get; set; }
        public string Option2 { get; set; }
        public string Option3 { get; set; }
        public string Option4 { get; set; }
        public string Answers { get; set; }
        public float Mark { get; set; }
        public long SegmentId { get; set; }
    }

    public class TrueFalseQuestionModel
    {
        public long? Id { get; set; }
        public string Question { get; set; }
        public bool Answer { get; set; }
        public float Mark { get; set; }
        public long SegmentId { get; set; }
    }

    public class FIGQuestionModel
    {
        public long? Id { get; set; }
        public string Question { get; set; }
        public string Answer { get; set; }
        public float Mark { get; set; }
        public long SegmentId { get; set; }
    }

    public class MatchingQuestionModel
    {
        public long? Id { get; set; }
        public string LeftSide { get; set; }
        public string RightSide { get; set; }
        public float Mark { get; set; }
        public long SegmentId { get; set; }
    }

    public class WrittenQuestionModel
    {
        public long? Id { get; set; }
        public string Question { get; set; }
        public float Mark { get; set; }
        public long SegmentId { get; set; }
    }

    public class SegmentWiseSetupModel
    {
        public long SegmentId { get; set; }
        public int NoOfMCQ { get; set; }
        public int NoOfTrueFalse { get; set; }
        public int NoOfFIG { get; set; }
        public int NoOfMatching { get; set; }
        public int NoOfWriting { get; set; }
    }
}
