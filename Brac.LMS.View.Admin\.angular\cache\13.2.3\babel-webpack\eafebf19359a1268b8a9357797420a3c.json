{"ast": null, "code": "import { Observable } from '../Observable';\nexport function scheduleArray(input, scheduler) {\n  return new Observable(subscriber => {\n    let i = 0;\n    return scheduler.schedule(function () {\n      if (i === input.length) {\n        subscriber.complete();\n      } else {\n        subscriber.next(input[i++]);\n\n        if (!subscriber.closed) {\n          this.schedule();\n        }\n      }\n    });\n  });\n} //# sourceMappingURL=scheduleArray.js.map", "map": null, "metadata": {}, "sourceType": "module"}