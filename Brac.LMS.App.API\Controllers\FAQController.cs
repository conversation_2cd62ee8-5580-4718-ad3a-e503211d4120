﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [Authorize(Roles = "Admin"), RoutePrefix("api/faq")]
    public class FAQController : ApplicationController
    {
        private readonly IFAQService _service;

        public FAQController()
        {
            _service = new FAQService();
        }

        [HttpPost, Route("create-or-update")]
        public async Task<IHttpActionResult> FAQCreateOrUpdate(FAQModel model)
        {
            var _nservice = new FAQService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.FAQCreateOrUpdate(model, User.Identity));
        }

        [HttpGet, Route("list")]
        public async Task<IHttpActionResult> GetFAQList(int size, int pageNumber)
        {
            return Ok(await _service.GetFAQList(size, pageNumber));
        }
        [HttpGet, Route("course/list")]
        public async Task<IHttpActionResult> GetCourseFAQList(int size, int pageNumber, Guid? courseId)
        {
            return Ok(await _service.GetCourseFAQList(size, pageNumber,courseId));
        }

        [HttpGet, Route("get/{id}")]
        public async Task<IHttpActionResult> GetFAQById(long id)
        {
            return Ok(await _service.GetFAQById(id));
        }

        [HttpPost, Route("delete/{id}")]
        public async Task<IHttpActionResult> DeleteFAQById(long id)
        {
            return Ok(await _service.DeleteFAQById(id));
        }

        [HttpGet, Route("move")]
        public async Task<IHttpActionResult> MoveFAQById(long id, Movement movement)
        {
            return Ok(await _service.MoveFAQById(id, movement));
        }
    }
}
