﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{C913671A-1513-4D41-A3DB-DA7BC9C3292F}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Brac.LMS.App.ViewModels</RootNamespace>
    <AssemblyName>Brac.LMS.App.ViewModels</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AdminAccountModel.cs" />
    <Compile Include="APIResponse.cs" />
    <Compile Include="CertificateConfigurationModel.cs" />
    <Compile Include="ConfigurationModel.cs" />
    <Compile Include="CourseContentModel.cs" />
    <Compile Include="CourseDiscussionModel.cs" />
    <Compile Include="GhooriCertificateModel.cs" />
    <Compile Include="EvaluationExamModel.cs" />
    <Compile Include="CourseExamModel.cs" />
    <Compile Include="CourseContentDependencyModel.cs" />
    <Compile Include="ExamFetchModel.cs" />
    <Compile Include="ExternalCourseModel.cs" />
    <Compile Include="FeedbackQuestionModel.cs" />
    <Compile Include="LibraryModel.cs" />
    <Compile Include="MockTestModel.cs" />
    <Compile Include="OpenMaterialModel.cs" />
    <Compile Include="CourseMaterialModel.cs" />
    <Compile Include="CourseModel.cs" />
    <Compile Include="DepartmentModel.cs" />
    <Compile Include="DivisionModel.cs" />
    <Compile Include="ExamAnserSaveModel.cs" />
    <Compile Include="ExamMarkingModel.cs" />
    <Compile Include="LoginModel.cs" />
    <Compile Include="FAQModel.cs" />
    <Compile Include="ForumTraineeCategoryModel.cs" />
    <Compile Include="ForumCategoryTagModel.cs" />
    <Compile Include="CategoryModel.cs" />
    <Compile Include="ForumPostModel.cs" />
    <Compile Include="ForumTopicModel.cs" />
    <Compile Include="MaterialResourceModel.cs" />
    <Compile Include="NotifyTraineeModel.cs" />
    <Compile Include="PasswordChangeModel.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="QuestionFetchModel.cs" />
    <Compile Include="RatingSaveModel.cs" />
    <Compile Include="SegmentModel.cs" />
    <Compile Include="SubUnitModel.cs" />
    <Compile Include="TraineeDeviceModel.cs" />
    <Compile Include="TraineeCourseModel.cs" />
    <Compile Include="UnitModel.cs" />
    <Compile Include="TraineeModel.cs" />
    <Compile Include="TraineeProfileModel.cs" />
    <Compile Include="UserGroupModel.cs" />
    <Compile Include="UserProfileModel.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Brac.LMS.Models\Brac.LMS.Models.csproj">
      <Project>{EC959511-D275-40BB-8E2C-DC414F4C31D5}</Project>
      <Name>Brac.LMS.Models</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>