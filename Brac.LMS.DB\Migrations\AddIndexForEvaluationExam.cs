﻿namespace Brac.LMS.DB.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddCertificateVersioning : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.CertificateConfiguration", "Version", c => c.Int(nullable: false, defaultValue: 1));

            AddColumn("dbo.CertificateConfiguration", "ChangeReason", c => c.String(maxLength: 250, unicode: false));

            AddColumn("dbo.TraineeCertificate", "CertificateConfigurationId", c => c.<PERSON>());

            CreateIndex("dbo.TraineeCertificate", "CertificateConfigurationId");
            AddForeignKey("dbo.TraineeCertificate", "CertificateConfigurationId", "dbo.CertificateConfiguration", "Id");

            // Update existing TraineeCertificate records to link with current CertificateConfiguration
            Sql(@"
                UPDATE tc
                SET CertificateConfigurationId = cc.Id
                FROM TraineeCertificate tc
                INNER JOIN CertificateConfiguration cc ON tc.CourseId = cc.CourseId
                WHERE tc.CertificateConfigurationId IS NULL
            ");

            // Create composite index for better performance on CourseId + Version queries
            Sql("CREATE INDEX IX_CertificateConfiguration_CourseId_Version ON CertificateConfiguration (CourseId, Version)");
        }

        public override void Down()
        {
            // Drop indexes first
            Sql("DROP INDEX IX_CertificateConfiguration_CourseId_Version ON CertificateConfiguration");
            DropIndex("dbo.TraineeCertificate", new[] { "CertificateConfigurationId" });

            // Drop foreign key
            DropForeignKey("dbo.TraineeCertificate", "CertificateConfigurationId", "dbo.CertificateConfiguration");

            // Drop columns
            DropColumn("dbo.TraineeCertificate", "CertificateConfigurationId");
            DropColumn("dbo.CertificateConfiguration", "ChangeReason");
            DropColumn("dbo.CertificateConfiguration", "Version");
        }
    }
}
