{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Validators } from '@angular/forms';\nimport { BlockUI } from 'ng-block-ui';\nimport { ColumnMode } from '@swimlane/ngx-datatable';\nimport { Page } from '../_models/page';\nimport { debounceTime } from 'rxjs/operators';\nimport { ResponseStatus } from '../_models/enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"ngx-toastr\";\nimport * as i3 from \"../_helpers/confirm-dialog/confirm.service\";\nimport * as i4 from \"../_services/common.service\";\nimport * as i5 from \"ng-block-ui\";\nimport * as i6 from \"@swimlane/ngx-datatable\";\n\nfunction DepartmentComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const rowIndex_r4 = ctx.rowIndex;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.page.pageNumber * ctx_r0.page.size + rowIndex_r4 + 1, \" \");\n  }\n}\n\nfunction DepartmentComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r5 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r5, \" \");\n  }\n}\n\nfunction DepartmentComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r6 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r6 === true ? \"Yes\" : \"No\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r6 === true ? \"Yes\" : \"No\", \" \");\n  }\n}\n\nexport class DepartmentComponent {\n  constructor(formBuilder, toastr, confirmService, _service) {\n    this.formBuilder = formBuilder;\n    this.toastr = toastr;\n    this.confirmService = confirmService;\n    this._service = _service;\n    this.submitted = false;\n    this.formTitle = 'Create Department';\n    this.btnSaveText = 'Save';\n    this.modalLgConfig = {\n      class: 'gray modal-md',\n      backdrop: 'static'\n    };\n    this.page = new Page();\n    this.rows = [];\n    this.loadingIndicator = false;\n    this.ColumnMode = ColumnMode;\n    this.scrollBarHorizontal = false;\n    this.page.pageNumber = 0;\n    this.page.size = 10;\n\n    window.onresize = () => {\n      this.scrollBarHorizontal = window.innerWidth < 1200;\n    };\n  }\n\n  ngOnInit() {\n    this.entryForm = this.formBuilder.group({\n      id: [null],\n      name: [null, [Validators.required, Validators.minLength(1), Validators.maxLength(250)]],\n      active: [true]\n    });\n    this.filterForm = this.formBuilder.group({\n      name: [null]\n    });\n    this.filterForm.get(\"name\").valueChanges.pipe(debounceTime(700)).subscribe(() => {\n      this.getList();\n    });\n    this.getList();\n  }\n\n  get f() {\n    return this.entryForm.controls;\n  }\n\n  setPage(pageInfo) {\n    this.page.pageNumber = pageInfo.offset;\n    this.getList();\n  }\n\n  getList() {\n    this.loadingIndicator = true;\n    const obj = {\n      name: this.filterForm.value.name ? this.filterForm.value.name.trim() : null,\n      size: this.page.size,\n      pageNumber: this.page.pageNumber\n    };\n\n    this._service.get('department/list', obj).subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.rows = res.Data.Records;\n      this.page.totalElements = res.Data.Total;\n      this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n      setTimeout(() => {\n        this.loadingIndicator = false;\n      }, 1000);\n    }, err => {\n      this.toastr.warning(err.Messaage || err, 'Warning!', {\n        closeButton: true,\n        disableTimeOut: false\n      });\n      setTimeout(() => {\n        this.loadingIndicator = false;\n      }, 1000);\n    });\n  }\n\n  getItem(id) {\n    this.blockUI.start('Getting data...');\n\n    this._service.get('department/get/' + id).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.formTitle = 'Update Department';\n      this.btnSaveText = 'Update';\n      this.entryForm.controls['id'].setValue(res.Data.Record.Id);\n      this.entryForm.controls['name'].setValue(res.Data.Record.Name);\n      this.entryForm.controls['active'].setValue(res.Data.Record.Active);\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.warning(err.Message || err, 'Warning!', {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  onFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid) {\n      return;\n    }\n\n    this.blockUI.start('Saving...');\n    const obj = {\n      Id: this.entryForm.value.id,\n      Name: this.entryForm.value.name.trim(),\n      Active: this.entryForm.value.active\n    };\n\n    const request = this._service.post('department/create-or-update', obj);\n\n    request.subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, 'Success!', {\n        timeOut: 2000\n      });\n      this.getList();\n      this.clearForm();\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.warning(err.Message || err, 'Warning!', {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  clearForm() {\n    this.entryForm.reset();\n    this.submitted = false;\n    this.formTitle = 'Create Department';\n    this.btnSaveText = 'Save';\n    this.entryForm.controls['active'].setValue(true);\n  }\n\n  deleteItem(id) {\n    this.confirmService.confirm('Are you sure?', 'You are deleting this Department.').subscribe(result => {\n      if (result) {\n        this.blockUI.start('Deleting data...');\n\n        this._service.get('Department/delete/' + id).subscribe(res => {\n          this.blockUI.stop();\n\n          if (res.Status === ResponseStatus.Warning) {\n            this.toastr.warning(res.Message, 'Warning!', {\n              closeButton: true,\n              disableTimeOut: false,\n              enableHtml: true\n            });\n            return;\n          } else if (res.Status === ResponseStatus.Error) {\n            this.toastr.error(res.Message, 'Error!', {\n              closeButton: true,\n              disableTimeOut: false,\n              enableHtml: true\n            });\n            return;\n          }\n\n          this.toastr.success(res.Message, 'SUCCESS!');\n          this.getList();\n        });\n      }\n    });\n  }\n\n}\n\nDepartmentComponent.ɵfac = function DepartmentComponent_Factory(t) {\n  return new (t || DepartmentComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ToastrService), i0.ɵɵdirectiveInject(i3.ConfirmService), i0.ɵɵdirectiveInject(i4.CommonService));\n};\n\nDepartmentComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: DepartmentComponent,\n  selectors: [[\"app-department\"]],\n  decls: 23,\n  vars: 18,\n  consts: [[1, \"row\"], [1, \"col-12\"], [1, \"card\", \"card-border-default\"], [1, \"card-header\"], [1, \"card-block\"], [\"autocomplete\", \"off\", 1, \"row\", 3, \"formGroup\"], [1, \"col-lg-8\", \"col-md-10\", \"col-12\"], [1, \"mb-3\", \"row\"], [\"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Search By name\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-lg-12\"], [\"rowHeight\", \"auto\", 1, \"material\", \"table-bordered\", 3, \"rows\", \"loadingIndicator\", \"externalPaging\", \"columnMode\", \"headerHeight\", \"footerHeight\", \"count\", \"offset\", \"limit\", \"page\"], [\"name\", \"SL#\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [\"ngx-datatable-cell-template\", \"\"], [\"prop\", \"Name\", 3, \"draggable\", \"sortable\"], [\"prop\", \"Active\", \"headerClass\", \"text-center\", \"cellClass\", \"text-center\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [3, \"title\"]],\n  template: function DepartmentComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"h5\");\n      i0.ɵɵtext(6, \"Department List \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"div\", 4);\n      i0.ɵɵelementStart(8, \"div\", 0);\n      i0.ɵɵelementStart(9, \"div\", 1);\n      i0.ɵɵelementStart(10, \"form\", 5);\n      i0.ɵɵelementStart(11, \"div\", 6);\n      i0.ɵɵelementStart(12, \"div\", 7);\n      i0.ɵɵelementStart(13, \"div\", 1);\n      i0.ɵɵelement(14, \"input\", 8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"div\", 9);\n      i0.ɵɵelementStart(16, \"ngx-datatable\", 10);\n      i0.ɵɵlistener(\"page\", function DepartmentComponent_Template_ngx_datatable_page_16_listener($event) {\n        return ctx.setPage($event);\n      });\n      i0.ɵɵelementStart(17, \"ngx-datatable-column\", 11);\n      i0.ɵɵtemplate(18, DepartmentComponent_ng_template_18_Template, 2, 1, \"ng-template\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"ngx-datatable-column\", 13);\n      i0.ɵɵtemplate(20, DepartmentComponent_ng_template_20_Template, 2, 2, \"ng-template\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(21, \"ngx-datatable-column\", 14);\n      i0.ɵɵtemplate(22, DepartmentComponent_ng_template_22_Template, 2, 2, \"ng-template\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"formGroup\", ctx.filterForm);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"rows\", ctx.rows)(\"loadingIndicator\", ctx.loadingIndicator)(\"externalPaging\", true)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"count\", ctx.page.totalElements)(\"offset\", ctx.page.pageNumber)(\"limit\", ctx.page.size);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"maxWidth\", 50)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 90)(\"draggable\", false)(\"sortable\", false);\n    }\n  },\n  directives: [i5.BlockUIComponent, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.DefaultValueAccessor, i1.NgControlStatus, i1.FormControlName, i6.DatatableComponent, i6.DataTableColumnDirective, i6.DataTableColumnCellDirective],\n  styles: [\"\"]\n});\n\n__decorate([BlockUI()], DepartmentComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}