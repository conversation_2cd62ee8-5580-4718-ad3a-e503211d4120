﻿
using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.Services
{
    public class DivisionService : IDivisionService
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditLogHelper _auditLogHelper;
        private readonly AuditLog audit;
        private readonly ApplicationUser _appUser;
        public DivisionService()
        {
            _context = new ApplicationDbContext();
        }
        public DivisionService(string controller, string method, ApplicationUser applicationUser)
        {
            _auditLogHelper = new AuditLogHelper(controller, method);
            audit = _auditLogHelper.auditLog;
            _appUser = applicationUser;
            audit.CreatedBy = _appUser.Id;
            _context = new ApplicationDbContext();
        }
        public async Task<APIResponse> DivisionCreateOrUpdate(DivisionModel model, IIdentity identity)
        {
            bool isEdit = true;
            try
            {
                if (await _context.Divisions.AnyAsync(x => x.Id != model.Id && x.Name == model.Name))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "Already exists: " + model.Name
                    };


                Division item;
                if (model.Id.HasValue && model.Id.Value > 0)
                {
                    item = await _context.Divisions.FindAsync(model.Id);
                    if (item == null)
                        return new APIResponse
                        {
                            Status = ResponseStatus.Warning,
                            Message = "Division not found"
                        };
                }
                else
                {
                    item = new Division();
                    isEdit = false;
                }

                item.Name = model.Name;
                item.Active = model.Active;
                item.CreatedDate = DateTime.UtcNow;

                if (!isEdit)
                {
                    _context.Divisions.Add(item);
                }
                else
                {
                    _context.Entry(item).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved"),
                    Data = item.Id
                };

            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())

                };

            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetDivisionList(string name, int size, int pageNumber)
        {
            try
            {
                var query = _context.Divisions.AsQueryable();
                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Name.Contains(name));
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderBy(x => x.CreatedDate)
               .Skip(pageNumber * size).Take(size);
                var data = await filteredQuery.Select(x => new { x.Id, x.Name, x.Active }).ToListAsync();

                var count = await ((!string.IsNullOrEmpty(name)) ? filteredQuery.CountAsync() : _context.Divisions.CountAsync());
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Records = data,
                        Total = count
                    }
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetDivisionById(long id)
        {
            try
            {
                var data = await _context.Divisions.Where(t => t.Id == id)
                .Select(t => new
                {
                    t.Id,
                    t.Name,
                    t.Active
                }).FirstOrDefaultAsync();
                if (data == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Division not found"
                };

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetDivisionDropDownList()
        {
            try
            {
                var data = await _context.Divisions.Where(x => x.Active).OrderBy(o => o.Name)
                .Select(t => new
                {
                    t.Id,
                    t.Name
                }).ToListAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> DeleteDivisionById(long id)
        {
            try
            {
                var item = await _context.Divisions.FindAsync(id);
                if (item == null) return new APIResponse
                {
                    Status = ResponseStatus.Warning,
                    Message = "Division not found"
                };

                if (await _context.Trainees.AnyAsync(x => x.DivisionId == item.Id))
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "This designation has already been tagged with any trainee. So, you can't delete this designation"
                    };

                _context.Entry(item).State = EntityState.Deleted;

                await _context.SaveChangesAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Deleted"
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }
    }

    public interface IDivisionService
    {
        Task<APIResponse> DivisionCreateOrUpdate(DivisionModel model, IIdentity identity);
        Task<APIResponse> GetDivisionList(string name, int size, int pageNumber);
        Task<APIResponse> GetDivisionById(long id);
        Task<APIResponse> GetDivisionDropDownList();
        Task<APIResponse> DeleteDivisionById(long id);
    }
}
