﻿using Dapper;
using FFMpegSharp.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.IO;
using System.Security.AccessControl;
using System.Security.Principal;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Brac.LMS.VideoTranscoder
{
    public partial class Form1 : Form
    {
        private List<VideoFile> videoFiles;
        private List<int> resolutions = new List<int> { 240, 360, 480, 720 };
        private bool _isRunning = false;
        private string _logPath = "";
        private string _basePath = "";

        public VideoFile VideoFile { get; set; }
        public int Index { get; set; }
        int progressPercent = 0;

        public Form1()
        {
            InitializeComponent();
        }

        private void btnStart_Click(object sender, EventArgs e)
        {
            IDbConnection dbCon = null;
            try
            {
                //var builder = new ConfigurationBuilder().AddJsonFile("appsettings.json");

                //Configuration = builder.Build();

                if (ConfigurationManager.AppSettings["logPath"] != null && !string.IsNullOrEmpty(ConfigurationManager.AppSettings["logPath"]))
                {
                    _logPath = ConfigurationManager.AppSettings["logPath"] + "/";
                    if (!Directory.Exists(_logPath))
                    {
                        Directory.CreateDirectory(_logPath);
                    }
                }

                if (ConfigurationManager.AppSettings["basePath"] != null && !string.IsNullOrEmpty(ConfigurationManager.AppSettings["basePath"]))
                {
                    _basePath = ConfigurationManager.AppSettings["basePath"];
                    if (!Directory.Exists(_basePath))
                    {
                        Directory.CreateDirectory(_basePath);
                    }
                }

                if (string.IsNullOrEmpty(ConfigurationManager.AppSettings["appUser"]))
                {
                    WriteLog("No credential provided for DATA MANAGER");
                    MessageBox.Show("No credential provided for DATA MANAGER");
                    Environment.Exit(0);
                }
                else
                {
                    if (ConfigurationManager.AppSettings["appUser"].ToLower() != Environment.UserName.ToLower())
                    {
                        WriteLog("Logged in user has no permission to run this app. Please log in from another user and then run. Looged User: " + Environment.UserName);
                        MessageBox.Show("Logged in user has no permission to run this app. Please log in from another user and then run");
                        Environment.Exit(0);
                    }
                    else
                    {
                        if (ConfigurationManager.AppSettings["interval"] != null && !string.IsNullOrEmpty(ConfigurationManager.AppSettings["interval"]))
                        {
                            int interval = Convert.ToInt32(ConfigurationManager.AppSettings["interval"]);
                            timer1.Interval = interval * 60 * 1000;
                        }
                        else
                        {
                            timer1.Interval = 10 * 60 * 1000;
                        }
                        timer1.Tick += timer1_Tick;
                        timer1.Start();
                        _isRunning = true;
                        btnStart.Text = "STOP";

                        dbCon = new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
                        videoFiles = new List<VideoFile>();
                        var rows = (List<dynamic>)dbCon.Query<dynamic>("SELECT Id, FilePath FROM CourseMaterial WHERE MaterialType=1 AND S3Path IS NULL");
                        foreach (var row in rows)
                        {
                            if (File.Exists(_basePath + row.FilePath))
                                videoFiles.Add(new VideoFile
                                {
                                    Id = row.Id,
                                    FilePath = Path.GetFullPath(Path.Combine(_basePath + row.FilePath))
                                });
                        }
                        if (videoFiles.Count > 0) TranscodeFile();
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog(ex.Message);
            }
            finally
            {
                dbCon?.Close();
            }
        }

        private void TranscodeFile()
        {
            int frameRate = 24;
            try
            {
                VideoFile = videoFiles[Index];
                if (!IsProcessRunning("ffmpeg"))
                {
                    string _rootPath = Environment.CurrentDirectory;
                    //string ffmpegOutput = VideoFile.FilePath.Replace(Path.GetFileName(VideoFile.FilePath), ""); 

                    string fileOutput = "/Files/Trannscoded/Videos/" + toUnderscore(Path.GetFileNameWithoutExtension(VideoFile.FilePath));

                    VideoFile.S3Path = fileOutput + "/" + "index.m3u8";
                    fileOutput += "\\";

                    if (!Directory.Exists(_basePath + fileOutput))
                    {
                        SetFolderPermission(_basePath + fileOutput);
                        DirectoryInfo di = Directory.CreateDirectory(_basePath + fileOutput);
                    }

                    //Create index as master playlist
                    string path = Path.Combine(_basePath + fileOutput + "index.m3u8");
                    File.Create(path).Dispose();
                    File.WriteAllLines(path, GenerateIndexLines(resolutions));

                    //Command
                    string conversionArgs = string.Format(GenerateConversionArgs(resolutions, frameRate, _basePath + fileOutput), VideoFile.FilePath, _basePath + fileOutput);

                    //Process
                    WriteLog("Start Transcoding: Video=> " + Path.GetFileName(_basePath + VideoFile.FilePath));
                    FFMpeg encoder = new FFMpeg();
                    encoder.OnProgress += Encoder_OnProgress;
                    encoder.OnConversionEnd += Encoder_OnConversionEnd;
                    Task.Run(() => encoder.ToTS(VideoFile.FilePath, conversionArgs));
                }
                else
                {
                    // Retry
                    WriteLog("Process already running. Retrying..");
                    RetryTranscoding();
                }
            }
            catch (Exception ex)
            {
                WriteLog("Transcode failed: Video=> " + Path.GetFileName(_basePath + VideoFile.FilePath) + " Error: " + ex.Message);
                progressBar.Value = 0;
            }
        }

        private async void RetryTranscoding()
        {
            bool isProcessing = IsProcessRunning("ffmpeg");
            if (isProcessing)
            {
                KillProcess("ffmpeg");
            }
            await Task.Delay(2000);
            TranscodeFile();
        }

        private void Encoder_OnProgress(int percentage)
        {
            Invoke(new Action(() =>
            {
                progressPercent = percentage;
                progressBar.Value = percentage;
            }));
        }

        private void Encoder_OnConversionEnd(bool success)
        {
            Invoke(new Action(() =>
            {
                if (success && progressBar.Value == 100)
                {
                    // complete
                    //this.OnTranscodingFinished?.Invoke(this, 100, FilePath, "Completed");

                    WriteLog("Transcoding Done: Video=> " + Path.GetFileName(VideoFile.FilePath));
                    progressBar.Value = 0;
                }
                else if (success)
                {
                    //canceled
                    //this.OnTranscodingFinished?.Invoke(this, progressPercent, FilePath, canceledByUser ? "" : "Cancelled");

                    WriteLog("Transcoding cancelled");
                    progressBar.Value = 0;

                }
                else
                {
                    //failed
                    //OnTranscodingFinished?.Invoke(this, progressPercent, FilePath, "Failed");


                    WriteLog("Transcoding Failed: Video=> " + Path.GetFileName(VideoFile.FilePath));
                    progressBar.Value = 0;
                }
                OnUploadComplete(VideoFile);
                if (Index < videoFiles.Count - 1)
                {
                    Index++;
                    TranscodeFile();
                }
                else
                    _isRunning = false;
            }));
        }

        private void OnUploadComplete(VideoFile videoFile)
        {
            IDbConnection dbCon = null;
            string fileName = Path.GetFileName(VideoFile.FilePath);
            try
            {
                dbCon = new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
                var executedRows = dbCon.Execute("UPDATE CourseMaterial SET S3Path=@url WHERE Id=@id", new { url = videoFile.S3Path, id = videoFile.Id });

                if (executedRows == 0) throw new Exception("S3Path Update Failed");

                //if (Directory.Exists(videoFile.FilePath)) Directory.Delete(videoFile.FilePath, true);

                WriteLog("Upload Completed: Video=> " + fileName);
            }
            catch (Exception ex)
            {
                WriteLog("Upload Failed: Video => " + fileName + "Error: " + ex.Message);
            }
            finally
            {
                dbCon?.Close();
            }
        }

        string[] GenerateIndexLines(List<int> resolutions)
        {
            List<string> argsLine = new List<string>();
            argsLine.Add("#EXTM3U");
            argsLine.Add("#EXT-X-VERSION:3");

            foreach (int resolution in resolutions)
            {
                if (resolution == 144)
                {
                    argsLine.Add("#EXT-X-STREAM-INF:BANDWIDTH=97280,RESOLUTION=256×144");
                    argsLine.Add("144p.m3u8");
                }
                else if (resolution == 240)
                {
                    argsLine.Add("#EXT-X-STREAM-INF:BANDWIDTH=153600,RESOLUTION=426x240");
                    argsLine.Add("240p.m3u8");
                }
                else if (resolution == 360)
                {
                    argsLine.Add("#EXT-X-STREAM-INF:BANDWIDTH=282624,RESOLUTION=640x360");
                    argsLine.Add("360p.m3u8");
                }
                else if (resolution == 480)
                {
                    argsLine.Add("#EXT-X-STREAM-INF:BANDWIDTH=768000,RESOLUTION=842x480");
                    argsLine.Add("480p.m3u8");
                }
                else if (resolution == 720)
                {
                    argsLine.Add("#EXT-X-STREAM-INF:BANDWIDTH=2097152,RESOLUTION=1280x720");
                    argsLine.Add("720p.m3u8");
                }
            }
            return argsLine.ToArray();
        }

        string GenerateConversionArgs(List<int> resolutions, int framerate, string fileOutput)
        {
            string args = "-hide_banner -y";

            foreach (int resolution in resolutions)
            {
                if (resolution == 144)
                {
                    args = args + " -i \"{0}\" -c:a aac -c:v h264 -profile:v baseline -level 3.0 -s 256×144 -start_number 0 -g 48 -hls_time 2 -hls_list_size 0 -hls_playlist_type vod -crf " + framerate + " -hls_segment_filename \"" + fileOutput + "144p_%d.ts\" \"" + fileOutput + "144p.m3u8\"";
                }
                else if (resolution == 240)
                {
                    args = args + " -i \"{0}\" -c:a aac -c:v h264 -profile:v baseline -level 3.0 -s 426x240 -start_number 0 -g 48 -hls_time 2 -hls_list_size 0 -hls_playlist_type vod -crf " + framerate + " -hls_segment_filename \"" + fileOutput + "240p_%d.ts\" \"" + fileOutput + "240p.m3u8\"";
                }
                else if (resolution == 360)
                {
                    args = args + " -i \"{0}\" -c:a aac -c:v h264 -profile:v baseline -level 3.0 -s 640x360 -start_number 0 -g 48 -hls_time 2 -hls_list_size 0 -hls_playlist_type vod -crf " + framerate + " -hls_segment_filename \"" + fileOutput + "360p_%d.ts\" \"" + fileOutput + "360p.m3u8\"";
                }
                else if (resolution == 480)
                {
                    args = args + " -i \"{0}\" -c:a aac -c:v h264 -profile:v baseline -level 3.0 -s 842x480 -start_number 0 -g 48 -hls_time 2 -hls_list_size 0 -hls_playlist_type vod -crf " + framerate + " -hls_segment_filename \"" + fileOutput + "480p_%d.ts\" \"" + fileOutput + "480p.m3u8\"";
                }
                else if (resolution == 720)
                {
                    args = args + " -i \"{0}\" -c:a aac -c:v h264 -profile:v baseline -level 3.0 -s 1280x720 -start_number 0 -g 48 -hls_time 2 -hls_list_size 0 -hls_playlist_type vod -crf " + framerate + " -hls_segment_filename \"" + fileOutput + "720p_%d.ts\" \"" + fileOutput + "720p.m3u8\"";
                }
            }
            return args;
        }

        void SetFolderPermission(string folderPath)
        {
            try
            {
                var directoryInfo = new DirectoryInfo(folderPath);
                var directorySecurity = directoryInfo.GetAccessControl();
                var currentUserIdentity = WindowsIdentity.GetCurrent();
                var fileSystemRule = new FileSystemAccessRule(currentUserIdentity.Name, FileSystemRights.Read, InheritanceFlags.ObjectInherit | InheritanceFlags.ContainerInherit, PropagationFlags.None, AccessControlType.Allow);
                directorySecurity.AddAccessRule(fileSystemRule);
                directoryInfo.SetAccessControl(directorySecurity);
            }
            catch (Exception ex)
            {
                ex.ToString();
            }
        }

        string toUnderscore(string stringReplace)
        {
            string _stringReplace = stringReplace.Replace(" ", "_");
            return _stringReplace;
        }

        bool IsProcessRunning(string sProcessName)
        {
            bool isProcessing = false;
            try
            {
                Process[] proc = Process.GetProcessesByName(sProcessName);
                if (proc.Length > 0)
                    isProcessing = true;
                else
                    isProcessing = false;
            }
            catch (Exception ex)
            {
                ex.ToString();
            }

            return isProcessing;
        }

        bool KillProcess(string sProcessName)
        {
            bool isProcessing = false;
            try
            {
                Process[] proc = Process.GetProcessesByName(sProcessName);
                if (proc.Length > 0)
                {
                    foreach (var process in proc)
                    {
                        process.Kill();
                    }
                    isProcessing = true;
                }
            }
            catch (Exception ex)
            {
                ex.ToString();
            }

            return isProcessing;
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            if (_isRunning) return;
            IDbConnection dbCon = null;
            try
            {
                dbCon = new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
                videoFiles = new List<VideoFile>();
                var rows = (List<dynamic>)dbCon.Query<dynamic>("SELECT Id, FilePath FROM CourseMaterial WHERE MaterialType=1 AND S3Path IS NULL");
                foreach (var row in rows)
                {
                    if (File.Exists(_basePath + row.FilePath))
                        videoFiles.Add(new VideoFile
                        {
                            Id = row.Id,
                            FilePath = Path.GetFullPath(Path.Combine(_basePath + row.FilePath))
                        });
                }
                if (videoFiles.Count > 0) TranscodeFile();
            }
            catch (Exception ex)
            {
                WriteLog(ex.Message);
            }
            finally
            {
                dbCon?.Close();
            }
        }

        private void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                bool isProcessing = IsProcessRunning("ffmpeg");
                if (isProcessing)
                {
                    KillProcess("ffmpeg");
                    string fileOutput = VideoFile.FilePath.Replace(Path.GetFileName(VideoFile.FilePath), "") + toUnderscore(Path.GetFileNameWithoutExtension(VideoFile.FilePath));
                    if (Directory.Exists(fileOutput)) Directory.Delete(fileOutput, true);
                }
                timer1.Stop();
                _isRunning = false;
                btnStart.Text = "START";
            }
            catch (Exception ex)
            {
                WriteLog("Form Closing Error: " + ex.Message);
            }
        }

        public void WriteLog(string msg)
        {
            System.IO.StreamWriter file = null;
            try
            {
                if (msg == null) return;
                if (txtStatus == null) return;

                SetText("\n" + DateTime.Now.ToString("dd MMM yy hh:mm:ss tt") + "\t" + msg);
                file = new System.IO.StreamWriter(_logPath + DateTime.Now.ToString("ddMMMyyyy") + ".txt", true);
                file.WriteLine("\n" + DateTime.Now.ToString("dd MMM yy hh:mm:ss tt") + "\t" + msg);
                file.Flush();

            }
            catch (Exception)
            {
                // ignored
            }
            finally
            {
                if (file != null)
                {
                    try
                    {
                        file.Close();
                        file.Dispose();
                    }
                    catch { }

                }
            }
        }

        delegate void SetTextCallback(string text);
        private void SetText(string text)
        {
            //InvokeRequired required compares the thread ID of the
            // calling thread to the thread ID of the creating thread.
            // If these threads are different, it returns true.
            if (this.txtStatus.InvokeRequired)
            {
                //SetTextCallback d = new SetTextCallback(SetText);
                //this.Invoke(d, new object[] { text });
                this.txtStatus.Invoke((Action)(() => SetText(text)));
                return;
            }
            else
            {
                this.txtStatus.AppendText(text);
            }
        }
    }
}
