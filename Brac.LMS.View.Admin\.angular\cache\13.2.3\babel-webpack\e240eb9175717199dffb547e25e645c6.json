{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SamplePageComponent } from './sample-page.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: SamplePageComponent\n}];\nexport let SamplePageRoutingModule = /*#__PURE__*/(() => {\n  class SamplePageRoutingModule {}\n\n  SamplePageRoutingModule.ɵfac = function SamplePageRoutingModule_Factory(t) {\n    return new (t || SamplePageRoutingModule)();\n  };\n\n  SamplePageRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SamplePageRoutingModule\n  });\n  SamplePageRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return SamplePageRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}