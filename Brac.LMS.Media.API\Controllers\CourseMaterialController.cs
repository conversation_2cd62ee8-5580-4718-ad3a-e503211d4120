﻿using Brac.LMS.Media.Services;
using Brac.LMS.Media.ViewModels;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace Brac.LMS.Media.API.Controllers
{
    [Authorize, RoutePrefix("api/course")]
    public class CourseMaterialController : ApplicationController
    {
        private readonly ICourseMaterialService _service;

        public CourseMaterialController()
        {
            _service = new CourseMaterialService();
        }


        [HttpPost, Route("material/create-or-update")]
        public async Task<IHttpActionResult> MaterialCreateOrUpdate()
        {
            try
            {
                var model = JsonConvert.DeserializeObject<CourseMaterialModel>(System.Web.HttpContext.Current.Request.Form["Model"]);
                return Ok(await _service.MaterialCreateOrUpdate(model, User.Identity));
            }
            catch (Exception ex)
            {
                return Ok(new { Success = false, ex.Message });
            }
        }

        [HttpGet, Route("material/get-video")]
        public async Task<IHttpActionResult> GetRandomVideo()
        {
            return Ok(await _service.GetRandomVideo());
        }

        [HttpGet, Route("download-document-file")]
        public HttpResponseMessage DownloadDocumentFile(string partialPath)
        {
            var fullPath = System.Web.Hosting.HostingEnvironment.MapPath("~") + partialPath;
            if (!File.Exists(fullPath))
            {
                return Request.CreateResponse(HttpStatusCode.NotFound);
            }
            else
            {
                HttpResponseMessage result = Request.CreateResponse(HttpStatusCode.OK);
                result.Content = new StreamContent(File.OpenRead(fullPath));
                result.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/pdf");
                return result;
            }
        }
    }
}
