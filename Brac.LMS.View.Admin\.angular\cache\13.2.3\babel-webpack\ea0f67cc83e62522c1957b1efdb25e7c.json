{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { BasicCardsRoutingModule } from './basic-cards-routing.module';\nimport { SharedModule } from '../../../../theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport let BasicCardsModule = /*#__PURE__*/(() => {\n  class BasicCardsModule {}\n\n  BasicCardsModule.ɵfac = function BasicCardsModule_Factory(t) {\n    return new (t || BasicCardsModule)();\n  };\n\n  BasicCardsModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: BasicCardsModule\n  });\n  BasicCardsModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, BasicCardsRoutingModule, SharedModule]]\n  });\n  return BasicCardsModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}