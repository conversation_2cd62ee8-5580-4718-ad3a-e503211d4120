﻿using Brac.LMS.Common;
using Google.Apis.Auth.OAuth2;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.Services
{
    public class FirebaseMessagingClient : IFirebaseMessagingClient
    {
        private readonly string _serverkey = "AAAA5Cnh22E:APA91bHoxWU-vlDS51lsXoZyCn94EenWdvCD1ZPK7wakukU0-st3pUiSsHfY1Z5Lq0bIsB-2tlVewmvcmQPFu-6zWk94vBrWe2jG9wR21oEQ5Y9ZF2AbuhSDen5W-h8CYDbtIoMRyNu4";
        private readonly string _senderId = "979955211105";

        private readonly string _firebaseProjectId = "bbl-elearning";
        //private readonly string _firebaseProjectId = "test-push-notification-s-418ac";
        private readonly string _serviceJsonPath = "C:\\ALO_SecureConfig\\bbl-elearning-firebase-adminsdk-fzp8c-c4d3765e0d.json";
        //private readonly string _serviceJsonPath = "C:\\SecureConfigs\\test-push-notification-s-418ac-firebase-adminsdk-fbsvc-6b40d27641.json";

        public FirebaseMessagingClient()
        {
            if (System.Configuration.ConfigurationManager.AppSettings["FCMServerKey"] == null) throw new Exception("FCMServerKey not configured");
            _serverkey = System.Configuration.ConfigurationManager.AppSettings["FCMServerKey"].ToString();
            if (System.Configuration.ConfigurationManager.AppSettings["FCMSenderID"] == null) throw new Exception("FCMSenderID not configured");
            _senderId = System.Configuration.ConfigurationManager.AppSettings["FCMSenderID"].ToString();
        }

        public async Task SendNotifications(string[] tokens, string title, object body)
        {
            WebRequest tRequest = WebRequest.Create("https://fcm.googleapis.com/fcm/send");
            tRequest.Method = "post";
            tRequest.ContentType = "application/json";
            var objNotification = new
            {
                registration_ids = tokens,
                priority = "high",
                data = new
                {
                    title = title,
                    body = body
                }
            };
            string jsonNotificationFormat = Newtonsoft.Json.JsonConvert.SerializeObject(objNotification);

            byte[] byteArray = Encoding.UTF8.GetBytes(jsonNotificationFormat);
            tRequest.Headers.Add(string.Format("Authorization: key={0}", _serverkey));
            tRequest.Headers.Add(string.Format("Sender: id={0}", _senderId));
            tRequest.ContentLength = byteArray.Length;
            tRequest.ContentType = "application/json";
            ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
            using (Stream dataStream = await tRequest.GetRequestStreamAsync())
            {
                await dataStream.WriteAsync(byteArray, 0, byteArray.Length);

                using (WebResponse tResponse = await tRequest.GetResponseAsync())
                {
                    using (Stream dataStreamResponse = tResponse.GetResponseStream())
                    {
                        using (StreamReader tReader = new StreamReader(dataStreamResponse))
                        {
                            string responseFromFirebaseServer = await tReader.ReadToEndAsync();

                            FCMResponse response = Newtonsoft.Json.JsonConvert.DeserializeObject<FCMResponse>(responseFromFirebaseServer);
                            if (response.failure == 1)
                            {
                                LogControl.Write($"#ERROR => Push Notification : Error sent from FCM server, after sending request : {responseFromFirebaseServer} , for following device info: {jsonNotificationFormat}");
                            }

                        }
                    }

                }
            }
        }

        // previous FCM method
        //public async Task SendNotifications(string[] tokens, string title, string body, object data)
        //{
        //    WebRequest tRequest = WebRequest.Create("https://fcm.googleapis.com/fcm/send");
        //    tRequest.Method = "post";
        //    tRequest.ContentType = "application/json";
        //    var objNotification = new
        //    {
        //        registration_ids = tokens,
        //        data,
        //        //data = new
        //        //{
        //        //    navigateTo = "profile",
        //        //    payload = "userId"
        //        //},
        //        notification = new
        //        {
        //            title,
        //            body
        //        },
        //        content_available = true,
        //        priority = "high"
        //    };
        //    string jsonNotificationFormat = Newtonsoft.Json.JsonConvert.SerializeObject(objNotification);

        //    byte[] byteArray = Encoding.UTF8.GetBytes(jsonNotificationFormat);
        //    tRequest.Headers.Add(string.Format("Authorization: key={0}", _serverkey));
        //    tRequest.Headers.Add(string.Format("Sender: id={0}", _senderId));
        //    tRequest.Headers.Add("apns-priority", "10");
        //    tRequest.Headers.Add("apns-push-type", "background"); // for iOS 13 required

        //    tRequest.ContentLength = byteArray.Length;
        //    tRequest.ContentType = "application/json";
        //    ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
        //    using (Stream dataStream = await tRequest.GetRequestStreamAsync())
        //    {
        //        dataStream.Write(byteArray, 0, byteArray.Length);

        //        using (WebResponse tResponse = await tRequest.GetResponseAsync())
        //        {
        //            using (Stream dataStreamResponse = tResponse.GetResponseStream())
        //            {
        //                using (StreamReader tReader = new StreamReader(dataStreamResponse))
        //                {
        //                    string responseFromFirebaseServer = await tReader.ReadToEndAsync();

        //                    FCMResponse response = Newtonsoft.Json.JsonConvert.DeserializeObject<FCMResponse>(responseFromFirebaseServer);
        //                    if (response.failure == 1)
        //                    {
        //                        LogControl.Write($"#ERROR => Push Notification : Error sent from FCM server, after sending request : {responseFromFirebaseServer} , for following device info: {jsonNotificationFormat}");
        //                    }

        //                }
        //            }

        //        }
        //    }
        //}

        public async Task SendNotifications(string[] tokens, string title, string body, object data)
        {
            try
            {
                string url = $"https://fcm.googleapis.com/v1/projects/{_firebaseProjectId}/messages:send";

                var payload = new
                {
                    message = new
                    {
                        token = tokens[0],  // FCM v1 API sends notifications one token at a time. so picked latest token
                        notification = new
                        {
                            title,
                            body
                        },
                        data
                    }
                };

                string jsonPayload = JsonConvert.SerializeObject(payload);

                string accessToken = await GetAccessTokenAsync();

                using (HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                    var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                    HttpResponseMessage response = await client.PostAsync(url, content);

                    string responseString = await response.Content.ReadAsStringAsync();

                    if (!response.IsSuccessStatusCode)
                    {
                        throw new Exception($"#ERROR => Push Notification : Error sent from FCM server, after sending request : {responseString} , for following device info: {tokens[0]}");
                    }
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        private async Task<string> GetAccessTokenAsync()
        {
            GoogleCredential credential;
            string token = string.Empty;
            try
            {
                // Try getting credentials from GOOGLE_APPLICATION_CREDENTIALS environment variable
                credential = await GoogleCredential.GetApplicationDefaultAsync();
                token = await credential.UnderlyingCredential.GetAccessTokenForRequestAsync();
                LogControl.Write("Credentials loaded from environment variable.");
            }
            catch (Exception ex)
            {
                LogControl.Write($"Failed to get credentials from environment variable: {ex.Message}");

                // Fall back to loading from a hardcoded file path
                if (!File.Exists(_serviceJsonPath))
                {
                    throw new FileNotFoundException("Service account JSON file not found at fallback path.", _serviceJsonPath);
                }

                using (var stream = new FileStream(_serviceJsonPath, FileMode.Open, FileAccess.Read))
                {
                    credential = GoogleCredential.FromStream(stream)
                        .CreateScoped("https://www.googleapis.com/auth/firebase.messaging");
                    token = await credential.UnderlyingCredential.GetAccessTokenForRequestAsync();
                    LogControl.Write("Credentials loaded from service account file.");
                }
            }
            return token;

            //GoogleCredential credential = await GoogleCredential.GetApplicationDefaultAsync(); // from env variable GOOGLE_APPLICATION_CREDENTIALS
            //LogControl.Write($"cred found: {JsonConvert.SerializeObject(credential)}");
            //credential = credential.CreateScoped("https://www.googleapis.com/auth/firebase.messaging");

            //using (var stream = new FileStream(_serviceJsonPath, FileMode.Open, FileAccess.Read))
            //{
            //    GoogleCredential credential1 = GoogleCredential.FromStream(stream)
            //        .CreateScoped("https://www.googleapis.com/auth/firebase.messaging");
            //}

            //    var token = await credential.UnderlyingCredential.GetAccessTokenForRequestAsync();
            //return token;
        }

        public async Task SendNotification(string token, string title, object body)
        {
            WebRequest tRequest = WebRequest.Create("https://fcm.googleapis.com/fcm/send");
            tRequest.Method = "post";
            tRequest.ContentType = "application/json";
            var objNotification = new
            {
                to = token,
                priority = "high",
                data = new
                {
                    title = title,
                    body = body
                }
            };
            string jsonNotificationFormat = Newtonsoft.Json.JsonConvert.SerializeObject(objNotification);

            byte[] byteArray = Encoding.UTF8.GetBytes(jsonNotificationFormat);
            tRequest.Headers.Add(string.Format("Authorization: key={0}", _serverkey));
            tRequest.Headers.Add(string.Format("Sender: id={0}", _senderId));
            tRequest.ContentLength = byteArray.Length;
            tRequest.ContentType = "application/json";
            ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
            using (Stream dataStream = await tRequest.GetRequestStreamAsync())
            {
                dataStream.Write(byteArray, 0, byteArray.Length);

                using (WebResponse tResponse = await tRequest.GetResponseAsync())
                {
                    using (Stream dataStreamResponse = tResponse.GetResponseStream())
                    {
                        using (StreamReader tReader = new StreamReader(dataStreamResponse))
                        {
                            string responseFromFirebaseServer = await tReader.ReadToEndAsync();

                            FCMResponse response = Newtonsoft.Json.JsonConvert.DeserializeObject<FCMResponse>(responseFromFirebaseServer);
                            //if (response.success == 1)
                            //{
                            //    new NotificationBLL().InsertNotificationLog(dayNumber, notification, true);
                            //}
                            //else if (response.failure == 1)
                            if (response.failure == 1)
                            {
                                LogControl.Write($"#ERROR => Push Notification : Error sent from FCM server, after sending request : {responseFromFirebaseServer} , for following device info: {jsonNotificationFormat}");
                            }

                        }
                    }

                }
            }
        }


    }

    public interface IFirebaseMessagingClient
    {
        Task SendNotification(string token, string title, object body);
        Task SendNotifications(string[] tokens, string title, string body, object data);
        Task SendNotifications(string[] tokens, string title, object body);
    }

    public class FCMResponse
    {
        public long multicast_id { get; set; }
        public int success { get; set; }
        public int failure { get; set; }
        public int canonical_ids { get; set; }
        public List<FCMResult> results { get; set; }
    }
    public class FCMResult
    {
        public string message_id { get; set; }
    }
}
