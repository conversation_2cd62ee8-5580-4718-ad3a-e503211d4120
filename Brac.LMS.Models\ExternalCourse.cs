﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class ExternalCourse : AuditableEntity
    {
        [Required, Column(TypeName = "VARCHAR"), StringLength(15)]
        public string Code { get; set; }


        [Required, StringLength(500)]
        public string Title { get; set; }

        public string Description { get; set; }


        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string ImagePath { get; set; }

        [StringLength(350)]
        public string ExternalUrl { get; set; }

        public bool Active { get; set; }

        public long? CategoryId { get; set; }
        public virtual CourseCategory Category { get; set; }
    }
}
