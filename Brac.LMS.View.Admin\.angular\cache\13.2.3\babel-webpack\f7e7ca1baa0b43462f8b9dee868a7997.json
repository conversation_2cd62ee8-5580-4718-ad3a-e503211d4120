{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ColumnMode } from \"@swimlane/ngx-datatable\";\nimport { Validators } from \"@angular/forms\";\nimport { BlockUI } from \"ng-block-ui\";\nimport { environment } from \"../../environments/environment\";\nimport { ResponseStatus } from \"../_models/enum\";\nimport { Editor } from \"ngx-editor\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../_services/common.service\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"ngx-bootstrap/modal\";\nimport * as i5 from \"ng-block-ui\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/flex-layout/extended\";\nimport * as i8 from \"ngx-bootstrap/datepicker\";\nimport * as i9 from \"ngx-editor\";\nconst _c0 = [\"pdfViewerOnDemand\"];\n\nfunction SystemConfigurationComponent_div_17_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \" Institute name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_div_17_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \" Institute name must be within 250 charecters\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, SystemConfigurationComponent_div_17_span_1_Template, 2, 0, \"span\", 48);\n    i0.ɵɵtemplate(2, SystemConfigurationComponent_div_17_span_2_Template, 2, 0, \"span\", 48);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.name.errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.name.errors.maxLength);\n  }\n}\n\nfunction SystemConfigurationComponent_div_23_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \" Institute email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_div_23_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \"Email must be a valid email address\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, SystemConfigurationComponent_div_23_span_1_Template, 2, 0, \"span\", 48);\n    i0.ɵɵtemplate(2, SystemConfigurationComponent_div_23_span_2_Template, 2, 0, \"span\", 48);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.email.errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.email.errors.email);\n  }\n}\n\nfunction SystemConfigurationComponent_div_29_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \" Institute phone is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, SystemConfigurationComponent_div_29_span_1_Template, 2, 0, \"span\", 48);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f.email.errors.required);\n  }\n}\n\nfunction SystemConfigurationComponent_div_36_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \" Institute address is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_div_36_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \" Institute address must be within 500 charecters\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, SystemConfigurationComponent_div_36_span_1_Template, 2, 0, \"span\", 48);\n    i0.ɵɵtemplate(2, SystemConfigurationComponent_div_36_span_2_Template, 2, 0, \"span\", 48);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.f.address.errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.f.address.errors.maxLength);\n  }\n}\n\nfunction SystemConfigurationComponent_div_43_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \" Last ERP Sync Date can't be unselected\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, SystemConfigurationComponent_div_43_span_1_Template, 2, 0, \"span\", 48);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.f.lastERPSyncDate.errors.required);\n  }\n}\n\nfunction SystemConfigurationComponent_button_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function SystemConfigurationComponent_button_50_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return ctx_r28.openPdf(\"document\");\n    });\n    i0.ɵɵtext(1, \"Download\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_button_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function SystemConfigurationComponent_button_57_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return ctx_r30.openPdf(\"info\");\n    });\n    i0.ɵɵtext(1, \"Download\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_div_64_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \" Android App Link is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, SystemConfigurationComponent_div_64_span_1_Template, 2, 0, \"span\", 48);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.f.andoridAppLink.errors.required);\n  }\n}\n\nfunction SystemConfigurationComponent_div_70_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \" iOS App Link is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, SystemConfigurationComponent_div_70_span_1_Template, 2, 0, \"span\", 48);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.f.iOSAppLink.errors.required);\n  }\n}\n\nfunction SystemConfigurationComponent_img_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 51);\n  }\n}\n\nfunction SystemConfigurationComponent_img_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 52);\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r12.logoURL, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction SystemConfigurationComponent_div_85_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \" Logo is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, SystemConfigurationComponent_div_85_span_1_Template, 2, 0, \"span\", 48);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.f.logo.errors.required);\n  }\n}\n\nfunction SystemConfigurationComponent_div_108_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \" MCQ mark is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_div_108_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, SystemConfigurationComponent_div_108_span_1_Template, 2, 0, \"span\", 48);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.f.mcqMark.errors.required);\n  }\n}\n\nfunction SystemConfigurationComponent_div_113_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \" FIG mark is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_div_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, SystemConfigurationComponent_div_113_span_1_Template, 2, 0, \"span\", 48);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.f.figMark.errors.required);\n  }\n}\n\nfunction SystemConfigurationComponent_div_118_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \" True False Mark is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_div_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, SystemConfigurationComponent_div_118_span_1_Template, 2, 0, \"span\", 48);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.f.trueFalseMark.errors.required);\n  }\n}\n\nfunction SystemConfigurationComponent_div_123_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \" Matching Mark is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_div_123_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, SystemConfigurationComponent_div_123_span_1_Template, 2, 0, \"span\", 48);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.f.matchingMark.errors.required);\n  }\n}\n\nfunction SystemConfigurationComponent_div_128_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1, \" Written Mark is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction SystemConfigurationComponent_div_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, SystemConfigurationComponent_div_128_span_1_Template, 2, 0, \"span\", 48);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.f.writtenMark.errors.required);\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nexport class SystemConfigurationComponent {\n  constructor(formBuilder, _service, toastr, modalService) {\n    this.formBuilder = formBuilder;\n    this._service = _service;\n    this.toastr = toastr;\n    this.modalService = modalService;\n    this.submitted = false;\n    this.btnSaveText = \"Save\";\n    this.logoFile = null;\n    this.signatureFile = null;\n    this.formTitle = \"Attachement\";\n    this.ColumnMode = ColumnMode;\n    this.toolbar = [[\"bold\", \"italic\"], [\"underline\", \"strike\"], [\"ordered_list\", \"bullet_list\"], [{\n      heading: [\"h1\", \"h2\", \"h3\", \"h4\", \"h5\", \"h6\"]\n    }], [\"text_color\", \"background_color\"], [\"align_left\", \"align_center\", \"align_right\", \"align_justify\"]];\n    this.modules = {\n      toolbar: [['bold', 'italic', 'underline', 'strike'], [{\n        'header': 1\n      }, {\n        'header': 2\n      }], [{\n        'list': 'ordered'\n      }, {\n        'list': 'bullet'\n      }], [{\n        'indent': '-1'\n      }, {\n        'indent': '+1'\n      }], [{\n        'header': [1, 2, 3, 4, 5, 6, false]\n      }], [{\n        'color': []\n      }, {\n        'background': []\n      }], [{\n        'align': []\n      }] // remove formatting button\n      // link and image, video\n      ]\n    };\n    this.baseUrl = environment.baseUrl;\n  }\n\n  ngOnInit() {\n    this.editor = new Editor();\n    this.bsConfig = Object.assign({}, {\n      containerClass: 'theme-blue',\n      minDate: new Date('05-Jun-2022'),\n      dateInputFormat: 'DD-MMM-YYYY'\n    });\n    this.bsConfig.maxDate = new Date();\n    this.bsConfig.maxDate.setDate(this.bsConfig.maxDate.getDate() - 1);\n    this.entryForm = this.formBuilder.group({\n      name: [null, [Validators.required, Validators.maxLength(250)]],\n      email: [null, [Validators.required, Validators.email]],\n      address: [null, [Validators.maxLength(500)]],\n      website: [null],\n      phone: [null, [Validators.required]],\n      logo: [null, [Validators.required]],\n      instruction: ['', [Validators.maxLength(2000)]],\n      mcqMark: [null, [Validators.required]],\n      trueFalseMark: [null, [Validators.required]],\n      figMark: [null, [Validators.required]],\n      matchingMark: [null, [Validators.required]],\n      writtenMark: [null, [Validators.required]],\n      andoridAppLink: [null, [Validators.required]],\n      iOSAppLink: [null, [Validators.required]],\n      lastERPSyncDate: [null, [Validators.required]],\n      documentFile: [null],\n      infoFile: [null]\n    });\n    this.getAttachments();\n    this.getItem();\n  }\n\n  get f() {\n    return this.entryForm.controls;\n  }\n\n  getItem() {\n    this.blockUI.start(\"Getting data...\");\n\n    this._service.get(\"configuration/get\").subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, \"Warning!\", {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, \"Error!\", {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        if (res.Data) {\n          this.btnSaveText = \"Update\";\n          this.entryForm.controls[\"name\"].setValue(res.Data.Name);\n          this.entryForm.controls[\"address\"].setValue(res.Data.Address);\n          this.entryForm.controls[\"phone\"].setValue(res.Data.ContactNo);\n          this.entryForm.controls[\"email\"].setValue(res.Data.InstituteEmail);\n          this.entryForm.controls[\"andoridAppLink\"].setValue(res.Data.AndoridAppLink);\n          this.entryForm.controls[\"iOSAppLink\"].setValue(res.Data.iOSAppLink);\n          this.entryForm.controls[\"website\"].setValue(res.Data.InstituteWebsite);\n          this.entryForm.controls[\"instruction\"].setValue(res.Data.ExamInstruction);\n          this.entryForm.controls[\"mcqMark\"].setValue(res.Data.MCQMark);\n          this.entryForm.controls[\"figMark\"].setValue(res.Data.FIGMark);\n          this.entryForm.controls[\"trueFalseMark\"].setValue(res.Data.TrueFalseMark);\n          this.entryForm.controls[\"matchingMark\"].setValue(res.Data.MatchingMark);\n          this.entryForm.controls[\"writtenMark\"].setValue(res.Data.WrittenMark);\n          this.entryForm.controls[\"logo\"].setValue(this.baseUrl + res.Data.LogoPath);\n          this.logoURL = this.baseUrl + res.Data.LogoPath;\n          if (res.Data.LastERPSyncDate) this.entryForm.controls[\"lastERPSyncDate\"].setValue(new Date(res.Data.LastERPSyncDate));\n        }\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.error(err.message || err, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  preview(files) {\n    if (files.length === 0) {\n      this.logoURL = null;\n      this.entryForm.controls[\"logo\"].setValue(null);\n      return;\n    }\n\n    var mimeType = files[0].type;\n\n    if (mimeType.match(/image\\/*/) == null) {\n      this.message = \"Only images are supported.\";\n      return;\n    }\n\n    var reader = new FileReader();\n    this.logoFile = files[0];\n    reader.readAsDataURL(files[0]);\n\n    reader.onload = _event => {\n      this.logoURL = reader.result;\n      this.entryForm.controls[\"logo\"].setValue(reader.result);\n    };\n  }\n\n  loadAttachment(files) {\n    if (files.length === 0) return;\n    this.documentFile = files[0];\n  }\n\n  loadInfo(files) {\n    if (files.length === 0) return;\n    this.infoFile = files[0];\n  }\n\n  onFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid) {\n      return;\n    }\n\n    this.blockUI.start(\"Saving data. Please wait...\");\n    const obj = {\n      Name: this.entryForm.value.name.trim(),\n      Address: this.entryForm.value.address ? this.entryForm.value.address.trim() : null,\n      ContactNo: this.entryForm.value.phone.trim(),\n      InstituteEmail: this.entryForm.value.email.trim(),\n      AndoridAppLink: this.entryForm.value.andoridAppLink.trim(),\n      iOSAppLink: this.entryForm.value.iOSAppLink.trim(),\n      InstituteWebsite: this.entryForm.value.website ? this.entryForm.value.website.trim() : null,\n      ExamInstruction: this.entryForm.value.instruction,\n      MCQMark: this.entryForm.value.mcqMark,\n      FIGMark: this.entryForm.value.figMark,\n      TrueFalseMark: this.entryForm.value.trueFalseMark,\n      MatchingMark: this.entryForm.value.matchingMark,\n      WrittenMark: this.entryForm.value.writtenMark,\n      LastERPSyncDate: this.entryForm.value.lastERPSyncDate\n    };\n    const configurationFormdata = new FormData();\n    configurationFormdata.append(\"Model\", JSON.stringify(obj));\n    configurationFormdata.append(\"Logo\", this.logoFile);\n    if (this.documentFile != null) configurationFormdata.append(\"Documentation\", this.documentFile);\n    if (this.infoFile != null) configurationFormdata.append(\"Info\", this.infoFile);\n\n    this._service.post(\"configuration/create-or-update\", configurationFormdata).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, \"Warning!\", {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, \"Error!\", {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.toastr.success(res.Message, \"Success!\", {\n          timeOut: 2000\n        });\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.error(err.message || err, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  openPdf(type) {\n    this._service.downloadFile('configuration/download-document-file?type=' + type).subscribe({\n      next: res => {\n        const url = window.URL.createObjectURL(res);\n        var link = document.createElement('a');\n        link.href = url;\n        link.download = \"AttachmentForLoginPanel.\" + 'pdf';\n        link.click();\n      },\n      error: err => {\n        this.toastr.warning(err.message || err, 'Warning!');\n        this.blockUI.stop();\n      },\n      complete: () => this.blockUI.stop()\n    }); // this.ngxSmartModalService.create('docModal', this.tpl).open();\n    // this._service\n    //   .getPDFFile(\n    //     this.mediaBaseUrl +\n    //       '/api/configuration/download-document-file?type='+type\n    //   ).subscribe((res) => {\n    //     this.docObj = res;\n    //     console.log('res res',res)\n    //     this.pdfSrc = res;\n    //   });\n\n  }\n\n  getAttachments() {\n    this.blockUI.start(\"Getting data...\");\n\n    this._service.get(\"configuration/doc-or-info-file-available\").subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.attachment = res.Data;\n      console.log(' this.attachment', res);\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.warning(err.Message || err, \"Warning!\", {\n        closeButton: true,\n        disableTimeOut: false\n      });\n    });\n  }\n\n  modalHide() {\n    this.modalRef.hide();\n  }\n\n}\n\nSystemConfigurationComponent.ɵfac = function SystemConfigurationComponent_Factory(t) {\n  return new (t || SystemConfigurationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.CommonService), i0.ɵɵdirectiveInject(i3.ToastrService), i0.ɵɵdirectiveInject(i4.BsModalService));\n};\n\nSystemConfigurationComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: SystemConfigurationComponent,\n  selectors: [[\"app-configuration\"]],\n  viewQuery: function SystemConfigurationComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.pdfViewerOnDemand = _t.first);\n    }\n  },\n  decls: 133,\n  vars: 56,\n  consts: [[1, \"col-12\"], [\"autocomplete\", \"off\", 3, \"formGroup\"], [1, \"row\"], [1, \"col-lg-12\"], [1, \"card\", \"table-1-card\", \"mb-0\"], [1, \"card-header\"], [1, \"card-block\"], [1, \"col-lg-6\"], [1, \"mb-3\", \"row\"], [1, \"col-sm-4\", \"col-form-label\", \"col-form-label-sm\", \"required\"], [1, \"col-sm-8\"], [\"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Enter Institute name\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"class\", \"error-text\", 4, \"ngIf\"], [\"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter Institute email\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"phone\", \"placeholder\", \"Enter Institute phone number\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"formControlName\", \"address\", \"placeholder\", \"Enter Institute address\", \"rows\", \"3\", 1, \"form-control\", \"form-control-sm\"], [1, \"input-group\"], [\"type\", \"text\", \"bsDatepicker\", \"\", \"formControlName\", \"lastERPSyncDate\", \"readonly\", \"\", \"placeholder\", \"Select a date \", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"bsConfig\"], [1, \"input-group\", \"my-3\"], [1, \"col-sm-4\", \"col-form-label\", \"col-form-label-sm\"], [\"type\", \"file\", \"formControlName\", \"documentFile\", \"accept\", \".doc,.docx,.pdf,.xls,.xlsx, .ppt, .pptx\", 1, \"form-control\", \"form-control-sm\", 3, \"change\"], [\"document\", \"\"], [\"class\", \"btn btn-link text-decoration-none\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"formControlName\", \"infoFile\", \"accept\", \".doc,.docx,.pdf,.xls,.xlsx, .ppt, .pptx\", 1, \"form-control\", \"form-control-sm\", 3, \"change\"], [\"info\", \"\"], [\"type\", \"text\", \"formControlName\", \"andoridAppLink\", \"placeholder\", \"Enter andorid app link\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"iOSAppLink\", \"placeholder\", \"Enter iOS app link\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"website\", \"placeholder\", \"Enter Institute website url\", 1, \"form-control\", \"form-control-sm\"], [\"class\", \"img-fluid admin-max-width-325\", \"src\", \"https://via.placeholder.com/325x65?text=No+Image\", \"height\", \"65\", 4, \"ngIf\"], [\"class\", \"img-fluid admin-max-width-325\", \"height\", \"65\", 3, \"src\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \"image/png,image/jpeg\", \"id\", \"inputGroupFile01\", 1, \"form-control\", \"form-control-sm\", 3, \"change\"], [\"logo\", \"\"], [1, \"col-lg-12\", \"mt-2\"], [1, \"card\", \"table-1-card\"], [1, \"NgxEditor__Wrapper\"], [3, \"editor\", \"toolbar\"], [\"formControlName\", \"instruction\", 3, \"editor\"], [1, \"col-lg-3\", \"col-md-4\"], [1, \"col-form-label\", \"col-form-label-sm\", \"required\"], [\"type\", \"text\", \"formControlName\", \"mcqMark\", \"number\", \"\", \"numericType\", \"decimal\", \"placeholder\", \"Enter mcq mark\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"figMark\", \"placeholder\", \"Enter fig mark\", \"number\", \"\", \"numericType\", \"decimal\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"trueFalseMark\", \"number\", \"\", \"numericType\", \"decimal\", \"placeholder\", \"Enter true false mark\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"matchingMark\", \"number\", \"\", \"numericType\", \"decimal\", \"placeholder\", \"Enter matching mark\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"writtenMark\", \"number\", \"\", \"numericType\", \"decimal\", \"placeholder\", \"Enter written mark\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [1, \"col-lg-12\", \"mt-2\", \"text-center\"], [1, \"btn\", \"btn-theme\", \"wid-150\", 3, \"click\"], [1, \"feather\", \"icon-save\"], [1, \"error-text\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"], [1, \"btn\", \"btn-link\", \"text-decoration-none\", 3, \"click\"], [\"src\", \"https://via.placeholder.com/325x65?text=No+Image\", \"height\", \"65\", 1, \"img-fluid\", \"admin-max-width-325\"], [\"height\", \"65\", 1, \"img-fluid\", \"admin-max-width-325\", 3, \"src\"]],\n  template: function SystemConfigurationComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r40 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"form\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"div\", 4);\n      i0.ɵɵelementStart(6, \"div\", 5);\n      i0.ɵɵelementStart(7, \"h5\");\n      i0.ɵɵtext(8, \"General information\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(9, \"div\", 6);\n      i0.ɵɵelementStart(10, \"div\", 2);\n      i0.ɵɵelementStart(11, \"div\", 7);\n      i0.ɵɵelementStart(12, \"div\", 8);\n      i0.ɵɵelementStart(13, \"label\", 9);\n      i0.ɵɵtext(14, \"Institute Info \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"div\", 10);\n      i0.ɵɵelement(16, \"input\", 11);\n      i0.ɵɵtemplate(17, SystemConfigurationComponent_div_17_Template, 3, 2, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"div\", 8);\n      i0.ɵɵelementStart(19, \"label\", 9);\n      i0.ɵɵtext(20, \"Institute Email \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(21, \"div\", 10);\n      i0.ɵɵelement(22, \"input\", 13);\n      i0.ɵɵtemplate(23, SystemConfigurationComponent_div_23_Template, 3, 2, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"div\", 8);\n      i0.ɵɵelementStart(25, \"label\", 9);\n      i0.ɵɵtext(26, \"Contact No \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(27, \"div\", 10);\n      i0.ɵɵelement(28, \"input\", 14);\n      i0.ɵɵtemplate(29, SystemConfigurationComponent_div_29_Template, 2, 1, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(30, \"div\", 8);\n      i0.ɵɵelementStart(31, \"label\", 9);\n      i0.ɵɵtext(32, \"Address \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(33, \"div\", 10);\n      i0.ɵɵelementStart(34, \"textarea\", 15);\n      i0.ɵɵtext(35, \"                      \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(36, SystemConfigurationComponent_div_36_Template, 3, 2, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(37, \"div\", 8);\n      i0.ɵɵelementStart(38, \"label\", 9);\n      i0.ɵɵtext(39, \" Last ERP Sync Date \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(40, \"div\", 10);\n      i0.ɵɵelementStart(41, \"div\", 16);\n      i0.ɵɵelement(42, \"input\", 17);\n      i0.ɵɵtemplate(43, SystemConfigurationComponent_div_43_Template, 2, 1, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(44, \"div\", 18);\n      i0.ɵɵelementStart(45, \"label\", 19);\n      i0.ɵɵtext(46, \" Documentation \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(47, \"div\", 10);\n      i0.ɵɵelementStart(48, \"input\", 20, 21);\n      i0.ɵɵlistener(\"change\", function SystemConfigurationComponent_Template_input_change_48_listener() {\n        i0.ɵɵrestoreView(_r40);\n\n        const _r5 = i0.ɵɵreference(49);\n\n        return ctx.loadAttachment(_r5.files);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(50, SystemConfigurationComponent_button_50_Template, 2, 0, \"button\", 22);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(51, \"div\", 16);\n      i0.ɵɵelementStart(52, \"label\", 19);\n      i0.ɵɵtext(53, \" Info \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(54, \"div\", 10);\n      i0.ɵɵelementStart(55, \"input\", 23, 24);\n      i0.ɵɵlistener(\"change\", function SystemConfigurationComponent_Template_input_change_55_listener() {\n        i0.ɵɵrestoreView(_r40);\n\n        const _r7 = i0.ɵɵreference(56);\n\n        return ctx.loadInfo(_r7.files);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(57, SystemConfigurationComponent_button_57_Template, 2, 0, \"button\", 22);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(58, \"div\", 7);\n      i0.ɵɵelementStart(59, \"div\", 8);\n      i0.ɵɵelementStart(60, \"label\", 9);\n      i0.ɵɵtext(61, \"Android App Link \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(62, \"div\", 10);\n      i0.ɵɵelement(63, \"input\", 25);\n      i0.ɵɵtemplate(64, SystemConfigurationComponent_div_64_Template, 2, 1, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(65, \"div\", 8);\n      i0.ɵɵelementStart(66, \"label\", 9);\n      i0.ɵɵtext(67, \"iOS App Link \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(68, \"div\", 10);\n      i0.ɵɵelement(69, \"input\", 26);\n      i0.ɵɵtemplate(70, SystemConfigurationComponent_div_70_Template, 2, 1, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(71, \"div\", 8);\n      i0.ɵɵelementStart(72, \"label\", 19);\n      i0.ɵɵtext(73, \"Institute Website \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(74, \"div\", 10);\n      i0.ɵɵelement(75, \"input\", 27);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(76, \"div\", 8);\n      i0.ɵɵelementStart(77, \"label\", 9);\n      i0.ɵɵtext(78, \"Logo (325x65) \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(79, \"div\", 10);\n      i0.ɵɵtemplate(80, SystemConfigurationComponent_img_80_Template, 1, 0, \"img\", 28);\n      i0.ɵɵtemplate(81, SystemConfigurationComponent_img_81_Template, 1, 1, \"img\", 29);\n      i0.ɵɵelementStart(82, \"div\", 18);\n      i0.ɵɵelementStart(83, \"input\", 30, 31);\n      i0.ɵɵlistener(\"change\", function SystemConfigurationComponent_Template_input_change_83_listener() {\n        i0.ɵɵrestoreView(_r40);\n\n        const _r13 = i0.ɵɵreference(84);\n\n        return ctx.preview(_r13.files);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(85, SystemConfigurationComponent_div_85_Template, 2, 1, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(86, \"div\", 32);\n      i0.ɵɵelementStart(87, \"div\", 33);\n      i0.ɵɵelementStart(88, \"div\", 5);\n      i0.ɵɵelementStart(89, \"h5\");\n      i0.ɵɵtext(90, \"Exam Instructions\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(91, \"div\", 6);\n      i0.ɵɵelementStart(92, \"div\", 34);\n      i0.ɵɵelement(93, \"ngx-editor-menu\", 35);\n      i0.ɵɵelement(94, \"ngx-editor\", 36);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(95, \"div\", 32);\n      i0.ɵɵelementStart(96, \"div\", 33);\n      i0.ɵɵelementStart(97, \"div\", 5);\n      i0.ɵɵelementStart(98, \"h5\");\n      i0.ɵɵtext(99, \"Question Mark Setup\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(100, \"div\", 6);\n      i0.ɵɵelementStart(101, \"div\", 2);\n      i0.ɵɵelementStart(102, \"div\", 0);\n      i0.ɵɵelementStart(103, \"div\", 8);\n      i0.ɵɵelementStart(104, \"div\", 37);\n      i0.ɵɵelementStart(105, \"label\", 38);\n      i0.ɵɵtext(106, \" MCQ Mark \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(107, \"input\", 39);\n      i0.ɵɵtemplate(108, SystemConfigurationComponent_div_108_Template, 2, 1, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(109, \"div\", 37);\n      i0.ɵɵelementStart(110, \"label\", 38);\n      i0.ɵɵtext(111, \" Fill in the gap Q. Mark \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(112, \"input\", 40);\n      i0.ɵɵtemplate(113, SystemConfigurationComponent_div_113_Template, 2, 1, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(114, \"div\", 37);\n      i0.ɵɵelementStart(115, \"label\", 38);\n      i0.ɵɵtext(116, \" True/False Q. Mark \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(117, \"input\", 41);\n      i0.ɵɵtemplate(118, SystemConfigurationComponent_div_118_Template, 2, 1, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(119, \"div\", 37);\n      i0.ɵɵelementStart(120, \"label\", 38);\n      i0.ɵɵtext(121, \" Matching Q. Mark \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(122, \"input\", 42);\n      i0.ɵɵtemplate(123, SystemConfigurationComponent_div_123_Template, 2, 1, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(124, \"div\", 37);\n      i0.ɵɵelementStart(125, \"label\", 38);\n      i0.ɵɵtext(126, \"Written Q. Mark\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(127, \"input\", 43);\n      i0.ɵɵtemplate(128, SystemConfigurationComponent_div_128_Template, 2, 1, \"div\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(129, \"div\", 44);\n      i0.ɵɵelementStart(130, \"button\", 45);\n      i0.ɵɵlistener(\"click\", function SystemConfigurationComponent_Template_button_click_130_listener() {\n        return ctx.onFormSubmit();\n      });\n      i0.ɵɵelement(131, \"i\", 46);\n      i0.ɵɵtext(132);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.entryForm);\n      i0.ɵɵadvance(14);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(34, _c1, ctx.submitted && ctx.f.name.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.name.errors);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(36, _c1, ctx.submitted && ctx.f.email.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.email.errors);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(38, _c1, ctx.submitted && ctx.f.phone.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.phone.errors);\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.address.errors);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(40, _c1, ctx.submitted && ctx.f.lastERPSyncDate.errors))(\"bsConfig\", ctx.bsConfig);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.lastERPSyncDate.errors);\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"ngIf\", ctx.attachment != null && ctx.attachment.DocumentationPath != \"\");\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"ngIf\", ctx.attachment != null && ctx.attachment.InfoPath != \"\");\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(42, _c1, ctx.submitted && ctx.f.andoridAppLink.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.andoridAppLink.errors);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(44, _c1, ctx.submitted && ctx.f.iOSAppLink.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.iOSAppLink.errors);\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"ngIf\", !ctx.logoURL);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.logoURL);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.logo.errors);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"editor\", ctx.editor)(\"toolbar\", ctx.toolbar);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"editor\", ctx.editor);\n      i0.ɵɵadvance(13);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(46, _c1, ctx.submitted && ctx.f.mcqMark.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.mcqMark.errors);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(48, _c1, ctx.submitted && ctx.f.figMark.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.figMark.errors);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(50, _c1, ctx.submitted && ctx.f.trueFalseMark.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.trueFalseMark.errors);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(52, _c1, ctx.submitted && ctx.f.matchingMark.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.matchingMark.errors);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(54, _c1, ctx.submitted && ctx.f.writtenMark.errors));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.writtenMark.errors);\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate1(\" \", ctx.btnSaveText, \" \");\n    }\n  },\n  directives: [i5.BlockUIComponent, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.DefaultValueAccessor, i1.NgControlStatus, i1.FormControlName, i6.NgClass, i7.DefaultClassDirective, i6.NgIf, i8.BsDatepickerInputDirective, i8.BsDatepickerDirective, i9.MenuComponent, i9.NgxEditorComponent],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], SystemConfigurationComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}