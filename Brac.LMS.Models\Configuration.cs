﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class Configuration : NumberAuditableEntity
    {
        [Required, Column(TypeName = "VARCHAR"), StringLength(250)]
        public string Name { get; set; }

        [Required, Column(TypeName = "VARCHAR"), StringLength(1000)]
        public string Address { get; set; }

        [Column(TypeName = "VARCHAR"), StringLength(50)]
        public string ContactNo { get; set; }

        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string InstituteWebsite { get; set; }

        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string InstituteEmail { get; set; }

        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string LogoPath { get; set; }
        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string DocumentationPath { get; set; }
        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string InfoPath { get; set; }
        public string ExamInstruction { get; set; }
        public float? MCQMark { get; set; }
        public float? FIGMark { get; set; }
        public float? TrueFalseMark { get; set; }
        public float? MatchingMark { get; set; }
        public float? WrittenMark { get; set; }

        [Column(TypeName = "VARCHAR"), StringLength(500)]
        public string AndoridAppLink { get; set; }

        [Column(TypeName = "VARCHAR"), StringLength(500)]
        public string iOSAppLink { get; set; }

        [Column(TypeName = "DATE")]
        public DateTime? LastERPSyncDate { get; set; }
    }
}
