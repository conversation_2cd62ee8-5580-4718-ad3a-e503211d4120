﻿using Brac.LMS.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{

    public class EvaluationExam : AuditableEntity
    {
        public string ExamName { get; set; }
        public string ExamInstructions { get; set; }
        public int Quota { get; set; }
        public int Marks { get; set; }
        public int DurationMnt { get; set; }
        public bool MCQOnly { get; set; }
        public bool Random { get; set; }
        public bool Publish { get; set; }
        public int ExamMCQNo { get; set; }
        public int ExamTrueFalseNo { get; set; }
        public int ExamFIGNo { get; set; }
        public int ExamMatchingNo { get; set; }
        public int ExamWritingNo { get; set; }
        public bool Active { get; set; }
        public int? Order { get; set; }
        private DateTime? _StartDate;
        public DateTime? StartDate {
            get
            { return _StartDate; }
            set
            { _StartDate = value.HasValue ? value.Value.ToKindUtc() : value; }
        }


        private DateTime? _EndDate;
        public DateTime? EndDate {
            get
            { return _EndDate; }
            set
            { _EndDate = value.HasValue ? value.Value.ToKindUtc() : value; }
        }


        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string ImagePath { get; set; }

        public long? CategoryId { get; set; }
        public virtual LhCategory Category { get; set; }

        public OMAllowFor AllowFor { get; set; }
        public long? DivisionId { get; set; }
        public virtual Division Division { get; set; }

        public long? DepartmentId { get; set; }
        public virtual Department Department { get; set; }

        public long? UnitId { get; set; }
        public virtual Unit Unit { get; set; }

        public virtual ICollection<Trainee> Trainees { get; set; }
        public virtual ICollection<TraineeEvaluationExamAttempt> TraineeAttempts { get; set; }
        public virtual ICollection<MCQEvaluationQuestion> MCQQuestions { get; set; }
        public virtual ICollection<TrueFalseEvaluationQuestion> TrueFalseQuestions { get; set; }
        public virtual ICollection<FIGEvaluationQuestion> FIGQuestions { get; set; }
        public virtual ICollection<MatchingEvaluationQuestion> MatchingQuestions { get; set; }
        public virtual ICollection<WrittenEvaluationQuestion> WrittenQuestions { get; set; }
    }
}
