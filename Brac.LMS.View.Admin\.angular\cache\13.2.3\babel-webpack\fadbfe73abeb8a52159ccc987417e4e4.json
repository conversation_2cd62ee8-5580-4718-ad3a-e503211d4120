{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SystemConfigurationRoutingModule } from './configuration-routing.module';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport { NgxEditorModule } from 'ngx-editor';\nimport { PopoverModule } from 'ngx-bootstrap/popover';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-bootstrap/popover\";\nexport let SystemConfigurationModule = /*#__PURE__*/(() => {\n  class SystemConfigurationModule {}\n\n  SystemConfigurationModule.ɵfac = function SystemConfigurationModule_Factory(t) {\n    return new (t || SystemConfigurationModule)();\n  };\n\n  SystemConfigurationModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SystemConfigurationModule\n  });\n  SystemConfigurationModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, SystemConfigurationRoutingModule, SharedModule, NgxEditorModule, PopoverModule.forRoot()]]\n  });\n  return SystemConfigurationModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}