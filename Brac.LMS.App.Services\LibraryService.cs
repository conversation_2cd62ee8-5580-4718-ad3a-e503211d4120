﻿using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Hosting;
using WMPLib;

namespace Brac.LMS.App.Services
{
    public class LibraryService : ILibraryService
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditLogHelper _auditLogHelper;
        private readonly AuditLog audit;
        private readonly ApplicationUser _appUser;
        public LibraryService()
        {
            _context = new ApplicationDbContext();
        }
        public LibraryService(string controller, string method, ApplicationUser applicationUser)
        {
            _auditLogHelper = new AuditLogHelper(controller, method);
            audit = _auditLogHelper.auditLog;
            _appUser = applicationUser;
            audit.CreatedBy = _appUser.Id;
            _context = new ApplicationDbContext();
        }

        public async Task<APIResponse> LibraryContentCreateOrUpdate(LibraryModel model, IIdentity identity)
        {
            Library item = null;

            bool isEdit = true;
            try
            {
                if (await _context.Libraries.AnyAsync(x => x.Id != model.Id && x.Title == model.Title))
                    throw new Exception("Already exists in this course: " + model.Title);

                if (model.Id.HasValue && model.Id > 0)
                {
                    item = await _context.Libraries.FindAsync(model.Id);
                    if (item == null) throw new Exception("Item not found");
                }
                else
                {
                    item = new Library();
                    isEdit = false;

                    if (!HttpContext.Current.Request.Files.AllKeys.Contains("Image"))
                        return new APIResponse
                        {
                            Status = ResponseStatus.Warning,
                            Message = "No cover image found"
                        };

                    if (!HttpContext.Current.Request.Files.AllKeys.Contains("File"))
                        return new APIResponse
                        {
                            Status = ResponseStatus.Warning,
                            Message = "No file found"
                        };
                }




                foreach (string key in HttpContext.Current.Request.Files.Keys)
                {
                    switch (key)
                    {
                        case "Image":
                            item.ImagePath = Utility.SaveImage(DateTime.Today.ToString("yyMMddHms") + "_" + Utility.RandomString(3, false), "/Files/Library/Images/", HttpContext.Current.Request.Files.Get(key), item.ImagePath, 400, 210);
                            break;
                        case "File":
                            switch (model.MaterialType)
                            {
                                case MaterialType.Document:
                                    item = SaveDocumentFile("DOC" + Utility.RandomString(4, false) + DateTime.Now.ToString("yyyMMddHHmmss"), "/Files/Library/Documents/", HttpContext.Current.Request.Files.Get(key), item);
                                    break;
                                case MaterialType.Video:
                                    item = SaveVideoFile("VID" + Utility.RandomString(4, false) + DateTime.Now.ToString("yyyMMddHHmmss"), "/Files/Library/Videos/", HttpContext.Current.Request.Files.Get(key), item);
                                    break;
                            }
                            break;
                    }
                }

                item.MaterialType = model.MaterialType;
                item.CategoryId = model.CategoryId;
                item.Title = model.Title;
                if (item.MaterialType == MaterialType.Document) item.CanDownload = !string.IsNullOrEmpty(item.FilePath) && item.FilePath.Split('.').Last().ToLower() == "pdf" ? model.CanDownload : true;

                item.SetAuditTrailEntity(identity);
                if (!isEdit)
                {
                    _context.Libraries.Add(item);
                }
                else
                {
                    _context.Entry(item).State = EntityState.Modified;
                }

                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                };

            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);

                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())

                };
            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);

                return new APIResponse { Status = ResponseStatus.Error, Message = ex.Message };
            }
        }

        private Library SaveVideoFile(string fileName, string partialPath, HttpPostedFile hpf, Library item)
        {
            try
            {
                string serverPath = HostingEnvironment.MapPath("~"), extension, filePath;

                if (!Directory.Exists(serverPath + partialPath)) Directory.CreateDirectory(serverPath + partialPath);

                if (!string.IsNullOrEmpty(item.FilePath) && File.Exists(serverPath + item.FilePath))
                {
                    File.Delete(serverPath + item.FilePath);
                }

                string[] mediaExtensions = { ".AVI", ".MP4", ".DIVX", ".WMV" };

                extension = Path.GetExtension(hpf.FileName);
                if (mediaExtensions.Contains(extension.ToUpper()))
                {
                    if (hpf.ContentLength > 524288000)
                    {
                        throw new Exception("File size exceeded. Max file size is 500MB. Your selected file size is " + hpf.ContentLength / (1024 * 1024) + ".");
                    }
                    filePath = partialPath + fileName + extension;

                    if (File.Exists(serverPath + filePath))
                        File.Delete(serverPath + filePath);

                    hpf.SaveAs(serverPath + filePath);

                    WindowsMediaPlayer wmp = new WindowsMediaPlayer();
                    IWMPMedia mediainfo = wmp.newMedia(serverPath + filePath);

                    //var info = new FFmpegMediaInfo(serverPath + filePath, Generator.FFMPEG_EXE_PATH, Generator.FFPROBE_EXE_PATH);

                    item.FileSizeKb = (long)(hpf.ContentLength / 1024);
                    item.VideoDurationSecond = (int)mediainfo.duration;
                    item.FileType = ServiceHelper.GetFileType(serverPath + filePath);

                    item.FilePath = filePath;
                    return item;
                }
                else throw new Exception("Only AVI, MP4, DIVX or WMV video files are allowed to upload.");
            }
            catch (Exception ex)
            {
                throw new Exception("SaveVideoFile Error: " + ex.Message);
            }
        }

        private Library SaveDocumentFile(string fileName, string partialPath, HttpPostedFile hpf, Library item)
        {
            try
            {
                string serverPath = HostingEnvironment.MapPath("~"), extension, filePath;

                if (!Directory.Exists(serverPath + partialPath)) Directory.CreateDirectory(serverPath + partialPath);

                if (!string.IsNullOrEmpty(item.FilePath) && File.Exists(serverPath + item.FilePath))
                {
                    File.Delete(serverPath + item.FilePath);
                }

                string[] fileExtensions = { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".jpg", ".jpeg", ".png" };

                extension = Path.GetExtension(hpf.FileName);
                if (fileExtensions.Contains(extension.ToLower()))
                {
                    if (hpf.ContentLength > 102400000)
                    {
                        throw new Exception("File size exceeded. Max file size is 100MB. Your selected file size is " + hpf.ContentLength / 1000 + ".");
                    }
                    filePath = partialPath + fileName + extension;

                    if (File.Exists(serverPath + filePath))
                        File.Delete(serverPath + filePath);

                    hpf.SaveAs(serverPath + filePath);
                    item.FilePath = filePath;
                    item.FileSizeKb = (long)(hpf.ContentLength / 1024);
                    item.FileType = ServiceHelper.GetFileType(serverPath + filePath);

                    return item;
                }
                else throw new Exception("Only PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, JPG, JPEG or PNG files are allowed to upload.");
            }
            catch (Exception ex)
            {
                throw new Exception("SaveDocumentFile Error: " + ex.Message);
            }
        }


        public async Task<APIResponse> GetLibraryContentList(string name, long? categoryId, int size, int pageNumber)
        {
            try
            {
                var query = _context.Libraries.Where(c => c.Category.Active).AsQueryable();
                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Title.Contains(name) || x.MaterialType.ToString().Contains(name));
                if (categoryId.HasValue) query = query.Where(x => x.CategoryId == categoryId);
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderByDescending(x => new { x.MaterialType, x.Title })
                .Skip(pageNumber * size).Take(size);
                var data = await filteredQuery.Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.FilePath,
                    x.FileSizeKb,
                    x.ImagePath,
                    x.S3Path,
                    x.CategoryId,
                    x.CanDownload,
                    Category = x.Category != null ? x.Category.Name : null,
                    MaterialType = x.MaterialType.ToString()
                }).ToListAsync();


                var count = await ((((!string.IsNullOrEmpty(name)) || (categoryId.HasValue)) || ((!string.IsNullOrEmpty(name)) && (categoryId.HasValue))) ? filteredQuery.CountAsync() : _context.Libraries.CountAsync());
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data, Total = count }
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetLibraryContentById(long id)
        {
            try
            {
                var query = _context.Libraries.Where(x => x.Id == id).AsQueryable();
                var data = await query
                            .Select(x => new
                            {
                                x.Id,
                                x.Title,
                                x.ImagePath,
                                x.CategoryId,
                                x.CanDownload
                            })
                            .FirstOrDefaultAsync();
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> DeleteLibraryContentById(long id)
        {
            try
            {
                var item = await _context.Libraries.FindAsync(id);
                if (item == null)
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "This library content not found"
                    };

                _context.Entry(item).State = EntityState.Deleted;

                await _context.SaveChangesAsync();

                if (File.Exists(HostingEnvironment.MapPath("~") + item.FilePath))
                    File.Delete(HostingEnvironment.MapPath("~") + item.FilePath);

                if (File.Exists(HostingEnvironment.MapPath("~") + item.ImagePath))
                    File.Delete(HostingEnvironment.MapPath("~") + item.ImagePath);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Deleted!"
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }
    }

    public interface ILibraryService
    {
        Task<APIResponse> GetLibraryContentList(string name, long? categoryId, int size, int pageNumber);
        Task<APIResponse> GetLibraryContentById(long id);
        Task<APIResponse> DeleteLibraryContentById(long id);
        Task<APIResponse> LibraryContentCreateOrUpdate(LibraryModel model, IIdentity identity);
    }
}
