﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class MaterialResource : AuditableEntity
    {
        public Guid MaterialId { get; set; }
        public virtual CourseMaterial Material { get; set; }

        [Required]
        [StringLength(250)]
        public string Title { get; set; }

        [Required]
        [Column(TypeName = "VARCHAR")]
        [StringLength(150)]
        public string FilePath { get; set; }
        public long FileSizeKb { get; set; }
    }
}
