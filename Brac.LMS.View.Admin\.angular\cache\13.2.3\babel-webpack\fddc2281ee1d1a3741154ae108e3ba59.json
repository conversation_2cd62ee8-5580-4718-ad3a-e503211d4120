{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { TraineeGroupRoutingModule } from './trainee-group-routing.module';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport let TraineeGroupModule = /*#__PURE__*/(() => {\n  class TraineeGroupModule {}\n\n  TraineeGroupModule.ɵfac = function TraineeGroupModule_Factory(t) {\n    return new (t || TraineeGroupModule)();\n  };\n\n  TraineeGroupModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TraineeGroupModule\n  });\n  TraineeGroupModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, TraineeGroupRoutingModule, SharedModule]]\n  });\n  return TraineeGroupModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}