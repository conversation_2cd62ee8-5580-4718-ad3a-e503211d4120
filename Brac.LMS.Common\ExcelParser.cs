﻿using ClosedXML.Excel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Brac.LMS.Common
{
    public static class ExcelParser
    {
        private static Tuple<T, IEnumerable<T>> HeadAndTail<T>(this IEnumerable<T> source)
        {
            if (source == null)
                throw new ArgumentNullException("source");
            var en = source.GetEnumerator();
            en.MoveNext();
            return Tuple.Create(en.Current, EnumerateTail(en));
        }

        private static IEnumerable<T> EnumerateTail<T>(IEnumerator<T> en)
        {
            while (en.MoveNext()) yield return en.Current;
        }

        public static Tuple<IList<string>, IEnumerable<IList<string>>> Parse(HttpPostedFile file)
        {
            if (file.ContentLength > 0)
            {
                if (file.FileName.EndsWith(".xlsx") || file.FileName.EndsWith(".xls"))
                {
                    XLWorkbook Workbook;
                    try//incase if the file is corrupt
                    {
                        Workbook = new XLWorkbook(file.InputStream);
                        return ParseHeadAndTail(Workbook);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"Check your file. {ex.Message}");
                    }
                }

                throw new Exception("Only .xlsx and .xls files are allowed");
            }
            throw new Exception("Not a valid file");


        }

        public static Tuple<IList<string>, IEnumerable<IList<string>>> ParseHeadAndTail(XLWorkbook workbook)
        {
            return HeadAndTail(Parse(workbook));
        }

        public static IEnumerable<IList<string>> Parse(XLWorkbook workbook)
        {
            var record = new List<List<string>>();
            List<string> rowItems = new List<string>();

            IXLWorksheet workSheet;
            try//incase if the sheet you are looking for is not found
            {
                workSheet = workbook.Worksheet("sheet1");

            }
            catch
            {
                throw new Exception("sheet1 not found!");
            }

            int cellCount = workSheet.RowsUsed().FirstOrDefault().CellsUsed().Count();

            foreach (var row in workSheet.RowsUsed())
            {
                rowItems = new List<string>();
                for (int i = 1; i <= cellCount; i++)
                {
                    rowItems.Add(Convert.ToString(row.Cell(i).Value).Trim());
                }
                record.Add(rowItems);
            }
            return record;
        }
    }
}
