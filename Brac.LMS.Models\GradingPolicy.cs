﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public enum GradeResult { Failed, Passed }

    public class GradingPolicy : AuditableEntity
    {
        public string GradeLetter { get; set; }
        public int MinValue { get; set; }
        public string Range { get; set; }
        public GradeResult Result { get; set; }
        public int GroupCode { get; set; }
        public bool Active { get; set; }
    }
}
