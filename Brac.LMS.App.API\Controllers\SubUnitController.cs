﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [Authorize(Roles = "Admin"), RoutePrefix("api/sub-unit")]
    public class SubUnitController : ApplicationController
    {
        private readonly ISubUnitService _service;

        public SubUnitController()
        {
            _service = new SubUnitService();
        }

        [HttpPost, Route("create-or-update")]
        public async Task<IHttpActionResult> SubUnitCreateOrUpdate(SubUnitModel model)
        {
            var _nservice = new SubUnitService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.SubUnitCreateOrUpdate(model, User.Identity));
        }

        [HttpGet, Route("list")]
        public async Task<IHttpActionResult> GetSubUnitList(string name, int size, int pageNumber)
        {
            return Ok(await _service.GetSubUnitList(name, size, pageNumber));
        }

        [HttpGet, Route("dropdown-list")]
        public async Task<IHttpActionResult> GetSubUnitDropDownList()
        {
            return Ok(await _service.GetSubUnitDropDownList());
        }

        [HttpGet, Route("get/{id}")]
        public async Task<IHttpActionResult> GetSubUnitById(long id)
        {
            return Ok(await _service.GetSubUnitById(id));
        }

        [HttpGet, Route("delete/{id}")]
        public async Task<IHttpActionResult> DeleteSubUnitById(long id)
        {
            var _nservice = new SubUnitService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.DeleteSubUnitById(id));
        }
    }
}
