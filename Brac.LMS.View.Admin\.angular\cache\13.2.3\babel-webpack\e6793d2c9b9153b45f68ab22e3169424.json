{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { LearningHourQuestionListRoutingModule } from './learning-hour-question-list-routing.module';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport let LearningHourQuestionListModule = /*#__PURE__*/(() => {\n  class LearningHourQuestionListModule {}\n\n  LearningHourQuestionListModule.ɵfac = function LearningHourQuestionListModule_Factory(t) {\n    return new (t || LearningHourQuestionListModule)();\n  };\n\n  LearningHourQuestionListModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: LearningHourQuestionListModule\n  });\n  LearningHourQuestionListModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, LearningHourQuestionListRoutingModule, SharedModule]]\n  });\n  return LearningHourQuestionListModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}