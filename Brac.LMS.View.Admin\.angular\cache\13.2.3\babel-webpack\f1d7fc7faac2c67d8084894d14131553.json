{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { DivisionComponent } from './division.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: DivisionComponent\n}];\nexport let DivisionRoutingModule = /*#__PURE__*/(() => {\n  class DivisionRoutingModule {}\n\n  DivisionRoutingModule.ɵfac = function DivisionRoutingModule_Factory(t) {\n    return new (t || DivisionRoutingModule)();\n  };\n\n  DivisionRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: DivisionRoutingModule\n  });\n  DivisionRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return DivisionRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}