﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.ViewModels
{
    public enum CourseProgress { InProgress, NotStarted, Completed }
    public class CourseModel
    {
        public Guid? Id { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string ImagePath { get; set; }
        public bool Active { get; set; }
        public bool Published { get; set; }
        public Guid TrainerId { get; set; }
        public bool SelfEnrollment { get; set; }
        public int? ExpiryMonth { get; set; }
        public int? CertificateExpiryMonth { get; set; }
        public long CategoryId { get; set; }
        public string ShortTitle { get; set; }
    }
}
