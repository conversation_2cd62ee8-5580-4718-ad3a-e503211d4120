﻿
using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.Services
{
    public class TraineeDeviceService : ITraineeDeviceService
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditLogHelper _auditLogHelper;
        private readonly AuditLog audit;
        private readonly ApplicationUser _appUser;
        public TraineeDeviceService()
        {
            _context = new ApplicationDbContext();
        }
        public TraineeDeviceService(string controller, string method, ApplicationUser applicationUser)
        {
            _auditLogHelper = new AuditLogHelper(controller, method);
            audit = _auditLogHelper.auditLog;
            _appUser = applicationUser;
            audit.CreatedBy = _appUser.Id;
            _context = new ApplicationDbContext();
        }
        public async Task<APIResponse> TraineeDeviceCreateOrUpdate(TraineeDeviceModel model, ApplicationUser user)
        {
            bool isEdit = true;
            try
            {
                TraineeDevice item = await _context.TraineeDevices.FirstOrDefaultAsync(x => x.TraineeId == user.Trainee.Id && x.OsId == model.OsId);
                if (item == null)
                {
                    item = new TraineeDevice { TraineeId = user.Trainee.Id, OsId = model.OsId };
                    isEdit = false;
                }

                item.Token = model.Token;
                item.Device = model.Device;
                item.SetAuditTrailEntity(user.User.Identity);

                if (!isEdit)
                {
                    _context.TraineeDevices.Add(item);
                }
                else
                {
                    _context.Entry(item).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                };

            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())

                };

            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }
    }

    public interface ITraineeDeviceService
    {
        Task<APIResponse> TraineeDeviceCreateOrUpdate(TraineeDeviceModel model, ApplicationUser user);
    }
}
