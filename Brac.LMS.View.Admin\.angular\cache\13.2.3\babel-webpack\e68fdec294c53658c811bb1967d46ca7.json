{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { BasicElementsComponent } from './basic-elements.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: BasicElementsComponent\n}];\nexport let BasicElementsRoutingModule = /*#__PURE__*/(() => {\n  class BasicElementsRoutingModule {}\n\n  BasicElementsRoutingModule.ɵfac = function BasicElementsRoutingModule_Factory(t) {\n    return new (t || BasicElementsRoutingModule)();\n  };\n\n  BasicElementsRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: BasicElementsRoutingModule\n  });\n  BasicElementsRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return BasicElementsRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}