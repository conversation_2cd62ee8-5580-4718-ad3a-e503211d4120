﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.ViewModels
{
    public class CourseContentModel
    {
        public CourseContentModel() { Resources = new List<MaterialResourceFetchModel>(); }

        public Guid Id { get; set; }
        public string Title { get; set; }
        public string Type { get; set; }
        public string FilePath { get; set; }
        public string S3Path { get; set; }
        public string ExternalLink { get; set; }
        public string YoutubeID { get; set; }
        public long FileSizeKb { get; set; }
        public int VideoDurationSecond { get; set; }
        public int Sequence { get; set; }
        public bool Studied { get; set; }
        public bool Restricted { get; set; }
        public int? LastStudyTimeSec { get; set; }
        public int RequiredStudyTimeSec { get; set; }
        public bool CanDownload { get; set; }
        public List<MaterialResourceFetchModel> Resources { get; set; }
    }

    public class CourseContentRestrictionModel
    {
        public Guid Id { get; set; }
        public int Sequence { get; set; }
        public bool Studied { get; set; }
        public bool Restricted { get; set; }
        public int LastStudyTimeSec { get; set; }
    }
}
