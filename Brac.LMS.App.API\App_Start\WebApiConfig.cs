﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Formatting;
using System.Web.Http;
using System.Web.Http.Cors;
using Microsoft.Owin.Security.OAuth;
using Newtonsoft.Json.Serialization;
using WebApiThrottle;

namespace Brac.LMS.App.API
{
    public static class WebApiConfig
    {
        public static void Register(HttpConfiguration config)
        {
            // Web API configuration and services
            // Configure Web API to use only bearer token authentication.
            config.SuppressDefaultHostAuthentication();
            config.Filters.Add(new HostAuthenticationFilter(OAuthDefaults.AuthenticationType));
            config.Filters.Add(new CustomAuthorize());

            // Web API routes
            config.MapHttpAttributeRoutes();
            //var cors = new EnableCorsAttribute("https://localhost:4205/", "*", "*");
            //EnableCorsAttribute cors = new EnableCorsAttribute("https://localhost:4205/", "*", "*");
            config.EnableCors();
            //config.EnableCors();
            //config.EnableCors();

            config.Routes.MapHttpRoute(
                name: "DefaultApi",
                routeTemplate: "api/{controller}/{id}",
                defaults: new { id = RouteParameter.Optional }
            );
            
            //config.Formatters.Clear();
            //config.Formatters.Add(new JsonMediaTypeFormatter());

            //config.Formatters.JsonFormatter.SerializerSettings.Converters = new List<Newtonsoft.Json.JsonConverter>
            //{
            //    new AntiXssConverter()
            //};
            //config.Formatters.JsonFormatter.SerializerSettings.StringEscapeHandling = Newtonsoft.Json.StringEscapeHandling.EscapeHtml;


            //var jsonFormatter = config.Formatters.OfType<JsonMediaTypeFormatter>().First();
            //config.Formatters.Remove(jsonFormatter);
            //config.Formatters.Remove(config.Formatters.XmlFormatter);
            //config.Formatters.Add(new SanitizingJsonMediaTypeFormatter());

            config.Filters.Add(new ThrottlingFilter()
            {
                Policy = new ThrottlePolicy()
                {
                    IpThrottling = true,
                    ClientThrottling = true,
                    EndpointThrottling = true,
                    //    EndpointRules = new Dictionary<string, RateLimits>
                    //{
                    //    { "api/forum/topic/create-or-update", new RateLimits { PerDay=1  } }
                    //}
                },
                Repository = new MemoryCacheRepository(),
                QuotaExceededResponseCode = HttpStatusCode.ServiceUnavailable,
                QuotaExceededMessage = "You have exceeded your limit for today !  We can only allow {0} per {1}"
            });
        }
    }
}
