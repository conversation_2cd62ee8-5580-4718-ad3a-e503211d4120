{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { BlockUI } from 'ng-block-ui';\nimport { Page } from '../_models/page';\nimport { ResponseStatus } from '../_models/ResponseStatus';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"ngx-toastr\";\nimport * as i3 from \"../_helpers/confirm-dialog/confirm.service\";\nimport * as i4 from \"../_services/common.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"ng-block-ui\";\nimport * as i8 from \"ngx-bootstrap/tooltip\";\nimport * as i9 from \"@angular/flex-layout/extended\";\nimport * as i10 from \"src/app/_helpers/safe-pipe\";\nimport * as i11 from \"ngx-moment\";\n\nfunction ForumPostDetailsComponent_div_2_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const tag_r11 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tag_r11, \" \");\n  }\n}\n\nfunction ForumPostDetailsComponent_div_2_img_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 45);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"alt\", ctx_r2.topic.Creator);\n  }\n}\n\nfunction ForumPostDetailsComponent_div_2_img_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 46);\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"alt\", ctx_r3.topic.Creator);\n  }\n}\n\nfunction ForumPostDetailsComponent_div_2_img_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 47);\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"alt\", ctx_r4.topic.Creator);\n  }\n}\n\nfunction ForumPostDetailsComponent_div_2_img_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 48);\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"alt\", ctx_r5.topic.Creator);\n    i0.ɵɵproperty(\"src\", ctx_r5.baseUrl + ctx_r5.topic.CreatorImage, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction ForumPostDetailsComponent_div_2_ng_container_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function ForumPostDetailsComponent_div_2_ng_container_40_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return ctx_r12.approveTopic(ctx_r12.topic.Id);\n    });\n    i0.ɵɵelement(2, \"i\", 50);\n    i0.ɵɵtext(3, \" Approve \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function ForumPostDetailsComponent_div_2_ng_container_40_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return ctx_r14.deleteTopic(ctx_r14.topic.Id);\n    });\n    i0.ɵɵelement(5, \"i\", 52);\n    i0.ɵɵtext(6, \" Delete \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\n\nfunction ForumPostDetailsComponent_div_2_button_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function ForumPostDetailsComponent_div_2_button_41_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return ctx_r15.closeTopic(ctx_r15.topic.Id);\n    });\n    i0.ɵɵelement(1, \"i\", 54);\n    i0.ɵɵtext(2, \" Close \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ForumPostDetailsComponent_div_2_button_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function ForumPostDetailsComponent_div_2_button_42_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return ctx_r17.approveTopic(ctx_r17.topic.Id);\n    });\n    i0.ɵɵelement(1, \"i\", 56);\n    i0.ɵɵtext(2, \" Open \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c0 = function (a0, a1) {\n  return {\n    \"bg-warning\": a0,\n    \"bg-danger\": a1\n  };\n};\n\nfunction ForumPostDetailsComponent_div_2_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵelementStart(1, \"span\", 58);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c0, ctx_r9.topic.Status === \"Pending\", ctx_r9.topic.Status === \"Closed\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.topic.Status, \" \");\n  }\n}\n\nfunction ForumPostDetailsComponent_div_2_div_55_img_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 78);\n  }\n\n  if (rf & 2) {\n    const item_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r19.Creator);\n  }\n}\n\nfunction ForumPostDetailsComponent_div_2_div_55_img_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 79);\n  }\n\n  if (rf & 2) {\n    const item_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r19.Creator);\n  }\n}\n\nfunction ForumPostDetailsComponent_div_2_div_55_img_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 80);\n  }\n\n  if (rf & 2) {\n    const item_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r19.Creator);\n  }\n}\n\nfunction ForumPostDetailsComponent_div_2_div_55_img_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 81);\n  }\n\n  if (rf & 2) {\n    const item_r19 = i0.ɵɵnextContext().$implicit;\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r19.Creator);\n    i0.ɵɵproperty(\"src\", ctx_r23.baseUrl + item_r19.CreatorImage, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction ForumPostDetailsComponent_div_2_div_55_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 82);\n    i0.ɵɵelementStart(1, \"div\", 60);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 83);\n    i0.ɵɵlistener(\"click\", function ForumPostDetailsComponent_div_2_div_55_div_27_Template_span_click_3_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r31);\n      const reply_r29 = restoredCtx.$implicit;\n      const ctx_r30 = i0.ɵɵnextContext(3);\n      return ctx_r30.deleteComment(reply_r29.Id, \"reply\");\n    });\n    i0.ɵɵelement(4, \"i\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 84);\n    i0.ɵɵtext(6, \" \\u2013 \");\n    i0.ɵɵelementStart(7, \"span\", 85);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 86);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"amCalendar\");\n    i0.ɵɵpipe(12, \"amLocal\");\n    i0.ɵɵpipe(13, \"amFromUtc\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const reply_r29 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", reply_r29.Comment, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(reply_r29.Creator);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(11, 3, i0.ɵɵpipeBind1(12, 5, i0.ɵɵpipeBind1(13, 7, reply_r29.CreatedDate))), \" \");\n  }\n}\n\nconst _c1 = function (a0, a1) {\n  return {\n    \"fa-solid text-primary\": a0,\n    \"fa-regular\": a1\n  };\n};\n\nfunction ForumPostDetailsComponent_div_2_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelementStart(1, \"div\", 60);\n    i0.ɵɵelement(2, \"p\", 61);\n    i0.ɵɵpipe(3, \"safe\");\n    i0.ɵɵelementStart(4, \"span\", 62);\n    i0.ɵɵlistener(\"click\", function ForumPostDetailsComponent_div_2_div_55_Template_span_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r33);\n      const item_r19 = restoredCtx.$implicit;\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return ctx_r32.deleteComment(item_r19.Id, \"comment\");\n    });\n    i0.ɵɵelement(5, \"i\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 64);\n    i0.ɵɵelementStart(7, \"div\", 65);\n    i0.ɵɵtemplate(8, ForumPostDetailsComponent_div_2_div_55_img_8_Template, 1, 1, \"img\", 66);\n    i0.ɵɵtemplate(9, ForumPostDetailsComponent_div_2_div_55_img_9_Template, 1, 1, \"img\", 67);\n    i0.ɵɵtemplate(10, ForumPostDetailsComponent_div_2_div_55_img_10_Template, 1, 1, \"img\", 68);\n    i0.ɵɵtemplate(11, ForumPostDetailsComponent_div_2_div_55_img_11_Template, 1, 2, \"img\", 69);\n    i0.ɵɵelementStart(12, \"div\", 70);\n    i0.ɵɵelementStart(13, \"h4\", 71);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 28);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"amTimeAgo\");\n    i0.ɵɵpipe(18, \"amLocal\");\n    i0.ɵɵpipe(19, \"amFromUtc\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 72);\n    i0.ɵɵelementStart(21, \"button\", 73);\n    i0.ɵɵelement(22, \"i\", 74);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 75);\n    i0.ɵɵelement(25, \"i\", 76);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, ForumPostDetailsComponent_div_2_div_55_div_27_Template, 14, 9, \"div\", 77);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r19 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind2(3, 12, item_r19.Comment, \"html\"), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", !item_r19.CreatorImage && item_r19.CreatorGender == \"Male\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r19.CreatorImage && item_r19.CreatorGender == \"Female\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r19.CreatorImage && item_r19.CreatorGender == \"Others\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r19.CreatorImage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r19.Creator);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(17, 15, i0.ɵɵpipeBind1(18, 17, i0.ɵɵpipeBind1(19, 19, item_r19.CreatedDate))));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(21, _c1, item_r19.Liked, !item_r19.Liked));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r19.NoOfLikes, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r19.Replies.length, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", item_r19.Replies);\n  }\n}\n\nfunction ForumPostDetailsComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵelementStart(1, \"div\", 3);\n    i0.ɵɵelementStart(2, \"div\", 4);\n    i0.ɵɵelementStart(3, \"div\", 5);\n    i0.ɵɵelementStart(4, \"h1\", 6);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"a\", 7);\n    i0.ɵɵlistener(\"click\", function ForumPostDetailsComponent_div_2_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return ctx_r34.backClicked();\n    });\n    i0.ɵɵelement(7, \"i\", 8);\n    i0.ɵɵtext(8, \"Go Back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"hr\", 9);\n    i0.ɵɵelementStart(10, \"div\", 0);\n    i0.ɵɵelementStart(11, \"div\", 10);\n    i0.ɵɵelementStart(12, \"div\", 11);\n    i0.ɵɵelement(13, \"p\", 12);\n    i0.ɵɵpipe(14, \"safe\");\n    i0.ɵɵelementStart(15, \"div\", 13);\n    i0.ɵɵelementStart(16, \"button\", 14);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, ForumPostDetailsComponent_div_2_button_18_Template, 2, 1, \"button\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 16);\n    i0.ɵɵelementStart(20, \"div\", 17);\n    i0.ɵɵelementStart(21, \"div\", 18);\n    i0.ɵɵelementStart(22, \"div\", 19);\n    i0.ɵɵtemplate(23, ForumPostDetailsComponent_div_2_img_23_Template, 1, 1, \"img\", 20);\n    i0.ɵɵtemplate(24, ForumPostDetailsComponent_div_2_img_24_Template, 1, 1, \"img\", 21);\n    i0.ɵɵtemplate(25, ForumPostDetailsComponent_div_2_img_25_Template, 1, 1, \"img\", 22);\n    i0.ɵɵtemplate(26, ForumPostDetailsComponent_div_2_img_26_Template, 1, 2, \"img\", 23);\n    i0.ɵɵelementStart(27, \"div\", 24);\n    i0.ɵɵelementStart(28, \"div\", 25);\n    i0.ɵɵelementStart(29, \"h6\", 26);\n    i0.ɵɵtext(30, \" Posted by: \");\n    i0.ɵɵelementStart(31, \"span\", 27);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"span\", 28);\n    i0.ɵɵtext(34);\n    i0.ɵɵpipe(35, \"amTimeAgo\");\n    i0.ɵɵpipe(36, \"amLocal\");\n    i0.ɵɵpipe(37, \"amFromUtc\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 29);\n    i0.ɵɵelementStart(39, \"div\", 30);\n    i0.ɵɵtemplate(40, ForumPostDetailsComponent_div_2_ng_container_40_Template, 7, 0, \"ng-container\", 31);\n    i0.ɵɵtemplate(41, ForumPostDetailsComponent_div_2_button_41_Template, 3, 0, \"button\", 32);\n    i0.ɵɵtemplate(42, ForumPostDetailsComponent_div_2_button_42_Template, 3, 0, \"button\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 34);\n    i0.ɵɵelementStart(44, \"div\", 35);\n    i0.ɵɵelement(45, \"i\", 36);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 0);\n    i0.ɵɵelementStart(48, \"div\", 37);\n    i0.ɵɵelementStart(49, \"div\", 38);\n    i0.ɵɵelementStart(50, \"h1\", 39);\n    i0.ɵɵtext(51, \"Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 40);\n    i0.ɵɵtemplate(53, ForumPostDetailsComponent_div_2_div_53_Template, 3, 5, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 42);\n    i0.ɵɵtemplate(55, ForumPostDetailsComponent_div_2_div_55_Template, 28, 24, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.topic.Title);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind2(14, 17, ctx_r0.topic.Description, \"html\"), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.topic.Category);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.topic.Tags);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.topic.CreatorImage && ctx_r0.topic.CreatorGender == \"Male\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.topic.CreatorImage && ctx_r0.topic.CreatorGender == \"Female\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.topic.CreatorImage && ctx_r0.topic.CreatorGender == \"Others\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.topic.CreatorImage);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.topic.Creator);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(35, 20, i0.ɵɵpipeBind1(36, 22, i0.ɵɵpipeBind1(37, 24, ctx_r0.topic.CreatedDate))));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngSwitch\", ctx_r0.topic.Status);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Pending\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Open\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Closed\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.topic.NoOfReplies, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.topic.Status !== \"Open\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.postList);\n  }\n}\n\nexport class ForumPostDetailsComponent {\n  constructor(formBuilder, toastr, confirmService, _service, route, router, _location) {\n    this.formBuilder = formBuilder;\n    this.toastr = toastr;\n    this.confirmService = confirmService;\n    this._service = _service;\n    this.route = route;\n    this.router = router;\n    this._location = _location;\n    this.id = null;\n    this.submitted = false;\n    this.postList = [];\n    this.page = new Page();\n    this.baseUrl = environment.baseUrl;\n    this.page.pageNumber = 0;\n    this.page.size = 10;\n  }\n\n  ngOnInit() {\n    if (this.route.snapshot.paramMap.has(\"id\")) {\n      this.id = this.route.snapshot.paramMap.get(\"id\");\n      this.getItem();\n      this.getTopicPosts();\n    }\n  }\n\n  getItem() {\n    this.blockUI.start('Getting data. Please wait...');\n\n    this._service.get('forum/get-topic-details/' + this.id).subscribe({\n      next: res => {\n        this.blockUI.stop();\n\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.topic = res.Data;\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.warning(err.Message || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  getTopicPosts() {\n    const obj = {\n      id: this.id,\n      size: this.page.size,\n      pageNumber: this.page.pageNumber\n    };\n\n    this._service.get('forum/topic/get-posts', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.postList = res.Data.Records;\n        this.postList.forEach(element => {\n          element.IsCollapsed = true;\n        });\n        this.page.totalElements = res.Data.Total;\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n      },\n      error: err => {\n        this.toastr.warning(err.Messaage || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => console.log('complete')\n    });\n  }\n\n  onFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid) {\n      return;\n    }\n\n    this.blockUI.start('Saving...');\n    const obj = {\n      id: this.entryForm.value.id,\n      status: this.entryForm.value.status\n    };\n\n    const request = this._service.get('forum/topic/change-status', obj);\n\n    request.subscribe({\n      next: res => {\n        this.blockUI.stop();\n\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.toastr.success(res.Message, 'Success!', {\n          timeOut: 2000\n        });\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.warning(err.Message || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  deleteComment(id, type) {\n    let url = 'forum/comment-or-reply/delete/';\n    this.confirmService.confirm('Are you sure?', 'You are deleting this ' + type + '.').subscribe(result => {\n      if (result) {\n        this.blockUI.start('Deleting data...');\n\n        this._service.get(url + id).subscribe(res => {\n          this.blockUI.stop();\n\n          if (res.Status === ResponseStatus.Warning) {\n            this.toastr.warning(res.Message, 'Warning!', {\n              timeOut: 2000\n            });\n            return;\n          } else if (res.Status === ResponseStatus.Error) {\n            this.toastr.error(res.Message, 'Error!', {\n              closeButton: true,\n              disableTimeOut: false,\n              enableHtml: true\n            });\n            return;\n          }\n\n          this.getTopicPosts();\n          this.toastr.success(res.Message, 'SUCCESS!');\n        });\n      }\n    });\n  }\n\n  approveTopic(id) {\n    this.blockUI.start('loading...');\n\n    this._service.get('forum/topic/approve/' + id).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.router.navigate(['/forum-posts']);\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n      },\n      complete: () => {\n        this.blockUI.stop();\n      }\n    });\n  }\n\n  closeTopic(id) {\n    this.blockUI.start('loading...');\n\n    this._service.get('forum/topic/close/' + id).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.router.navigate(['/forum-posts']);\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n      },\n      complete: () => {\n        this.blockUI.stop();\n      }\n    });\n  }\n\n  deleteTopic(id) {\n    this.blockUI.start('loading...');\n\n    this._service.get('forum/topic/delete/' + id).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            timeOut: 2000\n          });\n          return;\n        }\n\n        this.router.navigate(['/forum-posts']);\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.warning(err.Messaage || err, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false\n        });\n      },\n      complete: () => {\n        this.blockUI.stop();\n      }\n    });\n  }\n\n  backClicked() {\n    localStorage.setItem('reload-with-page', '1');\n\n    this._location.back();\n  }\n\n}\n\nForumPostDetailsComponent.ɵfac = function ForumPostDetailsComponent_Factory(t) {\n  return new (t || ForumPostDetailsComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ToastrService), i0.ɵɵdirectiveInject(i3.ConfirmService), i0.ɵɵdirectiveInject(i4.CommonService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i6.Location));\n};\n\nForumPostDetailsComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: ForumPostDetailsComponent,\n  selectors: [[\"app-forum-post-details\"]],\n  decls: 3,\n  vars: 1,\n  consts: [[1, \"row\"], [\"class\", \"col-lg-12\", 4, \"ngIf\"], [1, \"col-lg-12\"], [1, \"d-flex\", \"flex-column\", \"h-100\", \"bg-light\", \"rounded-1\", \"shadow-lg\"], [1, \"pt-2\", \"p-md-3\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-1\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-2\", \"text-break\"], [1, \"btn\", \"btn-link\", \"fs-5\", \"fw-bold\", \"btn-sm\", \"mb-2\", \"text-primary\", 3, \"click\"], [1, \"fs-5\", \"fa\", \"fa-arrow-left\", \"fs-base\", \"me-2\"], [1, \"mt-1\", \"mb-4\"], [1, \"col-12\", \"border-end\"], [1, \"border-bottom\"], [1, \"mb-1\", 3, \"innerHTML\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-start\", \"text-center\", \"text-sm-start\", \"py-2\"], [\"tooltip\", \"Category\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-2\", \"py-1\", \"px-2\", \"fs-7\"], [\"type\", \"button\", \"class\", \"btn btn-outline-primary btn-sm me-2 py-1 px-2 fs-7\", \"tooltip\", \"tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\", \"d-flex\", \"align-content-stretch\", \"border-top\", \"border-bottom\", \"mb-4\"], [1, \"col-lg-5\", \"col-12\", \"py-3\", \"pe-md-3\", \"border-end\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\", \"justify-content-md-start\"], [1, \"d-flex\", \"align-items-center\", \"me-grid-gutter\"], [\"class\", \"rounded-circle me-1\", \"src\", \"assets/images/user/male.jpg\", \"width\", \"64\", 3, \"alt\", 4, \"ngIf\"], [\"class\", \"rounded-circle me-1\", \"src\", \"assets/images/user/female.jpg\", \"width\", \"64\", 3, \"alt\", 4, \"ngIf\"], [\"class\", \"rounded-circle me-1\", \"src\", \"assets/images/user/other.jpg\", \"width\", \"64\", 3, \"alt\", 4, \"ngIf\"], [\"class\", \"rounded-circle me-1\", \"width\", \"64\", 3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"ps-2\"], [1, \"text-nowrap\"], [1, \"fs-6\", \"mb-n1\"], [1, \"text-primary\"], [1, \"fs-xs\", \"text-muted\"], [1, \"col-lg-5\", \"col-12\", \"ps-md-3\", \"py-3\", \"border-end\", \"d-flex\", \"align-items-center\", \"justify-content-center\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"class\", \"btn btn-outline-danger me-1\", \"container\", \"body\", \"tooltip\", \"Close Post\", 3, \"click\", 4, \"ngSwitchCase\"], [\"class\", \"btn btn-success me-1\", \"container\", \"body\", \"tooltip\", \"Open Post\", 3, \"click\", 4, \"ngSwitchCase\"], [1, \"col-lg-2\", \"col-12\", \"ps-md-3\", \"py-3\", \"d-flex\", \"align-items-center\", \"justify-content-end\"], [1, \"btn-comment\", \"cursor-default\", \"fs-5\"], [1, \"far\", \"fa-comment-alt\", \"text-primary\"], [1, \"col-md-8\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-between\", \"pb-2\", \"text-center\", \"text-sm-start\"], [1, \"h3\", \"mb-0\", \"text-nowrap\"], [1, \"col-md-4\"], [\"class\", \"d-sm-flex align-items-center justify-content-end text-center text-sm-start\", 4, \"ngIf\"], [1, \"py-3\"], [\"class\", \"comment\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"tooltip\", \"tag\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"me-2\", \"py-1\", \"px-2\", \"fs-7\"], [\"src\", \"assets/images/user/male.jpg\", \"width\", \"64\", 1, \"rounded-circle\", \"me-1\", 3, \"alt\"], [\"src\", \"assets/images/user/female.jpg\", \"width\", \"64\", 1, \"rounded-circle\", \"me-1\", 3, \"alt\"], [\"src\", \"assets/images/user/other.jpg\", \"width\", \"64\", 1, \"rounded-circle\", \"me-1\", 3, \"alt\"], [\"width\", \"64\", 1, \"rounded-circle\", \"me-1\", 3, \"src\", \"alt\"], [\"container\", \"body\", \"tooltip\", \"Approve Post\", 1, \"btn\", \"btn-success\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [\"container\", \"body\", \"tooltip\", \"Delete Post\", 1, \"btn\", \"btn-outline-danger\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"container\", \"body\", \"tooltip\", \"Close Post\", 1, \"btn\", \"btn-outline-danger\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"container\", \"body\", \"tooltip\", \"Open Post\", 1, \"btn\", \"btn-success\", \"me-1\", 3, \"click\"], [1, \"feather\", \"icon-check\"], [1, \"d-sm-flex\", \"align-items-center\", \"justify-content-end\", \"text-center\", \"text-sm-start\"], [1, \"badge\", \"bg-success\", \"me-2\", \"px-3\", \"py-2\", \"fs-6\", \"rounded-3\", 3, \"ngClass\"], [1, \"comment\"], [1, \"d-flex\", \"justify-content-start\"], [1, \"admin-font-16\", 3, \"innerHTML\"], [\"tooltip\", \"Delete the comment\", 1, \"label\", \"label-danger\", \"text-danger\", \"ms-2\", \"cursor-div\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"m-0\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"d-flex\", \"align-items-center\"], [\"class\", \"rounded-circle\", \"src\", \"assets/img/user/male.jpg\", \"width\", \"42\", 3, \"alt\", 4, \"ngIf\"], [\"class\", \"rounded-circle\", \"src\", \"assets/img/user/female.jpg\", \"width\", \"42\", 3, \"alt\", 4, \"ngIf\"], [\"class\", \"rounded-circle\", \"src\", \"assets/img/user/other.jpg\", \"width\", \"42\", 3, \"alt\", 4, \"ngIf\"], [\"class\", \"rounded-circle\", \"width\", \"42\", 3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"ps-2\", \"ms-1\"], [1, \"fs-sm\", \"mb-0\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\", \"justify-content-md-end\"], [\"type\", \"button\", 1, \"btn-comment-like\", \"fs-5\", \"fw-bold\", \"me-2\", 3, \"disabled\"], [1, \"far\", \"fa-thumbs-up\", \"me-1\", 3, \"ngClass\"], [\"type\", \"button\", 1, \"btn-comment\", \"fs-5\", \"fw-bold\", \"cursor-default\"], [1, \"far\", \"fa-comment-alt\", \"me-1\"], [\"class\", \"comment mt-3\", 4, \"ngFor\", \"ngForOf\"], [\"src\", \"assets/img/user/male.jpg\", \"width\", \"42\", 1, \"rounded-circle\", 3, \"alt\"], [\"src\", \"assets/img/user/female.jpg\", \"width\", \"42\", 1, \"rounded-circle\", 3, \"alt\"], [\"src\", \"assets/img/user/other.jpg\", \"width\", \"42\", 1, \"rounded-circle\", 3, \"alt\"], [\"width\", \"42\", 1, \"rounded-circle\", 3, \"src\", \"alt\"], [1, \"comment\", \"mt-3\"], [\"tooltip\", \"Delete the reply\", 1, \"label\", \"label-danger\", \"text-danger\", \"ms-2\", \"my-auto\", \"cursor-div\", 3, \"click\"], [1, \"d-inline-flex\", \"align-items-center\"], [1, \"fw-bold\", \"text-primary\"], [1, \"text-muted\"]],\n  template: function ForumPostDetailsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵtemplate(2, ForumPostDetailsComponent_div_2_Template, 56, 26, \"div\", 1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.topic);\n    }\n  },\n  directives: [i7.BlockUIComponent, i6.NgIf, i8.TooltipDirective, i6.NgForOf, i6.NgSwitch, i6.NgSwitchCase, i6.NgClass, i9.DefaultClassDirective],\n  pipes: [i10.SafePipe, i11.TimeAgoPipe, i11.LocalTimePipe, i11.FromUtcPipe, i11.CalendarPipe],\n  styles: [\"\"]\n});\n\n__decorate([BlockUI()], ForumPostDetailsComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}