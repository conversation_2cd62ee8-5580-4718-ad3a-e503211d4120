﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using FastReport;
using FastReport.Export.Pdf;
using Microsoft.AspNet.Identity.Owin;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [RoutePrefix("api/notification")]
    public class NotificationController : ApplicationController
    {
        private readonly INotificationService _service;

        public NotificationController()
        {
            _service = new NotificationService();
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("admin/list")]
        public async Task<IHttpActionResult> GetAdminNotifications(int size, int pageNumber, bool unseenOnly, Guid? activeNotificationId = null)
        {
            return Ok(await _service.GetAllNotifications(size, pageNumber, LMS.Models.UserType.Admin, CurrentUser, unseenOnly, activeNotificationId));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("trainee/list")]
        public async Task<IHttpActionResult> GetTraineeNotifications(int size, int pageNumber, bool unseenOnly, Guid? activeNotificationId=null)
        {
            return Ok(await _service.GetAllNotifications(size, pageNumber, LMS.Models.UserType.Trainee, CurrentUser, unseenOnly, activeNotificationId));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("trainee/unseen-count")]
        public async Task<IHttpActionResult> GetTraineeUnseenCount()
        {
            return Ok(await _service.GetUnseenCount(LMS.Models.UserType.Trainee, CurrentUser));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("admin/unseen-count")]
        public async Task<IHttpActionResult> GetAdminUnseenCount()
        {
            return Ok(await _service.GetUnseenCount(LMS.Models.UserType.Trainee, CurrentUser));
        }

        [Authorize(Roles = "Admin, Trainee, Guest"), HttpGet, Route("seen/{id}")]
        public async Task<IHttpActionResult> NotificationSeen(Guid id)
        {
            return Ok(await _service.NotificationSeen(id));
        }

        [Authorize(Roles = "Admin, Trainee, Guest"), HttpPost, Route("delete/{id}")]
        public async Task<IHttpActionResult> DeleteNotification(Guid id)
        {
            return Ok(await _service.DeleteNotification(id));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpPost, Route("test/{notificationType}")]
        public async Task<IHttpActionResult> TestNotification(NotificationType notificationType)
        {
            return Ok(await _service.TestNotification(notificationType, CurrentUser));
        }
    }
}
