﻿using iTextSharp.text;
using iTextSharp.text.pdf;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Common
{
    public class PDFWriterEvents : PdfPageEventHelper
    {
        private BaseFont bf = null;
        private PdfContentByte cb;
        private PdfTemplate template;
        private readonly bool _showPagination;
        private readonly bool _showPrintingDate;
        private readonly int _timeZoneOffset;


        public PDFWriterEvents(bool showPagination, bool showPrintingDate) : base()
        {
            _showPagination = showPagination;
            _showPrintingDate = showPrintingDate;
        }

        public PDFWriterEvents(bool showPagination, bool showPrintingDate, int timeZoneOffset) : base()
        {
            _showPagination = showPagination;
            _showPrintingDate = showPrintingDate;
            _timeZoneOffset = timeZoneOffset;
        }

        public override void OnOpenDocument(PdfWriter writer, Document document)
        {
            try
            {
                bf = BaseFont.CreateFont(BaseFont.TIMES_ROMAN, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
                cb = writer.DirectContent;
                template = cb.CreateTemplate(50, 50);
            }
            catch (DocumentException de)
            {
                throw new Exception("OnOpenDocument Document Error: " + de.Message);
            }
            catch (System.IO.IOException ioe)
            {
                throw new Exception("OnOpenDocument IO Error: " + ioe.Message);
            }
        }

        public override void OnStartPage(PdfWriter writer, Document document)
        {
            base.OnStartPage(writer, document);
        }

        public override void OnEndPage(PdfWriter writer, Document document)
        {
            base.OnEndPage(writer, document);



            if (_showPagination)
            {
                string text = "Page " + writer.CurrentPageNumber + " of ";
                cb.SetRGBColorFill(100, 100, 100);
                cb.BeginText();
                cb.SetFontAndSize(bf, 8);
                cb.ShowTextAligned(PdfContentByte.ALIGN_RIGHT,
                    text,
                    document.PageSize.GetRight(40),
                    document.PageSize.GetBottom(15), 0);
                cb.EndText();
                cb.AddTemplate(template, document.PageSize.GetRight(40), document.PageSize.GetBottom(15));
            }

            if (_showPrintingDate)
            {
                cb.BeginText();
                cb.SetFontAndSize(BaseFont.CreateFont(BaseFont.TIMES_ROMAN, BaseFont.CP1252, BaseFont.NOT_EMBEDDED), 8);
                cb.SetColorFill(BaseColor.LIGHT_GRAY);
                cb.SetTextMatrix(document.PageSize.GetLeft(40), document.PageSize.GetBottom(15));
                cb.ShowText("Printed On: " + (_timeZoneOffset == 0 ? DateTime.Now.ToString("dd MMM, yyyy hh:mm:ss tt") : Utility.UTCToLocal(DateTime.UtcNow, _timeZoneOffset).ToString("dd-MMM-yyyy HH:mm:ss tt")));
                cb.EndText();

            }


        }

        public override void OnCloseDocument(PdfWriter writer, Document document)
        {
            base.OnCloseDocument(writer, document);


            if (_showPagination)
            {
                template.BeginText();
                template.SetFontAndSize(bf, 8);
                template.SetTextMatrix(0, 0);
                template.ShowTextAligned(PdfContentByte.ALIGN_LEFT, writer.PageNumber.ToString(), template.XTLM, template.YTLM, 0);
                template.EndText();
            }

        }

    }
}
