﻿using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using Microsoft.AspNet.Identity.Owin;
using Microsoft.Owin.Security;
using Brac.LMS.DB;
using Brac.LMS.ViewModels;
using Brac.LMS.App.Services;
using System.Web.Mvc;

namespace Brac.LMS.App.API.Controllers

{
    public class HomeController : Controller
    {
        [HttpGet]
        public ActionResult Index()
        {
            ViewBag.Title = "Home Page";

            return View();
        }
    }
}