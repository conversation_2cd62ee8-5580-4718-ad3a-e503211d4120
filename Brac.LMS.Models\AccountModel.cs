﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.ViewModels
{
    public class AccountModel
    {
        public AccountModel()
        {
            IsActive = true;
        }
        public string Id { get; set; }
        public string UserName { get; set; }

        [Required]
        [Display(Name = "Email")]
        public string Email { get; set; }

        [Required]
        [StringLength(100, ErrorMessage = "The {0} must be at least {2} characters long.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Display(Name = "Password")]
        public string Password { get; set; }

        [Required]
        public string FirstName { get; set; }

        [Required]
        public string LastName { get; set; }
        public long? UserGroupId { get; set; }
        public bool IsActive { get; set; }
        public string[] Roles { get; set; }
    }

    public class GuestAccountModel
    {
        public GuestAccountModel()
        {
            IsActive = true;
        }
        public string Id { get; set; }
        public string Email { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string PhoneNo { get; set; }
        public bool IsActive { get; set; }
        public string[] Roles { get; set; }
    }
}
