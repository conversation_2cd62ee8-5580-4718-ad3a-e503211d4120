{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { TraineeEvaluationTestPublishRoutingModule } from './trainee-evaluation-test-publish-routing.module';\nimport { SharedModule } from '../theme/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport let TraineeEvaluationTestPublishModule = /*#__PURE__*/(() => {\n  class TraineeEvaluationTestPublishModule {}\n\n  TraineeEvaluationTestPublishModule.ɵfac = function TraineeEvaluationTestPublishModule_Factory(t) {\n    return new (t || TraineeEvaluationTestPublishModule)();\n  };\n\n  TraineeEvaluationTestPublishModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TraineeEvaluationTestPublishModule\n  });\n  TraineeEvaluationTestPublishModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[CommonModule, TraineeEvaluationTestPublishRoutingModule, SharedModule]]\n  });\n  return TraineeEvaluationTestPublishModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}