{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  children: [{\n    path: 'static',\n    loadChildren: () => import('./theme-static/theme-static.module').then(module => module.ThemeStaticModule)\n  }, {\n    path: 'horizontal',\n    loadChildren: () => import('./theme-horizontal/theme-horizontal.module').then(module => module.ThemeHorizontalModule)\n  }]\n}];\nexport let LayoutRoutingModule = /*#__PURE__*/(() => {\n  class LayoutRoutingModule {}\n\n  LayoutRoutingModule.ɵfac = function LayoutRoutingModule_Factory(t) {\n    return new (t || LayoutRoutingModule)();\n  };\n\n  LayoutRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: LayoutRoutingModule\n  });\n  LayoutRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return LayoutRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}