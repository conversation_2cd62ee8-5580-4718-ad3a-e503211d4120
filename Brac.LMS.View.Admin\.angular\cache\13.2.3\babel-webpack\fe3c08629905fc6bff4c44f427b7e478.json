{"ast": null, "code": "import { Slice, Fragment, <PERSON>, Node } from 'prosemirror-model';\nimport { ReplaceStep, ReplaceAroundStep, Transform } from 'prosemirror-transform';\nvar classesById = Object.create(null); // ::- Superclass for editor selections. Every selection type should\n// extend this. Should not be instantiated directly.\n\nvar Selection = function Selection($anchor, $head, ranges) {\n  // :: [SelectionRange]\n  // The ranges covered by the selection.\n  this.ranges = ranges || [new SelectionRange($anchor.min($head), $anchor.max($head))]; // :: ResolvedPos\n  // The resolved anchor of the selection (the side that stays in\n  // place when the selection is modified).\n\n  this.$anchor = $anchor; // :: ResolvedPos\n  // The resolved head of the selection (the side that moves when\n  // the selection is modified).\n\n  this.$head = $head;\n};\n\nvar prototypeAccessors = {\n  anchor: {\n    configurable: true\n  },\n  head: {\n    configurable: true\n  },\n  from: {\n    configurable: true\n  },\n  to: {\n    configurable: true\n  },\n  $from: {\n    configurable: true\n  },\n  $to: {\n    configurable: true\n  },\n  empty: {\n    configurable: true\n  }\n}; // :: number\n// The selection's anchor, as an unresolved position.\n\nprototypeAccessors.anchor.get = function () {\n  return this.$anchor.pos;\n}; // :: number\n// The selection's head.\n\n\nprototypeAccessors.head.get = function () {\n  return this.$head.pos;\n}; // :: number\n// The lower bound of the selection's main range.\n\n\nprototypeAccessors.from.get = function () {\n  return this.$from.pos;\n}; // :: number\n// The upper bound of the selection's main range.\n\n\nprototypeAccessors.to.get = function () {\n  return this.$to.pos;\n}; // :: ResolvedPos\n// The resolved lowerbound of the selection's main range.\n\n\nprototypeAccessors.$from.get = function () {\n  return this.ranges[0].$from;\n}; // :: ResolvedPos\n// The resolved upper bound of the selection's main range.\n\n\nprototypeAccessors.$to.get = function () {\n  return this.ranges[0].$to;\n}; // :: bool\n// Indicates whether the selection contains any content.\n\n\nprototypeAccessors.empty.get = function () {\n  var ranges = this.ranges;\n\n  for (var i = 0; i < ranges.length; i++) {\n    if (ranges[i].$from.pos != ranges[i].$to.pos) {\n      return false;\n    }\n  }\n\n  return true;\n}; // eq:: (Selection) → bool\n// Test whether the selection is the same as another selection.\n// map:: (doc: Node, mapping: Mappable) → Selection\n// Map this selection through a [mappable](#transform.Mappable) thing. `doc`\n// should be the new document to which we are mapping.\n// :: () → Slice\n// Get the content of this selection as a slice.\n\n\nSelection.prototype.content = function content() {\n  return this.$from.node(0).slice(this.from, this.to, true);\n}; // :: (Transaction, ?Slice)\n// Replace the selection with a slice or, if no slice is given,\n// delete the selection. Will append to the given transaction.\n\n\nSelection.prototype.replace = function replace(tr, content) {\n  if (content === void 0) content = Slice.empty; // Put the new selection at the position after the inserted\n  // content. When that ended in an inline node, search backwards,\n  // to get the position after that node. If not, search forward.\n\n  var lastNode = content.content.lastChild,\n      lastParent = null;\n\n  for (var i = 0; i < content.openEnd; i++) {\n    lastParent = lastNode;\n    lastNode = lastNode.lastChild;\n  }\n\n  var mapFrom = tr.steps.length,\n      ranges = this.ranges;\n\n  for (var i$1 = 0; i$1 < ranges.length; i$1++) {\n    var ref = ranges[i$1];\n    var $from = ref.$from;\n    var $to = ref.$to;\n    var mapping = tr.mapping.slice(mapFrom);\n    tr.replaceRange(mapping.map($from.pos), mapping.map($to.pos), i$1 ? Slice.empty : content);\n\n    if (i$1 == 0) {\n      selectionToInsertionEnd(tr, mapFrom, (lastNode ? lastNode.isInline : lastParent && lastParent.isTextblock) ? -1 : 1);\n    }\n  }\n}; // :: (Transaction, Node)\n// Replace the selection with the given node, appending the changes\n// to the given transaction.\n\n\nSelection.prototype.replaceWith = function replaceWith(tr, node) {\n  var mapFrom = tr.steps.length,\n      ranges = this.ranges;\n\n  for (var i = 0; i < ranges.length; i++) {\n    var ref = ranges[i];\n    var $from = ref.$from;\n    var $to = ref.$to;\n    var mapping = tr.mapping.slice(mapFrom);\n    var from = mapping.map($from.pos),\n        to = mapping.map($to.pos);\n\n    if (i) {\n      tr.deleteRange(from, to);\n    } else {\n      tr.replaceRangeWith(from, to, node);\n      selectionToInsertionEnd(tr, mapFrom, node.isInline ? -1 : 1);\n    }\n  }\n}; // toJSON:: () → Object\n// Convert the selection to a JSON representation. When implementing\n// this for a custom selection class, make sure to give the object a\n// `type` property whose value matches the ID under which you\n// [registered](#state.Selection^jsonID) your class.\n// :: (ResolvedPos, number, ?bool) → ?Selection\n// Find a valid cursor or leaf node selection starting at the given\n// position and searching back if `dir` is negative, and forward if\n// positive. When `textOnly` is true, only consider cursor\n// selections. Will return null when no valid selection position is\n// found.\n\n\nSelection.findFrom = function findFrom($pos, dir, textOnly) {\n  var inner = $pos.parent.inlineContent ? new TextSelection($pos) : findSelectionIn($pos.node(0), $pos.parent, $pos.pos, $pos.index(), dir, textOnly);\n\n  if (inner) {\n    return inner;\n  }\n\n  for (var depth = $pos.depth - 1; depth >= 0; depth--) {\n    var found = dir < 0 ? findSelectionIn($pos.node(0), $pos.node(depth), $pos.before(depth + 1), $pos.index(depth), dir, textOnly) : findSelectionIn($pos.node(0), $pos.node(depth), $pos.after(depth + 1), $pos.index(depth) + 1, dir, textOnly);\n\n    if (found) {\n      return found;\n    }\n  }\n}; // :: (ResolvedPos, ?number) → Selection\n// Find a valid cursor or leaf node selection near the given\n// position. Searches forward first by default, but if `bias` is\n// negative, it will search backwards first.\n\n\nSelection.near = function near($pos, bias) {\n  if (bias === void 0) bias = 1;\n  return this.findFrom($pos, bias) || this.findFrom($pos, -bias) || new AllSelection($pos.node(0));\n}; // :: (Node) → Selection\n// Find the cursor or leaf node selection closest to the start of\n// the given document. Will return an\n// [`AllSelection`](#state.AllSelection) if no valid position\n// exists.\n\n\nSelection.atStart = function atStart(doc) {\n  return findSelectionIn(doc, doc, 0, 0, 1) || new AllSelection(doc);\n}; // :: (Node) → Selection\n// Find the cursor or leaf node selection closest to the end of the\n// given document.\n\n\nSelection.atEnd = function atEnd(doc) {\n  return findSelectionIn(doc, doc, doc.content.size, doc.childCount, -1) || new AllSelection(doc);\n}; // :: (Node, Object) → Selection\n// Deserialize the JSON representation of a selection. Must be\n// implemented for custom classes (as a static class method).\n\n\nSelection.fromJSON = function fromJSON(doc, json) {\n  if (!json || !json.type) {\n    throw new RangeError(\"Invalid input for Selection.fromJSON\");\n  }\n\n  var cls = classesById[json.type];\n\n  if (!cls) {\n    throw new RangeError(\"No selection type \" + json.type + \" defined\");\n  }\n\n  return cls.fromJSON(doc, json);\n}; // :: (string, constructor<Selection>)\n// To be able to deserialize selections from JSON, custom selection\n// classes must register themselves with an ID string, so that they\n// can be disambiguated. Try to pick something that's unlikely to\n// clash with classes from other modules.\n\n\nSelection.jsonID = function jsonID(id, selectionClass) {\n  if (id in classesById) {\n    throw new RangeError(\"Duplicate use of selection JSON ID \" + id);\n  }\n\n  classesById[id] = selectionClass;\n  selectionClass.prototype.jsonID = id;\n  return selectionClass;\n}; // :: () → SelectionBookmark\n// Get a [bookmark](#state.SelectionBookmark) for this selection,\n// which is a value that can be mapped without having access to a\n// current document, and later resolved to a real selection for a\n// given document again. (This is used mostly by the history to\n// track and restore old selections.) The default implementation of\n// this method just converts the selection to a text selection and\n// returns the bookmark for that.\n\n\nSelection.prototype.getBookmark = function getBookmark() {\n  return TextSelection.between(this.$anchor, this.$head).getBookmark();\n};\n\nObject.defineProperties(Selection.prototype, prototypeAccessors); // :: bool\n// Controls whether, when a selection of this type is active in the\n// browser, the selected range should be visible to the user. Defaults\n// to `true`.\n\nSelection.prototype.visible = true; // SelectionBookmark:: interface\n// A lightweight, document-independent representation of a selection.\n// You can define a custom bookmark type for a custom selection class\n// to make the history handle it well.\n//\n//   map:: (mapping: Mapping) → SelectionBookmark\n//   Map the bookmark through a set of changes.\n//\n//   resolve:: (doc: Node) → Selection\n//   Resolve the bookmark to a real selection again. This may need to\n//   do some error checking and may fall back to a default (usually\n//   [`TextSelection.between`](#state.TextSelection^between)) if\n//   mapping made the bookmark invalid.\n// ::- Represents a selected range in a document.\n\nvar SelectionRange = function SelectionRange($from, $to) {\n  // :: ResolvedPos\n  // The lower bound of the range.\n  this.$from = $from; // :: ResolvedPos\n  // The upper bound of the range.\n\n  this.$to = $to;\n}; // ::- A text selection represents a classical editor selection, with\n// a head (the moving side) and anchor (immobile side), both of which\n// point into textblock nodes. It can be empty (a regular cursor\n// position).\n\n\nvar TextSelection = /*@__PURE__*/function (Selection) {\n  function TextSelection($anchor, $head) {\n    if ($head === void 0) $head = $anchor;\n    Selection.call(this, $anchor, $head);\n  }\n\n  if (Selection) TextSelection.__proto__ = Selection;\n  TextSelection.prototype = Object.create(Selection && Selection.prototype);\n  TextSelection.prototype.constructor = TextSelection;\n  var prototypeAccessors$1 = {\n    $cursor: {\n      configurable: true\n    }\n  }; // :: ?ResolvedPos\n  // Returns a resolved position if this is a cursor selection (an\n  // empty text selection), and null otherwise.\n\n  prototypeAccessors$1.$cursor.get = function () {\n    return this.$anchor.pos == this.$head.pos ? this.$head : null;\n  };\n\n  TextSelection.prototype.map = function map(doc, mapping) {\n    var $head = doc.resolve(mapping.map(this.head));\n\n    if (!$head.parent.inlineContent) {\n      return Selection.near($head);\n    }\n\n    var $anchor = doc.resolve(mapping.map(this.anchor));\n    return new TextSelection($anchor.parent.inlineContent ? $anchor : $head, $head);\n  };\n\n  TextSelection.prototype.replace = function replace(tr, content) {\n    if (content === void 0) content = Slice.empty;\n    Selection.prototype.replace.call(this, tr, content);\n\n    if (content == Slice.empty) {\n      var marks = this.$from.marksAcross(this.$to);\n\n      if (marks) {\n        tr.ensureMarks(marks);\n      }\n    }\n  };\n\n  TextSelection.prototype.eq = function eq(other) {\n    return other instanceof TextSelection && other.anchor == this.anchor && other.head == this.head;\n  };\n\n  TextSelection.prototype.getBookmark = function getBookmark() {\n    return new TextBookmark(this.anchor, this.head);\n  };\n\n  TextSelection.prototype.toJSON = function toJSON() {\n    return {\n      type: \"text\",\n      anchor: this.anchor,\n      head: this.head\n    };\n  };\n\n  TextSelection.fromJSON = function fromJSON(doc, json) {\n    if (typeof json.anchor != \"number\" || typeof json.head != \"number\") {\n      throw new RangeError(\"Invalid input for TextSelection.fromJSON\");\n    }\n\n    return new TextSelection(doc.resolve(json.anchor), doc.resolve(json.head));\n  }; // :: (Node, number, ?number) → TextSelection\n  // Create a text selection from non-resolved positions.\n\n\n  TextSelection.create = function create(doc, anchor, head) {\n    if (head === void 0) head = anchor;\n    var $anchor = doc.resolve(anchor);\n    return new this($anchor, head == anchor ? $anchor : doc.resolve(head));\n  }; // :: (ResolvedPos, ResolvedPos, ?number) → Selection\n  // Return a text selection that spans the given positions or, if\n  // they aren't text positions, find a text selection near them.\n  // `bias` determines whether the method searches forward (default)\n  // or backwards (negative number) first. Will fall back to calling\n  // [`Selection.near`](#state.Selection^near) when the document\n  // doesn't contain a valid text position.\n\n\n  TextSelection.between = function between($anchor, $head, bias) {\n    var dPos = $anchor.pos - $head.pos;\n\n    if (!bias || dPos) {\n      bias = dPos >= 0 ? 1 : -1;\n    }\n\n    if (!$head.parent.inlineContent) {\n      var found = Selection.findFrom($head, bias, true) || Selection.findFrom($head, -bias, true);\n\n      if (found) {\n        $head = found.$head;\n      } else {\n        return Selection.near($head, bias);\n      }\n    }\n\n    if (!$anchor.parent.inlineContent) {\n      if (dPos == 0) {\n        $anchor = $head;\n      } else {\n        $anchor = (Selection.findFrom($anchor, -bias, true) || Selection.findFrom($anchor, bias, true)).$anchor;\n\n        if ($anchor.pos < $head.pos != dPos < 0) {\n          $anchor = $head;\n        }\n      }\n    }\n\n    return new TextSelection($anchor, $head);\n  };\n\n  Object.defineProperties(TextSelection.prototype, prototypeAccessors$1);\n  return TextSelection;\n}(Selection);\n\nSelection.jsonID(\"text\", TextSelection);\n\nvar TextBookmark = function TextBookmark(anchor, head) {\n  this.anchor = anchor;\n  this.head = head;\n};\n\nTextBookmark.prototype.map = function map(mapping) {\n  return new TextBookmark(mapping.map(this.anchor), mapping.map(this.head));\n};\n\nTextBookmark.prototype.resolve = function resolve(doc) {\n  return TextSelection.between(doc.resolve(this.anchor), doc.resolve(this.head));\n}; // ::- A node selection is a selection that points at a single node.\n// All nodes marked [selectable](#model.NodeSpec.selectable) can be\n// the target of a node selection. In such a selection, `from` and\n// `to` point directly before and after the selected node, `anchor`\n// equals `from`, and `head` equals `to`..\n\n\nvar NodeSelection = /*@__PURE__*/function (Selection) {\n  function NodeSelection($pos) {\n    var node = $pos.nodeAfter;\n    var $end = $pos.node(0).resolve($pos.pos + node.nodeSize);\n    Selection.call(this, $pos, $end); // :: Node The selected node.\n\n    this.node = node;\n  }\n\n  if (Selection) NodeSelection.__proto__ = Selection;\n  NodeSelection.prototype = Object.create(Selection && Selection.prototype);\n  NodeSelection.prototype.constructor = NodeSelection;\n\n  NodeSelection.prototype.map = function map(doc, mapping) {\n    var ref = mapping.mapResult(this.anchor);\n    var deleted = ref.deleted;\n    var pos = ref.pos;\n    var $pos = doc.resolve(pos);\n\n    if (deleted) {\n      return Selection.near($pos);\n    }\n\n    return new NodeSelection($pos);\n  };\n\n  NodeSelection.prototype.content = function content() {\n    return new Slice(Fragment.from(this.node), 0, 0);\n  };\n\n  NodeSelection.prototype.eq = function eq(other) {\n    return other instanceof NodeSelection && other.anchor == this.anchor;\n  };\n\n  NodeSelection.prototype.toJSON = function toJSON() {\n    return {\n      type: \"node\",\n      anchor: this.anchor\n    };\n  };\n\n  NodeSelection.prototype.getBookmark = function getBookmark() {\n    return new NodeBookmark(this.anchor);\n  };\n\n  NodeSelection.fromJSON = function fromJSON(doc, json) {\n    if (typeof json.anchor != \"number\") {\n      throw new RangeError(\"Invalid input for NodeSelection.fromJSON\");\n    }\n\n    return new NodeSelection(doc.resolve(json.anchor));\n  }; // :: (Node, number) → NodeSelection\n  // Create a node selection from non-resolved positions.\n\n\n  NodeSelection.create = function create(doc, from) {\n    return new this(doc.resolve(from));\n  }; // :: (Node) → bool\n  // Determines whether the given node may be selected as a node\n  // selection.\n\n\n  NodeSelection.isSelectable = function isSelectable(node) {\n    return !node.isText && node.type.spec.selectable !== false;\n  };\n\n  return NodeSelection;\n}(Selection);\n\nNodeSelection.prototype.visible = false;\nSelection.jsonID(\"node\", NodeSelection);\n\nvar NodeBookmark = function NodeBookmark(anchor) {\n  this.anchor = anchor;\n};\n\nNodeBookmark.prototype.map = function map(mapping) {\n  var ref = mapping.mapResult(this.anchor);\n  var deleted = ref.deleted;\n  var pos = ref.pos;\n  return deleted ? new TextBookmark(pos, pos) : new NodeBookmark(pos);\n};\n\nNodeBookmark.prototype.resolve = function resolve(doc) {\n  var $pos = doc.resolve(this.anchor),\n      node = $pos.nodeAfter;\n\n  if (node && NodeSelection.isSelectable(node)) {\n    return new NodeSelection($pos);\n  }\n\n  return Selection.near($pos);\n}; // ::- A selection type that represents selecting the whole document\n// (which can not necessarily be expressed with a text selection, when\n// there are for example leaf block nodes at the start or end of the\n// document).\n\n\nvar AllSelection = /*@__PURE__*/function (Selection) {\n  function AllSelection(doc) {\n    Selection.call(this, doc.resolve(0), doc.resolve(doc.content.size));\n  }\n\n  if (Selection) AllSelection.__proto__ = Selection;\n  AllSelection.prototype = Object.create(Selection && Selection.prototype);\n  AllSelection.prototype.constructor = AllSelection;\n\n  AllSelection.prototype.replace = function replace(tr, content) {\n    if (content === void 0) content = Slice.empty;\n\n    if (content == Slice.empty) {\n      tr.delete(0, tr.doc.content.size);\n      var sel = Selection.atStart(tr.doc);\n\n      if (!sel.eq(tr.selection)) {\n        tr.setSelection(sel);\n      }\n    } else {\n      Selection.prototype.replace.call(this, tr, content);\n    }\n  };\n\n  AllSelection.prototype.toJSON = function toJSON() {\n    return {\n      type: \"all\"\n    };\n  };\n\n  AllSelection.fromJSON = function fromJSON(doc) {\n    return new AllSelection(doc);\n  };\n\n  AllSelection.prototype.map = function map(doc) {\n    return new AllSelection(doc);\n  };\n\n  AllSelection.prototype.eq = function eq(other) {\n    return other instanceof AllSelection;\n  };\n\n  AllSelection.prototype.getBookmark = function getBookmark() {\n    return AllBookmark;\n  };\n\n  return AllSelection;\n}(Selection);\n\nSelection.jsonID(\"all\", AllSelection);\nvar AllBookmark = {\n  map: function map() {\n    return this;\n  },\n  resolve: function resolve(doc) {\n    return new AllSelection(doc);\n  }\n}; // FIXME we'll need some awareness of text direction when scanning for selections\n// Try to find a selection inside the given node. `pos` points at the\n// position where the search starts. When `text` is true, only return\n// text selections.\n\nfunction findSelectionIn(doc, node, pos, index, dir, text) {\n  if (node.inlineContent) {\n    return TextSelection.create(doc, pos);\n  }\n\n  for (var i = index - (dir > 0 ? 0 : 1); dir > 0 ? i < node.childCount : i >= 0; i += dir) {\n    var child = node.child(i);\n\n    if (!child.isAtom) {\n      var inner = findSelectionIn(doc, child, pos + dir, dir < 0 ? child.childCount : 0, dir, text);\n\n      if (inner) {\n        return inner;\n      }\n    } else if (!text && NodeSelection.isSelectable(child)) {\n      return NodeSelection.create(doc, pos - (dir < 0 ? child.nodeSize : 0));\n    }\n\n    pos += child.nodeSize * dir;\n  }\n}\n\nfunction selectionToInsertionEnd(tr, startLen, bias) {\n  var last = tr.steps.length - 1;\n\n  if (last < startLen) {\n    return;\n  }\n\n  var step = tr.steps[last];\n\n  if (!(step instanceof ReplaceStep || step instanceof ReplaceAroundStep)) {\n    return;\n  }\n\n  var map = tr.mapping.maps[last],\n      end;\n  map.forEach(function (_from, _to, _newFrom, newTo) {\n    if (end == null) {\n      end = newTo;\n    }\n  });\n  tr.setSelection(Selection.near(tr.doc.resolve(end), bias));\n}\n\nvar UPDATED_SEL = 1,\n    UPDATED_MARKS = 2,\n    UPDATED_SCROLL = 4; // ::- An editor state transaction, which can be applied to a state to\n// create an updated state. Use\n// [`EditorState.tr`](#state.EditorState.tr) to create an instance.\n//\n// Transactions track changes to the document (they are a subclass of\n// [`Transform`](#transform.Transform)), but also other state changes,\n// like selection updates and adjustments of the set of [stored\n// marks](#state.EditorState.storedMarks). In addition, you can store\n// metadata properties in a transaction, which are extra pieces of\n// information that client code or plugins can use to describe what a\n// transacion represents, so that they can update their [own\n// state](#state.StateField) accordingly.\n//\n// The [editor view](#view.EditorView) uses a few metadata properties:\n// it will attach a property `\"pointer\"` with the value `true` to\n// selection transactions directly caused by mouse or touch input, and\n// a `\"uiEvent\"` property of that may be `\"paste\"`, `\"cut\"`, or `\"drop\"`.\n\nvar Transaction = /*@__PURE__*/function (Transform) {\n  function Transaction(state) {\n    Transform.call(this, state.doc); // :: number\n    // The timestamp associated with this transaction, in the same\n    // format as `Date.now()`.\n\n    this.time = Date.now();\n    this.curSelection = state.selection; // The step count for which the current selection is valid.\n\n    this.curSelectionFor = 0; // :: ?[Mark]\n    // The stored marks set by this transaction, if any.\n\n    this.storedMarks = state.storedMarks; // Bitfield to track which aspects of the state were updated by\n    // this transaction.\n\n    this.updated = 0; // Object used to store metadata properties for the transaction.\n\n    this.meta = Object.create(null);\n  }\n\n  if (Transform) Transaction.__proto__ = Transform;\n  Transaction.prototype = Object.create(Transform && Transform.prototype);\n  Transaction.prototype.constructor = Transaction;\n  var prototypeAccessors = {\n    selection: {\n      configurable: true\n    },\n    selectionSet: {\n      configurable: true\n    },\n    storedMarksSet: {\n      configurable: true\n    },\n    isGeneric: {\n      configurable: true\n    },\n    scrolledIntoView: {\n      configurable: true\n    }\n  }; // :: Selection\n  // The transaction's current selection. This defaults to the editor\n  // selection [mapped](#state.Selection.map) through the steps in the\n  // transaction, but can be overwritten with\n  // [`setSelection`](#state.Transaction.setSelection).\n\n  prototypeAccessors.selection.get = function () {\n    if (this.curSelectionFor < this.steps.length) {\n      this.curSelection = this.curSelection.map(this.doc, this.mapping.slice(this.curSelectionFor));\n      this.curSelectionFor = this.steps.length;\n    }\n\n    return this.curSelection;\n  }; // :: (Selection) → Transaction\n  // Update the transaction's current selection. Will determine the\n  // selection that the editor gets when the transaction is applied.\n\n\n  Transaction.prototype.setSelection = function setSelection(selection) {\n    if (selection.$from.doc != this.doc) {\n      throw new RangeError(\"Selection passed to setSelection must point at the current document\");\n    }\n\n    this.curSelection = selection;\n    this.curSelectionFor = this.steps.length;\n    this.updated = (this.updated | UPDATED_SEL) & ~UPDATED_MARKS;\n    this.storedMarks = null;\n    return this;\n  }; // :: bool\n  // Whether the selection was explicitly updated by this transaction.\n\n\n  prototypeAccessors.selectionSet.get = function () {\n    return (this.updated & UPDATED_SEL) > 0;\n  }; // :: (?[Mark]) → Transaction\n  // Set the current stored marks.\n\n\n  Transaction.prototype.setStoredMarks = function setStoredMarks(marks) {\n    this.storedMarks = marks;\n    this.updated |= UPDATED_MARKS;\n    return this;\n  }; // :: ([Mark]) → Transaction\n  // Make sure the current stored marks or, if that is null, the marks\n  // at the selection, match the given set of marks. Does nothing if\n  // this is already the case.\n\n\n  Transaction.prototype.ensureMarks = function ensureMarks(marks) {\n    if (!Mark.sameSet(this.storedMarks || this.selection.$from.marks(), marks)) {\n      this.setStoredMarks(marks);\n    }\n\n    return this;\n  }; // :: (Mark) → Transaction\n  // Add a mark to the set of stored marks.\n\n\n  Transaction.prototype.addStoredMark = function addStoredMark(mark) {\n    return this.ensureMarks(mark.addToSet(this.storedMarks || this.selection.$head.marks()));\n  }; // :: (union<Mark, MarkType>) → Transaction\n  // Remove a mark or mark type from the set of stored marks.\n\n\n  Transaction.prototype.removeStoredMark = function removeStoredMark(mark) {\n    return this.ensureMarks(mark.removeFromSet(this.storedMarks || this.selection.$head.marks()));\n  }; // :: bool\n  // Whether the stored marks were explicitly set for this transaction.\n\n\n  prototypeAccessors.storedMarksSet.get = function () {\n    return (this.updated & UPDATED_MARKS) > 0;\n  };\n\n  Transaction.prototype.addStep = function addStep(step, doc) {\n    Transform.prototype.addStep.call(this, step, doc);\n    this.updated = this.updated & ~UPDATED_MARKS;\n    this.storedMarks = null;\n  }; // :: (number) → Transaction\n  // Update the timestamp for the transaction.\n\n\n  Transaction.prototype.setTime = function setTime(time) {\n    this.time = time;\n    return this;\n  }; // :: (Slice) → Transaction\n  // Replace the current selection with the given slice.\n\n\n  Transaction.prototype.replaceSelection = function replaceSelection(slice) {\n    this.selection.replace(this, slice);\n    return this;\n  }; // :: (Node, ?bool) → Transaction\n  // Replace the selection with the given node. When `inheritMarks` is\n  // true and the content is inline, it inherits the marks from the\n  // place where it is inserted.\n\n\n  Transaction.prototype.replaceSelectionWith = function replaceSelectionWith(node, inheritMarks) {\n    var selection = this.selection;\n\n    if (inheritMarks !== false) {\n      node = node.mark(this.storedMarks || (selection.empty ? selection.$from.marks() : selection.$from.marksAcross(selection.$to) || Mark.none));\n    }\n\n    selection.replaceWith(this, node);\n    return this;\n  }; // :: () → Transaction\n  // Delete the selection.\n\n\n  Transaction.prototype.deleteSelection = function deleteSelection() {\n    this.selection.replace(this);\n    return this;\n  }; // :: (string, from: ?number, to: ?number) → Transaction\n  // Replace the given range, or the selection if no range is given,\n  // with a text node containing the given string.\n\n\n  Transaction.prototype.insertText = function insertText(text, from, to) {\n    if (to === void 0) to = from;\n    var schema = this.doc.type.schema;\n\n    if (from == null) {\n      if (!text) {\n        return this.deleteSelection();\n      }\n\n      return this.replaceSelectionWith(schema.text(text), true);\n    } else {\n      if (!text) {\n        return this.deleteRange(from, to);\n      }\n\n      var marks = this.storedMarks;\n\n      if (!marks) {\n        var $from = this.doc.resolve(from);\n        marks = to == from ? $from.marks() : $from.marksAcross(this.doc.resolve(to));\n      }\n\n      this.replaceRangeWith(from, to, schema.text(text, marks));\n\n      if (!this.selection.empty) {\n        this.setSelection(Selection.near(this.selection.$to));\n      }\n\n      return this;\n    }\n  }; // :: (union<string, Plugin, PluginKey>, any) → Transaction\n  // Store a metadata property in this transaction, keyed either by\n  // name or by plugin.\n\n\n  Transaction.prototype.setMeta = function setMeta(key, value) {\n    this.meta[typeof key == \"string\" ? key : key.key] = value;\n    return this;\n  }; // :: (union<string, Plugin, PluginKey>) → any\n  // Retrieve a metadata property for a given name or plugin.\n\n\n  Transaction.prototype.getMeta = function getMeta(key) {\n    return this.meta[typeof key == \"string\" ? key : key.key];\n  }; // :: bool\n  // Returns true if this transaction doesn't contain any metadata,\n  // and can thus safely be extended.\n\n\n  prototypeAccessors.isGeneric.get = function () {\n    for (var _ in this.meta) {\n      return false;\n    }\n\n    return true;\n  }; // :: () → Transaction\n  // Indicate that the editor should scroll the selection into view\n  // when updated to the state produced by this transaction.\n\n\n  Transaction.prototype.scrollIntoView = function scrollIntoView() {\n    this.updated |= UPDATED_SCROLL;\n    return this;\n  };\n\n  prototypeAccessors.scrolledIntoView.get = function () {\n    return (this.updated & UPDATED_SCROLL) > 0;\n  };\n\n  Object.defineProperties(Transaction.prototype, prototypeAccessors);\n  return Transaction;\n}(Transform);\n\nfunction bind(f, self) {\n  return !self || !f ? f : f.bind(self);\n}\n\nvar FieldDesc = function FieldDesc(name, desc, self) {\n  this.name = name;\n  this.init = bind(desc.init, self);\n  this.apply = bind(desc.apply, self);\n};\n\nvar baseFields = [new FieldDesc(\"doc\", {\n  init: function init(config) {\n    return config.doc || config.schema.topNodeType.createAndFill();\n  },\n  apply: function apply(tr) {\n    return tr.doc;\n  }\n}), new FieldDesc(\"selection\", {\n  init: function init(config, instance) {\n    return config.selection || Selection.atStart(instance.doc);\n  },\n  apply: function apply(tr) {\n    return tr.selection;\n  }\n}), new FieldDesc(\"storedMarks\", {\n  init: function init(config) {\n    return config.storedMarks || null;\n  },\n  apply: function apply(tr, _marks, _old, state) {\n    return state.selection.$cursor ? tr.storedMarks : null;\n  }\n}), new FieldDesc(\"scrollToSelection\", {\n  init: function init() {\n    return 0;\n  },\n  apply: function apply(tr, prev) {\n    return tr.scrolledIntoView ? prev + 1 : prev;\n  }\n})]; // Object wrapping the part of a state object that stays the same\n// across transactions. Stored in the state's `config` property.\n\nvar Configuration = function Configuration(schema, plugins) {\n  var this$1 = this;\n  this.schema = schema;\n  this.fields = baseFields.concat();\n  this.plugins = [];\n  this.pluginsByKey = Object.create(null);\n\n  if (plugins) {\n    plugins.forEach(function (plugin) {\n      if (this$1.pluginsByKey[plugin.key]) {\n        throw new RangeError(\"Adding different instances of a keyed plugin (\" + plugin.key + \")\");\n      }\n\n      this$1.plugins.push(plugin);\n      this$1.pluginsByKey[plugin.key] = plugin;\n\n      if (plugin.spec.state) {\n        this$1.fields.push(new FieldDesc(plugin.key, plugin.spec.state, plugin));\n      }\n    });\n  }\n}; // ::- The state of a ProseMirror editor is represented by an object\n// of this type. A state is a persistent data structure—it isn't\n// updated, but rather a new state value is computed from an old one\n// using the [`apply`](#state.EditorState.apply) method.\n//\n// A state holds a number of built-in fields, and plugins can\n// [define](#state.PluginSpec.state) additional fields.\n\n\nvar EditorState = function EditorState(config) {\n  this.config = config;\n};\n\nvar prototypeAccessors$1 = {\n  schema: {\n    configurable: true\n  },\n  plugins: {\n    configurable: true\n  },\n  tr: {\n    configurable: true\n  }\n}; // doc:: Node\n// The current document.\n// selection:: Selection\n// The selection.\n// storedMarks:: ?[Mark]\n// A set of marks to apply to the next input. Will be null when\n// no explicit marks have been set.\n// :: Schema\n// The schema of the state's document.\n\nprototypeAccessors$1.schema.get = function () {\n  return this.config.schema;\n}; // :: [Plugin]\n// The plugins that are active in this state.\n\n\nprototypeAccessors$1.plugins.get = function () {\n  return this.config.plugins;\n}; // :: (Transaction) → EditorState\n// Apply the given transaction to produce a new state.\n\n\nEditorState.prototype.apply = function apply(tr) {\n  return this.applyTransaction(tr).state;\n}; // : (Transaction) → bool\n\n\nEditorState.prototype.filterTransaction = function filterTransaction(tr, ignore) {\n  if (ignore === void 0) ignore = -1;\n\n  for (var i = 0; i < this.config.plugins.length; i++) {\n    if (i != ignore) {\n      var plugin = this.config.plugins[i];\n\n      if (plugin.spec.filterTransaction && !plugin.spec.filterTransaction.call(plugin, tr, this)) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}; // :: (Transaction) → {state: EditorState, transactions: [Transaction]}\n// Verbose variant of [`apply`](#state.EditorState.apply) that\n// returns the precise transactions that were applied (which might\n// be influenced by the [transaction\n// hooks](#state.PluginSpec.filterTransaction) of\n// plugins) along with the new state.\n\n\nEditorState.prototype.applyTransaction = function applyTransaction(rootTr) {\n  if (!this.filterTransaction(rootTr)) {\n    return {\n      state: this,\n      transactions: []\n    };\n  }\n\n  var trs = [rootTr],\n      newState = this.applyInner(rootTr),\n      seen = null; // This loop repeatedly gives plugins a chance to respond to\n  // transactions as new transactions are added, making sure to only\n  // pass the transactions the plugin did not see before.\n\n  for (;;) {\n    var haveNew = false;\n\n    for (var i = 0; i < this.config.plugins.length; i++) {\n      var plugin = this.config.plugins[i];\n\n      if (plugin.spec.appendTransaction) {\n        var n = seen ? seen[i].n : 0,\n            oldState = seen ? seen[i].state : this;\n        var tr = n < trs.length && plugin.spec.appendTransaction.call(plugin, n ? trs.slice(n) : trs, oldState, newState);\n\n        if (tr && newState.filterTransaction(tr, i)) {\n          tr.setMeta(\"appendedTransaction\", rootTr);\n\n          if (!seen) {\n            seen = [];\n\n            for (var j = 0; j < this.config.plugins.length; j++) {\n              seen.push(j < i ? {\n                state: newState,\n                n: trs.length\n              } : {\n                state: this,\n                n: 0\n              });\n            }\n          }\n\n          trs.push(tr);\n          newState = newState.applyInner(tr);\n          haveNew = true;\n        }\n\n        if (seen) {\n          seen[i] = {\n            state: newState,\n            n: trs.length\n          };\n        }\n      }\n    }\n\n    if (!haveNew) {\n      return {\n        state: newState,\n        transactions: trs\n      };\n    }\n  }\n}; // : (Transaction) → EditorState\n\n\nEditorState.prototype.applyInner = function applyInner(tr) {\n  if (!tr.before.eq(this.doc)) {\n    throw new RangeError(\"Applying a mismatched transaction\");\n  }\n\n  var newInstance = new EditorState(this.config),\n      fields = this.config.fields;\n\n  for (var i = 0; i < fields.length; i++) {\n    var field = fields[i];\n    newInstance[field.name] = field.apply(tr, this[field.name], this, newInstance);\n  }\n\n  for (var i$1 = 0; i$1 < applyListeners.length; i$1++) {\n    applyListeners[i$1](this, tr, newInstance);\n  }\n\n  return newInstance;\n}; // :: Transaction\n// Start a [transaction](#state.Transaction) from this state.\n\n\nprototypeAccessors$1.tr.get = function () {\n  return new Transaction(this);\n}; // :: (Object) → EditorState\n// Create a new state.\n//\n// config::- Configuration options. Must contain `schema` or `doc` (or both).\n//\n//    schema:: ?Schema\n//    The schema to use (only relevant if no `doc` is specified).\n//\n//    doc:: ?Node\n//    The starting document.\n//\n//    selection:: ?Selection\n//    A valid selection in the document.\n//\n//    storedMarks:: ?[Mark]\n//    The initial set of [stored marks](#state.EditorState.storedMarks).\n//\n//    plugins:: ?[Plugin]\n//    The plugins that should be active in this state.\n\n\nEditorState.create = function create(config) {\n  var $config = new Configuration(config.doc ? config.doc.type.schema : config.schema, config.plugins);\n  var instance = new EditorState($config);\n\n  for (var i = 0; i < $config.fields.length; i++) {\n    instance[$config.fields[i].name] = $config.fields[i].init(config, instance);\n  }\n\n  return instance;\n}; // :: (Object) → EditorState\n// Create a new state based on this one, but with an adjusted set of\n// active plugins. State fields that exist in both sets of plugins\n// are kept unchanged. Those that no longer exist are dropped, and\n// those that are new are initialized using their\n// [`init`](#state.StateField.init) method, passing in the new\n// configuration object..\n//\n// config::- configuration options\n//\n//   plugins:: [Plugin]\n//   New set of active plugins.\n\n\nEditorState.prototype.reconfigure = function reconfigure(config) {\n  var $config = new Configuration(this.schema, config.plugins);\n  var fields = $config.fields,\n      instance = new EditorState($config);\n\n  for (var i = 0; i < fields.length; i++) {\n    var name = fields[i].name;\n    instance[name] = this.hasOwnProperty(name) ? this[name] : fields[i].init(config, instance);\n  }\n\n  return instance;\n}; // :: (?union<Object<Plugin>, string, number>) → Object\n// Serialize this state to JSON. If you want to serialize the state\n// of plugins, pass an object mapping property names to use in the\n// resulting JSON object to plugin objects. The argument may also be\n// a string or number, in which case it is ignored, to support the\n// way `JSON.stringify` calls `toString` methods.\n\n\nEditorState.prototype.toJSON = function toJSON(pluginFields) {\n  var result = {\n    doc: this.doc.toJSON(),\n    selection: this.selection.toJSON()\n  };\n\n  if (this.storedMarks) {\n    result.storedMarks = this.storedMarks.map(function (m) {\n      return m.toJSON();\n    });\n  }\n\n  if (pluginFields && typeof pluginFields == 'object') {\n    for (var prop in pluginFields) {\n      if (prop == \"doc\" || prop == \"selection\") {\n        throw new RangeError(\"The JSON fields `doc` and `selection` are reserved\");\n      }\n\n      var plugin = pluginFields[prop],\n          state = plugin.spec.state;\n\n      if (state && state.toJSON) {\n        result[prop] = state.toJSON.call(plugin, this[plugin.key]);\n      }\n    }\n  }\n\n  return result;\n}; // :: (Object, Object, ?Object<Plugin>) → EditorState\n// Deserialize a JSON representation of a state. `config` should\n// have at least a `schema` field, and should contain array of\n// plugins to initialize the state with. `pluginFields` can be used\n// to deserialize the state of plugins, by associating plugin\n// instances with the property names they use in the JSON object.\n//\n// config::- configuration options\n//\n//   schema:: Schema\n//   The schema to use.\n//\n//   plugins:: ?[Plugin]\n//   The set of active plugins.\n\n\nEditorState.fromJSON = function fromJSON(config, json, pluginFields) {\n  if (!json) {\n    throw new RangeError(\"Invalid input for EditorState.fromJSON\");\n  }\n\n  if (!config.schema) {\n    throw new RangeError(\"Required config field 'schema' missing\");\n  }\n\n  var $config = new Configuration(config.schema, config.plugins);\n  var instance = new EditorState($config);\n  $config.fields.forEach(function (field) {\n    if (field.name == \"doc\") {\n      instance.doc = Node.fromJSON(config.schema, json.doc);\n    } else if (field.name == \"selection\") {\n      instance.selection = Selection.fromJSON(instance.doc, json.selection);\n    } else if (field.name == \"storedMarks\") {\n      if (json.storedMarks) {\n        instance.storedMarks = json.storedMarks.map(config.schema.markFromJSON);\n      }\n    } else {\n      if (pluginFields) {\n        for (var prop in pluginFields) {\n          var plugin = pluginFields[prop],\n              state = plugin.spec.state;\n\n          if (plugin.key == field.name && state && state.fromJSON && Object.prototype.hasOwnProperty.call(json, prop)) {\n            // This field belongs to a plugin mapped to a JSON field, read it from there.\n            instance[field.name] = state.fromJSON.call(plugin, config, json[prop], instance);\n            return;\n          }\n        }\n      }\n\n      instance[field.name] = field.init(config, instance);\n    }\n  });\n  return instance;\n}; // Kludge to allow the view to track mappings between different\n// instances of a state.\n//\n// FIXME this is no longer needed as of prosemirror-view 1.9.0,\n// though due to backwards-compat we should probably keep it around\n// for a while (if only as a no-op)\n\n\nEditorState.addApplyListener = function addApplyListener(f) {\n  applyListeners.push(f);\n};\n\nEditorState.removeApplyListener = function removeApplyListener(f) {\n  var found = applyListeners.indexOf(f);\n\n  if (found > -1) {\n    applyListeners.splice(found, 1);\n  }\n};\n\nObject.defineProperties(EditorState.prototype, prototypeAccessors$1);\nvar applyListeners = []; // PluginSpec:: interface\n//\n// This is the type passed to the [`Plugin`](#state.Plugin)\n// constructor. It provides a definition for a plugin.\n//\n//   props:: ?EditorProps\n//   The [view props](#view.EditorProps) added by this plugin. Props\n//   that are functions will be bound to have the plugin instance as\n//   their `this` binding.\n//\n//   state:: ?StateField<any>\n//   Allows a plugin to define a [state field](#state.StateField), an\n//   extra slot in the state object in which it can keep its own data.\n//\n//   key:: ?PluginKey\n//   Can be used to make this a keyed plugin. You can have only one\n//   plugin with a given key in a given state, but it is possible to\n//   access the plugin's configuration and state through the key,\n//   without having access to the plugin instance object.\n//\n//   view:: ?(EditorView) → Object\n//   When the plugin needs to interact with the editor view, or\n//   set something up in the DOM, use this field. The function\n//   will be called when the plugin's state is associated with an\n//   editor view.\n//\n//     return::-\n//     Should return an object with the following optional\n//     properties:\n//\n//       update:: ?(view: EditorView, prevState: EditorState)\n//       Called whenever the view's state is updated.\n//\n//       destroy:: ?()\n//       Called when the view is destroyed or receives a state\n//       with different plugins.\n//\n//   filterTransaction:: ?(Transaction, EditorState) → bool\n//   When present, this will be called before a transaction is\n//   applied by the state, allowing the plugin to cancel it (by\n//   returning false).\n//\n//   appendTransaction:: ?(transactions: [Transaction], oldState: EditorState, newState: EditorState) → ?Transaction\n//   Allows the plugin to append another transaction to be applied\n//   after the given array of transactions. When another plugin\n//   appends a transaction after this was called, it is called again\n//   with the new state and new transactions—but only the new\n//   transactions, i.e. it won't be passed transactions that it\n//   already saw.\n\nfunction bindProps(obj, self, target) {\n  for (var prop in obj) {\n    var val = obj[prop];\n\n    if (val instanceof Function) {\n      val = val.bind(self);\n    } else if (prop == \"handleDOMEvents\") {\n      val = bindProps(val, self, {});\n    }\n\n    target[prop] = val;\n  }\n\n  return target;\n} // ::- Plugins bundle functionality that can be added to an editor.\n// They are part of the [editor state](#state.EditorState) and\n// may influence that state and the view that contains it.\n\n\nvar Plugin = function Plugin(spec) {\n  // :: EditorProps\n  // The [props](#view.EditorProps) exported by this plugin.\n  this.props = {};\n\n  if (spec.props) {\n    bindProps(spec.props, this, this.props);\n  } // :: Object\n  // The plugin's [spec object](#state.PluginSpec).\n\n\n  this.spec = spec;\n  this.key = spec.key ? spec.key.key : createKey(\"plugin\");\n}; // :: (EditorState) → any\n// Extract the plugin's state field from an editor state.\n\n\nPlugin.prototype.getState = function getState(state) {\n  return state[this.key];\n}; // StateField:: interface<T>\n// A plugin spec may provide a state field (under its\n// [`state`](#state.PluginSpec.state) property) of this type, which\n// describes the state it wants to keep. Functions provided here are\n// always called with the plugin instance as their `this` binding.\n//\n//   init:: (config: Object, instance: EditorState) → T\n//   Initialize the value of the field. `config` will be the object\n//   passed to [`EditorState.create`](#state.EditorState^create). Note\n//   that `instance` is a half-initialized state instance, and will\n//   not have values for plugin fields initialized after this one.\n//\n//   apply:: (tr: Transaction, value: T, oldState: EditorState, newState: EditorState) → T\n//   Apply the given transaction to this state field, producing a new\n//   field value. Note that the `newState` argument is again a partially\n//   constructed state does not yet contain the state from plugins\n//   coming after this one.\n//\n//   toJSON:: ?(value: T) → *\n//   Convert this field to JSON. Optional, can be left off to disable\n//   JSON serialization for the field.\n//\n//   fromJSON:: ?(config: Object, value: *, state: EditorState) → T\n//   Deserialize the JSON representation of this field. Note that the\n//   `state` argument is again a half-initialized state.\n\n\nvar keys = Object.create(null);\n\nfunction createKey(name) {\n  if (name in keys) {\n    return name + \"$\" + ++keys[name];\n  }\n\n  keys[name] = 0;\n  return name + \"$\";\n} // ::- A key is used to [tag](#state.PluginSpec.key)\n// plugins in a way that makes it possible to find them, given an\n// editor state. Assigning a key does mean only one plugin of that\n// type can be active in a state.\n\n\nvar PluginKey = function PluginKey(name) {\n  if (name === void 0) name = \"key\";\n  this.key = createKey(name);\n}; // :: (EditorState) → ?Plugin\n// Get the active plugin with this key, if any, from an editor\n// state.\n\n\nPluginKey.prototype.get = function get(state) {\n  return state.config.pluginsByKey[this.key];\n}; // :: (EditorState) → ?any\n// Get the plugin's state from an editor state.\n\n\nPluginKey.prototype.getState = function getState(state) {\n  return state[this.key];\n};\n\nexport { AllSelection, EditorState, NodeSelection, Plugin, PluginKey, Selection, SelectionRange, TextSelection, Transaction }; //# sourceMappingURL=index.es.js.map", "map": null, "metadata": {}, "sourceType": "module"}