{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nexport class QueueAction extends AsyncAction {\n  constructor(scheduler, work) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n  }\n\n  schedule(state, delay = 0) {\n    if (delay > 0) {\n      return super.schedule(state, delay);\n    }\n\n    this.delay = delay;\n    this.state = state;\n    this.scheduler.flush(this);\n    return this;\n  }\n\n  execute(state, delay) {\n    return delay > 0 || this.closed ? super.execute(state, delay) : this._execute(state, delay);\n  }\n\n  requestAsyncId(scheduler, id, delay = 0) {\n    if (delay != null && delay > 0 || delay == null && this.delay > 0) {\n      return super.requestAsyncId(scheduler, id, delay);\n    }\n\n    return scheduler.flush(this);\n  }\n\n} //# sourceMappingURL=QueueAction.js.map", "map": null, "metadata": {}, "sourceType": "module"}