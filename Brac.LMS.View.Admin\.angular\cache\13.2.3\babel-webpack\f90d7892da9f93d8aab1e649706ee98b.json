{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  children: [{\n    path: 'bt-basic',\n    loadChildren: () => import('./tbl-basic/tbl-basic.module').then(module => module.TblBasicModule)\n  }]\n}];\nexport let TblBootstrapRoutingModule = /*#__PURE__*/(() => {\n  class TblBootstrapRoutingModule {}\n\n  TblBootstrapRoutingModule.ɵfac = function TblBootstrapRoutingModule_Factory(t) {\n    return new (t || TblBootstrapRoutingModule)();\n  };\n\n  TblBootstrapRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TblBootstrapRoutingModule\n  });\n  TblBootstrapRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return TblBootstrapRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}