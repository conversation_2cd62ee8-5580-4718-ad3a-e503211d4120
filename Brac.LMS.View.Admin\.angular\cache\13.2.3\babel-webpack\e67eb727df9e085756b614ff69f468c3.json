{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CourseProgressReportComponent } from './course-progress-report.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: CourseProgressReportComponent\n}];\nexport let CourseProgressReportRoutingModule = /*#__PURE__*/(() => {\n  class CourseProgressReportRoutingModule {}\n\n  CourseProgressReportRoutingModule.ɵfac = function CourseProgressReportRoutingModule_Factory(t) {\n    return new (t || CourseProgressReportRoutingModule)();\n  };\n\n  CourseProgressReportRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CourseProgressReportRoutingModule\n  });\n  CourseProgressReportRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return CourseProgressReportRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}