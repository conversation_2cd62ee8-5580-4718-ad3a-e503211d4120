{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, PLATFORM_ID, Directive, Inject, Input, Optional, Self, SecurityContext, NgModule } from '@angular/core';\nimport * as i1 from '@angular/flex-layout/core';\nimport { StyleBuilder, BaseDirective2, SERVER_TOKEN, LAYOUT_CONFIG, CoreModule } from '@angular/flex-layout/core';\nimport * as i2 from '@angular/common';\nimport { isPlatformServer, NgClass, NgStyle } from '@angular/common';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i2$1 from '@angular/platform-browser';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass ImgSrcStyleBuilder extends StyleBuilder {\n  buildStyles(url) {\n    return {\n      'content': url ? `url(${url})` : ''\n    };\n  }\n\n}\n\nImgSrcStyleBuilder.ɵfac = /* @__PURE__ */function () {\n  let ɵImgSrcStyleBuilder_BaseFactory;\n  return function ImgSrcStyleBuilder_Factory(t) {\n    return (ɵImgSrcStyleBuilder_BaseFactory || (ɵImgSrcStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(ImgSrcStyleBuilder)))(t || ImgSrcStyleBuilder);\n  };\n}();\n\nImgSrcStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ImgSrcStyleBuilder,\n  factory: ImgSrcStyleBuilder.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ImgSrcStyleBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\nclass ImgSrcDirective extends BaseDirective2 {\n  constructor(elementRef, styleBuilder, styler, marshal, platformId, serverModuleLoaded) {\n    super(elementRef, styleBuilder, styler, marshal);\n    this.platformId = platformId;\n    this.serverModuleLoaded = serverModuleLoaded;\n    this.DIRECTIVE_KEY = 'img-src';\n    this.defaultSrc = '';\n    this.styleCache = imgSrcCache;\n    this.init();\n    this.setValue(this.nativeElement.getAttribute('src') || '', '');\n\n    if (isPlatformServer(this.platformId) && this.serverModuleLoaded) {\n      this.nativeElement.setAttribute('src', '');\n    }\n  }\n\n  set src(val) {\n    this.defaultSrc = val;\n    this.setValue(this.defaultSrc, '');\n  }\n  /**\n   * Use the [responsively] activated input value to update\n   * the host img src attribute or assign a default `img.src=''`\n   * if the src has not been defined.\n   *\n   * Do nothing to standard `<img src=\"\">` usages, only when responsive\n   * keys are present do we actually call `setAttribute()`\n   */\n\n\n  updateWithValue(value) {\n    const url = value || this.defaultSrc;\n\n    if (isPlatformServer(this.platformId) && this.serverModuleLoaded) {\n      this.addStyles(url);\n    } else {\n      this.nativeElement.setAttribute('src', url);\n    }\n  }\n\n}\n\nImgSrcDirective.ɵfac = function ImgSrcDirective_Factory(t) {\n  return new (t || ImgSrcDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ImgSrcStyleBuilder), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(i1.MediaMarshaller), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(SERVER_TOKEN));\n};\n\nImgSrcDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: ImgSrcDirective,\n  inputs: {\n    src: \"src\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ImgSrcDirective, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: ImgSrcStyleBuilder\n    }, {\n      type: i1.StyleUtils\n    }, {\n      type: i1.MediaMarshaller\n    }, {\n      type: Object,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [SERVER_TOKEN]\n      }]\n    }];\n  }, {\n    src: [{\n      type: Input,\n      args: ['src']\n    }]\n  });\n})();\n\nconst imgSrcCache = new Map();\nconst inputs$3 = ['src.xs', 'src.sm', 'src.md', 'src.lg', 'src.xl', 'src.lt-sm', 'src.lt-md', 'src.lt-lg', 'src.lt-xl', 'src.gt-xs', 'src.gt-sm', 'src.gt-md', 'src.gt-lg'];\nconst selector$3 = `\n  img[src.xs],    img[src.sm],    img[src.md],    img[src.lg],   img[src.xl],\n  img[src.lt-sm], img[src.lt-md], img[src.lt-lg], img[src.lt-xl],\n  img[src.gt-xs], img[src.gt-sm], img[src.gt-md], img[src.gt-lg]\n`;\n/**\n * This directive provides a responsive API for the HTML <img> 'src' attribute\n * and will update the img.src property upon each responsive activation.\n *\n * e.g.\n *      <img src=\"defaultScene.jpg\" src.xs=\"mobileScene.jpg\"></img>\n *\n * @see https://css-tricks.com/responsive-images-youre-just-changing-resolutions-use-src/\n */\n\nclass DefaultImgSrcDirective extends ImgSrcDirective {\n  constructor() {\n    super(...arguments);\n    this.inputs = inputs$3;\n  }\n\n}\n\nDefaultImgSrcDirective.ɵfac = /* @__PURE__ */function () {\n  let ɵDefaultImgSrcDirective_BaseFactory;\n  return function DefaultImgSrcDirective_Factory(t) {\n    return (ɵDefaultImgSrcDirective_BaseFactory || (ɵDefaultImgSrcDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultImgSrcDirective)))(t || DefaultImgSrcDirective);\n  };\n}();\n\nDefaultImgSrcDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: DefaultImgSrcDirective,\n  selectors: [[\"img\", \"src.xs\", \"\"], [\"img\", \"src.sm\", \"\"], [\"img\", \"src.md\", \"\"], [\"img\", \"src.lg\", \"\"], [\"img\", \"src.xl\", \"\"], [\"img\", \"src.lt-sm\", \"\"], [\"img\", \"src.lt-md\", \"\"], [\"img\", \"src.lt-lg\", \"\"], [\"img\", \"src.lt-xl\", \"\"], [\"img\", \"src.gt-xs\", \"\"], [\"img\", \"src.gt-sm\", \"\"], [\"img\", \"src.gt-md\", \"\"], [\"img\", \"src.gt-lg\", \"\"]],\n  inputs: {\n    \"src.xs\": \"src.xs\",\n    \"src.sm\": \"src.sm\",\n    \"src.md\": \"src.md\",\n    \"src.lg\": \"src.lg\",\n    \"src.xl\": \"src.xl\",\n    \"src.lt-sm\": \"src.lt-sm\",\n    \"src.lt-md\": \"src.lt-md\",\n    \"src.lt-lg\": \"src.lt-lg\",\n    \"src.lt-xl\": \"src.lt-xl\",\n    \"src.gt-xs\": \"src.gt-xs\",\n    \"src.gt-sm\": \"src.gt-sm\",\n    \"src.gt-md\": \"src.gt-md\",\n    \"src.gt-lg\": \"src.gt-lg\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultImgSrcDirective, [{\n    type: Directive,\n    args: [{\n      selector: selector$3,\n      inputs: inputs$3\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass ClassDirective extends BaseDirective2 {\n  constructor(elementRef, styler, marshal, iterableDiffers, keyValueDiffers, renderer2, ngClassInstance) {\n    super(elementRef, null, styler, marshal);\n    this.ngClassInstance = ngClassInstance;\n    this.DIRECTIVE_KEY = 'ngClass';\n\n    if (!this.ngClassInstance) {\n      // Create an instance NgClass Directive instance only if `ngClass=\"\"` has NOT been defined on\n      // the same host element; since the responsive variations may be defined...\n      this.ngClassInstance = new NgClass(iterableDiffers, keyValueDiffers, elementRef, renderer2);\n    }\n\n    this.init();\n    this.setValue('', '');\n  }\n  /**\n   * Capture class assignments so we cache the default classes\n   * which are merged with activated styles and used as fallbacks.\n   */\n\n\n  set klass(val) {\n    this.ngClassInstance.klass = val;\n    this.setValue(val, '');\n  }\n\n  updateWithValue(value) {\n    this.ngClassInstance.ngClass = value;\n    this.ngClassInstance.ngDoCheck();\n  } // ******************************************************************\n  // Lifecycle Hooks\n  // ******************************************************************\n\n  /**\n   * For ChangeDetectionStrategy.onPush and ngOnChanges() updates\n   */\n\n\n  ngDoCheck() {\n    this.ngClassInstance.ngDoCheck();\n  }\n\n}\n\nClassDirective.ɵfac = function ClassDirective_Factory(t) {\n  return new (t || ClassDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(i1.MediaMarshaller), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(i0.KeyValueDiffers), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.NgClass, 10));\n};\n\nClassDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: ClassDirective,\n  inputs: {\n    klass: [\"class\", \"klass\"]\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ClassDirective, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.StyleUtils\n    }, {\n      type: i1.MediaMarshaller\n    }, {\n      type: i0.IterableDiffers\n    }, {\n      type: i0.KeyValueDiffers\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i2.NgClass,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Self\n      }]\n    }];\n  }, {\n    klass: [{\n      type: Input,\n      args: ['class']\n    }]\n  });\n})();\n\nconst inputs$2 = ['ngClass', 'ngClass.xs', 'ngClass.sm', 'ngClass.md', 'ngClass.lg', 'ngClass.xl', 'ngClass.lt-sm', 'ngClass.lt-md', 'ngClass.lt-lg', 'ngClass.lt-xl', 'ngClass.gt-xs', 'ngClass.gt-sm', 'ngClass.gt-md', 'ngClass.gt-lg'];\nconst selector$2 = `\n  [ngClass], [ngClass.xs], [ngClass.sm], [ngClass.md], [ngClass.lg], [ngClass.xl],\n  [ngClass.lt-sm], [ngClass.lt-md], [ngClass.lt-lg], [ngClass.lt-xl],\n  [ngClass.gt-xs], [ngClass.gt-sm], [ngClass.gt-md], [ngClass.gt-lg]\n`;\n/**\n * Directive to add responsive support for ngClass.\n * This maintains the core functionality of 'ngClass' and adds responsive API\n * Note: this class is a no-op when rendered on the server\n */\n\nclass DefaultClassDirective extends ClassDirective {\n  constructor() {\n    super(...arguments);\n    this.inputs = inputs$2;\n  }\n\n}\n\nDefaultClassDirective.ɵfac = /* @__PURE__ */function () {\n  let ɵDefaultClassDirective_BaseFactory;\n  return function DefaultClassDirective_Factory(t) {\n    return (ɵDefaultClassDirective_BaseFactory || (ɵDefaultClassDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultClassDirective)))(t || DefaultClassDirective);\n  };\n}();\n\nDefaultClassDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: DefaultClassDirective,\n  selectors: [[\"\", \"ngClass\", \"\"], [\"\", \"ngClass.xs\", \"\"], [\"\", \"ngClass.sm\", \"\"], [\"\", \"ngClass.md\", \"\"], [\"\", \"ngClass.lg\", \"\"], [\"\", \"ngClass.xl\", \"\"], [\"\", \"ngClass.lt-sm\", \"\"], [\"\", \"ngClass.lt-md\", \"\"], [\"\", \"ngClass.lt-lg\", \"\"], [\"\", \"ngClass.lt-xl\", \"\"], [\"\", \"ngClass.gt-xs\", \"\"], [\"\", \"ngClass.gt-sm\", \"\"], [\"\", \"ngClass.gt-md\", \"\"], [\"\", \"ngClass.gt-lg\", \"\"]],\n  inputs: {\n    ngClass: \"ngClass\",\n    \"ngClass.xs\": \"ngClass.xs\",\n    \"ngClass.sm\": \"ngClass.sm\",\n    \"ngClass.md\": \"ngClass.md\",\n    \"ngClass.lg\": \"ngClass.lg\",\n    \"ngClass.xl\": \"ngClass.xl\",\n    \"ngClass.lt-sm\": \"ngClass.lt-sm\",\n    \"ngClass.lt-md\": \"ngClass.lt-md\",\n    \"ngClass.lt-lg\": \"ngClass.lt-lg\",\n    \"ngClass.lt-xl\": \"ngClass.lt-xl\",\n    \"ngClass.gt-xs\": \"ngClass.gt-xs\",\n    \"ngClass.gt-sm\": \"ngClass.gt-sm\",\n    \"ngClass.gt-md\": \"ngClass.gt-md\",\n    \"ngClass.gt-lg\": \"ngClass.gt-lg\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultClassDirective, [{\n    type: Directive,\n    args: [{\n      selector: selector$2,\n      inputs: inputs$2\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass ShowHideStyleBuilder extends StyleBuilder {\n  buildStyles(show, parent) {\n    const shouldShow = show === 'true';\n    return {\n      'display': shouldShow ? parent.display || (parent.isServer ? 'initial' : '') : 'none'\n    };\n  }\n\n}\n\nShowHideStyleBuilder.ɵfac = /* @__PURE__ */function () {\n  let ɵShowHideStyleBuilder_BaseFactory;\n  return function ShowHideStyleBuilder_Factory(t) {\n    return (ɵShowHideStyleBuilder_BaseFactory || (ɵShowHideStyleBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(ShowHideStyleBuilder)))(t || ShowHideStyleBuilder);\n  };\n}();\n\nShowHideStyleBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ShowHideStyleBuilder,\n  factory: ShowHideStyleBuilder.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ShowHideStyleBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\nclass ShowHideDirective extends BaseDirective2 {\n  constructor(elementRef, styleBuilder, styler, marshal, layoutConfig, platformId, serverModuleLoaded) {\n    super(elementRef, styleBuilder, styler, marshal);\n    this.layoutConfig = layoutConfig;\n    this.platformId = platformId;\n    this.serverModuleLoaded = serverModuleLoaded;\n    this.DIRECTIVE_KEY = 'show-hide';\n    /** Original DOM Element CSS display style */\n\n    this.display = '';\n    this.hasLayout = false;\n    this.hasFlexChild = false;\n  } // *********************************************\n  // Lifecycle Methods\n  // *********************************************\n\n\n  ngAfterViewInit() {\n    this.trackExtraTriggers();\n    const children = Array.from(this.nativeElement.children);\n\n    for (let i = 0; i < children.length; i++) {\n      if (this.marshal.hasValue(children[i], 'flex')) {\n        this.hasFlexChild = true;\n        break;\n      }\n    }\n\n    if (DISPLAY_MAP.has(this.nativeElement)) {\n      this.display = DISPLAY_MAP.get(this.nativeElement);\n    } else {\n      this.display = this.getDisplayStyle();\n      DISPLAY_MAP.set(this.nativeElement, this.display);\n    }\n\n    this.init(); // set the default to show unless explicitly overridden\n\n    const defaultValue = this.marshal.getValue(this.nativeElement, this.DIRECTIVE_KEY, '');\n\n    if (defaultValue === undefined || defaultValue === '') {\n      this.setValue(true, '');\n    } else {\n      this.triggerUpdate();\n    }\n  }\n  /**\n   * On changes to any @Input properties...\n   * Default to use the non-responsive Input value ('fxShow')\n   * Then conditionally override with the mq-activated Input's current value\n   */\n\n\n  ngOnChanges(changes) {\n    Object.keys(changes).forEach(key => {\n      if (this.inputs.indexOf(key) !== -1) {\n        const inputKey = key.split('.');\n        const bp = inputKey.slice(1).join('.');\n        const inputValue = changes[key].currentValue;\n        let shouldShow = inputValue !== '' ? inputValue !== 0 ? coerceBooleanProperty(inputValue) : false : true;\n\n        if (inputKey[0] === 'fxHide') {\n          shouldShow = !shouldShow;\n        }\n\n        this.setValue(shouldShow, bp);\n      }\n    });\n  } // *********************************************\n  // Protected methods\n  // *********************************************\n\n  /**\n   *  Watch for these extra triggers to update fxShow, fxHide stylings\n   */\n\n\n  trackExtraTriggers() {\n    this.hasLayout = this.marshal.hasValue(this.nativeElement, 'layout');\n    ['layout', 'layout-align'].forEach(key => {\n      this.marshal.trackValue(this.nativeElement, key).pipe(takeUntil(this.destroySubject)).subscribe(this.triggerUpdate.bind(this));\n    });\n  }\n  /**\n   * Override accessor to the current HTMLElement's `display` style\n   * Note: Show/Hide will not change the display to 'flex' but will set it to 'block'\n   * unless it was already explicitly specified inline or in a CSS stylesheet.\n   */\n\n\n  getDisplayStyle() {\n    return this.hasLayout || this.hasFlexChild && this.layoutConfig.addFlexToParent ? 'flex' : this.styler.lookupStyle(this.nativeElement, 'display', true);\n  }\n  /** Validate the visibility value and then update the host's inline display style */\n\n\n  updateWithValue(value = true) {\n    if (value === '') {\n      return;\n    }\n\n    const isServer = isPlatformServer(this.platformId);\n    this.addStyles(value ? 'true' : 'false', {\n      display: this.display,\n      isServer\n    });\n\n    if (isServer && this.serverModuleLoaded) {\n      this.nativeElement.style.setProperty('display', '');\n    }\n\n    this.marshal.triggerUpdate(this.parentElement, 'layout-gap');\n  }\n\n}\n\nShowHideDirective.ɵfac = function ShowHideDirective_Factory(t) {\n  return new (t || ShowHideDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ShowHideStyleBuilder), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(i1.MediaMarshaller), i0.ɵɵdirectiveInject(LAYOUT_CONFIG), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(SERVER_TOKEN));\n};\n\nShowHideDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: ShowHideDirective,\n  features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ShowHideDirective, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: ShowHideStyleBuilder\n    }, {\n      type: i1.StyleUtils\n    }, {\n      type: i1.MediaMarshaller\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [LAYOUT_CONFIG]\n      }]\n    }, {\n      type: Object,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [SERVER_TOKEN]\n      }]\n    }];\n  }, null);\n})();\n\nconst DISPLAY_MAP = new WeakMap();\nconst inputs$1 = ['fxShow', 'fxShow.print', 'fxShow.xs', 'fxShow.sm', 'fxShow.md', 'fxShow.lg', 'fxShow.xl', 'fxShow.lt-sm', 'fxShow.lt-md', 'fxShow.lt-lg', 'fxShow.lt-xl', 'fxShow.gt-xs', 'fxShow.gt-sm', 'fxShow.gt-md', 'fxShow.gt-lg', 'fxHide', 'fxHide.print', 'fxHide.xs', 'fxHide.sm', 'fxHide.md', 'fxHide.lg', 'fxHide.xl', 'fxHide.lt-sm', 'fxHide.lt-md', 'fxHide.lt-lg', 'fxHide.lt-xl', 'fxHide.gt-xs', 'fxHide.gt-sm', 'fxHide.gt-md', 'fxHide.gt-lg'];\nconst selector$1 = `\n  [fxShow], [fxShow.print],\n  [fxShow.xs], [fxShow.sm], [fxShow.md], [fxShow.lg], [fxShow.xl],\n  [fxShow.lt-sm], [fxShow.lt-md], [fxShow.lt-lg], [fxShow.lt-xl],\n  [fxShow.gt-xs], [fxShow.gt-sm], [fxShow.gt-md], [fxShow.gt-lg],\n  [fxHide], [fxHide.print],\n  [fxHide.xs], [fxHide.sm], [fxHide.md], [fxHide.lg], [fxHide.xl],\n  [fxHide.lt-sm], [fxHide.lt-md], [fxHide.lt-lg], [fxHide.lt-xl],\n  [fxHide.gt-xs], [fxHide.gt-sm], [fxHide.gt-md], [fxHide.gt-lg]\n`;\n/**\n * 'show' Layout API directive\n */\n\nclass DefaultShowHideDirective extends ShowHideDirective {\n  constructor() {\n    super(...arguments);\n    this.inputs = inputs$1;\n  }\n\n}\n\nDefaultShowHideDirective.ɵfac = /* @__PURE__ */function () {\n  let ɵDefaultShowHideDirective_BaseFactory;\n  return function DefaultShowHideDirective_Factory(t) {\n    return (ɵDefaultShowHideDirective_BaseFactory || (ɵDefaultShowHideDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultShowHideDirective)))(t || DefaultShowHideDirective);\n  };\n}();\n\nDefaultShowHideDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: DefaultShowHideDirective,\n  selectors: [[\"\", \"fxShow\", \"\"], [\"\", \"fxShow.print\", \"\"], [\"\", \"fxShow.xs\", \"\"], [\"\", \"fxShow.sm\", \"\"], [\"\", \"fxShow.md\", \"\"], [\"\", \"fxShow.lg\", \"\"], [\"\", \"fxShow.xl\", \"\"], [\"\", \"fxShow.lt-sm\", \"\"], [\"\", \"fxShow.lt-md\", \"\"], [\"\", \"fxShow.lt-lg\", \"\"], [\"\", \"fxShow.lt-xl\", \"\"], [\"\", \"fxShow.gt-xs\", \"\"], [\"\", \"fxShow.gt-sm\", \"\"], [\"\", \"fxShow.gt-md\", \"\"], [\"\", \"fxShow.gt-lg\", \"\"], [\"\", \"fxHide\", \"\"], [\"\", \"fxHide.print\", \"\"], [\"\", \"fxHide.xs\", \"\"], [\"\", \"fxHide.sm\", \"\"], [\"\", \"fxHide.md\", \"\"], [\"\", \"fxHide.lg\", \"\"], [\"\", \"fxHide.xl\", \"\"], [\"\", \"fxHide.lt-sm\", \"\"], [\"\", \"fxHide.lt-md\", \"\"], [\"\", \"fxHide.lt-lg\", \"\"], [\"\", \"fxHide.lt-xl\", \"\"], [\"\", \"fxHide.gt-xs\", \"\"], [\"\", \"fxHide.gt-sm\", \"\"], [\"\", \"fxHide.gt-md\", \"\"], [\"\", \"fxHide.gt-lg\", \"\"]],\n  inputs: {\n    fxShow: \"fxShow\",\n    \"fxShow.print\": \"fxShow.print\",\n    \"fxShow.xs\": \"fxShow.xs\",\n    \"fxShow.sm\": \"fxShow.sm\",\n    \"fxShow.md\": \"fxShow.md\",\n    \"fxShow.lg\": \"fxShow.lg\",\n    \"fxShow.xl\": \"fxShow.xl\",\n    \"fxShow.lt-sm\": \"fxShow.lt-sm\",\n    \"fxShow.lt-md\": \"fxShow.lt-md\",\n    \"fxShow.lt-lg\": \"fxShow.lt-lg\",\n    \"fxShow.lt-xl\": \"fxShow.lt-xl\",\n    \"fxShow.gt-xs\": \"fxShow.gt-xs\",\n    \"fxShow.gt-sm\": \"fxShow.gt-sm\",\n    \"fxShow.gt-md\": \"fxShow.gt-md\",\n    \"fxShow.gt-lg\": \"fxShow.gt-lg\",\n    fxHide: \"fxHide\",\n    \"fxHide.print\": \"fxHide.print\",\n    \"fxHide.xs\": \"fxHide.xs\",\n    \"fxHide.sm\": \"fxHide.sm\",\n    \"fxHide.md\": \"fxHide.md\",\n    \"fxHide.lg\": \"fxHide.lg\",\n    \"fxHide.xl\": \"fxHide.xl\",\n    \"fxHide.lt-sm\": \"fxHide.lt-sm\",\n    \"fxHide.lt-md\": \"fxHide.lt-md\",\n    \"fxHide.lt-lg\": \"fxHide.lt-lg\",\n    \"fxHide.lt-xl\": \"fxHide.lt-xl\",\n    \"fxHide.gt-xs\": \"fxHide.gt-xs\",\n    \"fxHide.gt-sm\": \"fxHide.gt-sm\",\n    \"fxHide.gt-md\": \"fxHide.gt-md\",\n    \"fxHide.gt-lg\": \"fxHide.gt-lg\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultShowHideDirective, [{\n    type: Directive,\n    args: [{\n      selector: selector$1,\n      inputs: inputs$1\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** NgStyle allowed inputs */\n\n\nclass NgStyleKeyValue {\n  constructor(key, value, noQuotes = true) {\n    this.key = key;\n    this.value = value;\n    this.key = noQuotes ? key.replace(/['\"]/g, '').trim() : key.trim();\n    this.value = noQuotes ? value.replace(/['\"]/g, '').trim() : value.trim();\n    this.value = this.value.replace(/;/, '');\n  }\n\n}\n\nfunction getType(target) {\n  let what = typeof target;\n\n  if (what === 'object') {\n    return target.constructor === Array ? 'array' : target.constructor === Set ? 'set' : 'object';\n  }\n\n  return what;\n}\n/**\n * Split string of key:value pairs into Array of k-v pairs\n * e.g.  'key:value; key:value; key:value;' -> ['key:value',...]\n */\n\n\nfunction buildRawList(source, delimiter = ';') {\n  return String(source).trim().split(delimiter).map(val => val.trim()).filter(val => val !== '');\n}\n/** Convert array of key:value strings to a iterable map object */\n\n\nfunction buildMapFromList$1(styles, sanitize) {\n  const sanitizeValue = it => {\n    if (sanitize) {\n      it.value = sanitize(it.value);\n    }\n\n    return it;\n  };\n\n  return styles.map(stringToKeyValue).filter(entry => !!entry).map(sanitizeValue).reduce(keyValuesToMap, {});\n}\n/** Convert Set<string> or raw Object to an iterable NgStyleMap */\n\n\nfunction buildMapFromSet(source, sanitize) {\n  let list = [];\n\n  if (getType(source) === 'set') {\n    source.forEach(entry => list.push(entry));\n  } else {\n    Object.keys(source).forEach(key => {\n      list.push(`${key}:${source[key]}`);\n    });\n  }\n\n  return buildMapFromList$1(list, sanitize);\n}\n/** Convert 'key:value' -> [key, value] */\n\n\nfunction stringToKeyValue(it) {\n  const [key, ...vals] = it.split(':');\n  return new NgStyleKeyValue(key, vals.join(':'));\n}\n/** Convert [ [key,value] ] -> { key : value } */\n\n\nfunction keyValuesToMap(map, entry) {\n  if (!!entry.key) {\n    map[entry.key] = entry.value;\n  }\n\n  return map;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass StyleDirective extends BaseDirective2 {\n  constructor(elementRef, styler, marshal, sanitizer, differs, renderer2, ngStyleInstance, serverLoaded, platformId) {\n    var _a;\n\n    super(elementRef, null, styler, marshal);\n    this.sanitizer = sanitizer;\n    this.ngStyleInstance = ngStyleInstance;\n    this.DIRECTIVE_KEY = 'ngStyle';\n\n    if (!this.ngStyleInstance) {\n      // Create an instance NgStyle Directive instance only if `ngStyle=\"\"` has NOT been\n      // defined on the same host element; since the responsive variations may be defined...\n      this.ngStyleInstance = new NgStyle(elementRef, differs, renderer2);\n    }\n\n    this.init();\n    const styles = (_a = this.nativeElement.getAttribute('style')) !== null && _a !== void 0 ? _a : '';\n    this.fallbackStyles = this.buildStyleMap(styles);\n    this.isServer = serverLoaded && isPlatformServer(platformId);\n  }\n  /** Add generated styles */\n\n\n  updateWithValue(value) {\n    const styles = this.buildStyleMap(value);\n    this.ngStyleInstance.ngStyle = Object.assign(Object.assign({}, this.fallbackStyles), styles);\n\n    if (this.isServer) {\n      this.applyStyleToElement(styles);\n    }\n\n    this.ngStyleInstance.ngDoCheck();\n  }\n  /** Remove generated styles */\n\n\n  clearStyles() {\n    this.ngStyleInstance.ngStyle = this.fallbackStyles;\n    this.ngStyleInstance.ngDoCheck();\n  }\n  /**\n   * Convert raw strings to ngStyleMap; which is required by ngStyle\n   * NOTE: Raw string key-value pairs MUST be delimited by `;`\n   *       Comma-delimiters are not supported due to complexities of\n   *       possible style values such as `rgba(x,x,x,x)` and others\n   */\n\n\n  buildStyleMap(styles) {\n    // Always safe-guard (aka sanitize) style property values\n    const sanitizer = val => {\n      var _a;\n\n      return (_a = this.sanitizer.sanitize(SecurityContext.STYLE, val)) !== null && _a !== void 0 ? _a : '';\n    };\n\n    if (styles) {\n      switch (getType(styles)) {\n        case 'string':\n          return buildMapFromList(buildRawList(styles), sanitizer);\n\n        case 'array':\n          return buildMapFromList(styles, sanitizer);\n\n        case 'set':\n          return buildMapFromSet(styles, sanitizer);\n\n        default:\n          return buildMapFromSet(styles, sanitizer);\n      }\n    }\n\n    return {};\n  } // ******************************************************************\n  // Lifecycle Hooks\n  // ******************************************************************\n\n  /** For ChangeDetectionStrategy.onPush and ngOnChanges() updates */\n\n\n  ngDoCheck() {\n    this.ngStyleInstance.ngDoCheck();\n  }\n\n}\n\nStyleDirective.ɵfac = function StyleDirective_Factory(t) {\n  return new (t || StyleDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.StyleUtils), i0.ɵɵdirectiveInject(i1.MediaMarshaller), i0.ɵɵdirectiveInject(i2$1.DomSanitizer), i0.ɵɵdirectiveInject(i0.KeyValueDiffers), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.NgStyle, 10), i0.ɵɵdirectiveInject(SERVER_TOKEN), i0.ɵɵdirectiveInject(PLATFORM_ID));\n};\n\nStyleDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: StyleDirective,\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StyleDirective, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.StyleUtils\n    }, {\n      type: i1.MediaMarshaller\n    }, {\n      type: i2$1.DomSanitizer\n    }, {\n      type: i0.KeyValueDiffers\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i2.NgStyle,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Self\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [SERVER_TOKEN]\n      }]\n    }, {\n      type: Object,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }];\n  }, null);\n})();\n\nconst inputs = ['ngStyle', 'ngStyle.xs', 'ngStyle.sm', 'ngStyle.md', 'ngStyle.lg', 'ngStyle.xl', 'ngStyle.lt-sm', 'ngStyle.lt-md', 'ngStyle.lt-lg', 'ngStyle.lt-xl', 'ngStyle.gt-xs', 'ngStyle.gt-sm', 'ngStyle.gt-md', 'ngStyle.gt-lg'];\nconst selector = `\n  [ngStyle],\n  [ngStyle.xs], [ngStyle.sm], [ngStyle.md], [ngStyle.lg], [ngStyle.xl],\n  [ngStyle.lt-sm], [ngStyle.lt-md], [ngStyle.lt-lg], [ngStyle.lt-xl],\n  [ngStyle.gt-xs], [ngStyle.gt-sm], [ngStyle.gt-md], [ngStyle.gt-lg]\n`;\n/**\n * Directive to add responsive support for ngStyle.\n *\n */\n\nclass DefaultStyleDirective extends StyleDirective {\n  constructor() {\n    super(...arguments);\n    this.inputs = inputs;\n  }\n\n}\n\nDefaultStyleDirective.ɵfac = /* @__PURE__ */function () {\n  let ɵDefaultStyleDirective_BaseFactory;\n  return function DefaultStyleDirective_Factory(t) {\n    return (ɵDefaultStyleDirective_BaseFactory || (ɵDefaultStyleDirective_BaseFactory = i0.ɵɵgetInheritedFactory(DefaultStyleDirective)))(t || DefaultStyleDirective);\n  };\n}();\n\nDefaultStyleDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: DefaultStyleDirective,\n  selectors: [[\"\", \"ngStyle\", \"\"], [\"\", \"ngStyle.xs\", \"\"], [\"\", \"ngStyle.sm\", \"\"], [\"\", \"ngStyle.md\", \"\"], [\"\", \"ngStyle.lg\", \"\"], [\"\", \"ngStyle.xl\", \"\"], [\"\", \"ngStyle.lt-sm\", \"\"], [\"\", \"ngStyle.lt-md\", \"\"], [\"\", \"ngStyle.lt-lg\", \"\"], [\"\", \"ngStyle.lt-xl\", \"\"], [\"\", \"ngStyle.gt-xs\", \"\"], [\"\", \"ngStyle.gt-sm\", \"\"], [\"\", \"ngStyle.gt-md\", \"\"], [\"\", \"ngStyle.gt-lg\", \"\"]],\n  inputs: {\n    ngStyle: \"ngStyle\",\n    \"ngStyle.xs\": \"ngStyle.xs\",\n    \"ngStyle.sm\": \"ngStyle.sm\",\n    \"ngStyle.md\": \"ngStyle.md\",\n    \"ngStyle.lg\": \"ngStyle.lg\",\n    \"ngStyle.xl\": \"ngStyle.xl\",\n    \"ngStyle.lt-sm\": \"ngStyle.lt-sm\",\n    \"ngStyle.lt-md\": \"ngStyle.lt-md\",\n    \"ngStyle.lt-lg\": \"ngStyle.lt-lg\",\n    \"ngStyle.lt-xl\": \"ngStyle.lt-xl\",\n    \"ngStyle.gt-xs\": \"ngStyle.gt-xs\",\n    \"ngStyle.gt-sm\": \"ngStyle.gt-sm\",\n    \"ngStyle.gt-md\": \"ngStyle.gt-md\",\n    \"ngStyle.gt-lg\": \"ngStyle.gt-lg\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultStyleDirective, [{\n    type: Directive,\n    args: [{\n      selector,\n      inputs\n    }]\n  }], null, null);\n})();\n/** Build a styles map from a list of styles, while sanitizing bad values first */\n\n\nfunction buildMapFromList(styles, sanitize) {\n  const sanitizeValue = it => {\n    if (sanitize) {\n      it.value = sanitize(it.value);\n    }\n\n    return it;\n  };\n\n  return styles.map(stringToKeyValue).filter(entry => !!entry).map(sanitizeValue).reduce(keyValuesToMap, {});\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst ALL_DIRECTIVES = [DefaultShowHideDirective, DefaultClassDirective, DefaultStyleDirective, DefaultImgSrcDirective];\n/**\n * *****************************************************************\n * Define module for the Extended API\n * *****************************************************************\n */\n\nclass ExtendedModule {}\n\nExtendedModule.ɵfac = function ExtendedModule_Factory(t) {\n  return new (t || ExtendedModule)();\n};\n\nExtendedModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ExtendedModule\n});\nExtendedModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CoreModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ExtendedModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CoreModule],\n      declarations: [...ALL_DIRECTIVES],\n      exports: [...ALL_DIRECTIVES]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { ClassDirective, DefaultClassDirective, DefaultImgSrcDirective, DefaultShowHideDirective, DefaultStyleDirective, ExtendedModule, ImgSrcDirective, ImgSrcStyleBuilder, ShowHideDirective, ShowHideStyleBuilder, StyleDirective };", "map": {"version": 3, "sources": ["D:/Office Projects/brac-lms/Brac.LMS.View.Admin/node_modules/@angular/flex-layout/fesm2015/angular-flex-layout-extended.mjs"], "names": ["i0", "Injectable", "PLATFORM_ID", "Directive", "Inject", "Input", "Optional", "Self", "SecurityContext", "NgModule", "i1", "StyleBuilder", "BaseDirective2", "SERVER_TOKEN", "LAYOUT_CONFIG", "CoreModule", "i2", "isPlatformServer", "Ng<PERSON><PERSON>", "NgStyle", "coerceBooleanProperty", "takeUntil", "i2$1", "ImgSrcStyleBuilder", "buildStyles", "url", "ɵfac", "ɵprov", "type", "args", "providedIn", "ImgSrcDirective", "constructor", "elementRef", "styleBuilder", "styler", "marshal", "platformId", "serverModuleLoaded", "DIRECTIVE_KEY", "defaultSrc", "styleCache", "imgSrcCache", "init", "setValue", "nativeElement", "getAttribute", "setAttribute", "src", "val", "updateWithValue", "value", "addStyles", "ElementRef", "StyleUtils", "MediaMarshaller", "ɵdir", "Object", "decorators", "undefined", "Map", "inputs$3", "selector$3", "DefaultImgSrcDirective", "arguments", "inputs", "selector", "ClassDirective", "iterable<PERSON>iffers", "key<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderer2", "ngClassInstance", "klass", "ngClass", "ngDoCheck", "Iterable<PERSON><PERSON><PERSON>", "KeyValueDiffers", "Renderer2", "inputs$2", "selector$2", "DefaultClassDirective", "ShowHideStyleBuilder", "show", "parent", "shouldShow", "display", "isServer", "ShowHideDirective", "layoutConfig", "hasLayout", "hasFlexChild", "ngAfterViewInit", "trackExtraTriggers", "children", "Array", "from", "i", "length", "hasValue", "DISPLAY_MAP", "has", "get", "getDisplayStyle", "set", "defaultValue", "getValue", "triggerUpdate", "ngOnChanges", "changes", "keys", "for<PERSON>ach", "key", "indexOf", "inputKey", "split", "bp", "slice", "join", "inputValue", "currentValue", "trackValue", "pipe", "destroySubject", "subscribe", "bind", "addFlexToParent", "lookupStyle", "style", "setProperty", "parentElement", "WeakMap", "inputs$1", "selector$1", "DefaultShowHideDirective", "NgStyleKeyValue", "noQuotes", "replace", "trim", "getType", "target", "what", "Set", "buildRawList", "source", "delimiter", "String", "map", "filter", "buildMapFromList$1", "styles", "sanitize", "sanitizeValue", "it", "stringToKeyValue", "entry", "reduce", "keyValuesToMap", "buildMapFromSet", "list", "push", "vals", "StyleDirective", "sanitizer", "differs", "ngStyleInstance", "serverLoaded", "_a", "fallbackStyles", "buildStyleMap", "ngStyle", "assign", "applyStyleToElement", "clearStyles", "STYLE", "buildMapFromList", "Dom<PERSON><PERSON><PERSON>zer", "DefaultStyleDirective", "ALL_DIRECTIVES", "ExtendedModule", "ɵmod", "ɵinj", "imports", "declarations", "exports"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,WAArB,EAAkCC,SAAlC,EAA6CC,MAA7C,EAAqDC,KAArD,EAA4DC,QAA5D,EAAsEC,IAAtE,EAA4EC,eAA5E,EAA6FC,QAA7F,QAA6G,eAA7G;AACA,OAAO,KAAKC,EAAZ,MAAoB,2BAApB;AACA,SAASC,YAAT,EAAuBC,cAAvB,EAAuCC,YAAvC,EAAqDC,aAArD,EAAoEC,UAApE,QAAsF,2BAAtF;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,gBAAT,EAA2BC,OAA3B,EAAoCC,OAApC,QAAmD,iBAAnD;AACA,SAASC,qBAAT,QAAsC,uBAAtC;AACA,SAASC,SAAT,QAA0B,gBAA1B;AACA,OAAO,KAAKC,IAAZ,MAAsB,2BAAtB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,kBAAN,SAAiCZ,YAAjC,CAA8C;AAC1Ca,EAAAA,WAAW,CAACC,GAAD,EAAM;AACb,WAAO;AAAE,iBAAWA,GAAG,GAAI,OAAMA,GAAI,GAAd,GAAmB;AAAnC,KAAP;AACH;;AAHyC;;AAK9CF,kBAAkB,CAACG,IAAnB;AAAA;AAAA;AAAA,kFAAqG1B,EAArG,uBAA+GuB,kBAA/G,SAA+GA,kBAA/G;AAAA;AAAA;;AACAA,kBAAkB,CAACI,KAAnB,kBADqG3B,EACrG;AAAA,SAAmHuB,kBAAnH;AAAA,WAAmHA,kBAAnH;AAAA,cAAmJ;AAAnJ;;AACA;AAAA,qDAFqGvB,EAErG,mBAA2FuB,kBAA3F,EAA2H,CAAC;AAChHK,IAAAA,IAAI,EAAE3B,UAD0G;AAEhH4B,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAF0G,GAAD,CAA3H;AAAA;;AAIA,MAAMC,eAAN,SAA8BnB,cAA9B,CAA6C;AACzCoB,EAAAA,WAAW,CAACC,UAAD,EAAaC,YAAb,EAA2BC,MAA3B,EAAmCC,OAAnC,EAA4CC,UAA5C,EAAwDC,kBAAxD,EAA4E;AACnF,UAAML,UAAN,EAAkBC,YAAlB,EAAgCC,MAAhC,EAAwCC,OAAxC;AACA,SAAKC,UAAL,GAAkBA,UAAlB;AACA,SAAKC,kBAAL,GAA0BA,kBAA1B;AACA,SAAKC,aAAL,GAAqB,SAArB;AACA,SAAKC,UAAL,GAAkB,EAAlB;AACA,SAAKC,UAAL,GAAkBC,WAAlB;AACA,SAAKC,IAAL;AACA,SAAKC,QAAL,CAAc,KAAKC,aAAL,CAAmBC,YAAnB,CAAgC,KAAhC,KAA0C,EAAxD,EAA4D,EAA5D;;AACA,QAAI7B,gBAAgB,CAAC,KAAKoB,UAAN,CAAhB,IAAqC,KAAKC,kBAA9C,EAAkE;AAC9D,WAAKO,aAAL,CAAmBE,YAAnB,CAAgC,KAAhC,EAAuC,EAAvC;AACH;AACJ;;AACM,MAAHC,GAAG,CAACC,GAAD,EAAM;AACT,SAAKT,UAAL,GAAkBS,GAAlB;AACA,SAAKL,QAAL,CAAc,KAAKJ,UAAnB,EAA+B,EAA/B;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIU,EAAAA,eAAe,CAACC,KAAD,EAAQ;AACnB,UAAM1B,GAAG,GAAG0B,KAAK,IAAI,KAAKX,UAA1B;;AACA,QAAIvB,gBAAgB,CAAC,KAAKoB,UAAN,CAAhB,IAAqC,KAAKC,kBAA9C,EAAkE;AAC9D,WAAKc,SAAL,CAAe3B,GAAf;AACH,KAFD,MAGK;AACD,WAAKoB,aAAL,CAAmBE,YAAnB,CAAgC,KAAhC,EAAuCtB,GAAvC;AACH;AACJ;;AAlCwC;;AAoC7CM,eAAe,CAACL,IAAhB;AAAA,mBAA4GK,eAA5G,EA1CqG/B,EA0CrG,mBAA6IA,EAAE,CAACqD,UAAhJ,GA1CqGrD,EA0CrG,mBAAuKuB,kBAAvK,GA1CqGvB,EA0CrG,mBAAsMU,EAAE,CAAC4C,UAAzM,GA1CqGtD,EA0CrG,mBAAgOU,EAAE,CAAC6C,eAAnO,GA1CqGvD,EA0CrG,mBAA+PE,WAA/P,GA1CqGF,EA0CrG,mBAAuRa,YAAvR;AAAA;;AACAkB,eAAe,CAACyB,IAAhB,kBA3CqGxD,EA2CrG;AAAA,QAAgG+B,eAAhG;AAAA;AAAA;AAAA;AAAA,aA3CqG/B,EA2CrG;AAAA;;AACA;AAAA,qDA5CqGA,EA4CrG,mBAA2F+B,eAA3F,EAAwH,CAAC;AAC7GH,IAAAA,IAAI,EAAEzB;AADuG,GAAD,CAAxH,EAE4B,YAAY;AAChC,WAAO,CAAC;AAAEyB,MAAAA,IAAI,EAAE5B,EAAE,CAACqD;AAAX,KAAD,EAA0B;AAAEzB,MAAAA,IAAI,EAAEL;AAAR,KAA1B,EAAwD;AAAEK,MAAAA,IAAI,EAAElB,EAAE,CAAC4C;AAAX,KAAxD,EAAiF;AAAE1B,MAAAA,IAAI,EAAElB,EAAE,CAAC6C;AAAX,KAAjF,EAA+G;AAAE3B,MAAAA,IAAI,EAAE6B,MAAR;AAAgBC,MAAAA,UAAU,EAAE,CAAC;AACnI9B,QAAAA,IAAI,EAAExB,MAD6H;AAEnIyB,QAAAA,IAAI,EAAE,CAAC3B,WAAD;AAF6H,OAAD;AAA5B,KAA/G,EAGW;AAAE0B,MAAAA,IAAI,EAAE+B,SAAR;AAAmBD,MAAAA,UAAU,EAAE,CAAC;AAClC9B,QAAAA,IAAI,EAAExB,MAD4B;AAElCyB,QAAAA,IAAI,EAAE,CAAChB,YAAD;AAF4B,OAAD;AAA/B,KAHX,CAAP;AAOH,GAVL,EAUuB;AAAEmC,IAAAA,GAAG,EAAE,CAAC;AACfpB,MAAAA,IAAI,EAAEvB,KADS;AAEfwB,MAAAA,IAAI,EAAE,CAAC,KAAD;AAFS,KAAD;AAAP,GAVvB;AAAA;;AAcA,MAAMa,WAAW,GAAG,IAAIkB,GAAJ,EAApB;AACA,MAAMC,QAAQ,GAAG,CACb,QADa,EACH,QADG,EACO,QADP,EACiB,QADjB,EAC2B,QAD3B,EAEb,WAFa,EAEA,WAFA,EAEa,WAFb,EAE0B,WAF1B,EAGb,WAHa,EAGA,WAHA,EAGa,WAHb,EAG0B,WAH1B,CAAjB;AAKA,MAAMC,UAAU,GAAI;AACpB;AACA;AACA;AACA,CAJA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,sBAAN,SAAqChC,eAArC,CAAqD;AACjDC,EAAAA,WAAW,GAAG;AACV,UAAM,GAAGgC,SAAT;AACA,SAAKC,MAAL,GAAcJ,QAAd;AACH;;AAJgD;;AAMrDE,sBAAsB,CAACrC,IAAvB;AAAA;AAAA;AAAA,0FApFqG1B,EAoFrG,uBAAmH+D,sBAAnH,SAAmHA,sBAAnH;AAAA;AAAA;;AACAA,sBAAsB,CAACP,IAAvB,kBArFqGxD,EAqFrG;AAAA,QAAuG+D,sBAAvG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aArFqG/D,EAqFrG;AAAA;;AACA;AAAA,qDAtFqGA,EAsFrG,mBAA2F+D,sBAA3F,EAA+H,CAAC;AACpHnC,IAAAA,IAAI,EAAEzB,SAD8G;AAEpH0B,IAAAA,IAAI,EAAE,CAAC;AAAEqC,MAAAA,QAAQ,EAAEJ,UAAZ;AAAwBG,MAAAA,MAAM,EAAEJ;AAAhC,KAAD;AAF8G,GAAD,CAA/H;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMM,cAAN,SAA6BvD,cAA7B,CAA4C;AACxCoB,EAAAA,WAAW,CAACC,UAAD,EAAaE,MAAb,EAAqBC,OAArB,EAA8BgC,eAA9B,EAA+CC,eAA/C,EAAgEC,SAAhE,EAA2EC,eAA3E,EAA4F;AACnG,UAAMtC,UAAN,EAAkB,IAAlB,EAAwBE,MAAxB,EAAgCC,OAAhC;AACA,SAAKmC,eAAL,GAAuBA,eAAvB;AACA,SAAKhC,aAAL,GAAqB,SAArB;;AACA,QAAI,CAAC,KAAKgC,eAAV,EAA2B;AACvB;AACA;AACA,WAAKA,eAAL,GAAuB,IAAIrD,OAAJ,CAAYkD,eAAZ,EAA6BC,eAA7B,EAA8CpC,UAA9C,EAA0DqC,SAA1D,CAAvB;AACH;;AACD,SAAK3B,IAAL;AACA,SAAKC,QAAL,CAAc,EAAd,EAAkB,EAAlB;AACH;AACD;AACJ;AACA;AACA;;;AACa,MAAL4B,KAAK,CAACvB,GAAD,EAAM;AACX,SAAKsB,eAAL,CAAqBC,KAArB,GAA6BvB,GAA7B;AACA,SAAKL,QAAL,CAAcK,GAAd,EAAmB,EAAnB;AACH;;AACDC,EAAAA,eAAe,CAACC,KAAD,EAAQ;AACnB,SAAKoB,eAAL,CAAqBE,OAArB,GAA+BtB,KAA/B;AACA,SAAKoB,eAAL,CAAqBG,SAArB;AACH,GAxBuC,CAyBxC;AACA;AACA;;AACA;AACJ;AACA;;;AACIA,EAAAA,SAAS,GAAG;AACR,SAAKH,eAAL,CAAqBG,SAArB;AACH;;AAjCuC;;AAmC5CP,cAAc,CAACzC,IAAf;AAAA,mBAA2GyC,cAA3G,EArIqGnE,EAqIrG,mBAA2IA,EAAE,CAACqD,UAA9I,GArIqGrD,EAqIrG,mBAAqKU,EAAE,CAAC4C,UAAxK,GArIqGtD,EAqIrG,mBAA+LU,EAAE,CAAC6C,eAAlM,GArIqGvD,EAqIrG,mBAA8NA,EAAE,CAAC2E,eAAjO,GArIqG3E,EAqIrG,mBAA6PA,EAAE,CAAC4E,eAAhQ,GArIqG5E,EAqIrG,mBAA4RA,EAAE,CAAC6E,SAA/R,GArIqG7E,EAqIrG,mBAAqTgB,EAAE,CAACE,OAAxT;AAAA;;AACAiD,cAAc,CAACX,IAAf,kBAtIqGxD,EAsIrG;AAAA,QAA+FmE,cAA/F;AAAA;AAAA;AAAA;AAAA,aAtIqGnE,EAsIrG;AAAA;;AACA;AAAA,qDAvIqGA,EAuIrG,mBAA2FmE,cAA3F,EAAuH,CAAC;AAC5GvC,IAAAA,IAAI,EAAEzB;AADsG,GAAD,CAAvH,EAE4B,YAAY;AAChC,WAAO,CAAC;AAAEyB,MAAAA,IAAI,EAAE5B,EAAE,CAACqD;AAAX,KAAD,EAA0B;AAAEzB,MAAAA,IAAI,EAAElB,EAAE,CAAC4C;AAAX,KAA1B,EAAmD;AAAE1B,MAAAA,IAAI,EAAElB,EAAE,CAAC6C;AAAX,KAAnD,EAAiF;AAAE3B,MAAAA,IAAI,EAAE5B,EAAE,CAAC2E;AAAX,KAAjF,EAA+G;AAAE/C,MAAAA,IAAI,EAAE5B,EAAE,CAAC4E;AAAX,KAA/G,EAA6I;AAAEhD,MAAAA,IAAI,EAAE5B,EAAE,CAAC6E;AAAX,KAA7I,EAAqK;AAAEjD,MAAAA,IAAI,EAAEZ,EAAE,CAACE,OAAX;AAAoBwC,MAAAA,UAAU,EAAE,CAAC;AAC7L9B,QAAAA,IAAI,EAAEtB;AADuL,OAAD,EAE7L;AACCsB,QAAAA,IAAI,EAAErB;AADP,OAF6L;AAAhC,KAArK,CAAP;AAKH,GARL,EAQuB;AAAEiE,IAAAA,KAAK,EAAE,CAAC;AACjB5C,MAAAA,IAAI,EAAEvB,KADW;AAEjBwB,MAAAA,IAAI,EAAE,CAAC,OAAD;AAFW,KAAD;AAAT,GARvB;AAAA;;AAYA,MAAMiD,QAAQ,GAAG,CACb,SADa,EACF,YADE,EACY,YADZ,EAC0B,YAD1B,EACwC,YADxC,EACsD,YADtD,EAEb,eAFa,EAEI,eAFJ,EAEqB,eAFrB,EAEsC,eAFtC,EAGb,eAHa,EAGI,eAHJ,EAGqB,eAHrB,EAGsC,eAHtC,CAAjB;AAKA,MAAMC,UAAU,GAAI;AACpB;AACA;AACA;AACA,CAJA;AAKA;AACA;AACA;AACA;AACA;;AACA,MAAMC,qBAAN,SAAoCb,cAApC,CAAmD;AAC/CnC,EAAAA,WAAW,GAAG;AACV,UAAM,GAAGgC,SAAT;AACA,SAAKC,MAAL,GAAca,QAAd;AACH;;AAJ8C;;AAMnDE,qBAAqB,CAACtD,IAAtB;AAAA;AAAA;AAAA,wFAxKqG1B,EAwKrG,uBAAkHgF,qBAAlH,SAAkHA,qBAAlH;AAAA;AAAA;;AACAA,qBAAqB,CAACxB,IAAtB,kBAzKqGxD,EAyKrG;AAAA,QAAsGgF,qBAAtG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAzKqGhF,EAyKrG;AAAA;;AACA;AAAA,qDA1KqGA,EA0KrG,mBAA2FgF,qBAA3F,EAA8H,CAAC;AACnHpD,IAAAA,IAAI,EAAEzB,SAD6G;AAEnH0B,IAAAA,IAAI,EAAE,CAAC;AAAEqC,MAAAA,QAAQ,EAAEa,UAAZ;AAAwBd,MAAAA,MAAM,EAAEa;AAAhC,KAAD;AAF6G,GAAD,CAA9H;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMG,oBAAN,SAAmCtE,YAAnC,CAAgD;AAC5Ca,EAAAA,WAAW,CAAC0D,IAAD,EAAOC,MAAP,EAAe;AACtB,UAAMC,UAAU,GAAGF,IAAI,KAAK,MAA5B;AACA,WAAO;AAAE,iBAAWE,UAAU,GAAGD,MAAM,CAACE,OAAP,KAAmBF,MAAM,CAACG,QAAP,GAAkB,SAAlB,GAA8B,EAAjD,CAAH,GAA0D;AAAjF,KAAP;AACH;;AAJ2C;;AAMhDL,oBAAoB,CAACvD,IAArB;AAAA;AAAA;AAAA,sFA5LqG1B,EA4LrG,uBAAiHiF,oBAAjH,SAAiHA,oBAAjH;AAAA;AAAA;;AACAA,oBAAoB,CAACtD,KAArB,kBA7LqG3B,EA6LrG;AAAA,SAAqHiF,oBAArH;AAAA,WAAqHA,oBAArH;AAAA,cAAuJ;AAAvJ;;AACA;AAAA,qDA9LqGjF,EA8LrG,mBAA2FiF,oBAA3F,EAA6H,CAAC;AAClHrD,IAAAA,IAAI,EAAE3B,UAD4G;AAElH4B,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAF4G,GAAD,CAA7H;AAAA;;AAIA,MAAMyD,iBAAN,SAAgC3E,cAAhC,CAA+C;AAC3CoB,EAAAA,WAAW,CAACC,UAAD,EAAaC,YAAb,EAA2BC,MAA3B,EAAmCC,OAAnC,EAA4CoD,YAA5C,EAA0DnD,UAA1D,EAAsEC,kBAAtE,EAA0F;AACjG,UAAML,UAAN,EAAkBC,YAAlB,EAAgCC,MAAhC,EAAwCC,OAAxC;AACA,SAAKoD,YAAL,GAAoBA,YAApB;AACA,SAAKnD,UAAL,GAAkBA,UAAlB;AACA,SAAKC,kBAAL,GAA0BA,kBAA1B;AACA,SAAKC,aAAL,GAAqB,WAArB;AACA;;AACA,SAAK8C,OAAL,GAAe,EAAf;AACA,SAAKI,SAAL,GAAiB,KAAjB;AACA,SAAKC,YAAL,GAAoB,KAApB;AACH,GAX0C,CAY3C;AACA;AACA;;;AACAC,EAAAA,eAAe,GAAG;AACd,SAAKC,kBAAL;AACA,UAAMC,QAAQ,GAAGC,KAAK,CAACC,IAAN,CAAW,KAAKlD,aAAL,CAAmBgD,QAA9B,CAAjB;;AACA,SAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,QAAQ,CAACI,MAA7B,EAAqCD,CAAC,EAAtC,EAA0C;AACtC,UAAI,KAAK5D,OAAL,CAAa8D,QAAb,CAAsBL,QAAQ,CAACG,CAAD,CAA9B,EAAmC,MAAnC,CAAJ,EAAgD;AAC5C,aAAKN,YAAL,GAAoB,IAApB;AACA;AACH;AACJ;;AACD,QAAIS,WAAW,CAACC,GAAZ,CAAgB,KAAKvD,aAArB,CAAJ,EAAyC;AACrC,WAAKwC,OAAL,GAAec,WAAW,CAACE,GAAZ,CAAgB,KAAKxD,aAArB,CAAf;AACH,KAFD,MAGK;AACD,WAAKwC,OAAL,GAAe,KAAKiB,eAAL,EAAf;AACAH,MAAAA,WAAW,CAACI,GAAZ,CAAgB,KAAK1D,aAArB,EAAoC,KAAKwC,OAAzC;AACH;;AACD,SAAK1C,IAAL,GAhBc,CAiBd;;AACA,UAAM6D,YAAY,GAAG,KAAKpE,OAAL,CAAaqE,QAAb,CAAsB,KAAK5D,aAA3B,EAA0C,KAAKN,aAA/C,EAA8D,EAA9D,CAArB;;AACA,QAAIiE,YAAY,KAAK7C,SAAjB,IAA8B6C,YAAY,KAAK,EAAnD,EAAuD;AACnD,WAAK5D,QAAL,CAAc,IAAd,EAAoB,EAApB;AACH,KAFD,MAGK;AACD,WAAK8D,aAAL;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;AACIC,EAAAA,WAAW,CAACC,OAAD,EAAU;AACjBnD,IAAAA,MAAM,CAACoD,IAAP,CAAYD,OAAZ,EAAqBE,OAArB,CAA6BC,GAAG,IAAI;AAChC,UAAI,KAAK9C,MAAL,CAAY+C,OAAZ,CAAoBD,GAApB,MAA6B,CAAC,CAAlC,EAAqC;AACjC,cAAME,QAAQ,GAAGF,GAAG,CAACG,KAAJ,CAAU,GAAV,CAAjB;AACA,cAAMC,EAAE,GAAGF,QAAQ,CAACG,KAAT,CAAe,CAAf,EAAkBC,IAAlB,CAAuB,GAAvB,CAAX;AACA,cAAMC,UAAU,GAAGV,OAAO,CAACG,GAAD,CAAP,CAAaQ,YAAhC;AACA,YAAInC,UAAU,GAAGkC,UAAU,KAAK,EAAf,GACbA,UAAU,KAAK,CAAf,GAAmBlG,qBAAqB,CAACkG,UAAD,CAAxC,GAAuD,KAD1C,GAEX,IAFN;;AAGA,YAAIL,QAAQ,CAAC,CAAD,CAAR,KAAgB,QAApB,EAA8B;AAC1B7B,UAAAA,UAAU,GAAG,CAACA,UAAd;AACH;;AACD,aAAKxC,QAAL,CAAcwC,UAAd,EAA0B+B,EAA1B;AACH;AACJ,KAbD;AAcH,GA7D0C,CA8D3C;AACA;AACA;;AACA;AACJ;AACA;;;AACIvB,EAAAA,kBAAkB,GAAG;AACjB,SAAKH,SAAL,GAAiB,KAAKrD,OAAL,CAAa8D,QAAb,CAAsB,KAAKrD,aAA3B,EAA0C,QAA1C,CAAjB;AACA,KAAC,QAAD,EAAW,cAAX,EAA2BiE,OAA3B,CAAmCC,GAAG,IAAI;AACtC,WAAK3E,OAAL,CACKoF,UADL,CACgB,KAAK3E,aADrB,EACoCkE,GADpC,EAEKU,IAFL,CAEUpG,SAAS,CAAC,KAAKqG,cAAN,CAFnB,EAGKC,SAHL,CAGe,KAAKjB,aAAL,CAAmBkB,IAAnB,CAAwB,IAAxB,CAHf;AAIH,KALD;AAMH;AACD;AACJ;AACA;AACA;AACA;;;AACItB,EAAAA,eAAe,GAAG;AACd,WAAQ,KAAKb,SAAL,IAAmB,KAAKC,YAAL,IAAqB,KAAKF,YAAL,CAAkBqC,eAA3D,GACH,MADG,GACM,KAAK1F,MAAL,CAAY2F,WAAZ,CAAwB,KAAKjF,aAA7B,EAA4C,SAA5C,EAAuD,IAAvD,CADb;AAEH;AACD;;;AACAK,EAAAA,eAAe,CAACC,KAAK,GAAG,IAAT,EAAe;AAC1B,QAAIA,KAAK,KAAK,EAAd,EAAkB;AACd;AACH;;AACD,UAAMmC,QAAQ,GAAGrE,gBAAgB,CAAC,KAAKoB,UAAN,CAAjC;AACA,SAAKe,SAAL,CAAeD,KAAK,GAAG,MAAH,GAAY,OAAhC,EAAyC;AAAEkC,MAAAA,OAAO,EAAE,KAAKA,OAAhB;AAAyBC,MAAAA;AAAzB,KAAzC;;AACA,QAAIA,QAAQ,IAAI,KAAKhD,kBAArB,EAAyC;AACrC,WAAKO,aAAL,CAAmBkF,KAAnB,CAAyBC,WAAzB,CAAqC,SAArC,EAAgD,EAAhD;AACH;;AACD,SAAK5F,OAAL,CAAasE,aAAb,CAA2B,KAAKuB,aAAhC,EAA+C,YAA/C;AACH;;AAjG0C;;AAmG/C1C,iBAAiB,CAAC7D,IAAlB;AAAA,mBAA8G6D,iBAA9G,EArSqGvF,EAqSrG,mBAAiJA,EAAE,CAACqD,UAApJ,GArSqGrD,EAqSrG,mBAA2KiF,oBAA3K,GArSqGjF,EAqSrG,mBAA4MU,EAAE,CAAC4C,UAA/M,GArSqGtD,EAqSrG,mBAAsOU,EAAE,CAAC6C,eAAzO,GArSqGvD,EAqSrG,mBAAqQc,aAArQ,GArSqGd,EAqSrG,mBAA+RE,WAA/R,GArSqGF,EAqSrG,mBAAuTa,YAAvT;AAAA;;AACA0E,iBAAiB,CAAC/B,IAAlB,kBAtSqGxD,EAsSrG;AAAA,QAAkGuF,iBAAlG;AAAA,aAtSqGvF,EAsSrG,6BAtSqGA,EAsSrG;AAAA;;AACA;AAAA,qDAvSqGA,EAuSrG,mBAA2FuF,iBAA3F,EAA0H,CAAC;AAC/G3D,IAAAA,IAAI,EAAEzB;AADyG,GAAD,CAA1H,EAE4B,YAAY;AAChC,WAAO,CAAC;AAAEyB,MAAAA,IAAI,EAAE5B,EAAE,CAACqD;AAAX,KAAD,EAA0B;AAAEzB,MAAAA,IAAI,EAAEqD;AAAR,KAA1B,EAA0D;AAAErD,MAAAA,IAAI,EAAElB,EAAE,CAAC4C;AAAX,KAA1D,EAAmF;AAAE1B,MAAAA,IAAI,EAAElB,EAAE,CAAC6C;AAAX,KAAnF,EAAiH;AAAE3B,MAAAA,IAAI,EAAE+B,SAAR;AAAmBD,MAAAA,UAAU,EAAE,CAAC;AACxI9B,QAAAA,IAAI,EAAExB,MADkI;AAExIyB,QAAAA,IAAI,EAAE,CAACf,aAAD;AAFkI,OAAD;AAA/B,KAAjH,EAGW;AAAEc,MAAAA,IAAI,EAAE6B,MAAR;AAAgBC,MAAAA,UAAU,EAAE,CAAC;AAC/B9B,QAAAA,IAAI,EAAExB,MADyB;AAE/ByB,QAAAA,IAAI,EAAE,CAAC3B,WAAD;AAFyB,OAAD;AAA5B,KAHX,EAMW;AAAE0B,MAAAA,IAAI,EAAE+B,SAAR;AAAmBD,MAAAA,UAAU,EAAE,CAAC;AAClC9B,QAAAA,IAAI,EAAExB,MAD4B;AAElCyB,QAAAA,IAAI,EAAE,CAAChB,YAAD;AAF4B,OAAD;AAA/B,KANX,CAAP;AAUH,GAbL;AAAA;;AAcA,MAAMsF,WAAW,GAAG,IAAI+B,OAAJ,EAApB;AACA,MAAMC,QAAQ,GAAG,CACb,QADa,EACH,cADG,EAEb,WAFa,EAEA,WAFA,EAEa,WAFb,EAE0B,WAF1B,EAEuC,WAFvC,EAGb,cAHa,EAGG,cAHH,EAGmB,cAHnB,EAGmC,cAHnC,EAIb,cAJa,EAIG,cAJH,EAImB,cAJnB,EAImC,cAJnC,EAKb,QALa,EAKH,cALG,EAMb,WANa,EAMA,WANA,EAMa,WANb,EAM0B,WAN1B,EAMuC,WANvC,EAOb,cAPa,EAOG,cAPH,EAOmB,cAPnB,EAOmC,cAPnC,EAQb,cARa,EAQG,cARH,EAQmB,cARnB,EAQmC,cARnC,CAAjB;AAUA,MAAMC,UAAU,GAAI;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CATA;AAUA;AACA;AACA;;AACA,MAAMC,wBAAN,SAAuC9C,iBAAvC,CAAyD;AACrDvD,EAAAA,WAAW,GAAG;AACV,UAAM,GAAGgC,SAAT;AACA,SAAKC,MAAL,GAAckE,QAAd;AACH;;AAJoD;;AAMzDE,wBAAwB,CAAC3G,IAAzB;AAAA;AAAA;AAAA,8FAnVqG1B,EAmVrG,uBAAqHqI,wBAArH,SAAqHA,wBAArH;AAAA;AAAA;;AACAA,wBAAwB,CAAC7E,IAAzB,kBApVqGxD,EAoVrG;AAAA,QAAyGqI,wBAAzG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aApVqGrI,EAoVrG;AAAA;;AACA;AAAA,qDArVqGA,EAqVrG,mBAA2FqI,wBAA3F,EAAiI,CAAC;AACtHzG,IAAAA,IAAI,EAAEzB,SADgH;AAEtH0B,IAAAA,IAAI,EAAE,CAAC;AAAEqC,MAAAA,QAAQ,EAAEkE,UAAZ;AAAwBnE,MAAAA,MAAM,EAAEkE;AAAhC,KAAD;AAFgH,GAAD,CAAjI;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMG,eAAN,CAAsB;AAClBtG,EAAAA,WAAW,CAAC+E,GAAD,EAAM5D,KAAN,EAAaoF,QAAQ,GAAG,IAAxB,EAA8B;AACrC,SAAKxB,GAAL,GAAWA,GAAX;AACA,SAAK5D,KAAL,GAAaA,KAAb;AACA,SAAK4D,GAAL,GAAWwB,QAAQ,GAAGxB,GAAG,CAACyB,OAAJ,CAAY,OAAZ,EAAqB,EAArB,EAAyBC,IAAzB,EAAH,GAAqC1B,GAAG,CAAC0B,IAAJ,EAAxD;AACA,SAAKtF,KAAL,GAAaoF,QAAQ,GAAGpF,KAAK,CAACqF,OAAN,CAAc,OAAd,EAAuB,EAAvB,EAA2BC,IAA3B,EAAH,GAAuCtF,KAAK,CAACsF,IAAN,EAA5D;AACA,SAAKtF,KAAL,GAAa,KAAKA,KAAL,CAAWqF,OAAX,CAAmB,GAAnB,EAAwB,EAAxB,CAAb;AACH;;AAPiB;;AAStB,SAASE,OAAT,CAAiBC,MAAjB,EAAyB;AACrB,MAAIC,IAAI,GAAG,OAAOD,MAAlB;;AACA,MAAIC,IAAI,KAAK,QAAb,EAAuB;AACnB,WAAQD,MAAM,CAAC3G,WAAP,KAAuB8D,KAAxB,GAAiC,OAAjC,GACF6C,MAAM,CAAC3G,WAAP,KAAuB6G,GAAxB,GAA+B,KAA/B,GAAuC,QAD3C;AAEH;;AACD,SAAOD,IAAP;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASE,YAAT,CAAsBC,MAAtB,EAA8BC,SAAS,GAAG,GAA1C,EAA+C;AAC3C,SAAOC,MAAM,CAACF,MAAD,CAAN,CACFN,IADE,GAEFvB,KAFE,CAEI8B,SAFJ,EAGFE,GAHE,CAGGjG,GAAD,IAASA,GAAG,CAACwF,IAAJ,EAHX,EAIFU,MAJE,CAIKlG,GAAG,IAAIA,GAAG,KAAK,EAJpB,CAAP;AAKH;AACD;;;AACA,SAASmG,kBAAT,CAA4BC,MAA5B,EAAoCC,QAApC,EAA8C;AAC1C,QAAMC,aAAa,GAAIC,EAAD,IAAQ;AAC1B,QAAIF,QAAJ,EAAc;AACVE,MAAAA,EAAE,CAACrG,KAAH,GAAWmG,QAAQ,CAACE,EAAE,CAACrG,KAAJ,CAAnB;AACH;;AACD,WAAOqG,EAAP;AACH,GALD;;AAMA,SAAOH,MAAM,CACRH,GADE,CACEO,gBADF,EAEFN,MAFE,CAEKO,KAAK,IAAI,CAAC,CAACA,KAFhB,EAGFR,GAHE,CAGEK,aAHF,EAIFI,MAJE,CAIKC,cAJL,EAIqB,EAJrB,CAAP;AAKH;AACD;;;AACA,SAASC,eAAT,CAAyBd,MAAzB,EAAiCO,QAAjC,EAA2C;AACvC,MAAIQ,IAAI,GAAG,EAAX;;AACA,MAAIpB,OAAO,CAACK,MAAD,CAAP,KAAoB,KAAxB,EAA+B;AAC3BA,IAAAA,MAAM,CAACjC,OAAP,CAAe4C,KAAK,IAAII,IAAI,CAACC,IAAL,CAAUL,KAAV,CAAxB;AACH,GAFD,MAGK;AACDjG,IAAAA,MAAM,CAACoD,IAAP,CAAYkC,MAAZ,EAAoBjC,OAApB,CAA6BC,GAAD,IAAS;AACjC+C,MAAAA,IAAI,CAACC,IAAL,CAAW,GAAEhD,GAAI,IAAGgC,MAAM,CAAChC,GAAD,CAAM,EAAhC;AACH,KAFD;AAGH;;AACD,SAAOqC,kBAAkB,CAACU,IAAD,EAAOR,QAAP,CAAzB;AACH;AACD;;;AACA,SAASG,gBAAT,CAA0BD,EAA1B,EAA8B;AAC1B,QAAM,CAACzC,GAAD,EAAM,GAAGiD,IAAT,IAAiBR,EAAE,CAACtC,KAAH,CAAS,GAAT,CAAvB;AACA,SAAO,IAAIoB,eAAJ,CAAoBvB,GAApB,EAAyBiD,IAAI,CAAC3C,IAAL,CAAU,GAAV,CAAzB,CAAP;AACH;AACD;;;AACA,SAASuC,cAAT,CAAwBV,GAAxB,EAA6BQ,KAA7B,EAAoC;AAChC,MAAI,CAAC,CAACA,KAAK,CAAC3C,GAAZ,EAAiB;AACbmC,IAAAA,GAAG,CAACQ,KAAK,CAAC3C,GAAP,CAAH,GAAiB2C,KAAK,CAACvG,KAAvB;AACH;;AACD,SAAO+F,GAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMe,cAAN,SAA6BrJ,cAA7B,CAA4C;AACxCoB,EAAAA,WAAW,CAACC,UAAD,EAAaE,MAAb,EAAqBC,OAArB,EAA8B8H,SAA9B,EAAyCC,OAAzC,EAAkD7F,SAAlD,EAA6D8F,eAA7D,EAA8EC,YAA9E,EAA4FhI,UAA5F,EAAwG;AAC/G,QAAIiI,EAAJ;;AACA,UAAMrI,UAAN,EAAkB,IAAlB,EAAwBE,MAAxB,EAAgCC,OAAhC;AACA,SAAK8H,SAAL,GAAiBA,SAAjB;AACA,SAAKE,eAAL,GAAuBA,eAAvB;AACA,SAAK7H,aAAL,GAAqB,SAArB;;AACA,QAAI,CAAC,KAAK6H,eAAV,EAA2B;AACvB;AACA;AACA,WAAKA,eAAL,GAAuB,IAAIjJ,OAAJ,CAAYc,UAAZ,EAAwBkI,OAAxB,EAAiC7F,SAAjC,CAAvB;AACH;;AACD,SAAK3B,IAAL;AACA,UAAM0G,MAAM,GAAG,CAACiB,EAAE,GAAG,KAAKzH,aAAL,CAAmBC,YAAnB,CAAgC,OAAhC,CAAN,MAAoD,IAApD,IAA4DwH,EAAE,KAAK,KAAK,CAAxE,GAA4EA,EAA5E,GAAiF,EAAhG;AACA,SAAKC,cAAL,GAAsB,KAAKC,aAAL,CAAmBnB,MAAnB,CAAtB;AACA,SAAK/D,QAAL,GAAgB+E,YAAY,IAAIpJ,gBAAgB,CAACoB,UAAD,CAAhD;AACH;AACD;;;AACAa,EAAAA,eAAe,CAACC,KAAD,EAAQ;AACnB,UAAMkG,MAAM,GAAG,KAAKmB,aAAL,CAAmBrH,KAAnB,CAAf;AACA,SAAKiH,eAAL,CAAqBK,OAArB,GAA+BhH,MAAM,CAACiH,MAAP,CAAcjH,MAAM,CAACiH,MAAP,CAAc,EAAd,EAAkB,KAAKH,cAAvB,CAAd,EAAsDlB,MAAtD,CAA/B;;AACA,QAAI,KAAK/D,QAAT,EAAmB;AACf,WAAKqF,mBAAL,CAAyBtB,MAAzB;AACH;;AACD,SAAKe,eAAL,CAAqB1F,SAArB;AACH;AACD;;;AACAkG,EAAAA,WAAW,GAAG;AACV,SAAKR,eAAL,CAAqBK,OAArB,GAA+B,KAAKF,cAApC;AACA,SAAKH,eAAL,CAAqB1F,SAArB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACI8F,EAAAA,aAAa,CAACnB,MAAD,EAAS;AAClB;AACA,UAAMa,SAAS,GAAIjH,GAAD,IAAS;AAAE,UAAIqH,EAAJ;;AAAQ,aAAO,CAACA,EAAE,GAAG,KAAKJ,SAAL,CAAeZ,QAAf,CAAwB9I,eAAe,CAACqK,KAAxC,EAA+C5H,GAA/C,CAAN,MAA+D,IAA/D,IAAuEqH,EAAE,KAAK,KAAK,CAAnF,GAAuFA,EAAvF,GAA4F,EAAnG;AAAwG,KAA7I;;AACA,QAAIjB,MAAJ,EAAY;AACR,cAAQX,OAAO,CAACW,MAAD,CAAf;AACI,aAAK,QAAL;AAAe,iBAAOyB,gBAAgB,CAAChC,YAAY,CAACO,MAAD,CAAb,EAAuBa,SAAvB,CAAvB;;AACf,aAAK,OAAL;AAAc,iBAAOY,gBAAgB,CAACzB,MAAD,EAASa,SAAT,CAAvB;;AACd,aAAK,KAAL;AAAY,iBAAOL,eAAe,CAACR,MAAD,EAASa,SAAT,CAAtB;;AACZ;AAAS,iBAAOL,eAAe,CAACR,MAAD,EAASa,SAAT,CAAtB;AAJb;AAMH;;AACD,WAAO,EAAP;AACH,GAjDuC,CAkDxC;AACA;AACA;;AACA;;;AACAxF,EAAAA,SAAS,GAAG;AACR,SAAK0F,eAAL,CAAqB1F,SAArB;AACH;;AAxDuC;;AA0D5CuF,cAAc,CAACvI,IAAf;AAAA,mBAA2GuI,cAA3G,EAveqGjK,EAuerG,mBAA2IA,EAAE,CAACqD,UAA9I,GAveqGrD,EAuerG,mBAAqKU,EAAE,CAAC4C,UAAxK,GAveqGtD,EAuerG,mBAA+LU,EAAE,CAAC6C,eAAlM,GAveqGvD,EAuerG,mBAA8NsB,IAAI,CAACyJ,YAAnO,GAveqG/K,EAuerG,mBAA4PA,EAAE,CAAC4E,eAA/P,GAveqG5E,EAuerG,mBAA2RA,EAAE,CAAC6E,SAA9R,GAveqG7E,EAuerG,mBAAoTgB,EAAE,CAACG,OAAvT,OAveqGnB,EAuerG,mBAAuWa,YAAvW,GAveqGb,EAuerG,mBAAgYE,WAAhY;AAAA;;AACA+J,cAAc,CAACzG,IAAf,kBAxeqGxD,EAwerG;AAAA,QAA+FiK,cAA/F;AAAA,aAxeqGjK,EAwerG;AAAA;;AACA;AAAA,qDAzeqGA,EAyerG,mBAA2FiK,cAA3F,EAAuH,CAAC;AAC5GrI,IAAAA,IAAI,EAAEzB;AADsG,GAAD,CAAvH,EAE4B,YAAY;AAChC,WAAO,CAAC;AAAEyB,MAAAA,IAAI,EAAE5B,EAAE,CAACqD;AAAX,KAAD,EAA0B;AAAEzB,MAAAA,IAAI,EAAElB,EAAE,CAAC4C;AAAX,KAA1B,EAAmD;AAAE1B,MAAAA,IAAI,EAAElB,EAAE,CAAC6C;AAAX,KAAnD,EAAiF;AAAE3B,MAAAA,IAAI,EAAEN,IAAI,CAACyJ;AAAb,KAAjF,EAA8G;AAAEnJ,MAAAA,IAAI,EAAE5B,EAAE,CAAC4E;AAAX,KAA9G,EAA4I;AAAEhD,MAAAA,IAAI,EAAE5B,EAAE,CAAC6E;AAAX,KAA5I,EAAoK;AAAEjD,MAAAA,IAAI,EAAEZ,EAAE,CAACG,OAAX;AAAoBuC,MAAAA,UAAU,EAAE,CAAC;AAC5L9B,QAAAA,IAAI,EAAEtB;AADsL,OAAD,EAE5L;AACCsB,QAAAA,IAAI,EAAErB;AADP,OAF4L;AAAhC,KAApK,EAIW;AAAEqB,MAAAA,IAAI,EAAE+B,SAAR;AAAmBD,MAAAA,UAAU,EAAE,CAAC;AAClC9B,QAAAA,IAAI,EAAExB,MAD4B;AAElCyB,QAAAA,IAAI,EAAE,CAAChB,YAAD;AAF4B,OAAD;AAA/B,KAJX,EAOW;AAAEe,MAAAA,IAAI,EAAE6B,MAAR;AAAgBC,MAAAA,UAAU,EAAE,CAAC;AAC/B9B,QAAAA,IAAI,EAAExB,MADyB;AAE/ByB,QAAAA,IAAI,EAAE,CAAC3B,WAAD;AAFyB,OAAD;AAA5B,KAPX,CAAP;AAWH,GAdL;AAAA;;AAeA,MAAM+D,MAAM,GAAG,CACX,SADW,EAEX,YAFW,EAEG,YAFH,EAEiB,YAFjB,EAE+B,YAF/B,EAE6C,YAF7C,EAGX,eAHW,EAGM,eAHN,EAGuB,eAHvB,EAGwC,eAHxC,EAIX,eAJW,EAIM,eAJN,EAIuB,eAJvB,EAIwC,eAJxC,CAAf;AAMA,MAAMC,QAAQ,GAAI;AAClB;AACA;AACA;AACA;AACA,CALA;AAMA;AACA;AACA;AACA;;AACA,MAAM8G,qBAAN,SAAoCf,cAApC,CAAmD;AAC/CjI,EAAAA,WAAW,GAAG;AACV,UAAM,GAAGgC,SAAT;AACA,SAAKC,MAAL,GAAcA,MAAd;AACH;;AAJ8C;;AAMnD+G,qBAAqB,CAACtJ,IAAtB;AAAA;AAAA;AAAA,wFA9gBqG1B,EA8gBrG,uBAAkHgL,qBAAlH,SAAkHA,qBAAlH;AAAA;AAAA;;AACAA,qBAAqB,CAACxH,IAAtB,kBA/gBqGxD,EA+gBrG;AAAA,QAAsGgL,qBAAtG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aA/gBqGhL,EA+gBrG;AAAA;;AACA;AAAA,qDAhhBqGA,EAghBrG,mBAA2FgL,qBAA3F,EAA8H,CAAC;AACnHpJ,IAAAA,IAAI,EAAEzB,SAD6G;AAEnH0B,IAAAA,IAAI,EAAE,CAAC;AAAEqC,MAAAA,QAAF;AAAYD,MAAAA;AAAZ,KAAD;AAF6G,GAAD,CAA9H;AAAA;AAIA;;;AACA,SAAS6G,gBAAT,CAA0BzB,MAA1B,EAAkCC,QAAlC,EAA4C;AACxC,QAAMC,aAAa,GAAIC,EAAD,IAAQ;AAC1B,QAAIF,QAAJ,EAAc;AACVE,MAAAA,EAAE,CAACrG,KAAH,GAAWmG,QAAQ,CAACE,EAAE,CAACrG,KAAJ,CAAnB;AACH;;AACD,WAAOqG,EAAP;AACH,GALD;;AAMA,SAAOH,MAAM,CACRH,GADE,CACEO,gBADF,EAEFN,MAFE,CAEKO,KAAK,IAAI,CAAC,CAACA,KAFhB,EAGFR,GAHE,CAGEK,aAHF,EAIFI,MAJE,CAIKC,cAJL,EAIqB,EAJrB,CAAP;AAKH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMqB,cAAc,GAAG,CACnB5C,wBADmB,EAEnBrD,qBAFmB,EAGnBgG,qBAHmB,EAInBjH,sBAJmB,CAAvB;AAMA;AACA;AACA;AACA;AACA;;AACA,MAAMmH,cAAN,CAAqB;;AAErBA,cAAc,CAACxJ,IAAf;AAAA,mBAA2GwJ,cAA3G;AAAA;;AACAA,cAAc,CAACC,IAAf,kBAxjBqGnL,EAwjBrG;AAAA,QAA4GkL;AAA5G;AAOAA,cAAc,CAACE,IAAf,kBA/jBqGpL,EA+jBrG;AAAA,YAAsI,CAACe,UAAD,CAAtI;AAAA;;AACA;AAAA,qDAhkBqGf,EAgkBrG,mBAA2FkL,cAA3F,EAAuH,CAAC;AAC5GtJ,IAAAA,IAAI,EAAEnB,QADsG;AAE5GoB,IAAAA,IAAI,EAAE,CAAC;AACCwJ,MAAAA,OAAO,EAAE,CAACtK,UAAD,CADV;AAECuK,MAAAA,YAAY,EAAE,CAAC,GAAGL,cAAJ,CAFf;AAGCM,MAAAA,OAAO,EAAE,CAAC,GAAGN,cAAJ;AAHV,KAAD;AAFsG,GAAD,CAAvH;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAAS9G,cAAT,EAAyBa,qBAAzB,EAAgDjB,sBAAhD,EAAwEsE,wBAAxE,EAAkG2C,qBAAlG,EAAyHE,cAAzH,EAAyInJ,eAAzI,EAA0JR,kBAA1J,EAA8KgE,iBAA9K,EAAiMN,oBAAjM,EAAuNgF,cAAvN", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, PLATFORM_ID, Directive, Inject, Input, Optional, Self, SecurityContext, NgModule } from '@angular/core';\nimport * as i1 from '@angular/flex-layout/core';\nimport { StyleBuilder, BaseDirective2, SERVER_TOKEN, LAYOUT_CONFIG, CoreModule } from '@angular/flex-layout/core';\nimport * as i2 from '@angular/common';\nimport { isPlatformServer, NgClass, NgStyle } from '@angular/common';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i2$1 from '@angular/platform-browser';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass ImgSrcStyleBuilder extends StyleBuilder {\n    buildStyles(url) {\n        return { 'content': url ? `url(${url})` : '' };\n    }\n}\nImgSrcStyleBuilder.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: ImgSrcStyleBuilder, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\nImgSrcStyleBuilder.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: ImgSrcStyleBuilder, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: ImgSrcStyleBuilder, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\nclass ImgSrcDirective extends BaseDirective2 {\n    constructor(elementRef, styleBuilder, styler, marshal, platformId, serverModuleLoaded) {\n        super(elementRef, styleBuilder, styler, marshal);\n        this.platformId = platformId;\n        this.serverModuleLoaded = serverModuleLoaded;\n        this.DIRECTIVE_KEY = 'img-src';\n        this.defaultSrc = '';\n        this.styleCache = imgSrcCache;\n        this.init();\n        this.setValue(this.nativeElement.getAttribute('src') || '', '');\n        if (isPlatformServer(this.platformId) && this.serverModuleLoaded) {\n            this.nativeElement.setAttribute('src', '');\n        }\n    }\n    set src(val) {\n        this.defaultSrc = val;\n        this.setValue(this.defaultSrc, '');\n    }\n    /**\n     * Use the [responsively] activated input value to update\n     * the host img src attribute or assign a default `img.src=''`\n     * if the src has not been defined.\n     *\n     * Do nothing to standard `<img src=\"\">` usages, only when responsive\n     * keys are present do we actually call `setAttribute()`\n     */\n    updateWithValue(value) {\n        const url = value || this.defaultSrc;\n        if (isPlatformServer(this.platformId) && this.serverModuleLoaded) {\n            this.addStyles(url);\n        }\n        else {\n            this.nativeElement.setAttribute('src', url);\n        }\n    }\n}\nImgSrcDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: ImgSrcDirective, deps: [{ token: i0.ElementRef }, { token: ImgSrcStyleBuilder }, { token: i1.StyleUtils }, { token: i1.MediaMarshaller }, { token: PLATFORM_ID }, { token: SERVER_TOKEN }], target: i0.ɵɵFactoryTarget.Directive });\nImgSrcDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.2\", type: ImgSrcDirective, inputs: { src: \"src\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: ImgSrcDirective, decorators: [{\n            type: Directive\n        }], ctorParameters: function () {\n        return [{ type: i0.ElementRef }, { type: ImgSrcStyleBuilder }, { type: i1.StyleUtils }, { type: i1.MediaMarshaller }, { type: Object, decorators: [{\n                        type: Inject,\n                        args: [PLATFORM_ID]\n                    }] }, { type: undefined, decorators: [{\n                        type: Inject,\n                        args: [SERVER_TOKEN]\n                    }] }];\n    }, propDecorators: { src: [{\n                type: Input,\n                args: ['src']\n            }] } });\nconst imgSrcCache = new Map();\nconst inputs$3 = [\n    'src.xs', 'src.sm', 'src.md', 'src.lg', 'src.xl',\n    'src.lt-sm', 'src.lt-md', 'src.lt-lg', 'src.lt-xl',\n    'src.gt-xs', 'src.gt-sm', 'src.gt-md', 'src.gt-lg'\n];\nconst selector$3 = `\n  img[src.xs],    img[src.sm],    img[src.md],    img[src.lg],   img[src.xl],\n  img[src.lt-sm], img[src.lt-md], img[src.lt-lg], img[src.lt-xl],\n  img[src.gt-xs], img[src.gt-sm], img[src.gt-md], img[src.gt-lg]\n`;\n/**\n * This directive provides a responsive API for the HTML <img> 'src' attribute\n * and will update the img.src property upon each responsive activation.\n *\n * e.g.\n *      <img src=\"defaultScene.jpg\" src.xs=\"mobileScene.jpg\"></img>\n *\n * @see https://css-tricks.com/responsive-images-youre-just-changing-resolutions-use-src/\n */\nclass DefaultImgSrcDirective extends ImgSrcDirective {\n    constructor() {\n        super(...arguments);\n        this.inputs = inputs$3;\n    }\n}\nDefaultImgSrcDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: DefaultImgSrcDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nDefaultImgSrcDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.2\", type: DefaultImgSrcDirective, selector: \"\\n  img[src.xs],    img[src.sm],    img[src.md],    img[src.lg],   img[src.xl],\\n  img[src.lt-sm], img[src.lt-md], img[src.lt-lg], img[src.lt-xl],\\n  img[src.gt-xs], img[src.gt-sm], img[src.gt-md], img[src.gt-lg]\\n\", inputs: { \"src.xs\": \"src.xs\", \"src.sm\": \"src.sm\", \"src.md\": \"src.md\", \"src.lg\": \"src.lg\", \"src.xl\": \"src.xl\", \"src.lt-sm\": \"src.lt-sm\", \"src.lt-md\": \"src.lt-md\", \"src.lt-lg\": \"src.lt-lg\", \"src.lt-xl\": \"src.lt-xl\", \"src.gt-xs\": \"src.gt-xs\", \"src.gt-sm\": \"src.gt-sm\", \"src.gt-md\": \"src.gt-md\", \"src.gt-lg\": \"src.gt-lg\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: DefaultImgSrcDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: selector$3, inputs: inputs$3 }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass ClassDirective extends BaseDirective2 {\n    constructor(elementRef, styler, marshal, iterableDiffers, keyValueDiffers, renderer2, ngClassInstance) {\n        super(elementRef, null, styler, marshal);\n        this.ngClassInstance = ngClassInstance;\n        this.DIRECTIVE_KEY = 'ngClass';\n        if (!this.ngClassInstance) {\n            // Create an instance NgClass Directive instance only if `ngClass=\"\"` has NOT been defined on\n            // the same host element; since the responsive variations may be defined...\n            this.ngClassInstance = new NgClass(iterableDiffers, keyValueDiffers, elementRef, renderer2);\n        }\n        this.init();\n        this.setValue('', '');\n    }\n    /**\n     * Capture class assignments so we cache the default classes\n     * which are merged with activated styles and used as fallbacks.\n     */\n    set klass(val) {\n        this.ngClassInstance.klass = val;\n        this.setValue(val, '');\n    }\n    updateWithValue(value) {\n        this.ngClassInstance.ngClass = value;\n        this.ngClassInstance.ngDoCheck();\n    }\n    // ******************************************************************\n    // Lifecycle Hooks\n    // ******************************************************************\n    /**\n     * For ChangeDetectionStrategy.onPush and ngOnChanges() updates\n     */\n    ngDoCheck() {\n        this.ngClassInstance.ngDoCheck();\n    }\n}\nClassDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: ClassDirective, deps: [{ token: i0.ElementRef }, { token: i1.StyleUtils }, { token: i1.MediaMarshaller }, { token: i0.IterableDiffers }, { token: i0.KeyValueDiffers }, { token: i0.Renderer2 }, { token: i2.NgClass, optional: true, self: true }], target: i0.ɵɵFactoryTarget.Directive });\nClassDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.2\", type: ClassDirective, inputs: { klass: [\"class\", \"klass\"] }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: ClassDirective, decorators: [{\n            type: Directive\n        }], ctorParameters: function () {\n        return [{ type: i0.ElementRef }, { type: i1.StyleUtils }, { type: i1.MediaMarshaller }, { type: i0.IterableDiffers }, { type: i0.KeyValueDiffers }, { type: i0.Renderer2 }, { type: i2.NgClass, decorators: [{\n                        type: Optional\n                    }, {\n                        type: Self\n                    }] }];\n    }, propDecorators: { klass: [{\n                type: Input,\n                args: ['class']\n            }] } });\nconst inputs$2 = [\n    'ngClass', 'ngClass.xs', 'ngClass.sm', 'ngClass.md', 'ngClass.lg', 'ngClass.xl',\n    'ngClass.lt-sm', 'ngClass.lt-md', 'ngClass.lt-lg', 'ngClass.lt-xl',\n    'ngClass.gt-xs', 'ngClass.gt-sm', 'ngClass.gt-md', 'ngClass.gt-lg'\n];\nconst selector$2 = `\n  [ngClass], [ngClass.xs], [ngClass.sm], [ngClass.md], [ngClass.lg], [ngClass.xl],\n  [ngClass.lt-sm], [ngClass.lt-md], [ngClass.lt-lg], [ngClass.lt-xl],\n  [ngClass.gt-xs], [ngClass.gt-sm], [ngClass.gt-md], [ngClass.gt-lg]\n`;\n/**\n * Directive to add responsive support for ngClass.\n * This maintains the core functionality of 'ngClass' and adds responsive API\n * Note: this class is a no-op when rendered on the server\n */\nclass DefaultClassDirective extends ClassDirective {\n    constructor() {\n        super(...arguments);\n        this.inputs = inputs$2;\n    }\n}\nDefaultClassDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: DefaultClassDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nDefaultClassDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.2\", type: DefaultClassDirective, selector: \"\\n  [ngClass], [ngClass.xs], [ngClass.sm], [ngClass.md], [ngClass.lg], [ngClass.xl],\\n  [ngClass.lt-sm], [ngClass.lt-md], [ngClass.lt-lg], [ngClass.lt-xl],\\n  [ngClass.gt-xs], [ngClass.gt-sm], [ngClass.gt-md], [ngClass.gt-lg]\\n\", inputs: { ngClass: \"ngClass\", \"ngClass.xs\": \"ngClass.xs\", \"ngClass.sm\": \"ngClass.sm\", \"ngClass.md\": \"ngClass.md\", \"ngClass.lg\": \"ngClass.lg\", \"ngClass.xl\": \"ngClass.xl\", \"ngClass.lt-sm\": \"ngClass.lt-sm\", \"ngClass.lt-md\": \"ngClass.lt-md\", \"ngClass.lt-lg\": \"ngClass.lt-lg\", \"ngClass.lt-xl\": \"ngClass.lt-xl\", \"ngClass.gt-xs\": \"ngClass.gt-xs\", \"ngClass.gt-sm\": \"ngClass.gt-sm\", \"ngClass.gt-md\": \"ngClass.gt-md\", \"ngClass.gt-lg\": \"ngClass.gt-lg\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: DefaultClassDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: selector$2, inputs: inputs$2 }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass ShowHideStyleBuilder extends StyleBuilder {\n    buildStyles(show, parent) {\n        const shouldShow = show === 'true';\n        return { 'display': shouldShow ? parent.display || (parent.isServer ? 'initial' : '') : 'none' };\n    }\n}\nShowHideStyleBuilder.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: ShowHideStyleBuilder, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\nShowHideStyleBuilder.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: ShowHideStyleBuilder, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: ShowHideStyleBuilder, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\nclass ShowHideDirective extends BaseDirective2 {\n    constructor(elementRef, styleBuilder, styler, marshal, layoutConfig, platformId, serverModuleLoaded) {\n        super(elementRef, styleBuilder, styler, marshal);\n        this.layoutConfig = layoutConfig;\n        this.platformId = platformId;\n        this.serverModuleLoaded = serverModuleLoaded;\n        this.DIRECTIVE_KEY = 'show-hide';\n        /** Original DOM Element CSS display style */\n        this.display = '';\n        this.hasLayout = false;\n        this.hasFlexChild = false;\n    }\n    // *********************************************\n    // Lifecycle Methods\n    // *********************************************\n    ngAfterViewInit() {\n        this.trackExtraTriggers();\n        const children = Array.from(this.nativeElement.children);\n        for (let i = 0; i < children.length; i++) {\n            if (this.marshal.hasValue(children[i], 'flex')) {\n                this.hasFlexChild = true;\n                break;\n            }\n        }\n        if (DISPLAY_MAP.has(this.nativeElement)) {\n            this.display = DISPLAY_MAP.get(this.nativeElement);\n        }\n        else {\n            this.display = this.getDisplayStyle();\n            DISPLAY_MAP.set(this.nativeElement, this.display);\n        }\n        this.init();\n        // set the default to show unless explicitly overridden\n        const defaultValue = this.marshal.getValue(this.nativeElement, this.DIRECTIVE_KEY, '');\n        if (defaultValue === undefined || defaultValue === '') {\n            this.setValue(true, '');\n        }\n        else {\n            this.triggerUpdate();\n        }\n    }\n    /**\n     * On changes to any @Input properties...\n     * Default to use the non-responsive Input value ('fxShow')\n     * Then conditionally override with the mq-activated Input's current value\n     */\n    ngOnChanges(changes) {\n        Object.keys(changes).forEach(key => {\n            if (this.inputs.indexOf(key) !== -1) {\n                const inputKey = key.split('.');\n                const bp = inputKey.slice(1).join('.');\n                const inputValue = changes[key].currentValue;\n                let shouldShow = inputValue !== '' ?\n                    inputValue !== 0 ? coerceBooleanProperty(inputValue) : false\n                    : true;\n                if (inputKey[0] === 'fxHide') {\n                    shouldShow = !shouldShow;\n                }\n                this.setValue(shouldShow, bp);\n            }\n        });\n    }\n    // *********************************************\n    // Protected methods\n    // *********************************************\n    /**\n     *  Watch for these extra triggers to update fxShow, fxHide stylings\n     */\n    trackExtraTriggers() {\n        this.hasLayout = this.marshal.hasValue(this.nativeElement, 'layout');\n        ['layout', 'layout-align'].forEach(key => {\n            this.marshal\n                .trackValue(this.nativeElement, key)\n                .pipe(takeUntil(this.destroySubject))\n                .subscribe(this.triggerUpdate.bind(this));\n        });\n    }\n    /**\n     * Override accessor to the current HTMLElement's `display` style\n     * Note: Show/Hide will not change the display to 'flex' but will set it to 'block'\n     * unless it was already explicitly specified inline or in a CSS stylesheet.\n     */\n    getDisplayStyle() {\n        return (this.hasLayout || (this.hasFlexChild && this.layoutConfig.addFlexToParent)) ?\n            'flex' : this.styler.lookupStyle(this.nativeElement, 'display', true);\n    }\n    /** Validate the visibility value and then update the host's inline display style */\n    updateWithValue(value = true) {\n        if (value === '') {\n            return;\n        }\n        const isServer = isPlatformServer(this.platformId);\n        this.addStyles(value ? 'true' : 'false', { display: this.display, isServer });\n        if (isServer && this.serverModuleLoaded) {\n            this.nativeElement.style.setProperty('display', '');\n        }\n        this.marshal.triggerUpdate(this.parentElement, 'layout-gap');\n    }\n}\nShowHideDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: ShowHideDirective, deps: [{ token: i0.ElementRef }, { token: ShowHideStyleBuilder }, { token: i1.StyleUtils }, { token: i1.MediaMarshaller }, { token: LAYOUT_CONFIG }, { token: PLATFORM_ID }, { token: SERVER_TOKEN }], target: i0.ɵɵFactoryTarget.Directive });\nShowHideDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.2\", type: ShowHideDirective, usesInheritance: true, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: ShowHideDirective, decorators: [{\n            type: Directive\n        }], ctorParameters: function () {\n        return [{ type: i0.ElementRef }, { type: ShowHideStyleBuilder }, { type: i1.StyleUtils }, { type: i1.MediaMarshaller }, { type: undefined, decorators: [{\n                        type: Inject,\n                        args: [LAYOUT_CONFIG]\n                    }] }, { type: Object, decorators: [{\n                        type: Inject,\n                        args: [PLATFORM_ID]\n                    }] }, { type: undefined, decorators: [{\n                        type: Inject,\n                        args: [SERVER_TOKEN]\n                    }] }];\n    } });\nconst DISPLAY_MAP = new WeakMap();\nconst inputs$1 = [\n    'fxShow', 'fxShow.print',\n    'fxShow.xs', 'fxShow.sm', 'fxShow.md', 'fxShow.lg', 'fxShow.xl',\n    'fxShow.lt-sm', 'fxShow.lt-md', 'fxShow.lt-lg', 'fxShow.lt-xl',\n    'fxShow.gt-xs', 'fxShow.gt-sm', 'fxShow.gt-md', 'fxShow.gt-lg',\n    'fxHide', 'fxHide.print',\n    'fxHide.xs', 'fxHide.sm', 'fxHide.md', 'fxHide.lg', 'fxHide.xl',\n    'fxHide.lt-sm', 'fxHide.lt-md', 'fxHide.lt-lg', 'fxHide.lt-xl',\n    'fxHide.gt-xs', 'fxHide.gt-sm', 'fxHide.gt-md', 'fxHide.gt-lg'\n];\nconst selector$1 = `\n  [fxShow], [fxShow.print],\n  [fxShow.xs], [fxShow.sm], [fxShow.md], [fxShow.lg], [fxShow.xl],\n  [fxShow.lt-sm], [fxShow.lt-md], [fxShow.lt-lg], [fxShow.lt-xl],\n  [fxShow.gt-xs], [fxShow.gt-sm], [fxShow.gt-md], [fxShow.gt-lg],\n  [fxHide], [fxHide.print],\n  [fxHide.xs], [fxHide.sm], [fxHide.md], [fxHide.lg], [fxHide.xl],\n  [fxHide.lt-sm], [fxHide.lt-md], [fxHide.lt-lg], [fxHide.lt-xl],\n  [fxHide.gt-xs], [fxHide.gt-sm], [fxHide.gt-md], [fxHide.gt-lg]\n`;\n/**\n * 'show' Layout API directive\n */\nclass DefaultShowHideDirective extends ShowHideDirective {\n    constructor() {\n        super(...arguments);\n        this.inputs = inputs$1;\n    }\n}\nDefaultShowHideDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: DefaultShowHideDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nDefaultShowHideDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.2\", type: DefaultShowHideDirective, selector: \"\\n  [fxShow], [fxShow.print],\\n  [fxShow.xs], [fxShow.sm], [fxShow.md], [fxShow.lg], [fxShow.xl],\\n  [fxShow.lt-sm], [fxShow.lt-md], [fxShow.lt-lg], [fxShow.lt-xl],\\n  [fxShow.gt-xs], [fxShow.gt-sm], [fxShow.gt-md], [fxShow.gt-lg],\\n  [fxHide], [fxHide.print],\\n  [fxHide.xs], [fxHide.sm], [fxHide.md], [fxHide.lg], [fxHide.xl],\\n  [fxHide.lt-sm], [fxHide.lt-md], [fxHide.lt-lg], [fxHide.lt-xl],\\n  [fxHide.gt-xs], [fxHide.gt-sm], [fxHide.gt-md], [fxHide.gt-lg]\\n\", inputs: { fxShow: \"fxShow\", \"fxShow.print\": \"fxShow.print\", \"fxShow.xs\": \"fxShow.xs\", \"fxShow.sm\": \"fxShow.sm\", \"fxShow.md\": \"fxShow.md\", \"fxShow.lg\": \"fxShow.lg\", \"fxShow.xl\": \"fxShow.xl\", \"fxShow.lt-sm\": \"fxShow.lt-sm\", \"fxShow.lt-md\": \"fxShow.lt-md\", \"fxShow.lt-lg\": \"fxShow.lt-lg\", \"fxShow.lt-xl\": \"fxShow.lt-xl\", \"fxShow.gt-xs\": \"fxShow.gt-xs\", \"fxShow.gt-sm\": \"fxShow.gt-sm\", \"fxShow.gt-md\": \"fxShow.gt-md\", \"fxShow.gt-lg\": \"fxShow.gt-lg\", fxHide: \"fxHide\", \"fxHide.print\": \"fxHide.print\", \"fxHide.xs\": \"fxHide.xs\", \"fxHide.sm\": \"fxHide.sm\", \"fxHide.md\": \"fxHide.md\", \"fxHide.lg\": \"fxHide.lg\", \"fxHide.xl\": \"fxHide.xl\", \"fxHide.lt-sm\": \"fxHide.lt-sm\", \"fxHide.lt-md\": \"fxHide.lt-md\", \"fxHide.lt-lg\": \"fxHide.lt-lg\", \"fxHide.lt-xl\": \"fxHide.lt-xl\", \"fxHide.gt-xs\": \"fxHide.gt-xs\", \"fxHide.gt-sm\": \"fxHide.gt-sm\", \"fxHide.gt-md\": \"fxHide.gt-md\", \"fxHide.gt-lg\": \"fxHide.gt-lg\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: DefaultShowHideDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: selector$1, inputs: inputs$1 }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** NgStyle allowed inputs */\nclass NgStyleKeyValue {\n    constructor(key, value, noQuotes = true) {\n        this.key = key;\n        this.value = value;\n        this.key = noQuotes ? key.replace(/['\"]/g, '').trim() : key.trim();\n        this.value = noQuotes ? value.replace(/['\"]/g, '').trim() : value.trim();\n        this.value = this.value.replace(/;/, '');\n    }\n}\nfunction getType(target) {\n    let what = typeof target;\n    if (what === 'object') {\n        return (target.constructor === Array) ? 'array' :\n            (target.constructor === Set) ? 'set' : 'object';\n    }\n    return what;\n}\n/**\n * Split string of key:value pairs into Array of k-v pairs\n * e.g.  'key:value; key:value; key:value;' -> ['key:value',...]\n */\nfunction buildRawList(source, delimiter = ';') {\n    return String(source)\n        .trim()\n        .split(delimiter)\n        .map((val) => val.trim())\n        .filter(val => val !== '');\n}\n/** Convert array of key:value strings to a iterable map object */\nfunction buildMapFromList$1(styles, sanitize) {\n    const sanitizeValue = (it) => {\n        if (sanitize) {\n            it.value = sanitize(it.value);\n        }\n        return it;\n    };\n    return styles\n        .map(stringToKeyValue)\n        .filter(entry => !!entry)\n        .map(sanitizeValue)\n        .reduce(keyValuesToMap, {});\n}\n/** Convert Set<string> or raw Object to an iterable NgStyleMap */\nfunction buildMapFromSet(source, sanitize) {\n    let list = [];\n    if (getType(source) === 'set') {\n        source.forEach(entry => list.push(entry));\n    }\n    else {\n        Object.keys(source).forEach((key) => {\n            list.push(`${key}:${source[key]}`);\n        });\n    }\n    return buildMapFromList$1(list, sanitize);\n}\n/** Convert 'key:value' -> [key, value] */\nfunction stringToKeyValue(it) {\n    const [key, ...vals] = it.split(':');\n    return new NgStyleKeyValue(key, vals.join(':'));\n}\n/** Convert [ [key,value] ] -> { key : value } */\nfunction keyValuesToMap(map, entry) {\n    if (!!entry.key) {\n        map[entry.key] = entry.value;\n    }\n    return map;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass StyleDirective extends BaseDirective2 {\n    constructor(elementRef, styler, marshal, sanitizer, differs, renderer2, ngStyleInstance, serverLoaded, platformId) {\n        var _a;\n        super(elementRef, null, styler, marshal);\n        this.sanitizer = sanitizer;\n        this.ngStyleInstance = ngStyleInstance;\n        this.DIRECTIVE_KEY = 'ngStyle';\n        if (!this.ngStyleInstance) {\n            // Create an instance NgStyle Directive instance only if `ngStyle=\"\"` has NOT been\n            // defined on the same host element; since the responsive variations may be defined...\n            this.ngStyleInstance = new NgStyle(elementRef, differs, renderer2);\n        }\n        this.init();\n        const styles = (_a = this.nativeElement.getAttribute('style')) !== null && _a !== void 0 ? _a : '';\n        this.fallbackStyles = this.buildStyleMap(styles);\n        this.isServer = serverLoaded && isPlatformServer(platformId);\n    }\n    /** Add generated styles */\n    updateWithValue(value) {\n        const styles = this.buildStyleMap(value);\n        this.ngStyleInstance.ngStyle = Object.assign(Object.assign({}, this.fallbackStyles), styles);\n        if (this.isServer) {\n            this.applyStyleToElement(styles);\n        }\n        this.ngStyleInstance.ngDoCheck();\n    }\n    /** Remove generated styles */\n    clearStyles() {\n        this.ngStyleInstance.ngStyle = this.fallbackStyles;\n        this.ngStyleInstance.ngDoCheck();\n    }\n    /**\n     * Convert raw strings to ngStyleMap; which is required by ngStyle\n     * NOTE: Raw string key-value pairs MUST be delimited by `;`\n     *       Comma-delimiters are not supported due to complexities of\n     *       possible style values such as `rgba(x,x,x,x)` and others\n     */\n    buildStyleMap(styles) {\n        // Always safe-guard (aka sanitize) style property values\n        const sanitizer = (val) => { var _a; return (_a = this.sanitizer.sanitize(SecurityContext.STYLE, val)) !== null && _a !== void 0 ? _a : ''; };\n        if (styles) {\n            switch (getType(styles)) {\n                case 'string': return buildMapFromList(buildRawList(styles), sanitizer);\n                case 'array': return buildMapFromList(styles, sanitizer);\n                case 'set': return buildMapFromSet(styles, sanitizer);\n                default: return buildMapFromSet(styles, sanitizer);\n            }\n        }\n        return {};\n    }\n    // ******************************************************************\n    // Lifecycle Hooks\n    // ******************************************************************\n    /** For ChangeDetectionStrategy.onPush and ngOnChanges() updates */\n    ngDoCheck() {\n        this.ngStyleInstance.ngDoCheck();\n    }\n}\nStyleDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: StyleDirective, deps: [{ token: i0.ElementRef }, { token: i1.StyleUtils }, { token: i1.MediaMarshaller }, { token: i2$1.DomSanitizer }, { token: i0.KeyValueDiffers }, { token: i0.Renderer2 }, { token: i2.NgStyle, optional: true, self: true }, { token: SERVER_TOKEN }, { token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Directive });\nStyleDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.2\", type: StyleDirective, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: StyleDirective, decorators: [{\n            type: Directive\n        }], ctorParameters: function () {\n        return [{ type: i0.ElementRef }, { type: i1.StyleUtils }, { type: i1.MediaMarshaller }, { type: i2$1.DomSanitizer }, { type: i0.KeyValueDiffers }, { type: i0.Renderer2 }, { type: i2.NgStyle, decorators: [{\n                        type: Optional\n                    }, {\n                        type: Self\n                    }] }, { type: undefined, decorators: [{\n                        type: Inject,\n                        args: [SERVER_TOKEN]\n                    }] }, { type: Object, decorators: [{\n                        type: Inject,\n                        args: [PLATFORM_ID]\n                    }] }];\n    } });\nconst inputs = [\n    'ngStyle',\n    'ngStyle.xs', 'ngStyle.sm', 'ngStyle.md', 'ngStyle.lg', 'ngStyle.xl',\n    'ngStyle.lt-sm', 'ngStyle.lt-md', 'ngStyle.lt-lg', 'ngStyle.lt-xl',\n    'ngStyle.gt-xs', 'ngStyle.gt-sm', 'ngStyle.gt-md', 'ngStyle.gt-lg'\n];\nconst selector = `\n  [ngStyle],\n  [ngStyle.xs], [ngStyle.sm], [ngStyle.md], [ngStyle.lg], [ngStyle.xl],\n  [ngStyle.lt-sm], [ngStyle.lt-md], [ngStyle.lt-lg], [ngStyle.lt-xl],\n  [ngStyle.gt-xs], [ngStyle.gt-sm], [ngStyle.gt-md], [ngStyle.gt-lg]\n`;\n/**\n * Directive to add responsive support for ngStyle.\n *\n */\nclass DefaultStyleDirective extends StyleDirective {\n    constructor() {\n        super(...arguments);\n        this.inputs = inputs;\n    }\n}\nDefaultStyleDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: DefaultStyleDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nDefaultStyleDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.2\", type: DefaultStyleDirective, selector: \"\\n  [ngStyle],\\n  [ngStyle.xs], [ngStyle.sm], [ngStyle.md], [ngStyle.lg], [ngStyle.xl],\\n  [ngStyle.lt-sm], [ngStyle.lt-md], [ngStyle.lt-lg], [ngStyle.lt-xl],\\n  [ngStyle.gt-xs], [ngStyle.gt-sm], [ngStyle.gt-md], [ngStyle.gt-lg]\\n\", inputs: { ngStyle: \"ngStyle\", \"ngStyle.xs\": \"ngStyle.xs\", \"ngStyle.sm\": \"ngStyle.sm\", \"ngStyle.md\": \"ngStyle.md\", \"ngStyle.lg\": \"ngStyle.lg\", \"ngStyle.xl\": \"ngStyle.xl\", \"ngStyle.lt-sm\": \"ngStyle.lt-sm\", \"ngStyle.lt-md\": \"ngStyle.lt-md\", \"ngStyle.lt-lg\": \"ngStyle.lt-lg\", \"ngStyle.lt-xl\": \"ngStyle.lt-xl\", \"ngStyle.gt-xs\": \"ngStyle.gt-xs\", \"ngStyle.gt-sm\": \"ngStyle.gt-sm\", \"ngStyle.gt-md\": \"ngStyle.gt-md\", \"ngStyle.gt-lg\": \"ngStyle.gt-lg\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: DefaultStyleDirective, decorators: [{\n            type: Directive,\n            args: [{ selector, inputs }]\n        }] });\n/** Build a styles map from a list of styles, while sanitizing bad values first */\nfunction buildMapFromList(styles, sanitize) {\n    const sanitizeValue = (it) => {\n        if (sanitize) {\n            it.value = sanitize(it.value);\n        }\n        return it;\n    };\n    return styles\n        .map(stringToKeyValue)\n        .filter(entry => !!entry)\n        .map(sanitizeValue)\n        .reduce(keyValuesToMap, {});\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst ALL_DIRECTIVES = [\n    DefaultShowHideDirective,\n    DefaultClassDirective,\n    DefaultStyleDirective,\n    DefaultImgSrcDirective,\n];\n/**\n * *****************************************************************\n * Define module for the Extended API\n * *****************************************************************\n */\nclass ExtendedModule {\n}\nExtendedModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: ExtendedModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nExtendedModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: ExtendedModule, declarations: [DefaultShowHideDirective,\n        DefaultClassDirective,\n        DefaultStyleDirective,\n        DefaultImgSrcDirective], imports: [CoreModule], exports: [DefaultShowHideDirective,\n        DefaultClassDirective,\n        DefaultStyleDirective,\n        DefaultImgSrcDirective] });\nExtendedModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: ExtendedModule, imports: [[CoreModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.2\", ngImport: i0, type: ExtendedModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CoreModule],\n                    declarations: [...ALL_DIRECTIVES],\n                    exports: [...ALL_DIRECTIVES]\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ClassDirective, DefaultClassDirective, DefaultImgSrcDirective, DefaultShowHideDirective, DefaultStyleDirective, ExtendedModule, ImgSrcDirective, ImgSrcStyleBuilder, ShowHideDirective, ShowHideStyleBuilder, StyleDirective };\n"]}, "metadata": {}, "sourceType": "module"}