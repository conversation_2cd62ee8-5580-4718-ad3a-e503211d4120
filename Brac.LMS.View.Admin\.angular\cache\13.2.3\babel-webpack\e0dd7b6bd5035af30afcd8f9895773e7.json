{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ColumnMode } from \"@swimlane/ngx-datatable\";\nimport { BlockUI } from \"ng-block-ui\";\nimport { ResponseStatus } from \"../_models/enum\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../_services/common.service\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"ng-block-ui\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@ng-select/ng-select\";\nimport * as i8 from \"ngx-moment\";\n\nconst _c0 = function () {\n  return {\n    standalone: true\n  };\n};\n\nfunction TraineeCertificateTestPublishComponent_ng_select_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 18);\n    i0.ɵɵlistener(\"ngModelChange\", function TraineeCertificateTestPublishComponent_ng_select_19_Template_ng_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return ctx_r4.selectedCourse = $event;\n    })(\"change\", function TraineeCertificateTestPublishComponent_ng_select_19_Template_ng_select_change_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return ctx_r6.filterList();\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.selectedCourse)(\"ngModelOptions\", i0.ɵɵpureFunction0(5, _c0))(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r0.courseList);\n  }\n}\n\nfunction TraineeCertificateTestPublishComponent_div_20_tr_26_p_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r8.Result, \" \");\n  }\n}\n\nfunction TraineeCertificateTestPublishComponent_div_20_tr_26_p_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r8.Result, \" \");\n  }\n}\n\nfunction TraineeCertificateTestPublishComponent_div_20_tr_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵelementStart(2, \"div\", 28);\n    i0.ɵɵelementStart(3, \"input\", 29);\n    i0.ɵɵlistener(\"ngModelChange\", function TraineeCertificateTestPublishComponent_div_20_tr_26_Template_input_ngModelChange_3_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const item_r8 = restoredCtx.$implicit;\n      return item_r8.Selected = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"label\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"amDateFormat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtemplate(15, TraineeCertificateTestPublishComponent_div_20_tr_26_p_15_Template, 2, 1, \"p\", 31);\n    i0.ɵɵtemplate(16, TraineeCertificateTestPublishComponent_div_20_tr_26_p_16_Template, 2, 1, \"p\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"name\", item_r8.Id);\n    i0.ɵɵpropertyInterpolate(\"id\", item_r8.Id);\n    i0.ɵɵpropertyInterpolate(\"value\", item_r8.Id);\n    i0.ɵɵproperty(\"ngModel\", item_r8.Selected)(\"ngModelOptions\", i0.ɵɵpureFunction0(16, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"for\", item_r8.Id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r8.Name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r8.Status, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(11, 13, item_r8.MarkedOn, \"DD MMM YYYY, h:mm a\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", item_r8.GainedMarks, \" / \", item_r8.TotalMarks, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r8.Result === \"Passed\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r8.Result === \"Failed\");\n  }\n}\n\nfunction TraineeCertificateTestPublishComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelementStart(1, \"div\", 20);\n    i0.ɵɵelementStart(2, \"h5\", 3);\n    i0.ɵɵtext(3, \"Select To Publish\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 21);\n    i0.ɵɵelementStart(5, \"input\", 22);\n    i0.ɵɵlistener(\"ngModelChange\", function TraineeCertificateTestPublishComponent_div_20_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return ctx_r15.selectAll = $event;\n    })(\"change\", function TraineeCertificateTestPublishComponent_div_20_Template_input_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return ctx_r17.onChangeSelectAll($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"label\", 23);\n    i0.ɵɵtext(7, \" Select All \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 24);\n    i0.ɵɵelementStart(9, \"div\", 25);\n    i0.ɵɵelementStart(10, \"table\", 26);\n    i0.ɵɵelementStart(11, \"thead\");\n    i0.ɵɵelementStart(12, \"tr\");\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"#\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\");\n    i0.ɵɵtext(16, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\");\n    i0.ɵɵtext(18, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\");\n    i0.ɵɵtext(20, \"Marked On\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"th\");\n    i0.ɵɵtext(22, \"Marks\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"th\");\n    i0.ɵɵtext(24, \"Result\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"tbody\");\n    i0.ɵɵtemplate(26, TraineeCertificateTestPublishComponent_div_20_tr_26_Template, 17, 17, \"tr\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.selectAll)(\"ngModelOptions\", i0.ɵɵpureFunction0(3, _c0));\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.rows);\n  }\n}\n\nfunction TraineeCertificateTestPublishComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"br\");\n    i0.ɵɵelementStart(2, \"h4\", 35);\n    i0.ɵɵtext(3, \"No Item Found!!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TraineeCertificateTestPublishComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function TraineeCertificateTestPublishComponent_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return ctx_r18.onPublish();\n    });\n    i0.ɵɵelement(1, \"i\", 37);\n    i0.ɵɵtext(2, \" Publish \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c1 = function () {\n  return [\"/trainee-certificate-test-list\"];\n};\n\nexport class TraineeCertificateTestPublishComponent {\n  constructor(formBuilder, _service, toastr) {\n    this.formBuilder = formBuilder;\n    this._service = _service;\n    this.toastr = toastr;\n    this.modalConfig = {\n      class: \"gray modal-lg\",\n      backdrop: \"static\"\n    };\n    this.courseList = [];\n    this.rows = [];\n    this.selectAll = false;\n    this.loadingIndicator = false;\n    this.ColumnMode = ColumnMode;\n  }\n\n  ngOnInit() {\n    this.getCourseList();\n  }\n\n  getCourseList() {\n    this._service.get(\"course/dropdown-list\").subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, \"Warning!\", {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.courseList = res.Data;\n    }, () => {});\n  }\n\n  filterList() {\n    this.blockUI.start('Getting data. Please wait...');\n\n    this._service.get(\"exam/certificate-test/get-unpublished-trainee-exam-list/\" + this.selectedCourse.Id).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, \"Warning!\", {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, \"Error!\", {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.rows = res.Data;\n        setTimeout(() => {\n          this.loadingIndicator = false;\n        }, 1000);\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.error(err.message || err, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  onPublish() {\n    let examList = this.rows.filter(x => x.Selected).map(x => x.Id);\n\n    if (examList.length === 0) {\n      this.toastr.warning('No exam has been selected to publish.', 'Warning!');\n      return;\n    }\n\n    this.blockUI.start(\"Publishing result. Please wait...\");\n\n    this._service.post(\"exam/certificate-test/publish/\" + this.selectedCourse.Id, examList).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, \"Warning!\", {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, \"Error!\", {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.toastr.success(res.Message, \"Success!\", {\n          timeOut: 2000\n        });\n        this.filterList();\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.error(err.message || err, \"Error!\", {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n  onChangeSelectAll(event) {\n    this.rows.forEach(element => {\n      element.Selected = event.target.checked;\n    });\n  }\n\n}\n\nTraineeCertificateTestPublishComponent.ɵfac = function TraineeCertificateTestPublishComponent_Factory(t) {\n  return new (t || TraineeCertificateTestPublishComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.CommonService), i0.ɵɵdirectiveInject(i3.ToastrService));\n};\n\nTraineeCertificateTestPublishComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: TraineeCertificateTestPublishComponent,\n  selectors: [[\"app-trainee-certificate-test-publish\"]],\n  decls: 24,\n  vars: 6,\n  consts: [[1, \"col-12\"], [1, \"card\", \"card-border-primary\"], [1, \"card-header\"], [1, \"card-title\"], [1, \"btn\", \"btn-primary\", \"btn-mini\", \"float-end\", 3, \"routerLink\"], [1, \"feather\", \"icon-arrow-left\"], [1, \"card-block\"], [1, \"row\"], [1, \"col-lg-12\"], [\"autocomplete\", \"off\"], [1, \"mb-3\", \"row\"], [1, \"col-sm-1\", \"col-form-label\", \"col-form-label-sm\", \"text-right\", \"required\"], [1, \"col-sm-3\"], [\"class\", \"form-control form-control-sm\", \"bindLabel\", \"Title\", \"placeholder\", \"Select a course\", 3, \"ngModel\", \"ngModelOptions\", \"clearable\", \"clearOnBackspace\", \"items\", \"ngModelChange\", \"change\", 4, \"ngIf\"], [\"class\", \"card mb-0\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"card-footer\"], [\"class\", \"btn btn-theme\", 3, \"click\", 4, \"ngIf\"], [\"bindLabel\", \"Title\", \"placeholder\", \"Select a course\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelOptions\", \"clearable\", \"clearOnBackspace\", \"items\", \"ngModelChange\", \"change\"], [1, \"card\", \"mb-0\"], [1, \"card-header\", \"p-2\"], [1, \"custom-control\", \"custom-checkbox\", \"float-end\"], [\"name\", \"selectAll\", \"id\", \"selectAll\", \"type\", \"checkbox\", 1, \"custom-control-input\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\", \"change\"], [\"for\", \"selectAll\", 1, \"custom-control-label\"], [1, \"card-body\", \"p-2\"], [1, \"table-responsive\"], [1, \"table\", \"table-sm\", \"table-hover\", \"table-bordered\", \"table-striped\"], [4, \"ngFor\", \"ngForOf\"], [1, \"custom-control\", \"custom-checkbox\"], [\"type\", \"checkbox\", 1, \"custom-control-input\", 3, \"name\", \"id\", \"ngModel\", \"ngModelOptions\", \"value\", \"ngModelChange\"], [1, \"custom-control-label\", 3, \"for\"], [\"class\", \"badge bg-success\", 4, \"ngIf\"], [\"class\", \"badge bg-danger\", 4, \"ngIf\"], [1, \"badge\", \"bg-success\"], [1, \"badge\", \"bg-danger\"], [1, \"text-danger\"], [1, \"btn\", \"btn-theme\", 3, \"click\"], [1, \"feather\", \"icon-check-circle\"]],\n  template: function TraineeCertificateTestPublishComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"h5\", 3);\n      i0.ɵɵtext(5, \"Trainee Certificate Test Publish\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"button\", 4);\n      i0.ɵɵelement(7, \"i\", 5);\n      i0.ɵɵtext(8, \" Go Back \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(9, \"div\", 6);\n      i0.ɵɵelementStart(10, \"div\", 7);\n      i0.ɵɵelementStart(11, \"div\", 8);\n      i0.ɵɵelementStart(12, \"form\", 9);\n      i0.ɵɵelementStart(13, \"div\", 7);\n      i0.ɵɵelementStart(14, \"div\", 8);\n      i0.ɵɵelementStart(15, \"div\", 10);\n      i0.ɵɵelementStart(16, \"label\", 11);\n      i0.ɵɵtext(17, \" Course \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"div\", 12);\n      i0.ɵɵtemplate(19, TraineeCertificateTestPublishComponent_ng_select_19_Template, 1, 6, \"ng-select\", 13);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(20, TraineeCertificateTestPublishComponent_div_20_Template, 27, 4, \"div\", 14);\n      i0.ɵɵtemplate(21, TraineeCertificateTestPublishComponent_div_21_Template, 4, 0, \"div\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(22, \"div\", 16);\n      i0.ɵɵtemplate(23, TraineeCertificateTestPublishComponent_button_23_Template, 3, 0, \"button\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(5, _c1));\n      i0.ɵɵadvance(13);\n      i0.ɵɵproperty(\"ngIf\", ctx.courseList.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.rows.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedCourse && ctx.rows.length === 0);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.rows.length > 0);\n    }\n  },\n  directives: [i4.BlockUIComponent, i5.RouterLink, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.NgForm, i6.NgIf, i7.NgSelectComponent, i1.NgControlStatus, i1.NgModel, i1.CheckboxControlValueAccessor, i6.NgForOf],\n  pipes: [i8.DateFormatPipe],\n  encapsulation: 2\n});\n\n__decorate([BlockUI()], TraineeCertificateTestPublishComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}