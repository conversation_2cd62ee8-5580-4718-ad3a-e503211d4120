using System;
using System.Diagnostics;
using Brac.LMS.Common;

namespace Brac.LMS.App.Services
{
    /// <summary>
    /// Performance monitoring helper for tracking operation execution times
    /// WHY: Provides detailed performance metrics to identify bottlenecks
    /// BENEFIT: Real-time performance monitoring and optimization insights
    /// </summary>
    public class PerformanceMonitor : IDisposable
    {
        private readonly Stopwatch _stopwatch;
        private readonly string _operationName;
        private readonly string _context;

        public PerformanceMonitor(string operationName, string context = null)
        {
            _operationName = operationName;
            _context = context;
            _stopwatch = Stopwatch.StartNew();
        }

        public void LogCheckpoint(string checkpointName)
        {
            var elapsed = _stopwatch.ElapsedMilliseconds;
            LogControl.Write($"PERF | {_operationName} | {checkpointName}: {elapsed}ms" + 
                           (!string.IsNullOrEmpty(_context) ? $" | Context: {_context}" : ""));
        }

        public void Dispose()
        {
            _stopwatch.Stop();
            var totalTime = _stopwatch.ElapsedMilliseconds;
            
            // Log performance metrics
            LogControl.Write($"PERF | {_operationName} | TOTAL: {totalTime}ms" + 
                           (!string.IsNullOrEmpty(_context) ? $" | Context: {_context}" : ""));
            
            // Alert if operation takes too long
            if (totalTime > 5000) // 5 seconds
            {
                LogControl.Write($"PERF ALERT | {_operationName} took {totalTime}ms - Consider optimization");
            }
        }
    }

    /// <summary>
    /// Extension methods for easy performance monitoring
    /// </summary>
    public static class PerformanceExtensions
    {
        public static PerformanceMonitor StartPerformanceMonitoring(this object obj, string operationName, string context = null)
        {
            return new PerformanceMonitor(operationName, context);
        }
    }
}
