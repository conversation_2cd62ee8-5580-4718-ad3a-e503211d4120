﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Hosting;

namespace Brac.LMS.Common
{
    public class Generator
    {
        public static string UploadSampleGradingPolicy = HostingEnvironment.MapPath("~/Files/UploadSamples/grading_policy.xlsx");
        public static string EmailTemplatePasswordReset = HostingEnvironment.MapPath("~/Files/EmailTemplates/password-reset.html");
        public static string EmailTemplateNotification = HostingEnvironment.MapPath("~/Files/EmailTemplates/notification.html");
        public static string UploadSampleTrainers = HostingEnvironment.MapPath("~/Files/UploadSamples/trainer.xlsx");
        public static string UploadSampleTrainees = HostingEnvironment.MapPath("~/Files/UploadSamples/trainee.xlsx");
        public static string UploadSampleMCQ = HostingEnvironment.MapPath("~/Files/UploadSamples/course_exam_mcq_sample_file.xlsx");
        public static string UploadSampleTFQ = HostingEnvironment.MapPath("~/Files/UploadSamples/course_exam_tfq_sample_file.xlsx");
        public static string UploadSampleFIGQ = HostingEnvironment.MapPath("~/Files/UploadSamples/course_exam_figq_sample_file.xlsx");
        public static string UploadSampleLRMQ = HostingEnvironment.MapPath("~/Files/UploadSamples/course_exam_lrmq_sample_file.xlsx");
        public static string UploadSampleWQ = HostingEnvironment.MapPath("~/Files/UploadSamples/course_exam_wq_sample_file.xlsx");
        public static string UploadSampleCourseEnrolment = HostingEnvironment.MapPath("~/Files/UploadSamples/course_enrolment_sample_file.xlsx");
        public static string UploadSampleGuestTrainee = HostingEnvironment.MapPath("~/Files/UploadSamples/guest_trainee_sample_file.xlsx");
        public static string CertificateImage = HostingEnvironment.MapPath("~/App_Data/certificate_v2.0.jpg");
        public static string FirebaseAccountKey = HostingEnvironment.MapPath("~/App_Data/serviceAccountKey.json");
    }
}
