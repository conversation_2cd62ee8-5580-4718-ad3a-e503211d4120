﻿using Brac.LMS.Common.SMS;
using Brac.LMS.Common.SMS.Enums;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Common
{
    public class SMSSender
    {
        //private readonly BracSoapSMSClient _smsClient;

        //public SMSSender()
        //{
        //    _smsClient = new BracSoapSMSClient();
        //}

        //public async Task SendAsync(string to, string body, SMSEventType eventType)
        //{
        //    try
        //    {
        //        LogControl.Write($"Sending SMS - Event: {eventType} | To: {to}");
        //        await _smsClient.SendMessageAsync(to, body, ((int)eventType).ToString().PadLeft(2, '0'));
        //    }
        //    catch (Exception ex)
        //    {
        //        LogControl.Write($"SMS Sending error: To: {to} | body: {body} | Event: {eventType} | Exception: {JsonConvert.SerializeObject(ex)}");
        //        throw new Exception("SMS sending failed", ex);
        //    }
        //}

        private readonly string _table = "";

        public SMSSender()
        {
            if (System.Configuration.ConfigurationManager.AppSettings["SMSTable"] == null) throw new Exception("SMSTable not configured");
            _table = System.Configuration.ConfigurationManager.AppSettings["SMSTable"].ToString();
        }

        public async Task SendAsync(string to, string body, SMSEventType eventType)
        {
            SqlConnection con = null;
            try
            {
                con = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["BBLSMSConnection"].ConnectionString);
                await con.OpenAsync();

                string sql = $"INSERT INTO {_table}(MOB_NO, BODY, STATUS, GUI_USER_NAME) VALUES(@param1, @param2, @param3, @param4)";
                using (SqlCommand cmd = new SqlCommand(sql, con))
                {
                    cmd.Parameters.Add("@param1", SqlDbType.VarChar, 50).Value = to;
                    cmd.Parameters.Add("@param2", SqlDbType.VarChar, 250).Value = body;
                    cmd.Parameters.Add("@param3", SqlDbType.Int).Value = 315;
                    cmd.Parameters.Add("@param4", SqlDbType.VarChar, 20).Value = "eLearning";
                    cmd.CommandType = CommandType.Text;
                    int result = await cmd.ExecuteNonQueryAsync();

                    // Check Error
                    if (result < 0) throw new Exception("Error inserting data into Database!");
                }
            }
            catch (Exception ex)
            {
                throw (ex);
            }
            finally
            {
                con?.Close();
            }
        }
    }
}
