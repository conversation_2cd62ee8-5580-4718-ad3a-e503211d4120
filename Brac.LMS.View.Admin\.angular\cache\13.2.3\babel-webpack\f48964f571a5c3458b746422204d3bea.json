{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ColumnMode } from '@swimlane/ngx-datatable';\nimport { Validators } from '@angular/forms';\nimport { BlockUI } from 'ng-block-ui';\nimport * as moment from 'moment';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport { ResponseStatus } from '../_models/enum';\nimport { Editor } from 'ngx-editor';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../app.component\";\nimport * as i2 from \"ngx-bootstrap/modal\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../_services/common.service\";\nimport * as i5 from \"ngx-toastr\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i8 from \"ng-block-ui\";\nimport * as i9 from \"@ng-select/ng-select\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/flex-layout/extended\";\nimport * as i12 from \"../_helpers/numbers-only\";\nimport * as i13 from \"ngx-editor\";\nimport * as i14 from \"@swimlane/ngx-datatable\";\nimport * as i15 from \"ngx-ui-switch\";\n\nfunction CertificationTestEntryComponent_small_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.ExamExistsMsg, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_div_20_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Course is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_div_20_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f.courseId.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_div_21_div_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Marks is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_div_21_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_div_21_div_4_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r73 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r73.f.duration.errors.required);\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nfunction CertificationTestEntryComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵelementStart(1, \"label\", 10);\n    i0.ɵɵtext(2, \" Exam Duration (Min) \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 66);\n    i0.ɵɵtemplate(4, CertificationTestEntryComponent_div_21_div_4_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ctx_r3.submitted && ctx_r3.f.duration.errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.submitted && ctx_r3.f.duration.errors);\n  }\n}\n\nfunction CertificationTestEntryComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵelementStart(1, \"label\", 68);\n    i0.ɵɵtext(2, \"Start Date & Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 69);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵelementStart(1, \"label\", 68);\n    i0.ɵɵtext(2, \"End Date & Time \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 70);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_div_24_div_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" quota is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_div_24_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_div_24_div_4_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r75 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r75.f.quota.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelementStart(1, \"label\", 10);\n    i0.ɵɵtext(2, \" Quota \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 72);\n    i0.ɵɵtemplate(4, CertificationTestEntryComponent_div_24_div_4_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ctx_r6.submitted && ctx_r6.f.quota.errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.submitted && ctx_r6.f.quota.errors);\n  }\n}\n\nfunction CertificationTestEntryComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵelementStart(1, \"div\", 73);\n    i0.ɵɵelement(2, \"input\", 74);\n    i0.ɵɵelementStart(3, \"label\", 75);\n    i0.ɵɵtext(4, \" Question Randomization \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵelementStart(1, \"div\", 73);\n    i0.ɵɵelement(2, \"input\", 76);\n    i0.ɵɵelementStart(3, \"label\", 77);\n    i0.ɵɵtext(4, \" Publish Immediate Result \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r78 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelementStart(1, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_div_27_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r78);\n      const ctx_r77 = i0.ɵɵnextContext();\n\n      const _r70 = i0.ɵɵreference(183);\n\n      return ctx_r77.openQuestionSetupModal(_r70);\n    });\n    i0.ɵɵtext(2, \"Qs. Setup\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_tr_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r79 = ctx.$implicit;\n    const i_r80 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r80 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r79.SegmentName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r79.NoOfMCQ);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r79.NoOfTrueFalse);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r79.NoOfFIG);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r79.NoOfMatching);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r79.NoOfWriting);\n  }\n}\n\nfunction CertificationTestEntryComponent_tfoot_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tfoot\");\n    i0.ɵɵelementStart(1, \"tr\");\n    i0.ɵɵelementStart(2, \"th\", 79);\n    i0.ɵɵtext(3, \" Total \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"th\", 80);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 80);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 80);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 80);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 80);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r11.totalQuestions.mcq);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r11.totalQuestions.tfq);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r11.totalQuestions.figq);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r11.totalQuestions.matchingq);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r11.totalQuestions.writtenq);\n  }\n}\n\nfunction CertificationTestEntryComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵelementStart(1, \"label\", 68);\n    i0.ɵɵtext(2, \"Instructions \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 82);\n    i0.ɵɵelement(4, \"ngx-editor-menu\", 83);\n    i0.ɵɵelement(5, \"ngx-editor\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"editor\", ctx_r12.editor)(\"toolbar\", ctx_r12.toolbar);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"editor\", ctx_r12.editor);\n  }\n}\n\nfunction CertificationTestEntryComponent_div_51_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r83 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_div_51_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r83);\n      const ctx_r82 = i0.ɵɵnextContext(2);\n      return ctx_r82.onFormSubmit();\n    });\n    i0.ɵɵelement(1, \"i\", 90);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r81 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r81.btnSaveText, \" \");\n  }\n}\n\nconst _c1 = function () {\n  return [\"/certification-test-list\"];\n};\n\nfunction CertificationTestEntryComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_div_51_button_1_Template, 3, 1, \"button\", 86);\n    i0.ɵɵelementStart(2, \"button\", 87);\n    i0.ɵɵelement(3, \"i\", 88);\n    i0.ɵɵtext(4, \" Go Back \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(2, _c1));\n  }\n}\n\nfunction CertificationTestEntryComponent_button_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r85 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_button_60_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r85);\n      const ctx_r84 = i0.ɵɵnextContext();\n      return ctx_r84.downloadQuestionList(\"MCQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 92);\n    i0.ɵɵtext(2, \" Download Excel \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r87 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelementStart(1, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_div_61_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r86 = i0.ɵɵnextContext();\n\n      const _r52 = i0.ɵɵreference(165);\n\n      return ctx_r86.openMCQModal(_r52);\n    });\n    i0.ɵɵelement(2, \"i\", 94);\n    i0.ɵɵtext(3, \" Add MCQ Questions \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r88 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r88);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r88, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r89 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r89);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r89, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r90 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r90);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r90, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r91 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r91);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r91, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r92 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r92);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r92, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r93 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r93);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r93, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r94 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r94);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", value_r94, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r95 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r95);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", value_r95, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_81_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r98 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_81_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r98);\n      const row_r96 = restoredCtx.row;\n      const ctx_r97 = i0.ɵɵnextContext();\n      return ctx_r97.deleteQuestion(row_r96.Id, \"MCQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 97);\n    i0.ɵɵtext(2, \" Delete \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_81_Template_button_click_3_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r98);\n      const row_r96 = restoredCtx.row;\n      const ctx_r99 = i0.ɵɵnextContext();\n\n      const _r54 = i0.ɵɵreference(167);\n\n      return ctx_r99.editMCQ(row_r96, _r54);\n    });\n    i0.ɵɵelement(4, \"i\", 99);\n    i0.ɵɵtext(5, \" Edit \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_button_89_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r101 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_button_89_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r101);\n      const ctx_r100 = i0.ɵɵnextContext();\n      return ctx_r100.downloadQuestionList(\"TFQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 100);\n    i0.ɵɵtext(2, \" Download Excel \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_div_90_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r103 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelementStart(1, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_div_90_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r103);\n      const ctx_r102 = i0.ɵɵnextContext();\n\n      const _r56 = i0.ɵɵreference(169);\n\n      return ctx_r102.openTFQModal(_r56);\n    });\n    i0.ɵɵelement(2, \"i\", 94);\n    i0.ɵɵtext(3, \" Add True/False Questions \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r104 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r104);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r104, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r105 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r105);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r105, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r106 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r106 ? \"TRUE\" : \"FALSE\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r106 ? \"TRUE\" : \"FALSE\", \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r107 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r107);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", value_r107, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_102_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r110 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_102_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r110);\n      const row_r108 = restoredCtx.row;\n      const ctx_r109 = i0.ɵɵnextContext();\n      return ctx_r109.deleteQuestion(row_r108.Id, \"TFQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 97);\n    i0.ɵɵtext(2, \" Delete \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_102_Template_button_click_3_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r110);\n      const row_r108 = restoredCtx.row;\n      const ctx_r111 = i0.ɵɵnextContext();\n\n      const _r58 = i0.ɵɵreference(171);\n\n      return ctx_r111.editTrueFalse(row_r108, _r58);\n    });\n    i0.ɵɵelement(4, \"i\", 99);\n    i0.ɵɵtext(5, \" Edit \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_button_110_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r113 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_button_110_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r113);\n      const ctx_r112 = i0.ɵɵnextContext();\n      return ctx_r112.downloadQuestionList(\"FIGQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 92);\n    i0.ɵɵtext(2, \" Download Excel \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_div_111_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r115 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelementStart(1, \"button\", 102);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_div_111_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r115);\n      const ctx_r114 = i0.ɵɵnextContext();\n\n      const _r60 = i0.ɵɵreference(173);\n\n      return ctx_r114.openFIGModal(_r60);\n    });\n    i0.ɵɵelement(2, \"i\", 94);\n    i0.ɵɵtext(3, \" Add Fill in the gaps Questions \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_115_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r116 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r116);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r116, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r117 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r117);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r117, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r118 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r118);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r118, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r119 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r119);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", value_r119, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_123_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r122 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_123_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r122);\n      const row_r120 = restoredCtx.row;\n      const ctx_r121 = i0.ɵɵnextContext();\n      return ctx_r121.deleteQuestion(row_r120.Id, \"FIGQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 97);\n    i0.ɵɵtext(2, \" Delete \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_123_Template_button_click_3_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r122);\n      const row_r120 = restoredCtx.row;\n      const ctx_r123 = i0.ɵɵnextContext();\n\n      const _r62 = i0.ɵɵreference(175);\n\n      return ctx_r123.editFIG(row_r120, _r62);\n    });\n    i0.ɵɵelement(4, \"i\", 99);\n    i0.ɵɵtext(5, \" Edit \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_button_131_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r125 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_button_131_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r125);\n      const ctx_r124 = i0.ɵɵnextContext();\n      return ctx_r124.downloadQuestionList(\"MatchingQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 92);\n    i0.ɵɵtext(2, \" Download Excel \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_div_132_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r127 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelementStart(1, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_div_132_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r127);\n      const ctx_r126 = i0.ɵɵnextContext();\n\n      const _r64 = i0.ɵɵreference(177);\n\n      return ctx_r126.openMatchingModal(_r64);\n    });\n    i0.ɵɵelement(2, \"i\", 94);\n    i0.ɵɵtext(3, \" Add Matching Questions \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_136_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r128 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r128);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r128, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_138_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r129 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r129);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r129, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_140_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r130 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r130);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r130, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_142_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r131 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r131);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", value_r131, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_144_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r134 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_144_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r134);\n      const row_r132 = restoredCtx.row;\n      const ctx_r133 = i0.ɵɵnextContext();\n      return ctx_r133.deleteQuestion(row_r132.Id, \"MatchingQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 97);\n    i0.ɵɵtext(2, \" Delete \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_144_Template_button_click_3_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r134);\n      const row_r132 = restoredCtx.row;\n      const ctx_r135 = i0.ɵɵnextContext();\n\n      const _r66 = i0.ɵɵreference(179);\n\n      return ctx_r135.editMatching(row_r132, _r66);\n    });\n    i0.ɵɵelement(4, \"i\", 99);\n    i0.ɵɵtext(5, \" Edit \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_button_152_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r137 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_button_152_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r137);\n      const ctx_r136 = i0.ɵɵnextContext();\n      return ctx_r136.downloadQuestionList(\"WQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 92);\n    i0.ɵɵtext(2, \" Download Excel \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_div_153_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r139 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelementStart(1, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_div_153_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r139);\n      const ctx_r138 = i0.ɵɵnextContext();\n\n      const _r68 = i0.ɵɵreference(181);\n\n      return ctx_r138.openWrittenModal(_r68);\n    });\n    i0.ɵɵelement(2, \"i\", 94);\n    i0.ɵɵtext(3, \" Add Written Questions \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_157_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r140 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r140);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r140, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_159_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r141 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r141);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r141, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_161_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r142 = ctx.value;\n    i0.ɵɵpropertyInterpolate(\"title\", value_r142);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", value_r142, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_163_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r145 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_163_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r145);\n      const row_r143 = restoredCtx.row;\n      const ctx_r144 = i0.ɵɵnextContext();\n      return ctx_r144.deleteQuestion(row_r143.Id, \"WQ\");\n    });\n    i0.ɵɵelement(1, \"i\", 106);\n    i0.ɵɵtext(2, \"Delete \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_164_div_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Segment is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_164_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_ng_template_164_div_13_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r146 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r146.mcqForm.controls.segmentId.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_164_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r152 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_164_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r152);\n      i0.ɵɵnextContext();\n\n      const _r147 = i0.ɵɵreference(23);\n\n      const ctx_r151 = i0.ɵɵnextContext();\n      return ctx_r151.resetMCQFile(_r147);\n    });\n    i0.ɵɵtext(1, \" remove \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_164_div_35_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelementStart(1, \"span\", 64);\n    i0.ɵɵtext(2, \" Question is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c2 = function () {\n  return {\n    standalone: true\n  };\n};\n\nfunction CertificationTestEntryComponent_ng_template_164_div_35_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r161 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 114);\n    i0.ɵɵelementStart(1, \"div\", 134);\n    i0.ɵɵelementStart(2, \"span\", 142);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 144);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_164_div_35_div_14_Template_input_ngModelChange_4_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r161);\n      const option_r158 = restoredCtx.$implicit;\n      return option_r158.Text = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 142);\n    i0.ɵɵelementStart(6, \"input\", 145);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_164_div_35_div_14_Template_input_ngModelChange_6_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r161);\n      const option_r158 = restoredCtx.$implicit;\n      return option_r158.Selected = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const option_r158 = ctx.$implicit;\n    const oi_r159 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(oi_r159 + 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"placeholder\", \"Option \", oi_r159 + 1, \"\");\n    i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(7, _c2))(\"ngModel\", option_r158.Text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", option_r158.Selected)(\"disabled\", !option_r158.Text)(\"ngModel\", option_r158.Selected);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_164_div_35_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelementStart(1, \"span\", 64);\n    i0.ɵɵtext(2, \" Mark is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_164_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r164 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵelementStart(1, \"div\", 133);\n    i0.ɵɵelementStart(2, \"div\", 0);\n    i0.ɵɵelementStart(3, \"div\", 133);\n    i0.ɵɵelementStart(4, \"div\", 134);\n    i0.ɵɵelementStart(5, \"span\", 135);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"textarea\", 136);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_164_div_35_Template_textarea_ngModelChange_7_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r164);\n      const item_r153 = restoredCtx.$implicit;\n      return item_r153.Question = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 137);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_164_div_35_Template_div_click_8_listener() {\n      i0.ɵɵrestoreView(_r164);\n      const ctx_r165 = i0.ɵɵnextContext(2);\n      return ctx_r165.addNewMCQ();\n    });\n    i0.ɵɵelement(9, \"i\", 138);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 137);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_164_div_35_Template_div_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r164);\n      const i_r154 = restoredCtx.index;\n      const ctx_r166 = i0.ɵɵnextContext(2);\n      return ctx_r166.deleteMCQ(i_r154);\n    });\n    i0.ɵɵelement(11, \"i\", 139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, CertificationTestEntryComponent_ng_template_164_div_35_div_12_Template, 3, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 118);\n    i0.ɵɵtemplate(14, CertificationTestEntryComponent_ng_template_164_div_35_div_14_Template, 7, 8, \"div\", 140);\n    i0.ɵɵelementStart(15, \"div\", 141);\n    i0.ɵɵelementStart(16, \"div\", 134);\n    i0.ɵɵelementStart(17, \"span\", 142);\n    i0.ɵɵtext(18, \"Mark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"input\", 143);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_164_div_35_Template_input_ngModelChange_19_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r164);\n      const item_r153 = restoredCtx.$implicit;\n      return item_r153.Mark = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, CertificationTestEntryComponent_ng_template_164_div_35_div_20_Template, 3, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"hr\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r153 = ctx.$implicit;\n    const i_r154 = ctx.index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"@inOutAnimation\", undefined);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Q\", i_r154 + 1, \":\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", item_r153.Question)(\"ngModelOptions\", i0.ɵɵpureFunction0(8, _c2));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !item_r153.Question);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r153.Options);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", item_r153.Mark);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r153.Mark);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_164_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r169 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵelement(1, \"h4\", 108);\n    i0.ɵɵelementStart(2, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_164_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r169);\n      const ctx_r168 = i0.ɵɵnextContext();\n      return ctx_r168.modalMCQHide();\n    });\n    i0.ɵɵelement(3, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 111);\n    i0.ɵɵelementStart(5, \"div\", 3);\n    i0.ɵɵelementStart(6, \"div\", 4);\n    i0.ɵɵelementStart(7, \"form\", 112);\n    i0.ɵɵelementStart(8, \"div\", 0);\n    i0.ɵɵelementStart(9, \"label\", 113);\n    i0.ɵɵtext(10, \"Segment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 114);\n    i0.ɵɵelement(12, \"ng-select\", 115);\n    i0.ɵɵtemplate(13, CertificationTestEntryComponent_ng_template_164_div_13_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 116);\n    i0.ɵɵelementStart(15, \"div\", 117);\n    i0.ɵɵelementStart(16, \"div\", 4);\n    i0.ɵɵelementStart(17, \"h5\", 5);\n    i0.ɵɵtext(18, \"Bulk Upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 6);\n    i0.ɵɵelementStart(20, \"div\", 118);\n    i0.ɵɵelementStart(21, \"div\", 119);\n    i0.ɵɵelementStart(22, \"input\", 120, 121);\n    i0.ɵɵlistener(\"change\", function CertificationTestEntryComponent_ng_template_164_Template_input_change_22_listener() {\n      i0.ɵɵrestoreView(_r169);\n\n      const _r147 = i0.ɵɵreference(23);\n\n      const ctx_r170 = i0.ɵɵnextContext();\n      return ctx_r170.loadMCQFile(_r147.files);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 122);\n    i0.ɵɵtemplate(25, CertificationTestEntryComponent_ng_template_164_button_25_Template, 2, 0, \"button\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 124);\n    i0.ɵɵelementStart(27, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_164_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r169);\n      const ctx_r171 = i0.ɵɵnextContext();\n      return ctx_r171.downloadSampleMCQ();\n    });\n    i0.ɵɵelement(28, \"i\", 126);\n    i0.ɵɵtext(29, \" Download Sample File \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 127);\n    i0.ɵɵelementStart(31, \"div\", 4);\n    i0.ɵɵelementStart(32, \"h5\", 5);\n    i0.ɵɵtext(33, \" Manual Entry \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 6);\n    i0.ɵɵtemplate(35, CertificationTestEntryComponent_ng_template_164_div_35_Template, 22, 9, \"div\", 128);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 129);\n    i0.ɵɵelementStart(37, \"div\", 130);\n    i0.ɵɵelementStart(38, \"button\", 131);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_164_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r169);\n      const ctx_r172 = i0.ɵɵnextContext();\n      return ctx_r172.modalMCQHide();\n    });\n    i0.ɵɵelement(39, \"i\", 110);\n    i0.ɵɵtext(40, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_164_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r169);\n      const ctx_r173 = i0.ɵɵnextContext();\n      return ctx_r173.onMCQFormSubmit();\n    });\n    i0.ɵɵelement(42, \"i\", 90);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHtml\", ctx_r53.modalMCQTitle, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r53.mcqForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r53.segmentList)(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx_r53.submitted && ctx_r53.mcqForm.controls.segmentId.errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r53.submitted && ctx_r53.mcqForm.controls.segmentId.errors);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r53.mcqFile);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r53.mcqQuestionList);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r53.btnMCQSaveText, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_166_div_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Question is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_166_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_ng_template_166_div_13_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r174 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r174.mcqEditForm.controls.question.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_166_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r182 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 114);\n    i0.ɵɵelementStart(1, \"div\", 134);\n    i0.ɵɵelementStart(2, \"span\", 142);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 144);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_166_div_15_Template_input_ngModelChange_4_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r182);\n      const option_r179 = restoredCtx.$implicit;\n      return option_r179.Text = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 142);\n    i0.ɵɵelementStart(6, \"input\", 151);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_166_div_15_Template_input_ngModelChange_6_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r182);\n      const option_r179 = restoredCtx.$implicit;\n      return option_r179.Selected = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const option_r179 = ctx.$implicit;\n    const oi_r180 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(oi_r180 + 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"placeholder\", \"Option \", oi_r180 + 1, \"\");\n    i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(8, _c2))(\"ngModel\", option_r179.Text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", option_r179.Selected)(\"disabled\", !option_r179.Text)(\"ngModel\", option_r179.Selected)(\"ngModelOptions\", i0.ɵɵpureFunction0(9, _c2));\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_166_div_21_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Mark is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_166_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_ng_template_166_div_21_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r176 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r176.mcqEditForm.controls.mark.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_166_div_27_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Segment is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_166_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_ng_template_166_div_27_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r177 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r177.mcqEditForm.controls.segmentId.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_166_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r187 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵelementStart(1, \"h4\", 146);\n    i0.ɵɵtext(2, \"Update MCQ Question\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_166_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r187);\n      const ctx_r186 = i0.ɵɵnextContext();\n      return ctx_r186.modalEditMCQHide();\n    });\n    i0.ɵɵelement(4, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 111);\n    i0.ɵɵelementStart(6, \"form\", 112);\n    i0.ɵɵelementStart(7, \"div\", 0);\n    i0.ɵɵelementStart(8, \"div\", 133);\n    i0.ɵɵelementStart(9, \"div\", 134);\n    i0.ɵɵelementStart(10, \"span\", 135);\n    i0.ɵɵtext(11, \"Q:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"textarea\", 147);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, CertificationTestEntryComponent_ng_template_166_div_13_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 118);\n    i0.ɵɵtemplate(15, CertificationTestEntryComponent_ng_template_166_div_15_Template, 7, 10, \"div\", 140);\n    i0.ɵɵelementStart(16, \"div\", 148);\n    i0.ɵɵelementStart(17, \"div\", 134);\n    i0.ɵɵelementStart(18, \"span\", 142);\n    i0.ɵɵtext(19, \"Mark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"input\", 149);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, CertificationTestEntryComponent_ng_template_166_div_21_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 148);\n    i0.ɵɵelementStart(23, \"div\", 134);\n    i0.ɵɵelementStart(24, \"label\", 150);\n    i0.ɵɵtext(25, \"Segment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"ng-select\", 115);\n    i0.ɵɵtemplate(27, CertificationTestEntryComponent_ng_template_166_div_27_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 129);\n    i0.ɵɵelementStart(29, \"div\", 130);\n    i0.ɵɵelementStart(30, \"button\", 131);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_166_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r187);\n      const ctx_r188 = i0.ɵɵnextContext();\n      return ctx_r188.modalEditMCQHide();\n    });\n    i0.ɵɵelement(31, \"i\", 110);\n    i0.ɵɵtext(32, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_166_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r187);\n      const ctx_r189 = i0.ɵɵnextContext();\n      return ctx_r189.onMCQUpdate();\n    });\n    i0.ɵɵelement(34, \"i\", 90);\n    i0.ɵɵtext(35, \" Update \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r55 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r55.mcqEditForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r55.editsubmitted && ctx_r55.mcqEditForm.controls.question.errors);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r55.mcqEditForm.controls.options.value);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r55.editsubmitted && ctx_r55.mcqEditForm.controls.mark.errors);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r55.segmentList)(\"ngClass\", i0.ɵɵpureFunction1(9, _c0, ctx_r55.editsubmitted && ctx_r55.mcqForm.controls.segmentId.errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r55.editsubmitted && ctx_r55.mcqEditForm.controls.segmentId.errors);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_168_div_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Segment is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_168_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_ng_template_168_div_13_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r190 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r190.tfqForm.controls.segmentId.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_168_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r196 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_168_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r196);\n      i0.ɵɵnextContext();\n\n      const _r191 = i0.ɵɵreference(23);\n\n      const ctx_r195 = i0.ɵɵnextContext();\n      return ctx_r195.resetTFQFile(_r191);\n    });\n    i0.ɵɵtext(1, \" remove \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_168_div_35_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelementStart(1, \"span\", 64);\n    i0.ɵɵtext(2, \" Question is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_168_div_35_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelementStart(1, \"span\", 64);\n    i0.ɵɵtext(2, \" Mark is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_168_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r202 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵelementStart(1, \"div\", 133);\n    i0.ɵɵelementStart(2, \"div\", 156);\n    i0.ɵɵelementStart(3, \"div\", 133);\n    i0.ɵɵelementStart(4, \"div\", 134);\n    i0.ɵɵelementStart(5, \"span\", 135);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"textarea\", 136);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_168_div_35_Template_textarea_ngModelChange_7_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r202);\n      const item_r197 = restoredCtx.$implicit;\n      return item_r197.Question = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 137);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_168_div_35_Template_div_click_8_listener() {\n      i0.ɵɵrestoreView(_r202);\n      const ctx_r203 = i0.ɵɵnextContext(2);\n      return ctx_r203.addNewTFQ();\n    });\n    i0.ɵɵelement(9, \"i\", 138);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 137);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_168_div_35_Template_div_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r202);\n      const i_r198 = restoredCtx.index;\n      const ctx_r204 = i0.ɵɵnextContext(2);\n      return ctx_r204.deleteTFQ(i_r198);\n    });\n    i0.ɵɵelement(11, \"i\", 139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, CertificationTestEntryComponent_ng_template_168_div_35_div_12_Template, 3, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 156);\n    i0.ɵɵelementStart(14, \"div\", 27);\n    i0.ɵɵelementStart(15, \"ui-switch\", 157);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_168_div_35_Template_ui_switch_ngModelChange_15_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r202);\n      const item_r197 = restoredCtx.$implicit;\n      return item_r197.Answer = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 27);\n    i0.ɵɵelementStart(17, \"div\", 134);\n    i0.ɵɵelementStart(18, \"span\", 142);\n    i0.ɵɵtext(19, \"Mark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"input\", 143);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_168_div_35_Template_input_ngModelChange_20_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r202);\n      const item_r197 = restoredCtx.$implicit;\n      return item_r197.Mark = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, CertificationTestEntryComponent_ng_template_168_div_35_div_21_Template, 3, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"hr\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r197 = ctx.$implicit;\n    const i_r198 = ctx.index;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Q\", i_r198 + 1, \":\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", item_r197.Question)(\"ngModelOptions\", i0.ɵɵpureFunction0(7, _c2));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !item_r197.Question);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", item_r197.Answer);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", item_r197.Mark);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r197.Mark);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_168_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r208 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵelement(1, \"h4\", 108);\n    i0.ɵɵelementStart(2, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_168_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r208);\n      const ctx_r207 = i0.ɵɵnextContext();\n      return ctx_r207.modalTFQHide();\n    });\n    i0.ɵɵelement(3, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 152);\n    i0.ɵɵelementStart(5, \"div\", 3);\n    i0.ɵɵelementStart(6, \"div\", 4);\n    i0.ɵɵelementStart(7, \"form\", 112);\n    i0.ɵɵelementStart(8, \"div\", 0);\n    i0.ɵɵelementStart(9, \"label\", 113);\n    i0.ɵɵtext(10, \"Segment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 114);\n    i0.ɵɵelement(12, \"ng-select\", 115);\n    i0.ɵɵtemplate(13, CertificationTestEntryComponent_ng_template_168_div_13_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 116);\n    i0.ɵɵelementStart(15, \"div\", 117);\n    i0.ɵɵelementStart(16, \"div\", 4);\n    i0.ɵɵelementStart(17, \"h5\", 5);\n    i0.ɵɵtext(18, \"Bulk Upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 6);\n    i0.ɵɵelementStart(20, \"div\", 118);\n    i0.ɵɵelementStart(21, \"div\", 119);\n    i0.ɵɵelementStart(22, \"input\", 120, 153);\n    i0.ɵɵlistener(\"change\", function CertificationTestEntryComponent_ng_template_168_Template_input_change_22_listener() {\n      i0.ɵɵrestoreView(_r208);\n\n      const _r191 = i0.ɵɵreference(23);\n\n      const ctx_r209 = i0.ɵɵnextContext();\n      return ctx_r209.loadTFQFile(_r191.files);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 122);\n    i0.ɵɵtemplate(25, CertificationTestEntryComponent_ng_template_168_button_25_Template, 2, 0, \"button\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 124);\n    i0.ɵɵelementStart(27, \"button\", 154);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_168_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r208);\n      const ctx_r210 = i0.ɵɵnextContext();\n      return ctx_r210.downloadSampleTFQ();\n    });\n    i0.ɵɵelement(28, \"i\", 126);\n    i0.ɵɵtext(29, \" Download Sample File \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 127);\n    i0.ɵɵelementStart(31, \"div\", 4);\n    i0.ɵɵelementStart(32, \"h5\", 5);\n    i0.ɵɵtext(33, \" Manual Entry \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 6);\n    i0.ɵɵtemplate(35, CertificationTestEntryComponent_ng_template_168_div_35_Template, 23, 8, \"div\", 128);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 129);\n    i0.ɵɵelementStart(37, \"div\", 130);\n    i0.ɵɵelementStart(38, \"button\", 131);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_168_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r208);\n      const ctx_r211 = i0.ɵɵnextContext();\n      return ctx_r211.modalTFQHide();\n    });\n    i0.ɵɵelement(39, \"i\", 110);\n    i0.ɵɵtext(40, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"button\", 155);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_168_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r208);\n      const ctx_r212 = i0.ɵɵnextContext();\n      return ctx_r212.onTFQFormSubmit();\n    });\n    i0.ɵɵelement(42, \"i\", 90);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r57 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHtml\", ctx_r57.modalTFTitle, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r57.tfqForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r57.segmentList)(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx_r57.submitted && ctx_r57.tfqForm.controls.segmentId.errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r57.submitted && ctx_r57.tfqForm.controls.segmentId.errors);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r57.tfqFile);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r57.tfQuestionList);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r57.btnTFQSaveText, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_170_div_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Question is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_170_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_ng_template_170_div_13_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r213 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r213.tfqEditForm.controls.question.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_170_div_22_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Mark is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_170_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_ng_template_170_div_22_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r214 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r214.tfqEditForm.controls.mark.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_170_div_28_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Segment is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_170_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_ng_template_170_div_28_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r215 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r215.tfqEditForm.controls.segmentId.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_170_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r220 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵelementStart(1, \"h4\", 146);\n    i0.ɵɵtext(2, \"Update True/False Question\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_170_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r220);\n      const ctx_r219 = i0.ɵɵnextContext();\n      return ctx_r219.modalEditTrueFalseHide();\n    });\n    i0.ɵɵelement(4, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 111);\n    i0.ɵɵelementStart(6, \"form\", 112);\n    i0.ɵɵelementStart(7, \"div\", 0);\n    i0.ɵɵelementStart(8, \"div\", 133);\n    i0.ɵɵelementStart(9, \"div\", 134);\n    i0.ɵɵelementStart(10, \"span\", 135);\n    i0.ɵɵtext(11, \"Q:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"textarea\", 147);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, CertificationTestEntryComponent_ng_template_170_div_13_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 118);\n    i0.ɵɵelementStart(15, \"div\", 158);\n    i0.ɵɵelement(16, \"ui-switch\", 159);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 160);\n    i0.ɵɵelementStart(18, \"div\", 134);\n    i0.ɵɵelementStart(19, \"span\", 142);\n    i0.ɵɵtext(20, \"Mark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"input\", 149);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, CertificationTestEntryComponent_ng_template_170_div_22_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 148);\n    i0.ɵɵelementStart(24, \"div\", 134);\n    i0.ɵɵelementStart(25, \"label\", 150);\n    i0.ɵɵtext(26, \"Segment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"ng-select\", 115);\n    i0.ɵɵtemplate(28, CertificationTestEntryComponent_ng_template_170_div_28_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 129);\n    i0.ɵɵelementStart(30, \"div\", 130);\n    i0.ɵɵelementStart(31, \"button\", 131);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_170_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r220);\n      const ctx_r221 = i0.ɵɵnextContext();\n      return ctx_r221.modalEditTrueFalseHide();\n    });\n    i0.ɵɵelement(32, \"i\", 110);\n    i0.ɵɵtext(33, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_170_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r220);\n      const ctx_r222 = i0.ɵɵnextContext();\n      return ctx_r222.onTrueFalseUpdate();\n    });\n    i0.ɵɵelement(35, \"i\", 90);\n    i0.ɵɵtext(36, \" Update \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r59 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r59.tfqEditForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r59.editsubmitted && ctx_r59.tfqEditForm.controls.question.errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r59.editsubmitted && ctx_r59.tfqEditForm.controls.mark.errors);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r59.segmentList)(\"ngClass\", i0.ɵɵpureFunction1(8, _c0, ctx_r59.editsubmitted && ctx_r59.mcqForm.controls.segmentId.errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r59.editsubmitted && ctx_r59.tfqEditForm.controls.segmentId.errors);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_172_div_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Segment is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_172_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_ng_template_172_div_13_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r223 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r223.figqForm.controls.segmentId.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_172_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r229 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_172_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r229);\n      i0.ɵɵnextContext();\n\n      const _r224 = i0.ɵɵreference(23);\n\n      const ctx_r228 = i0.ɵɵnextContext();\n      return ctx_r228.resetFIGQFile(_r224);\n    });\n    i0.ɵɵtext(1, \" remove \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_172_div_35_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelementStart(1, \"span\", 64);\n    i0.ɵɵtext(2, \" Question is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_172_div_35_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelementStart(1, \"span\", 64);\n    i0.ɵɵtext(2, \" Answer is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_172_div_35_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelementStart(1, \"span\", 64);\n    i0.ɵɵtext(2, \" Mark is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_172_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r236 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵelementStart(1, \"div\", 133);\n    i0.ɵɵelementStart(2, \"div\", 156);\n    i0.ɵɵelementStart(3, \"div\", 133);\n    i0.ɵɵelementStart(4, \"div\", 134);\n    i0.ɵɵelementStart(5, \"span\", 135);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"textarea\", 136);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_172_div_35_Template_textarea_ngModelChange_7_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r236);\n      const item_r230 = restoredCtx.$implicit;\n      return item_r230.Question = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 137);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_172_div_35_Template_div_click_8_listener() {\n      i0.ɵɵrestoreView(_r236);\n      const ctx_r237 = i0.ɵɵnextContext(2);\n      return ctx_r237.addNewFIG();\n    });\n    i0.ɵɵelement(9, \"i\", 138);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 137);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_172_div_35_Template_div_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r236);\n      const i_r231 = restoredCtx.index;\n      const ctx_r238 = i0.ɵɵnextContext(2);\n      return ctx_r238.deleteFIG(i_r231);\n    });\n    i0.ɵɵelement(11, \"i\", 139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, CertificationTestEntryComponent_ng_template_172_div_35_div_12_Template, 3, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 156);\n    i0.ɵɵelementStart(14, \"div\", 114);\n    i0.ɵɵelementStart(15, \"div\", 134);\n    i0.ɵɵelementStart(16, \"span\", 142);\n    i0.ɵɵtext(17, \"Answer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 163);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_172_div_35_Template_input_ngModelChange_18_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r236);\n      const item_r230 = restoredCtx.$implicit;\n      return item_r230.Answer = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, CertificationTestEntryComponent_ng_template_172_div_35_div_19_Template, 3, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 27);\n    i0.ɵɵelementStart(21, \"div\", 134);\n    i0.ɵɵelementStart(22, \"span\", 142);\n    i0.ɵɵtext(23, \"Mark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"input\", 143);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_172_div_35_Template_input_ngModelChange_24_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r236);\n      const item_r230 = restoredCtx.$implicit;\n      return item_r230.Mark = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, CertificationTestEntryComponent_ng_template_172_div_35_div_25_Template, 3, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"hr\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r230 = ctx.$implicit;\n    const i_r231 = ctx.index;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Q\", i_r231 + 1, \":\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", item_r230.Question)(\"ngModelOptions\", i0.ɵɵpureFunction0(8, _c2));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !item_r230.Question);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", item_r230.Answer);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r230.Answer);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", item_r230.Mark);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r230.Mark);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_172_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r242 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵelement(1, \"h4\", 161);\n    i0.ɵɵelementStart(2, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_172_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r242);\n      const ctx_r241 = i0.ɵɵnextContext();\n      return ctx_r241.modalFIGHide();\n    });\n    i0.ɵɵelement(3, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 152);\n    i0.ɵɵelementStart(5, \"div\", 3);\n    i0.ɵɵelementStart(6, \"div\", 4);\n    i0.ɵɵelementStart(7, \"form\", 112);\n    i0.ɵɵelementStart(8, \"div\", 0);\n    i0.ɵɵelementStart(9, \"label\", 113);\n    i0.ɵɵtext(10, \"Segment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 114);\n    i0.ɵɵelement(12, \"ng-select\", 115);\n    i0.ɵɵtemplate(13, CertificationTestEntryComponent_ng_template_172_div_13_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 116);\n    i0.ɵɵelementStart(15, \"div\", 117);\n    i0.ɵɵelementStart(16, \"div\", 4);\n    i0.ɵɵelementStart(17, \"h5\", 5);\n    i0.ɵɵtext(18, \"Bulk Upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 6);\n    i0.ɵɵelementStart(20, \"div\", 118);\n    i0.ɵɵelementStart(21, \"div\", 119);\n    i0.ɵɵelementStart(22, \"input\", 120, 162);\n    i0.ɵɵlistener(\"change\", function CertificationTestEntryComponent_ng_template_172_Template_input_change_22_listener() {\n      i0.ɵɵrestoreView(_r242);\n\n      const _r224 = i0.ɵɵreference(23);\n\n      const ctx_r243 = i0.ɵɵnextContext();\n      return ctx_r243.loadFIGQFile(_r224.files);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 122);\n    i0.ɵɵtemplate(25, CertificationTestEntryComponent_ng_template_172_button_25_Template, 2, 0, \"button\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 124);\n    i0.ɵɵelementStart(27, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_172_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r242);\n      const ctx_r244 = i0.ɵɵnextContext();\n      return ctx_r244.downloadSampleFIGQ();\n    });\n    i0.ɵɵelement(28, \"i\", 126);\n    i0.ɵɵtext(29, \" Download Sample File \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 127);\n    i0.ɵɵelementStart(31, \"div\", 4);\n    i0.ɵɵelementStart(32, \"h5\", 5);\n    i0.ɵɵtext(33, \" Manual Entry \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 6);\n    i0.ɵɵtemplate(35, CertificationTestEntryComponent_ng_template_172_div_35_Template, 27, 9, \"div\", 128);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 129);\n    i0.ɵɵelementStart(37, \"div\", 130);\n    i0.ɵɵelementStart(38, \"button\", 131);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_172_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r242);\n      const ctx_r245 = i0.ɵɵnextContext();\n      return ctx_r245.modalFIGHide();\n    });\n    i0.ɵɵelement(39, \"i\", 110);\n    i0.ɵɵtext(40, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"button\", 155);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_172_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r242);\n      const ctx_r246 = i0.ɵɵnextContext();\n      return ctx_r246.onFIGFormSubmit();\n    });\n    i0.ɵɵelement(42, \"i\", 90);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r61 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHtml\", ctx_r61.modalFIGTitle, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r61.figqForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r61.segmentList)(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx_r61.submitted && ctx_r61.figqForm.controls.segmentId.errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r61.submitted && ctx_r61.figqForm.controls.segmentId.errors);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r61.figqFile);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r61.figQuestionList);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r61.btnFIGSaveText, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_174_div_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Question is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_174_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_ng_template_174_div_13_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r247 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r247.figqEditForm.controls.question.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_174_div_25_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Mark is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_174_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_ng_template_174_div_25_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r248 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r248.figqEditForm.controls.mark.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_174_div_31_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Segment is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_174_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_ng_template_174_div_31_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r249 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r249.figqEditForm.controls.segmentId.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_174_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r254 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵelementStart(1, \"h4\", 146);\n    i0.ɵɵtext(2, \"Update Fill in the gap Question\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_174_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r254);\n      const ctx_r253 = i0.ɵɵnextContext();\n      return ctx_r253.modalEditFIGHide();\n    });\n    i0.ɵɵelement(4, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 111);\n    i0.ɵɵelementStart(6, \"form\", 112);\n    i0.ɵɵelementStart(7, \"div\", 0);\n    i0.ɵɵelementStart(8, \"div\", 133);\n    i0.ɵɵelementStart(9, \"div\", 134);\n    i0.ɵɵelementStart(10, \"span\", 135);\n    i0.ɵɵtext(11, \"Q:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"textarea\", 147);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, CertificationTestEntryComponent_ng_template_174_div_13_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 118);\n    i0.ɵɵelementStart(15, \"div\", 160);\n    i0.ɵɵelementStart(16, \"div\", 134);\n    i0.ɵɵelementStart(17, \"span\", 142);\n    i0.ɵɵtext(18, \"Answer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 164);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 165);\n    i0.ɵɵelementStart(21, \"div\", 134);\n    i0.ɵɵelementStart(22, \"span\", 142);\n    i0.ɵɵtext(23, \"Mark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"input\", 149);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, CertificationTestEntryComponent_ng_template_174_div_25_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 166);\n    i0.ɵɵelementStart(27, \"div\", 134);\n    i0.ɵɵelementStart(28, \"label\", 150);\n    i0.ɵɵtext(29, \"Segment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"ng-select\", 115);\n    i0.ɵɵtemplate(31, CertificationTestEntryComponent_ng_template_174_div_31_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 129);\n    i0.ɵɵelementStart(33, \"div\", 130);\n    i0.ɵɵelementStart(34, \"button\", 131);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_174_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r254);\n      const ctx_r255 = i0.ɵɵnextContext();\n      return ctx_r255.modalEditFIGHide();\n    });\n    i0.ɵɵelement(35, \"i\", 110);\n    i0.ɵɵtext(36, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_174_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r254);\n      const ctx_r256 = i0.ɵɵnextContext();\n      return ctx_r256.onFIGUpdate();\n    });\n    i0.ɵɵelement(38, \"i\", 90);\n    i0.ɵɵtext(39, \" Update \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r63 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r63.figqEditForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r63.editsubmitted && ctx_r63.figqEditForm.controls.question.errors);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r63.editsubmitted && ctx_r63.figqEditForm.controls.mark.errors);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r63.segmentList)(\"ngClass\", i0.ɵɵpureFunction1(8, _c0, ctx_r63.editsubmitted && ctx_r63.mcqForm.controls.segmentId.errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r63.editsubmitted && ctx_r63.figqEditForm.controls.segmentId.errors);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_176_div_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Segment is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_176_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_ng_template_176_div_13_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r257 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r257.mqForm.controls.segmentId.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_176_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r263 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_176_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r263);\n      i0.ɵɵnextContext();\n\n      const _r258 = i0.ɵɵreference(23);\n\n      const ctx_r262 = i0.ɵɵnextContext();\n      return ctx_r262.resetMatchingQFile(_r258);\n    });\n    i0.ɵɵtext(1, \" remove \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_176_div_35_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelementStart(1, \"span\", 64);\n    i0.ɵɵtext(2, \" Left hand side is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_176_div_35_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelementStart(1, \"span\", 64);\n    i0.ɵɵtext(2, \" Right hand side is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_176_div_35_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelementStart(1, \"span\", 64);\n    i0.ɵɵtext(2, \" Mark is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_176_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r270 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵelementStart(1, \"div\", 133);\n    i0.ɵɵelementStart(2, \"div\", 156);\n    i0.ɵɵelementStart(3, \"div\", 133);\n    i0.ɵɵelementStart(4, \"div\", 134);\n    i0.ɵɵelementStart(5, \"span\", 135);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"textarea\", 169);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_176_div_35_Template_textarea_ngModelChange_7_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r270);\n      const item_r264 = restoredCtx.$implicit;\n      return item_r264.LeftSide = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 137);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_176_div_35_Template_div_click_8_listener() {\n      i0.ɵɵrestoreView(_r270);\n      const ctx_r271 = i0.ɵɵnextContext(2);\n      return ctx_r271.addNewMatching();\n    });\n    i0.ɵɵelement(9, \"i\", 138);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 137);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_176_div_35_Template_div_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r270);\n      const i_r265 = restoredCtx.index;\n      const ctx_r272 = i0.ɵɵnextContext(2);\n      return ctx_r272.deleteMatching(i_r265);\n    });\n    i0.ɵɵelement(11, \"i\", 139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, CertificationTestEntryComponent_ng_template_176_div_35_div_12_Template, 3, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 156);\n    i0.ɵɵelementStart(14, \"div\", 170);\n    i0.ɵɵelementStart(15, \"div\", 134);\n    i0.ɵɵelementStart(16, \"span\", 142);\n    i0.ɵɵtext(17, \"RHS\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"textarea\", 171);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_176_div_35_Template_textarea_ngModelChange_18_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r270);\n      const item_r264 = restoredCtx.$implicit;\n      return item_r264.RightSide = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, CertificationTestEntryComponent_ng_template_176_div_35_div_19_Template, 3, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 27);\n    i0.ɵɵelementStart(21, \"div\", 134);\n    i0.ɵɵelementStart(22, \"span\", 142);\n    i0.ɵɵtext(23, \"Mark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"input\", 143);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_176_div_35_Template_input_ngModelChange_24_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r270);\n      const item_r264 = restoredCtx.$implicit;\n      return item_r264.Mark = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, CertificationTestEntryComponent_ng_template_176_div_35_div_25_Template, 3, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"hr\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r264 = ctx.$implicit;\n    const i_r265 = ctx.index;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"LHS\", i_r265 + 1, \":\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", item_r264.LeftSide)(\"ngModelOptions\", i0.ɵɵpureFunction0(8, _c2));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !item_r264.LeftSide);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", item_r264.RightSide);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r264.RightSide);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", item_r264.Mark);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r264.Mark);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_176_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r276 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵelement(1, \"h4\", 161);\n    i0.ɵɵelementStart(2, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_176_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r276);\n      const ctx_r275 = i0.ɵɵnextContext();\n      return ctx_r275.modalMatchingHide();\n    });\n    i0.ɵɵelement(3, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 152);\n    i0.ɵɵelementStart(5, \"div\", 3);\n    i0.ɵɵelementStart(6, \"div\", 4);\n    i0.ɵɵelementStart(7, \"form\", 112);\n    i0.ɵɵelementStart(8, \"div\", 0);\n    i0.ɵɵelementStart(9, \"label\", 113);\n    i0.ɵɵtext(10, \"Segment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 114);\n    i0.ɵɵelement(12, \"ng-select\", 115);\n    i0.ɵɵtemplate(13, CertificationTestEntryComponent_ng_template_176_div_13_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 116);\n    i0.ɵɵelementStart(15, \"div\", 117);\n    i0.ɵɵelementStart(16, \"div\", 4);\n    i0.ɵɵelementStart(17, \"h5\", 5);\n    i0.ɵɵtext(18, \"Bulk Upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 6);\n    i0.ɵɵelementStart(20, \"div\", 118);\n    i0.ɵɵelementStart(21, \"div\", 119);\n    i0.ɵɵelementStart(22, \"input\", 120, 167);\n    i0.ɵɵlistener(\"change\", function CertificationTestEntryComponent_ng_template_176_Template_input_change_22_listener() {\n      i0.ɵɵrestoreView(_r276);\n\n      const _r258 = i0.ɵɵreference(23);\n\n      const ctx_r277 = i0.ɵɵnextContext();\n      return ctx_r277.loadMatchingQFile(_r258.files);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 122);\n    i0.ɵɵtemplate(25, CertificationTestEntryComponent_ng_template_176_button_25_Template, 2, 0, \"button\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 124);\n    i0.ɵɵelementStart(27, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_176_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r276);\n      const ctx_r278 = i0.ɵɵnextContext();\n      return ctx_r278.downloadSampleMatchingQ();\n    });\n    i0.ɵɵelement(28, \"i\", 126);\n    i0.ɵɵtext(29, \" Download Sample File \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 127);\n    i0.ɵɵelementStart(31, \"div\", 4);\n    i0.ɵɵelementStart(32, \"h5\", 5);\n    i0.ɵɵtext(33, \" Manual Entry \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 6);\n    i0.ɵɵtemplate(35, CertificationTestEntryComponent_ng_template_176_div_35_Template, 27, 9, \"div\", 128);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 168);\n    i0.ɵɵelementStart(37, \"div\", 130);\n    i0.ɵɵelementStart(38, \"button\", 131);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_176_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r276);\n      const ctx_r279 = i0.ɵɵnextContext();\n      return ctx_r279.modalMatchingHide();\n    });\n    i0.ɵɵelement(39, \"i\", 110);\n    i0.ɵɵtext(40, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"button\", 155);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_176_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r276);\n      const ctx_r280 = i0.ɵɵnextContext();\n      return ctx_r280.onMatchingFormSubmit();\n    });\n    i0.ɵɵelement(42, \"i\", 90);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r65 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHtml\", ctx_r65.modalMatchingTitle, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r65.mqForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r65.segmentList)(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx_r65.submitted && ctx_r65.mqForm.controls.segmentId.errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r65.submitted && ctx_r65.mqForm.controls.segmentId.errors);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r65.matchingqFile);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r65.matchingQuestionList);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r65.btnMatchingSaveText, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_178_div_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Question is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_178_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_ng_template_178_div_13_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r281 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r281.mqEditForm.controls.leftSide.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_178_div_25_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Mark is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_178_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_ng_template_178_div_25_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r282 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r282.mqEditForm.controls.mark.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_178_div_31_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Segment is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_178_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_ng_template_178_div_31_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r283 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r283.mqEditForm.controls.segmentId.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_178_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r288 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵelementStart(1, \"h4\", 146);\n    i0.ɵɵtext(2, \"Update Matching Question\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_178_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r288);\n      const ctx_r287 = i0.ɵɵnextContext();\n      return ctx_r287.modalEditMatchingHide();\n    });\n    i0.ɵɵelement(4, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 111);\n    i0.ɵɵelementStart(6, \"form\", 112);\n    i0.ɵɵelementStart(7, \"div\", 0);\n    i0.ɵɵelementStart(8, \"div\", 133);\n    i0.ɵɵelementStart(9, \"div\", 134);\n    i0.ɵɵelementStart(10, \"span\", 135);\n    i0.ɵɵtext(11, \"LHS:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"textarea\", 172);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, CertificationTestEntryComponent_ng_template_178_div_13_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 118);\n    i0.ɵɵelementStart(15, \"div\", 166);\n    i0.ɵɵelementStart(16, \"div\", 134);\n    i0.ɵɵelementStart(17, \"span\", 142);\n    i0.ɵɵtext(18, \"RHS\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"textarea\", 173);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 174);\n    i0.ɵɵelementStart(21, \"div\", 134);\n    i0.ɵɵelementStart(22, \"span\", 142);\n    i0.ɵɵtext(23, \"Mark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"input\", 149);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, CertificationTestEntryComponent_ng_template_178_div_25_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 166);\n    i0.ɵɵelementStart(27, \"div\", 134);\n    i0.ɵɵelementStart(28, \"label\", 150);\n    i0.ɵɵtext(29, \"Segment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"ng-select\", 115);\n    i0.ɵɵtemplate(31, CertificationTestEntryComponent_ng_template_178_div_31_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 129);\n    i0.ɵɵelementStart(33, \"div\", 130);\n    i0.ɵɵelementStart(34, \"button\", 131);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_178_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r288);\n      const ctx_r289 = i0.ɵɵnextContext();\n      return ctx_r289.modalEditMatchingHide();\n    });\n    i0.ɵɵelement(35, \"i\", 110);\n    i0.ɵɵtext(36, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_178_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r288);\n      const ctx_r290 = i0.ɵɵnextContext();\n      return ctx_r290.onMatchingUpdate();\n    });\n    i0.ɵɵelement(38, \"i\", 90);\n    i0.ɵɵtext(39, \" Update \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r67 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r67.mqEditForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r67.editsubmitted && ctx_r67.mqEditForm.controls.leftSide.errors);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r67.editsubmitted && ctx_r67.mqEditForm.controls.mark.errors);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r67.segmentList)(\"ngClass\", i0.ɵɵpureFunction1(8, _c0, ctx_r67.editsubmitted && ctx_r67.mcqForm.controls.segmentId.errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r67.editsubmitted && ctx_r67.mqEditForm.controls.segmentId.errors);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_180_div_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \" Segment is required\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_180_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, CertificationTestEntryComponent_ng_template_180_div_13_span_1_Template, 2, 0, \"span\", 63);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r291 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r291.wqForm.controls.segmentId.errors.required);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_180_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r297 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_180_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r297);\n      i0.ɵɵnextContext();\n\n      const _r292 = i0.ɵɵreference(23);\n\n      const ctx_r296 = i0.ɵɵnextContext();\n      return ctx_r296.resetWQFile(_r292);\n    });\n    i0.ɵɵtext(1, \" remove \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_180_div_35_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelementStart(1, \"span\", 64);\n    i0.ɵɵtext(2, \" Question is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_180_div_35_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelementStart(1, \"span\", 64);\n    i0.ɵɵtext(2, \" Mark is required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_180_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r303 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵelementStart(1, \"div\", 133);\n    i0.ɵɵelementStart(2, \"div\", 156);\n    i0.ɵɵelementStart(3, \"div\", 133);\n    i0.ɵɵelementStart(4, \"div\", 134);\n    i0.ɵɵelementStart(5, \"span\", 135);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"textarea\", 136);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_180_div_35_Template_textarea_ngModelChange_7_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r303);\n      const item_r298 = restoredCtx.$implicit;\n      return item_r298.Question = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 137);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_180_div_35_Template_div_click_8_listener() {\n      i0.ɵɵrestoreView(_r303);\n      const ctx_r304 = i0.ɵɵnextContext(2);\n      return ctx_r304.addNewWritten();\n    });\n    i0.ɵɵelement(9, \"i\", 138);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 137);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_180_div_35_Template_div_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r303);\n      const i_r299 = restoredCtx.index;\n      const ctx_r305 = i0.ɵɵnextContext(2);\n      return ctx_r305.deleteWritten(i_r299);\n    });\n    i0.ɵɵelement(11, \"i\", 139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, CertificationTestEntryComponent_ng_template_180_div_35_div_12_Template, 3, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 156);\n    i0.ɵɵelementStart(14, \"div\", 114);\n    i0.ɵɵelementStart(15, \"div\", 134);\n    i0.ɵɵelementStart(16, \"span\", 142);\n    i0.ɵɵtext(17, \"Mark\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 143);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_180_div_35_Template_input_ngModelChange_18_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r303);\n      const item_r298 = restoredCtx.$implicit;\n      return item_r298.Mark = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, CertificationTestEntryComponent_ng_template_180_div_35_div_19_Template, 3, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"hr\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r298 = ctx.$implicit;\n    const i_r299 = ctx.index;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Q\", i_r299 + 1, \":\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", item_r298.Question)(\"ngModelOptions\", i0.ɵɵpureFunction0(6, _c2));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !item_r298.Question);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", item_r298.Mark);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r298.Mark);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_180_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r308 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵelement(1, \"h4\", 161);\n    i0.ɵɵelementStart(2, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_180_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r308);\n      const ctx_r307 = i0.ɵɵnextContext();\n      return ctx_r307.modalWrittenHide();\n    });\n    i0.ɵɵelement(3, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 152);\n    i0.ɵɵelementStart(5, \"div\", 3);\n    i0.ɵɵelementStart(6, \"div\", 4);\n    i0.ɵɵelementStart(7, \"form\", 112);\n    i0.ɵɵelementStart(8, \"div\", 0);\n    i0.ɵɵelementStart(9, \"label\", 113);\n    i0.ɵɵtext(10, \"Segment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 114);\n    i0.ɵɵelement(12, \"ng-select\", 115);\n    i0.ɵɵtemplate(13, CertificationTestEntryComponent_ng_template_180_div_13_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 116);\n    i0.ɵɵelementStart(15, \"div\", 117);\n    i0.ɵɵelementStart(16, \"div\", 4);\n    i0.ɵɵelementStart(17, \"h5\", 5);\n    i0.ɵɵtext(18, \"Bulk Upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 6);\n    i0.ɵɵelementStart(20, \"div\", 118);\n    i0.ɵɵelementStart(21, \"div\", 119);\n    i0.ɵɵelementStart(22, \"input\", 120, 175);\n    i0.ɵɵlistener(\"change\", function CertificationTestEntryComponent_ng_template_180_Template_input_change_22_listener() {\n      i0.ɵɵrestoreView(_r308);\n\n      const _r292 = i0.ɵɵreference(23);\n\n      const ctx_r309 = i0.ɵɵnextContext();\n      return ctx_r309.loadWQFile(_r292.files);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 122);\n    i0.ɵɵtemplate(25, CertificationTestEntryComponent_ng_template_180_button_25_Template, 2, 0, \"button\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 124);\n    i0.ɵɵelementStart(27, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_180_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r308);\n      const ctx_r310 = i0.ɵɵnextContext();\n      return ctx_r310.downloadSampleWQ();\n    });\n    i0.ɵɵelement(28, \"i\", 126);\n    i0.ɵɵtext(29, \" Download Sample File \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 127);\n    i0.ɵɵelementStart(31, \"div\", 4);\n    i0.ɵɵelementStart(32, \"h5\", 5);\n    i0.ɵɵtext(33, \" Manual Entry \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 6);\n    i0.ɵɵtemplate(35, CertificationTestEntryComponent_ng_template_180_div_35_Template, 21, 7, \"div\", 128);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 129);\n    i0.ɵɵelementStart(37, \"div\", 130);\n    i0.ɵɵelementStart(38, \"button\", 131);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_180_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r308);\n      const ctx_r311 = i0.ɵɵnextContext();\n      return ctx_r311.modalWrittenHide();\n    });\n    i0.ɵɵelement(39, \"i\", 110);\n    i0.ɵɵtext(40, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"button\", 155);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_180_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r308);\n      const ctx_r312 = i0.ɵɵnextContext();\n      return ctx_r312.onWrittenFormSubmit();\n    });\n    i0.ɵɵelement(42, \"i\", 90);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r69 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHtml\", ctx_r69.modalWrittenTitle, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r69.wqForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx_r69.segmentList)(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx_r69.submitted && ctx_r69.wqForm.controls.segmentId.errors));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r69.submitted && ctx_r69.wqForm.controls.segmentId.errors);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r69.wqFile);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r69.writtenQuestionList);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r69.btnWrittenSaveText, \" \");\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_182_tr_26_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r320 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 185);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_182_tr_26_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r320);\n      const i_r315 = i0.ɵɵnextContext().index;\n      const ctx_r318 = i0.ɵɵnextContext(2);\n      return ctx_r318.deleteRowFromQsSetupTable(i_r315);\n    });\n    i0.ɵɵelement(1, \"i\", 186);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_182_tr_26_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r322 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 187);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_182_tr_26_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r322);\n      const ctx_r321 = i0.ɵɵnextContext(3);\n      return ctx_r321.addRowIntoQsSetupTable();\n    });\n    i0.ɵɵelement(1, \"i\", 188);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_182_tr_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r324 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelementStart(1, \"th\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 179);\n    i0.ɵɵelementStart(4, \"ng-select\", 180);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_182_tr_26_Template_ng_select_ngModelChange_4_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r324);\n      const item_r314 = restoredCtx.$implicit;\n      return item_r314.SegmentId = $event;\n    })(\"change\", function CertificationTestEntryComponent_ng_template_182_tr_26_Template_ng_select_change_4_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r324);\n      const item_r314 = restoredCtx.$implicit;\n      const i_r315 = restoredCtx.index;\n      const ctx_r325 = i0.ɵɵnextContext(2);\n      return ctx_r325.onChangeSegment($event, item_r314, i_r315);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵelementStart(6, \"input\", 181);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_182_tr_26_Template_input_ngModelChange_6_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r324);\n      const item_r314 = restoredCtx.$implicit;\n      return item_r314.NoOfMCQ = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵelementStart(8, \"input\", 181);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_182_tr_26_Template_input_ngModelChange_8_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r324);\n      const item_r314 = restoredCtx.$implicit;\n      return item_r314.NoOfTrueFalse = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵelementStart(10, \"input\", 181);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_182_tr_26_Template_input_ngModelChange_10_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r324);\n      const item_r314 = restoredCtx.$implicit;\n      return item_r314.NoOfFIG = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵelementStart(12, \"input\", 181);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_182_tr_26_Template_input_ngModelChange_12_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r324);\n      const item_r314 = restoredCtx.$implicit;\n      return item_r314.NoOfMatching = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵelementStart(14, \"input\", 181);\n    i0.ɵɵlistener(\"ngModelChange\", function CertificationTestEntryComponent_ng_template_182_tr_26_Template_input_ngModelChange_14_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r324);\n      const item_r314 = restoredCtx.$implicit;\n      return item_r314.NoOfWriting = $event;\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 182);\n    i0.ɵɵtemplate(16, CertificationTestEntryComponent_ng_template_182_tr_26_button_16_Template, 2, 0, \"button\", 183);\n    i0.ɵɵtemplate(17, CertificationTestEntryComponent_ng_template_182_tr_26_button_17_Template, 2, 0, \"button\", 184);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r314 = ctx.$implicit;\n    const i_r315 = ctx.index;\n    const ctx_r313 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r315 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"clearable\", false)(\"clearOnBackspace\", false)(\"ngModel\", item_r314.SegmentId)(\"items\", ctx_r313.segmentList);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", item_r314.NoOfMCQ);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", item_r314.NoOfTrueFalse);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", item_r314.NoOfFIG);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", item_r314.NoOfMatching);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", item_r314.NoOfWriting);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i_r315 !== 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r315 === ctx_r313.segmentWiseQuestionsInput.length - 1);\n  }\n}\n\nfunction CertificationTestEntryComponent_ng_template_182_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r332 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵelementStart(1, \"h4\", 146);\n    i0.ɵɵtext(2, \"Segment wise questions setup\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_182_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r332);\n      const ctx_r331 = i0.ɵɵnextContext();\n      return ctx_r331.modalQsSetupHide();\n    });\n    i0.ɵɵelement(4, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 152);\n    i0.ɵɵelementStart(6, \"table\", 176);\n    i0.ɵɵelementStart(7, \"thead\");\n    i0.ɵɵelementStart(8, \"tr\");\n    i0.ɵɵelementStart(9, \"th\", 20);\n    i0.ɵɵtext(10, \"#\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 20);\n    i0.ɵɵtext(12, \"Segment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 20);\n    i0.ɵɵtext(14, \"MCQ\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\", 20);\n    i0.ɵɵtext(16, \"True/False\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\", 20);\n    i0.ɵɵtext(18, \"Fill in the gap\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\", 20);\n    i0.ɵɵtext(20, \"Matching\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"th\", 20);\n    i0.ɵɵtext(22, \"Writing\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"th\", 177);\n    i0.ɵɵtext(24, \"- / +\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"tbody\");\n    i0.ɵɵtemplate(26, CertificationTestEntryComponent_ng_template_182_tr_26_Template, 18, 12, \"tr\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 168);\n    i0.ɵɵelementStart(28, \"button\", 131);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_182_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r332);\n      const ctx_r333 = i0.ɵɵnextContext();\n      return ctx_r333.modalQsSetupHide();\n    });\n    i0.ɵɵelement(29, \"i\", 110);\n    i0.ɵɵtext(30, \" Close \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 155);\n    i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_ng_template_182_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r332);\n      const ctx_r334 = i0.ɵɵnextContext();\n      return ctx_r334.saveSegmentWiseQuestionSetup();\n    });\n    i0.ɵɵelement(32, \"i\", 178);\n    i0.ɵɵtext(33, \" DONE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r71 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(26);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r71.segmentWiseQuestionsInput);\n  }\n}\n\nexport class CertificationTestEntryComponent {\n  constructor(appComponent, modalService, formBuilder, _service, toastr, route, config) {\n    this.appComponent = appComponent;\n    this.modalService = modalService;\n    this.formBuilder = formBuilder;\n    this._service = _service;\n    this.toastr = toastr;\n    this.route = route;\n    this.submitted = false;\n    this.editsubmitted = false;\n    this.formTitle = 'Certification Test Entry';\n    this.btnSaveText = 'Save';\n    this.is_per = true;\n    this.ExamExists = false;\n    this.ExamExistsMsg = \"\";\n    this.modalTitle = 'Set Time';\n    this.rows = [];\n    this.loadingIndicator = false;\n    this.ColumnMode = ColumnMode;\n    this.isEdit = false;\n    this.mcqList = [];\n    this.loadingMCQ = false;\n    this.btnMCQSaveText = 'Save';\n    this.modalMCQTitle = 'Add MCQ Questions';\n    this.truFalseList = [];\n    this.tfQuestionList = [];\n    this.loadingTFQ = false;\n    this.btnTFQSaveText = 'Save';\n    this.modalTFTitle = 'Add True/False Questions';\n    this.figList = [];\n    this.figQuestionList = [];\n    this.loadingFIG = false;\n    this.btnFIGSaveText = 'Save';\n    this.modalFIGTitle = 'Add fill in the gaps Questions';\n    this.matchingList = [];\n    this.matchingQuestionList = [];\n    this.loadingMatching = false;\n    this.btnMatchingSaveText = 'Save';\n    this.modalMatchingTitle = 'Add Left Right Matching Questions';\n    this.writtenList = [];\n    this.writtenQuestionList = [];\n    this.loadingWritten = false;\n    this.btnWrittenSaveText = 'Save';\n    this.modalWrittenTitle = 'Add Written Questions';\n    this.modalConfig = {\n      class: 'gray modal-lg',\n      backdrop: 'static'\n    };\n    this.courseList = [];\n    this.examList = [];\n    this.setList = [];\n    this.mcqQuestionList = [];\n    this.segmentList = [];\n    this.segmentWiseQuestionsInput = [];\n    this.segmentWiseQuestions = [];\n    this.scrollBarHorizontal = window.innerWidth < 1200;\n    this.toolbar = [['bold', 'italic'], ['underline', 'strike'], ['ordered_list', 'bullet_list'], [{\n      heading: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']\n    }], ['text_color', 'background_color'], ['align_left', 'align_center', 'align_right', 'align_justify']];\n\n    window.onresize = () => {\n      this.scrollBarHorizontal = window.innerWidth < 1200;\n    };\n\n    this.bsConfig = Object.assign({}, {\n      containerClass: 'theme-blue'\n    });\n    config.seconds = false;\n    config.spinners = false;\n\n    if (this.route.snapshot.queryParamMap.has(\"id\")) {\n      this.id = this.route.snapshot.queryParamMap.get(\"id\");\n      this.isEdit = true;\n      this.getMarks();\n      this.getItem();\n    }\n  }\n\n  handleSelectClick(selectElement) {\n    this.appComponent.handleSelectClick(selectElement);\n  }\n\n  ngOnInit() {\n    this.editor = new Editor();\n    this.entryForm = this.formBuilder.group({\n      id: [this.id],\n      courseId: [null, [Validators.required]],\n      instructions: [''],\n      duration: [null, [Validators.required]],\n      mcqOnly: [true],\n      random: [false],\n      publish: [false],\n      marks: [null],\n      quota: [null, [Validators.required]],\n      // no_of_mcq: [null, [Validators.required]],\n      // no_of_tfq: [null],\n      // no_of_figq: [null],\n      // no_of_mq: [null],\n      // no_of_wq: [null],\n      startDate: [null],\n      endDate: [null]\n    });\n    this.mcqForm = this.formBuilder.group({\n      segmentId: [null, [Validators.required]]\n    });\n    this.tfqForm = this.formBuilder.group({\n      segmentId: [null, [Validators.required]]\n    });\n    this.figqForm = this.formBuilder.group({\n      segmentId: [null, [Validators.required]]\n    });\n    this.mqForm = this.formBuilder.group({\n      segmentId: [null, [Validators.required]]\n    });\n    this.wqForm = this.formBuilder.group({\n      segmentId: [null, [Validators.required]]\n    });\n    this.mcqEditForm = this.formBuilder.group({\n      id: [null, [Validators.required]],\n      segmentId: [null, [Validators.required]],\n      question: [null, [Validators.required]],\n      options: [null, [Validators.required]],\n      mark: [null, [Validators.required]]\n    });\n    this.tfqEditForm = this.formBuilder.group({\n      id: [null, [Validators.required]],\n      segmentId: [null, [Validators.required]],\n      question: [null, [Validators.required]],\n      answer: [false, [Validators.required]],\n      mark: [null, [Validators.required]]\n    });\n    this.figqEditForm = this.formBuilder.group({\n      id: [null, [Validators.required]],\n      segmentId: [null, [Validators.required]],\n      question: [null, [Validators.required]],\n      answer: [null, [Validators.required]],\n      mark: [null, [Validators.required]]\n    });\n    this.mqEditForm = this.formBuilder.group({\n      id: [null, [Validators.required]],\n      segmentId: [null, [Validators.required]],\n      leftSide: [null, [Validators.required]],\n      rightSide: [null, [Validators.required]],\n      mark: [null, [Validators.required]]\n    }); ///\n    // this.entryForm.get(\"random\").valueChanges.subscribe((isChecked) => {\n    //         if (isChecked) {\n    //           this.entryForm.get(\"publish\").disable();\n    //           this.entryForm.get(\"publish\").setValue(false);\n    //           this.entryForm.get(\"mcqOnly\").setValue(false);\n    //         } \n    //       });\n    //       this.entryForm.get(\"mcqOnly\").valueChanges.subscribe((isChecked) => {\n    //         if (isChecked) {\n    //           this.entryForm.get(\"random\").setValue(false);\n    //           this.entryForm.get(\"publish\").setValue(true);\n    //         }else{\n    //           this.entryForm.get(\"publish\").setValue(false);\n    //         }\n    //       });\n    ///\n\n    if (!this.isEdit) this.getMarks();\n    this.getCourseList();\n    this.getSegmentList();\n  }\n\n  ngOnDestroy() {\n    this.editor.destroy();\n  }\n\n  get f() {\n    return this.entryForm.controls;\n  }\n\n  getMarks() {\n    this._service.get('configuration/get-exam-data').subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.entryForm.controls['instructions'].setValue(res.Data.ExamInstruction);\n      this.MCQMark = res.Data.MCQMark;\n      this.TrueFalseMark = res.Data.TrueFalseMark;\n      this.FIGMark = res.Data.FIGMark;\n      this.MatchingMark = res.Data.MatchingMark;\n      this.WrittenMark = res.Data.WrittenMark;\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  formatAMPM(date) {\n    var hours = date.getHours();\n    var minutes = date.getMinutes();\n    var ampm = hours >= 12 ? 'PM' : 'AM';\n    hours = hours % 12;\n    hours = hours ? hours : 12; // the hour '0' should be '12'\n\n    minutes = minutes < 10 ? '0' + minutes : minutes;\n    var strTime = hours + ':' + minutes + ' ' + ampm;\n    return strTime;\n  }\n\n  getItem() {\n    const obj = {\n      id: this.id\n    };\n    this.blockUI.start('Getting data. Please wait ...');\n\n    this._service.get('exam/certificate-test/get/' + this.id).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.entryForm.controls['courseId'].setValue(res.Data.CourseId);\n      this.entryForm.controls['marks'].setValue(res.Data.Marks);\n      this.entryForm.controls['instructions'].setValue(res.Data.ExamInstructions);\n      this.entryForm.controls['quota'].setValue(res.Data.Quota);\n      this.entryForm.controls['random'].setValue(res.Data.Random);\n      this.entryForm.controls['publish'].setValue(res.Data.Publish);\n      this.entryForm.controls['duration'].setValue(res.Data.DurationMnt); // this.entryForm.controls['no_of_mcq'].setValue(res.Data.ExamMCQNo);\n      // this.entryForm.controls['no_of_tfq'].setValue(res.Data.ExamTrueFalseNo);\n      // this.entryForm.controls['no_of_figq'].setValue(res.Data.ExamFIGNo);\n      // this.entryForm.controls['no_of_mq'].setValue(res.Data.ExamMatchingNo);\n      // this.entryForm.controls['no_of_wq'].setValue(res.Data.ExamWritingNo);\n\n      this.segmentWiseQuestions = res.Data.QsSetups;\n      this.totalQuestions = {\n        mcq: this.segmentWiseQuestions.map(o => o.NoOfMCQ ? Number(o.NoOfMCQ) : 0).reduce((a, c) => {\n          return a + c;\n        }),\n        tfq: this.segmentWiseQuestions.map(o => o.NoOfTrueFalse ? Number(o.NoOfTrueFalse) : 0).reduce((a, c) => {\n          return a + c;\n        }),\n        figq: this.segmentWiseQuestions.map(o => o.NoOfFIG ? Number(o.NoOfFIG) : 0).reduce((a, c) => {\n          return a + c;\n        }),\n        matchingq: this.segmentWiseQuestions.map(o => o.NoOfMatching ? Number(o.NoOfMatching) : 0).reduce((a, c) => {\n          return a + c;\n        }),\n        writtenq: this.segmentWiseQuestions.map(o => o.NoOfWriting ? Number(o.NoOfWriting) : 0).reduce((a, c) => {\n          return a + c;\n        })\n      };\n      this.entryForm.controls['mcqOnly'].setValue(res.Data.MCQOnly);\n      if (res.Data.StartDate) this.entryForm.controls['startDate'].setValue(moment.utc(res.Data.StartDate).local().format('yyyy-MM-DDTHH:mm:ss'));\n      if (res.Data.EndDate) this.entryForm.controls['endDate'].setValue(moment.utc(res.Data.EndDate).local().format('yyyy-MM-DDTHH:mm:ss'));\n      this.btnSaveText = 'Update';\n      this.formTitle = 'Certification Test Update';\n      this.getMCQList();\n      this.getTFQList();\n      this.geWrittenList();\n      this.geFIGList();\n      this.getMatchingList();\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n\n    this.submitted = false;\n  }\n\n  getCourseList() {\n    this._service.get('course/dropdown-list').subscribe(res => {\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.courseList = res.Data;\n    }, err => {});\n  }\n\n  onFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid) {\n      return;\n    }\n\n    if (!this.entryForm.value.duration) {\n      this.toastr.warning('Please insert exam duration for this exam', 'WARNING!', {\n        timeOut: 3000\n      });\n      return;\n    }\n\n    if (this.segmentWiseQuestions.length === 0) {\n      this.toastr.warning('Segment wise question is not set yet.', 'WARNING!', {\n        timeOut: 3000\n      });\n      return;\n    }\n\n    this.segmentWiseQuestions.forEach(element => {\n      var _a, _b, _c, _d, _e;\n\n      element.NoOfFIG = (_a = element.NoOfFIG) !== null && _a !== void 0 ? _a : 0;\n      element.NoOfMCQ = (_b = element.NoOfMCQ) !== null && _b !== void 0 ? _b : 0;\n      element.NoOfMatching = (_c = element.NoOfMatching) !== null && _c !== void 0 ? _c : 0;\n      element.NoOfTrueFalse = (_d = element.NoOfTrueFalse) !== null && _d !== void 0 ? _d : 0;\n      element.NoOfWriting = (_e = element.NoOfWriting) !== null && _e !== void 0 ? _e : 0;\n    });\n    const obj = {\n      Id: this.entryForm.value.id ? this.entryForm.value.id : null,\n      CourseId: this.entryForm.value.courseId,\n      ExamInstructions: this.entryForm.value.instructions ? this.entryForm.value.instructions.trim() : '',\n      Quota: Number(this.entryForm.value.quota),\n      Random: Number(this.entryForm.value.random),\n      Publish: Number(this.entryForm.value.publish),\n      Marks: Number(this.entryForm.value.marks),\n      // ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,\n      // ExamTrueFalseNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,\n      // ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,\n      // ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,\n      // ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,\n      MCQOnly: this.entryForm.value.mcqOnly,\n      // MinPercentageForCertification: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,\n      // MinMarksForCertification: Number(this.entryForm.value.minMarks),\n      SegmentWiseSetups: this.segmentWiseQuestions,\n      DurationMnt: Number(this.entryForm.value.duration),\n      QuesType: 'NoQues',\n      StartDate: this.entryForm.value.startDate ? moment(this.entryForm.value.startDate).toISOString() : '',\n      EndDate: this.entryForm.value.endDate ? moment(this.entryForm.value.endDate).toISOString() : ''\n    };\n    const formdata = new FormData();\n    formdata.append('Model', JSON.stringify(obj));\n    this.blockUI.start('Saving data. Please wait...');\n\n    this._service.post('exam/certificate-test/create-or-update', formdata).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, 'Success!', {\n        timeOut: 2000\n      });\n      this.entryForm.controls['id'].setValue(res.Data);\n      this.btnSaveText = 'Update';\n      this.id = res.Data;\n      this.isEdit = true;\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  clearForm() {\n    this.entryForm.reset();\n    this.submitted = false;\n    this.formTitle = 'Create Certification Test';\n    this.btnSaveText = 'Save';\n    this.entryForm.controls['mcqOnly'].setValue(true);\n  }\n\n  deleteQuestion(id, type) {\n    let url = '';\n\n    switch (type) {\n      case 'MCQ':\n        url = 'exam/certificate-test/delete-mcq/' + id;\n        break;\n\n      case 'TFQ':\n        url = 'exam/certificate-test/delete-true-false/' + id;\n        break;\n\n      case 'FIGQ':\n        url = 'exam/certificate-test/delete-fill-in-the-gap/' + id;\n        break;\n\n      case 'WQ':\n        url = 'exam/certificate-test/delete-written/' + id;\n        break;\n\n      case 'MatchingQ':\n        url = 'exam/certificate-test/delete-matching/' + id;\n        break;\n    }\n\n    this.blockUI.start('Deleting data. Please wait ...');\n\n    this._service.get(url).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, 'SUCCESS!', {\n        timeOut: 2000\n      });\n\n      switch (type) {\n        case 'MCQ':\n          this.getMCQList();\n          break;\n\n        case 'TFQ':\n          this.getTFQList();\n          break;\n\n        case 'FIGQ':\n          this.geFIGList();\n          break;\n\n        case 'MatchingQ':\n          this.getMatchingList();\n          break;\n\n        case 'WQ':\n          this.geWrittenList();\n          break;\n      }\n    }, err => {\n      this.toastr.error(err.message || err, 'Error!', {\n        timeOut: 10000\n      });\n      this.blockUI.stop();\n    });\n  }\n\n  downloadQuestionList(type) {\n    let url = '',\n        title = '';\n    let timeZoneOffset = new Date().getTimezoneOffset();\n\n    switch (type) {\n      case 'MCQ':\n        url = 'exam/certificate-test/get-mcq-list-excel/' + this.id + \"/\" + timeZoneOffset;\n        title = 'certification_test_mcq_file.xlsx';\n        break;\n\n      case 'TFQ':\n        url = 'exam/certificate-test/get-true-false-list-excel/' + this.id + \"/\" + timeZoneOffset;\n        title = 'certification_test_true_false_file.xlsx';\n        break;\n\n      case 'FIGQ':\n        url = 'exam/certificate-test/get-fill-in=the=gap-list-excel/' + this.id + \"/\" + timeZoneOffset;\n        title = 'certification_test_fill_in_the_gap_file.xlsx';\n        break;\n\n      case 'MatchingQ':\n        url = 'exam/certificate-test/get-matching-list-excel/' + this.id + \"/\" + timeZoneOffset;\n        title = 'certification_test_left_right_matching_file.xlsx';\n        break;\n\n      case 'WQ':\n        url = 'exam/certificate-test/get-written-list-excel/' + this.id + \"/\" + timeZoneOffset;\n        title = 'certification_test_written_file.xlsx';\n        break;\n\n      default:\n        break;\n    }\n\n    this.blockUI.start('Generating excel file. Please wait ...');\n    return this._service.downloadFile(url).subscribe(res => {\n      this.blockUI.stop();\n      const url = window.URL.createObjectURL(res);\n      var link = document.createElement('a');\n      link.href = url;\n      link.download = title;\n      link.click();\n    }, error => {\n      this.toastr.error(error.message || error, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  } // ============== MCQ Question ====================\n\n\n  getMCQList() {\n    const obj = {\n      examId: this.id // examId: this.entryForm.value.examId,\n      // setId: this.entryForm.value.setId\n\n    };\n    this.loadingMCQ = true;\n\n    this._service.get('exam/certificate-test/get-mcq-list/' + this.id).subscribe(res => {\n      setTimeout(() => {\n        this.loadingMCQ = false;\n      }, 1000);\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.mcqList = res.Data; // let answers = [];\n      // res.Data.forEach(element => {\n      //   answers = element.Answers.split(',');\n      //   this.mcqList.push({\n      //     Question: element.Question,\n      //     Options: [{ Text: element.Option1, Selected: answers.indexOf('0') !== -1 },\n      //     { Text: element.Option2, Selected: answers.indexOf('1') !== -1 },\n      //     { Text: element.Option3, Selected: answers.indexOf('2') !== -1 },\n      //     { Text: element.Option4, Selected: answers.indexOf('3') !== -1 }],\n      //     Mark: element.Mark\n      //   });\n      // });\n    }, err => {\n      this.toastr.error(err.message || err, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n      setTimeout(() => {\n        this.loadingMCQ = false;\n      }, 1000);\n    });\n  }\n\n  openMCQModal(template) {\n    // let answers = [];\n    // if (this.mcqList.length === 0) {\n    //   this.btnMCQSaveText = 'Save';\n    //   this.addNewMCQ();\n    // } else {\n    //   this.mcqList.forEach(element => {\n    //     answers = element.Answers.split(',');\n    //     this.mcqQuestionList.push({\n    //       Question: element.Question,\n    //       Options: [{ Text: element.Option1, Selected: answers.indexOf('1') !== -1 },\n    //       { Text: element.Option2, Selected: answers.indexOf('2') !== -1 },\n    //       { Text: element.Option3, Selected: answers.indexOf('3') !== -1 },\n    //       { Text: element.Option4, Selected: answers.indexOf('4') !== -1 }],\n    //       Mark: element.Mark\n    //     });\n    //   });\n    // }\n    this.btnMCQSaveText = 'Save';\n    this.addNewMCQ();\n    this.modalMCQRef = this.modalService.show(template, this.modalConfig);\n  }\n\n  modalMCQHide() {\n    this.modalMCQRef.hide();\n    this.mcqQuestionList = [];\n    this.mcqFile = null;\n    this.mcqForm.reset();\n  }\n\n  deleteMCQ(index) {\n    this.mcqQuestionList.splice(index, 1);\n  }\n\n  addNewMCQ() {\n    this.mcqQuestionList.push({\n      Question: null,\n      Options: [{\n        Text: null,\n        Selected: false\n      }, {\n        Text: null,\n        Selected: false\n      }, {\n        Text: null,\n        Selected: false\n      }, {\n        Text: null,\n        Selected: false\n      }],\n      Mark: this.MCQMark\n    });\n  }\n\n  downloadSampleMCQ() {\n    return this._service.downloadFile('exam/download-sample-question', {\n      quesType: 'MCQ'\n    }).subscribe(res => {\n      const url = window.URL.createObjectURL(res);\n      var link = document.createElement('a');\n      link.href = url;\n      link.download = \"certification_test_mcq_sample_file.xlsx\";\n      link.click();\n    }, error => {\n      this.toastr.error(error.message || error, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  onMCQFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid || this.mcqForm.invalid) {\n      this.toastr.warning('Please fill up all the details of the exam first', 'WARNING!', {\n        timeOut: 3000\n      });\n      return;\n    }\n\n    if (!this.entryForm.value.duration) {\n      this.toastr.warning('Please insert exam duration for this exam', 'WARNING!', {\n        timeOut: 3000\n      });\n      return;\n    }\n\n    if (this.segmentWiseQuestions.length === 0) {\n      this.toastr.warning('Segment wise question is not set yet.', 'WARNING!', {\n        timeOut: 3000\n      });\n      return;\n    }\n\n    let questions = [];\n\n    for (let i = 0; i < this.mcqQuestionList.length; i++) {\n      const element = this.mcqQuestionList[i];\n\n      if (!element.Question) {\n        this.toastr.error('Please Enter Question for Question: ' + ++i, 'WARNING!', {\n          timeOut: 4000\n        });\n        return false;\n      } // if (!element.Options[0].Text || !element.Options[1].Text || !element.Options[2].Text || !element.Options[3].Text) {\n\n\n      if (element.Options.filter(x => x.Text === null || x.Text === '').length > 0) {\n        this.toastr.error('Value missing for an option of Question: ' + ++i, 'WARNING!', {\n          timeOut: 4000\n        });\n        return false;\n      } // if (!element.Option1.Selected && !element.Option1.Selected && !element.Option1.Selected && !element.Option1.Selected) {\n\n\n      if (element.Options.filter(x => x.Selected).length === 0) {\n        this.toastr.error('No option has been selected for answer of Question: ' + ++i, 'WARNING!', {\n          timeOut: 4000\n        });\n        return false;\n      }\n\n      if (!element.Mark) {\n        this.toastr.error('Please enter mark for Question: ' + ++i, 'WARNING!', {\n          timeOut: 4000\n        });\n        return false;\n      }\n\n      questions.push({\n        Question: element.Question,\n        Mark: element.Mark,\n        Option1: element.Options[0].Text,\n        Option2: element.Options[1].Text,\n        Option3: element.Options[2].Text,\n        Option4: element.Options[3].Text,\n        Answers: element.Options.map(function (x, i) {\n          if (x.Selected) return i + 1;else return -1;\n        }).filter(x => x >= 0).join()\n      });\n    }\n\n    this.segmentWiseQuestions.forEach(element => {\n      var _a, _b, _c, _d, _e;\n\n      element.NoOfFIG = (_a = element.NoOfFIG) !== null && _a !== void 0 ? _a : 0;\n      element.NoOfMCQ = (_b = element.NoOfMCQ) !== null && _b !== void 0 ? _b : 0;\n      element.NoOfMatching = (_c = element.NoOfMatching) !== null && _c !== void 0 ? _c : 0;\n      element.NoOfTrueFalse = (_d = element.NoOfTrueFalse) !== null && _d !== void 0 ? _d : 0;\n      element.NoOfWriting = (_e = element.NoOfWriting) !== null && _e !== void 0 ? _e : 0;\n    });\n    const obj = {\n      Id: this.entryForm.value.id ? this.entryForm.value.id : null,\n      CourseId: this.entryForm.value.courseId,\n      ExamInstructions: this.entryForm.value.instructions ? this.entryForm.value.instructions.trim() : '',\n      Quota: Number(this.entryForm.value.quota),\n      Random: Number(this.entryForm.value.random),\n      Publish: Number(this.entryForm.value.publish),\n      Marks: Number(this.entryForm.value.marks),\n      // ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,\n      // ExamTrueFalseNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,\n      // ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,\n      // ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,\n      // ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,\n      MCQOnly: this.entryForm.value.mcqOnly,\n      SegmentWiseSetups: this.segmentWiseQuestions,\n      // MinPercentageForCertification: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,\n      // MinMarksForCertification: Number(this.entryForm.value.minMarks),\n      DurationMnt: Number(this.entryForm.value.duration),\n      SegmentId: this.mcqForm.value.segmentId,\n      MCQs: questions,\n      QuesType: 'MCQ',\n      StartDate: this.entryForm.value.startDate ? moment(this.entryForm.value.startDate).toISOString() : '',\n      EndDate: this.entryForm.value.endDate ? moment(this.entryForm.value.endDate).toISOString() : ''\n    };\n    console.log('obj', obj);\n    const formdata = new FormData();\n    formdata.append('Model', JSON.stringify(obj));\n\n    if (this.mcqFile) {\n      formdata.append('MCQFile', this.mcqFile);\n    }\n\n    this.blockUI.start('Saving data. Please wait...');\n\n    this._service.post('exam/certificate-test/create-or-update', formdata).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          timeOut: 2000\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, 'Success!', {\n        timeOut: 2000\n      });\n      this.entryForm.controls['id'].setValue(res.Data);\n      this.btnSaveText = 'Update';\n      this.id = res.Data;\n      this.isEdit = true;\n      this.getMCQList();\n      this.modalMCQHide();\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  loadMCQFile(files) {\n    if (files.length === 0) return;\n    this.mcqFile = files[0];\n  }\n\n  resetMCQFile(element) {\n    element.value = \"\";\n    this.mcqFile = null;\n  } // ============== True/False Question ====================\n\n\n  getTFQList() {\n    const obj = {\n      examId: this.id\n    };\n    this.loadingTFQ = true;\n\n    this._service.get('exam/certificate-test/get-true-false-list/' + this.id).subscribe(res => {\n      setTimeout(() => {\n        this.loadingTFQ = false;\n      }, 1000);\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.truFalseList = res.Data;\n    }, err => {\n      this.toastr.error(err.message || err, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n      setTimeout(() => {\n        this.loadingTFQ = false;\n      }, 1000);\n    });\n  }\n\n  openTFQModal(template) {\n    // if (this.truFalseList.length === 0) {\n    //   this.btnTFQSaveText = 'Save';\n    //   this.addNewTFQ();\n    // } else {\n    //   this.truFalseList.forEach(element => {\n    //     this.tfQuestionList.push({\n    //       Question: element.Question,\n    //       Answer: element.Answer,\n    //       CorrectAnswer: element.CorrectAnswer,\n    //       Mark: element.Mark\n    //     });\n    //   });\n    // }\n    this.btnTFQSaveText = 'Save';\n    this.addNewTFQ();\n    this.modalTFRef = this.modalService.show(template, this.modalConfig);\n  }\n\n  modalTFQHide() {\n    this.modalTFRef.hide();\n    this.tfQuestionList = [];\n    this.tfqFile = null;\n    this.tfqForm.reset();\n  }\n\n  deleteTFQ(index) {\n    this.tfQuestionList.splice(index, 1);\n  }\n\n  addNewTFQ() {\n    this.tfQuestionList.push({\n      Id: null,\n      Question: null,\n      Answer: true,\n      CorrectAnswer: null,\n      Mark: this.TrueFalseMark\n    });\n  }\n\n  onTFQFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid || this.tfqForm.invalid) {\n      return;\n    }\n\n    if (this.segmentWiseQuestions.length === 0) {\n      this.toastr.warning('Segment wise question is not set yet.', 'WARNING!', {\n        timeOut: 3000\n      });\n      return;\n    }\n\n    for (let i = 0; i < this.tfQuestionList.length; i++) {\n      const element = this.tfQuestionList[i];\n\n      if (!element.Question) {\n        this.toastr.error('Please Enter Question for Question: ' + ++i, 'WARNING!', {\n          timeOut: 4000\n        });\n        return false;\n      } // if (!element.Answer && !element.CorrectAnswer) {\n      //   this.toastr.error('Please enter correct answer for Question: ' + ++i, 'WARNING!', { timeOut: 4000 });\n      //   return false;\n      // }\n\n\n      if (!element.Mark) {\n        this.toastr.error('Please enter mark for Question: ' + ++i, 'WARNING!', {\n          timeOut: 4000\n        });\n        return false;\n      }\n    }\n\n    this.segmentWiseQuestions.forEach(element => {\n      var _a, _b, _c, _d, _e;\n\n      element.NoOfFIG = (_a = element.NoOfFIG) !== null && _a !== void 0 ? _a : 0;\n      element.NoOfMCQ = (_b = element.NoOfMCQ) !== null && _b !== void 0 ? _b : 0;\n      element.NoOfMatching = (_c = element.NoOfMatching) !== null && _c !== void 0 ? _c : 0;\n      element.NoOfTrueFalse = (_d = element.NoOfTrueFalse) !== null && _d !== void 0 ? _d : 0;\n      element.NoOfWriting = (_e = element.NoOfWriting) !== null && _e !== void 0 ? _e : 0;\n    });\n    const obj = {\n      Id: this.entryForm.value.id ? this.entryForm.value.id : null,\n      CourseId: this.entryForm.value.courseId,\n      ExamInstructions: this.entryForm.value.instructions ? this.entryForm.value.instructions.trim() : '',\n      Quota: Number(this.entryForm.value.quota),\n      Random: Number(this.entryForm.value.random),\n      Publish: Number(this.entryForm.value.publish),\n      Marks: Number(this.entryForm.value.marks),\n      // ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,\n      // ExamTrueFalseNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,\n      // ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,\n      // ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,\n      // ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,\n      MCQOnly: this.entryForm.value.mcqOnly,\n      SegmentWiseSetups: this.segmentWiseQuestions,\n      // MinPercentageForCertification: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,\n      // MinMarksForCertification: Number(this.entryForm.value.minMarks),\n      DurationMnt: Number(this.entryForm.value.duration),\n      SegmentId: this.tfqForm.value.segmentId,\n      TruFalseQs: this.tfQuestionList,\n      QuesType: 'TrueFalse',\n      StartDate: this.entryForm.value.startDate ? moment(this.entryForm.value.startDate).toISOString() : '',\n      EndDate: this.entryForm.value.endDate ? moment(this.entryForm.value.endDate).toISOString() : ''\n    };\n    const formdata = new FormData();\n    formdata.append('Model', JSON.stringify(obj));\n\n    if (this.tfqFile) {\n      formdata.append('TFQFile', this.tfqFile);\n    }\n\n    this.blockUI.start('Saving data. Please wait...');\n\n    this._service.post('exam/certificate-test/create-or-update', formdata).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, 'Success!', {\n        timeOut: 2000\n      });\n      this.entryForm.controls['id'].setValue(res.Data);\n      this.btnSaveText = 'Update';\n      this.id = res.Data;\n      this.isEdit = true;\n      this.getTFQList();\n      this.modalTFQHide();\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  downloadSampleTFQ() {\n    return this._service.downloadFile('exam/download-sample-question', {\n      quesType: 'TrueFalse'\n    }).subscribe(res => {\n      const url = window.URL.createObjectURL(res);\n      var link = document.createElement('a');\n      link.href = url;\n      link.download = \"certification_test_true_false_sample_file.xlsx\";\n      link.click();\n    }, error => {\n      this.toastr.error(error.message || error, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  loadTFQFile(files) {\n    if (files.length === 0) return;\n    this.tfqFile = files[0];\n  }\n\n  resetTFQFile(element) {\n    element.value = \"\";\n    this.tfqFile = null;\n  } // ============== Fill in the gaps Question ====================\n\n\n  geFIGList() {\n    const obj = {\n      examId: this.id\n    };\n    this.loadingFIG = true;\n\n    this._service.get('exam/certificate-test/get-fill-in-the-gap-list/' + this.id).subscribe(res => {\n      setTimeout(() => {\n        this.loadingFIG = false;\n      }, 1000);\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.figList = res.Data;\n    }, err => {\n      this.toastr.error(err.message || err, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n      setTimeout(() => {\n        this.loadingFIG = false;\n      }, 1000);\n    });\n  }\n\n  openFIGModal(template) {\n    // if (this.figList.length === 0) {\n    //   this.btnFIGSaveText = 'Save';\n    //   this.addNewFIG();\n    // } else {\n    //   this.figList.forEach(element => {\n    //     this.figQuestionList.push({\n    //       Question: element.Question,\n    //       Answer: element.Answer,\n    //       //Serial: element.Serial,\n    //       Mark: element.Mark\n    //     });\n    //   });\n    // }\n    this.btnFIGSaveText = 'Save';\n    this.addNewFIG();\n    this.modalFIGRef = this.modalService.show(template, this.modalConfig);\n  }\n\n  modalFIGHide() {\n    this.modalFIGRef.hide();\n    this.figQuestionList = [];\n    this.figqFile = null;\n    this.figqForm.reset();\n  }\n\n  deleteFIG(index) {\n    this.figQuestionList.splice(index, 1);\n  }\n\n  addNewFIG() {\n    this.figQuestionList.push({\n      Question: null,\n      Answer: null,\n      //Serial: null,\n      Mark: this.FIGMark\n    });\n  }\n\n  onFIGFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid || this.figqForm.invalid) {\n      return;\n    }\n\n    if (this.segmentWiseQuestions.length === 0) {\n      this.toastr.warning('Segment wise question is not set yet.', 'WARNING!', {\n        timeOut: 3000\n      });\n      return;\n    }\n\n    for (let i = 0; i < this.figQuestionList.length; i++) {\n      const element = this.figQuestionList[i];\n\n      if (!element.Question) {\n        this.toastr.error('Please Enter Question for Question: ' + ++i, 'WARNING!', {\n          timeOut: 4000\n        });\n        return false;\n      }\n\n      if (!element.Answer) {\n        this.toastr.error('Please enter answer for Question: ' + ++i, 'WARNING!', {\n          timeOut: 4000\n        });\n        return false;\n      }\n\n      if (!element.Mark) {\n        this.toastr.error('Please enter mark for Question: ' + ++i, 'WARNING!', {\n          timeOut: 4000\n        });\n        return false;\n      }\n    }\n\n    this.segmentWiseQuestions.forEach(element => {\n      var _a, _b, _c, _d, _e;\n\n      element.NoOfFIG = (_a = element.NoOfFIG) !== null && _a !== void 0 ? _a : 0;\n      element.NoOfMCQ = (_b = element.NoOfMCQ) !== null && _b !== void 0 ? _b : 0;\n      element.NoOfMatching = (_c = element.NoOfMatching) !== null && _c !== void 0 ? _c : 0;\n      element.NoOfTrueFalse = (_d = element.NoOfTrueFalse) !== null && _d !== void 0 ? _d : 0;\n      element.NoOfWriting = (_e = element.NoOfWriting) !== null && _e !== void 0 ? _e : 0;\n    });\n    const obj = {\n      Id: this.entryForm.value.id ? this.entryForm.value.id : null,\n      CourseId: this.entryForm.value.courseId,\n      ExamInstructions: this.entryForm.value.instructions ? this.entryForm.value.instructions.trim() : '',\n      Quota: Number(this.entryForm.value.quota),\n      Random: Number(this.entryForm.value.random),\n      Publish: Number(this.entryForm.value.publish),\n      Marks: Number(this.entryForm.value.marks),\n      // ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,\n      // ExamTrueFalseNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,\n      // ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,\n      // ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,\n      // ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,\n      MCQOnly: this.entryForm.value.mcqOnly,\n      SegmentWiseSetups: this.segmentWiseQuestions,\n      // MinPercentageForCertification: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,\n      // MinMarksForCertification: Number(this.entryForm.value.minMarks),\n      DurationMnt: Number(this.entryForm.value.duration),\n      SegmentId: this.figqForm.value.segmentId,\n      FIGQs: this.figQuestionList,\n      QuesType: 'FIG',\n      StartDate: this.entryForm.value.startDate ? moment(this.entryForm.value.startDate).toISOString() : '',\n      EndDate: this.entryForm.value.endDate ? moment(this.entryForm.value.endDate).toISOString() : ''\n    };\n    const formdata = new FormData();\n    formdata.append('Model', JSON.stringify(obj));\n\n    if (this.figqFile) {\n      formdata.append('FIGQFile', this.figqFile);\n    }\n\n    this.blockUI.start('Saving data. Please wait...');\n\n    this._service.post('exam/certificate-test/create-or-update', formdata).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, 'Success!', {\n        timeOut: 2000\n      });\n      this.entryForm.controls['id'].setValue(res.Data);\n      this.btnSaveText = 'Update';\n      this.id = res.Data;\n      this.isEdit = true;\n      this.geFIGList();\n      this.modalFIGHide();\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  downloadSampleFIGQ() {\n    return this._service.downloadFile('exam/download-sample-question', {\n      quesType: 'FIG'\n    }).subscribe(res => {\n      const url = window.URL.createObjectURL(res);\n      var link = document.createElement('a');\n      link.href = url;\n      link.download = \"certification_test_fill_in_the_gap_sample_file.xlsx\";\n      link.click();\n    }, error => {\n      this.toastr.error(error.message || error, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  loadFIGQFile(files) {\n    if (files.length === 0) return;\n    this.figqFile = files[0];\n  }\n\n  resetFIGQFile(element) {\n    element.value = \"\";\n    this.figqFile = null;\n  } // ============== Matching Question ====================\n\n\n  getMatchingList() {\n    const obj = {\n      examId: this.id\n    };\n    this.loadingMatching = true;\n\n    this._service.get('exam/certificate-test/get-matching-list/' + this.id).subscribe(res => {\n      setTimeout(() => {\n        this.loadingMatching = false;\n      }, 1000);\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.matchingList = res.Data;\n    }, err => {\n      this.toastr.error(err.message || err, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n      setTimeout(() => {\n        this.loadingMatching = false;\n      }, 1000);\n    });\n  }\n\n  openMatchingModal(template) {\n    // if (this.matchingList.length === 0) {\n    //   this.btnMatchingSaveText = 'Save';\n    //   this.addNewMatching();\n    // } else {\n    //   this.matchingList.forEach(element => {\n    //     this.matchingQuestionList.push({\n    //       LeftSide: element.LeftSide,\n    //       RightSide: element.RightSide,\n    //       Mark: element.Mark\n    //     });\n    //   });\n    // }\n    this.btnMatchingSaveText = 'Save';\n    this.addNewMatching();\n    this.modalMatchingRef = this.modalService.show(template, this.modalConfig);\n  }\n\n  modalMatchingHide() {\n    this.modalMatchingRef.hide();\n    this.matchingQuestionList = [];\n    this.matchingqFile = null;\n    this.mqForm.reset();\n  }\n\n  deleteMatching(index) {\n    this.matchingQuestionList.splice(index, 1);\n  }\n\n  addNewMatching() {\n    this.matchingQuestionList.push({\n      LeftSide: null,\n      RightSide: null,\n      Mark: this.MatchingMark\n    });\n  }\n\n  onMatchingFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid || this.mqForm.invalid) {\n      return;\n    }\n\n    if (this.segmentWiseQuestions.length === 0) {\n      this.toastr.warning('Segment wise question is not set yet.', 'WARNING!', {\n        timeOut: 3000\n      });\n      return;\n    }\n\n    for (let i = 0; i < this.matchingQuestionList.length; i++) {\n      const element = this.matchingQuestionList[i];\n\n      if (!element.LeftSide) {\n        this.toastr.error('Please Enter Question for Question: ' + ++i, 'WARNING!', {\n          timeOut: 4000\n        });\n        return false;\n      }\n\n      if (!element.RightSide) {\n        this.toastr.error('Please enter right hand side for left hand side: ' + ++i, 'WARNING!', {\n          timeOut: 4000\n        });\n        return false;\n      }\n\n      if (!element.Mark) {\n        this.toastr.error('Please enter mark for Question: ' + ++i, 'WARNING!', {\n          timeOut: 4000\n        });\n        return false;\n      }\n    }\n\n    this.segmentWiseQuestions.forEach(element => {\n      var _a, _b, _c, _d, _e;\n\n      element.NoOfFIG = (_a = element.NoOfFIG) !== null && _a !== void 0 ? _a : 0;\n      element.NoOfMCQ = (_b = element.NoOfMCQ) !== null && _b !== void 0 ? _b : 0;\n      element.NoOfMatching = (_c = element.NoOfMatching) !== null && _c !== void 0 ? _c : 0;\n      element.NoOfTrueFalse = (_d = element.NoOfTrueFalse) !== null && _d !== void 0 ? _d : 0;\n      element.NoOfWriting = (_e = element.NoOfWriting) !== null && _e !== void 0 ? _e : 0;\n    });\n    const obj = {\n      Id: this.entryForm.value.id ? this.entryForm.value.id : null,\n      CourseId: this.entryForm.value.courseId,\n      ExamInstructions: this.entryForm.value.instructions ? this.entryForm.value.instructions.trim() : '',\n      Quota: Number(this.entryForm.value.quota),\n      Random: Number(this.entryForm.value.random),\n      Publish: Number(this.entryForm.value.publish),\n      Marks: Number(this.entryForm.value.marks),\n      // ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,\n      // ExamTrueFalseNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,\n      // ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,\n      // ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,\n      // ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,\n      MCQOnly: this.entryForm.value.mcqOnly,\n      SegmentWiseSetups: this.segmentWiseQuestions,\n      // MinPercentageForCertification: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,\n      // MinMarksForCertification: Number(this.entryForm.value.minMarks),\n      DurationMnt: Number(this.entryForm.value.duration),\n      SegmentId: this.mqForm.value.segmentId,\n      MatchingQs: this.matchingQuestionList,\n      QuesType: 'Matching',\n      StartDate: this.entryForm.value.startDate ? moment(this.entryForm.value.startDate).toISOString() : '',\n      EndDate: this.entryForm.value.endDate ? moment(this.entryForm.value.endDate).toISOString() : ''\n    };\n    const formdata = new FormData();\n    formdata.append('Model', JSON.stringify(obj));\n\n    if (this.matchingqFile) {\n      formdata.append('MatchingQFile', this.matchingqFile);\n    }\n\n    this.blockUI.start('Saving data. Please wait...');\n\n    this._service.post('exam/certificate-test/create-or-update', formdata).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, 'Success!', {\n        timeOut: 2000\n      });\n      this.entryForm.controls['id'].setValue(res.Data);\n      this.btnSaveText = 'Update';\n      this.id = res.Data;\n      this.isEdit = true;\n      this.getMatchingList();\n      this.modalMatchingHide();\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  downloadSampleMatchingQ() {\n    return this._service.downloadFile('exam/download-sample-question', {\n      quesType: 'Matching'\n    }).subscribe(res => {\n      const url = window.URL.createObjectURL(res);\n      var link = document.createElement('a');\n      link.href = url;\n      link.download = \"certification_test_left_right_matching_sample_file.xlsx\";\n      link.click();\n    }, error => {\n      this.toastr.error(error.message || error, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  loadMatchingQFile(files) {\n    if (files.length === 0) return;\n    this.matchingqFile = files[0];\n  }\n\n  resetMatchingQFile(element) {\n    element.value = \"\";\n    this.matchingqFile = null;\n  } // ============== Written Question ====================\n\n\n  geWrittenList() {\n    this.loadingWritten = true;\n\n    this._service.get('exam/certificate-test/get-written-list/' + this.id).subscribe(res => {\n      setTimeout(() => {\n        this.loadingWritten = false;\n      }, 1000);\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.writtenList = res.Data;\n    }, err => {\n      this.toastr.error(err.message || err, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n      setTimeout(() => {\n        this.loadingWritten = false;\n      }, 1000);\n    });\n  }\n\n  openWrittenModal(template) {\n    // if (this.writtenList.length === 0) {\n    //   this.btnWrittenSaveText = 'Save';\n    //   this.addNewWritten();\n    // } else {\n    //   this.writtenList.forEach(element => {\n    //     this.writtenQuestionList.push({\n    //       Question: element.Question,\n    //       Mark: element.Mark\n    //     });\n    //   });\n    // }\n    this.btnWrittenSaveText = 'Save';\n    this.addNewWritten();\n    this.modalWrittenRef = this.modalService.show(template, this.modalConfig);\n  }\n\n  modalWrittenHide() {\n    this.modalWrittenRef.hide();\n    this.writtenQuestionList = [];\n    this.wqFile = null;\n    this.wqForm.reset();\n  }\n\n  deleteWritten(index) {\n    this.writtenQuestionList.splice(index, 1);\n  }\n\n  addNewWritten() {\n    this.writtenQuestionList.push({\n      Question: null,\n      Mark: this.WrittenMark\n    });\n  }\n\n  onWrittenFormSubmit() {\n    this.submitted = true;\n\n    if (this.entryForm.invalid || this.wqForm.invalid) {\n      return;\n    }\n\n    if (this.segmentWiseQuestions.length === 0) {\n      this.toastr.warning('Segment wise question is not set yet.', 'WARNING!', {\n        timeOut: 3000\n      });\n      return;\n    }\n\n    for (let i = 0; i < this.writtenQuestionList.length; i++) {\n      const element = this.writtenQuestionList[i];\n\n      if (!element.Question) {\n        this.toastr.error('Please Enter Question for Question: ' + ++i, 'WARNING!', {\n          timeOut: 4000\n        });\n        return false;\n      }\n\n      if (!element.Mark) {\n        this.toastr.error('Please enter mark for Question: ' + ++i, 'WARNING!', {\n          timeOut: 4000\n        });\n        return false;\n      }\n    }\n\n    this.segmentWiseQuestions.forEach(element => {\n      var _a, _b, _c, _d, _e;\n\n      element.NoOfFIG = (_a = element.NoOfFIG) !== null && _a !== void 0 ? _a : 0;\n      element.NoOfMCQ = (_b = element.NoOfMCQ) !== null && _b !== void 0 ? _b : 0;\n      element.NoOfMatching = (_c = element.NoOfMatching) !== null && _c !== void 0 ? _c : 0;\n      element.NoOfTrueFalse = (_d = element.NoOfTrueFalse) !== null && _d !== void 0 ? _d : 0;\n      element.NoOfWriting = (_e = element.NoOfWriting) !== null && _e !== void 0 ? _e : 0;\n    });\n    const obj = {\n      Id: this.entryForm.value.id ? this.entryForm.value.id : null,\n      CourseId: this.entryForm.value.courseId,\n      ExamInstructions: this.entryForm.value.instructions ? this.entryForm.value.instructions.trim() : '',\n      Quota: Number(this.entryForm.value.quota),\n      Random: Number(this.entryForm.value.random),\n      Publish: Number(this.entryForm.value.publish),\n      Marks: Number(this.entryForm.value.marks),\n      // ExamMCQNo: this.entryForm.value.no_of_mcq ? Number(this.entryForm.value.no_of_mcq) : 0,\n      // ExamTrueFalseNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_tfq ? Number(this.entryForm.value.no_of_tfq) : 0,\n      // ExamFIGNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_figq ? Number(this.entryForm.value.no_of_figq) : 0,\n      // ExamMatchingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_mq ? Number(this.entryForm.value.no_of_mq) : 0,\n      // ExamWritingNo: !this.entryForm.value.mcqOnly && this.entryForm.value.no_of_wq ? Number(this.entryForm.value.no_of_wq) : 0,\n      MCQOnly: this.entryForm.value.mcqOnly,\n      SegmentWiseSetups: this.segmentWiseQuestions,\n      // MinPercentageForCertification: this.entryForm.value.minPercentage ? Number(this.entryForm.value.minPercentage) : 0,\n      // MinMarksForCertification: Number(this.entryForm.value.minMarks),\n      DurationMnt: Number(this.entryForm.value.duration),\n      SegmentId: this.wqForm.value.segmentId,\n      WrittenQs: this.writtenQuestionList,\n      QuesType: 'Written',\n      StartDate: this.entryForm.value.startDate ? moment(this.entryForm.value.startDate).toISOString() : '',\n      EndDate: this.entryForm.value.endDate ? moment(this.entryForm.value.endDate).toISOString() : ''\n    };\n    const formdata = new FormData();\n    formdata.append('Model', JSON.stringify(obj));\n\n    if (this.wqFile) {\n      formdata.append('WQFile', this.wqFile);\n    }\n\n    this.blockUI.start('Saving data. Please wait...');\n\n    this._service.post('exam/certificate-test/create-or-update', formdata).subscribe(res => {\n      this.blockUI.stop();\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.toastr.success(res.Message, 'Success!', {\n        timeOut: 2000\n      });\n      this.entryForm.controls['id'].setValue(res.Data);\n      this.btnSaveText = 'Update';\n      this.id = res.Data;\n      this.isEdit = true;\n      this.geWrittenList();\n      this.modalWrittenHide();\n    }, err => {\n      this.blockUI.stop();\n      this.toastr.error(err.message || err, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  downloadSampleWQ() {\n    return this._service.downloadFile('exam/download-sample-question', {\n      quesType: 'Written'\n    }).subscribe(res => {\n      const url = window.URL.createObjectURL(res);\n      var link = document.createElement('a');\n      link.href = url;\n      link.download = \"certification_test_written_question_sample_file.xlsx\";\n      link.click();\n    }, error => {\n      this.toastr.error(error.message || error, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n    });\n  }\n\n  loadWQFile(files) {\n    if (files.length === 0) return;\n    this.wqFile = files[0];\n  }\n\n  resetWQFile(element) {\n    element.value = \"\";\n    this.wqFile = null;\n  }\n\n  modalHide() {\n    this.modalRef.hide();\n    this.submitted = false;\n  }\n\n  openModal(template) {\n    // this.entryForm.controls['isActive'].setValue(true);\n    this.modalRef = this.modalService.show(template, this.modalConfig);\n  }\n\n  OnCourseChange(event) {\n    this.ExamExistsMsg = \"\";\n    this.loadingWritten = true;\n\n    this._service.get('course/has-certificate-exam/' + event.Id).subscribe(res => {\n      setTimeout(() => {\n        this.loadingWritten = false;\n      }, 1000);\n\n      if (res.Status === ResponseStatus.Warning) {\n        this.toastr.warning(res.Message, 'Warning!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      } else if (res.Status === ResponseStatus.Error) {\n        this.toastr.error(res.Message, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n        return;\n      }\n\n      this.ExamExists = res.Data;\n      if (this.ExamExists) this.ExamExistsMsg = \"There is already an exam for this course. You cannot add more. Try another course.\";\n    }, err => {\n      this.toastr.error(err.message || err, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      });\n      setTimeout(() => {\n        this.loadingWritten = false;\n      }, 1000);\n    });\n  }\n\n  getSegmentList() {\n    this._service.get('course-segment/dropdown-list').subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.segmentList = res.Data;\n      },\n      error: err => this.toastr.error(err.message || err, 'Error!', {\n        closeButton: true,\n        disableTimeOut: false,\n        enableHtml: true\n      }),\n      complete: () => {}\n    });\n  } // =================== Segment Wise Question Setup =======================\n\n\n  onChangeSegment(segment, item, idx) {\n    this.segmentWiseQuestionsInput.forEach((element, i) => {\n      if (i !== idx && element.SegmentId === segment.Id) {\n        this.toastr.warning('Already exist', 'WARNING!');\n        setTimeout(() => {\n          item.SegmentId = null;\n        }, 200);\n        return false;\n      }\n    });\n    item.SegmentName = segment ? segment.Name : null;\n  }\n\n  openQuestionSetupModal(template) {\n    this.segmentWiseQuestions.forEach(element => {\n      this.segmentWiseQuestionsInput.push(element);\n    });\n    if (this.segmentWiseQuestionsInput.length === 0) this.addRowIntoQsSetupTable();\n    this.modalRef = this.modalService.show(template, {\n      class: 'gray modal-xl',\n      backdrop: 'static'\n    });\n  }\n\n  modalQsSetupHide() {\n    this.segmentWiseQuestionsInput = [];\n    this.modalRef.hide();\n  }\n\n  addRowIntoQsSetupTable() {\n    this.segmentWiseQuestionsInput.push({\n      SegmentId: null,\n      SegmentName: null,\n      NoOfMCQ: null,\n      NoOfTrueFalse: null,\n      NoOfFIG: null,\n      NoOfMatching: null,\n      NoOfWriting: null\n    });\n  }\n\n  deleteRowFromQsSetupTable(idx) {\n    this.segmentWiseQuestionsInput.splice(idx, 1);\n  }\n\n  saveSegmentWiseQuestionSetup() {\n    this.segmentWiseQuestionsInput.forEach((element, idx) => {\n      if (!element.SegmentId || !element.NoOfMCQ && !element.NoOfTrueFalse && !element.NoOfFIG && !element.NoOfMatching && !element.NoOfWriting) {\n        this.toastr.warning('Invalid data in row: ' + (idx + 1), 'WARNING!!');\n        return false;\n      }\n    });\n    this.segmentWiseQuestions = this.segmentWiseQuestionsInput;\n    this.totalQuestions = {\n      mcq: this.segmentWiseQuestions.map(o => o.NoOfMCQ ? Number(o.NoOfMCQ) : 0).reduce((a, c) => {\n        return a + c;\n      }),\n      tfq: this.segmentWiseQuestions.map(o => o.NoOfTrueFalse ? Number(o.NoOfTrueFalse) : 0).reduce((a, c) => {\n        return a + c;\n      }),\n      figq: this.segmentWiseQuestions.map(o => o.NoOfFIG ? Number(o.NoOfFIG) : 0).reduce((a, c) => {\n        return a + c;\n      }),\n      matchingq: this.segmentWiseQuestions.map(o => o.NoOfMatching ? Number(o.NoOfMatching) : 0).reduce((a, c) => {\n        return a + c;\n      }),\n      writtenq: this.segmentWiseQuestions.map(o => o.NoOfWriting ? Number(o.NoOfWriting) : 0).reduce((a, c) => {\n        return a + c;\n      })\n    };\n    this.modalQsSetupHide();\n  } // =================== End of Segment Wise Question Setup =======================\n  // =================== MCQ Edit =======================\n\n\n  addOptions(preset) {\n    return [{\n      Text: preset ? preset.Option1 : null,\n      Selected: preset && preset.Answers.indexOf('1') !== -1\n    }, {\n      Text: preset ? preset.Option2 : null,\n      Selected: preset && preset.Answers.indexOf('2') !== -1\n    }, {\n      Text: preset ? preset.Option3 : null,\n      Selected: preset && preset.Answers.indexOf('3') !== -1\n    }, {\n      Text: preset ? preset.Option4 : null,\n      Selected: preset && preset.Answers.indexOf('4') !== -1\n    }];\n  }\n\n  editMCQ(item, template) {\n    console.log(item);\n    this.mcqEditForm.controls['id'].setValue(item.Id);\n    this.mcqEditForm.controls['question'].setValue(item.Question);\n    this.mcqEditForm.controls['segmentId'].setValue(item.SegmentId);\n    this.mcqEditForm.controls['mark'].setValue(item.Mark);\n    this.mcqEditForm.controls['options'].setValue(this.addOptions(item));\n    this.modalRef = this.modalService.show(template, {\n      class: 'gray modal-lg',\n      backdrop: 'static'\n    });\n  }\n\n  modalEditMCQHide() {\n    this.mcqEditForm.reset({\n      options: this.addOptions()\n    });\n    this.modalRef.hide();\n    this.editsubmitted = false;\n  }\n\n  onMCQUpdate() {\n    this.editsubmitted = true;\n    if (this.mcqEditForm.invalid) return;\n\n    if (this.mcqEditForm.value.options.filter(x => x.Text === null || x.Text === '').length > 0) {\n      this.toastr.error('Value missing for an option', 'WARNING!', {\n        timeOut: 4000\n      });\n      return false;\n    }\n\n    if (this.mcqEditForm.value.options.filter(x => x.Selected).length === 0) {\n      this.toastr.error('No option has been selected for answer', 'WARNING!', {\n        timeOut: 4000\n      });\n      return false;\n    }\n\n    const obj = {\n      Id: this.mcqEditForm.value.id,\n      SegmentId: this.mcqEditForm.value.segmentId,\n      Question: this.mcqEditForm.value.question,\n      Mark: this.mcqEditForm.value.mark,\n      Option1: this.mcqEditForm.value.options[0].Text,\n      Option2: this.mcqEditForm.value.options[1].Text,\n      Option3: this.mcqEditForm.value.options[2].Text,\n      Option4: this.mcqEditForm.value.options[3].Text,\n      Answers: this.mcqEditForm.value.options.map(function (x, i) {\n        if (x.Selected) return i + 1;else return -1;\n      }).filter(x => x >= 0).join()\n    };\n    this.blockUI.start('Updating data. Please wait...');\n\n    this._service.post('exam/certificate-test/mcq/update', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.toastr.success(res.Message, 'Success!', {\n          timeOut: 2000\n        });\n        this.modalEditMCQHide();\n        this.getMCQList();\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.error(err.message || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => this.blockUI.stop()\n    });\n  } // =================== End of MCQ Edit =======================\n  // =================== True/False Edit =======================\n\n\n  editTrueFalse(item, template) {\n    console.log(item);\n    this.tfqEditForm.controls['id'].setValue(item.Id);\n    this.tfqEditForm.controls['question'].setValue(item.Question);\n    this.tfqEditForm.controls['segmentId'].setValue(item.SegmentId);\n    this.tfqEditForm.controls['mark'].setValue(item.Mark);\n    this.tfqEditForm.controls['answer'].setValue(item.Answer);\n    this.modalRef = this.modalService.show(template, {\n      class: 'gray modal-lg',\n      backdrop: 'static'\n    });\n  }\n\n  modalEditTrueFalseHide() {\n    this.tfqEditForm.reset({\n      answer: false\n    });\n    this.modalRef.hide();\n    this.editsubmitted = false;\n  }\n\n  onTrueFalseUpdate() {\n    this.editsubmitted = true;\n    if (this.tfqEditForm.invalid) return;\n    const obj = {\n      Id: this.tfqEditForm.value.id,\n      SegmentId: this.tfqEditForm.value.segmentId,\n      Question: this.tfqEditForm.value.question,\n      Mark: this.tfqEditForm.value.mark,\n      Answer: this.tfqEditForm.value.answer\n    };\n    this.blockUI.start('Updating data. Please wait...');\n\n    this._service.post('exam/certificate-test/true-false/update', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.toastr.success(res.Message, 'Success!', {\n          timeOut: 2000\n        });\n        this.modalEditTrueFalseHide();\n        this.getTFQList();\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.error(err.message || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => this.blockUI.stop()\n    });\n  } // =================== End of True/False Edit =======================\n  // =================== Fill in the gap Edit =======================\n\n\n  editFIG(item, template) {\n    console.log(item);\n    this.figqEditForm.controls['id'].setValue(item.Id);\n    this.figqEditForm.controls['question'].setValue(item.Question);\n    this.figqEditForm.controls['segmentId'].setValue(item.SegmentId);\n    this.figqEditForm.controls['mark'].setValue(item.Mark);\n    this.figqEditForm.controls['answer'].setValue(item.Answer);\n    this.modalRef = this.modalService.show(template, {\n      class: 'gray modal-lg',\n      backdrop: 'static'\n    });\n  }\n\n  modalEditFIGHide() {\n    this.figqEditForm.reset({\n      answer: false\n    });\n    this.modalRef.hide();\n    this.editsubmitted = false;\n  }\n\n  onFIGUpdate() {\n    this.editsubmitted = true;\n    if (this.figqEditForm.invalid) return;\n    const obj = {\n      Id: this.figqEditForm.value.id,\n      SegmentId: this.figqEditForm.value.segmentId,\n      Question: this.figqEditForm.value.question,\n      Mark: this.figqEditForm.value.mark,\n      Answer: this.figqEditForm.value.answer\n    };\n    this.blockUI.start('Updating data. Please wait...');\n\n    this._service.post('exam/certificate-test/fill-in-the-gap/update', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.toastr.success(res.Message, 'Success!', {\n          timeOut: 2000\n        });\n        this.modalEditFIGHide();\n        this.geFIGList();\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.error(err.message || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => this.blockUI.stop()\n    });\n  } // =================== End of Fill in the gap Edit =======================\n  // =================== Matching Edit =======================\n\n\n  editMatching(item, template) {\n    console.log(item);\n    this.mqEditForm.controls['id'].setValue(item.Id);\n    this.mqEditForm.controls['leftSide'].setValue(item.LeftSide);\n    this.mqEditForm.controls['segmentId'].setValue(item.SegmentId);\n    this.mqEditForm.controls['mark'].setValue(item.Mark);\n    this.mqEditForm.controls['rightSide'].setValue(item.RightSide);\n    this.modalRef = this.modalService.show(template, {\n      class: 'gray modal-lg',\n      backdrop: 'static'\n    });\n  }\n\n  modalEditMatchingHide() {\n    this.mqEditForm.reset({\n      answer: false\n    });\n    this.modalRef.hide();\n    this.editsubmitted = false;\n  }\n\n  onMatchingUpdate() {\n    this.editsubmitted = true;\n    if (this.mqEditForm.invalid) return;\n    const obj = {\n      Id: this.mqEditForm.value.id,\n      SegmentId: this.mqEditForm.value.segmentId,\n      LeftSide: this.mqEditForm.value.leftSide,\n      Mark: this.mqEditForm.value.mark,\n      RightSide: this.mqEditForm.value.rightSide\n    };\n    this.blockUI.start('Updating data. Please wait...');\n\n    this._service.post('exam/certificate-test/matching/update', obj).subscribe({\n      next: res => {\n        if (res.Status === ResponseStatus.Warning) {\n          this.toastr.warning(res.Message, 'Warning!', {\n            timeOut: 2000\n          });\n          return;\n        } else if (res.Status === ResponseStatus.Error) {\n          this.toastr.error(res.Message, 'Error!', {\n            closeButton: true,\n            disableTimeOut: false,\n            enableHtml: true\n          });\n          return;\n        }\n\n        this.toastr.success(res.Message, 'Success!', {\n          timeOut: 2000\n        });\n        this.modalEditMatchingHide();\n        this.getMatchingList();\n      },\n      error: err => {\n        this.blockUI.stop();\n        this.toastr.error(err.message || err, 'Error!', {\n          closeButton: true,\n          disableTimeOut: false,\n          enableHtml: true\n        });\n      },\n      complete: () => this.blockUI.stop()\n    });\n  }\n\n}\n\nCertificationTestEntryComponent.ɵfac = function CertificationTestEntryComponent_Factory(t) {\n  return new (t || CertificationTestEntryComponent)(i0.ɵɵdirectiveInject(i1.AppComponent), i0.ɵɵdirectiveInject(i2.BsModalService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.CommonService), i0.ɵɵdirectiveInject(i5.ToastrService), i0.ɵɵdirectiveInject(i6.ActivatedRoute), i0.ɵɵdirectiveInject(i7.NgbTimepickerConfig));\n};\n\nCertificationTestEntryComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: CertificationTestEntryComponent,\n  selectors: [[\"app-certification-test-entry\"]],\n  decls: 184,\n  vars: 145,\n  consts: [[1, \"row\"], [1, \"col-lg-12\"], [1, \"col-sm-12\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\"], [1, \"card-body\"], [\"autocomplete\", \"off\", 1, \"custom-inline-form\", 3, \"formGroup\"], [1, \"col-lg-8\", \"col-md-7\", \"col-12\"], [1, \"mb-3\", \"col-lg-8\", \"col-md-8\", \"col-12\"], [1, \"col-form-label\", \"col-form-label-sm\", \"required\"], [\"formControlName\", \"courseId\", \"bindLabel\", \"Title\", \"bindValue\", \"Id\", \"placeholder\", \"Select\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"ngClass\", \"clearable\", \"clearOnBackspace\", \"items\", \"click\", \"change\"], [\"selectElement\", \"\"], [\"class\", \"ctec-style-1\", 4, \"ngIf\"], [\"class\", \"error-text\", 4, \"ngIf\"], [\"class\", \"mb-3 col-lg-4 col-md-4 col-12\", 4, \"ngIf\"], [\"class\", \"mb-3 col-md-5 col-12\", 4, \"ngIf\"], [\"class\", \"mb-3 col-md-2 col-12\", 4, \"ngIf\"], [1, \"col-lg-4\", \"col-md-5\", \"col-12\", \"table-responsive\", \"custom-scrollbar\"], [1, \"table\", \"table-sm\", \"table-light\", \"table-striped\", \"table-hover\"], [\"scope\", \"col\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"class\", \"mb-3 col-lg-9 col-12\", 4, \"ngIf\"], [\"class\", \"mb-3 col-lg-3 col-12\", 4, \"ngIf\"], [1, \"card\", \"card-border-info\"], [1, \"col-sm-7\", \"col-12\"], [1, \"col-sm-3\", \"col-12\"], [\"type\", \"button\", \"class\", \"btn btn-outline-secondary btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"col-sm-2 col-12\", 4, \"ngIf\"], [\"rowHeight\", \"auto\", 1, \"material\", 3, \"rows\", \"loadingIndicator\", \"columnMode\", \"headerHeight\", \"footerHeight\", \"limit\"], [\"name\", \"Segment\", \"prop\", \"Segment\", 3, \"width\", \"draggable\", \"sortable\"], [\"ngx-datatable-cell-template\", \"\"], [\"name\", \"Question\", \"prop\", \"Question\", 3, \"width\", \"draggable\", \"sortable\"], [\"name\", \"Option 1\", \"prop\", \"Option1\", 3, \"width\", \"draggable\", \"sortable\"], [\"name\", \"Option 2\", \"prop\", \"Option2\", 3, \"width\", \"draggable\", \"sortable\"], [\"name\", \"Option 3\", \"prop\", \"Option3\", 3, \"width\", \"draggable\", \"sortable\"], [\"name\", \"Option 4\", \"prop\", \"Option4\", 3, \"width\", \"draggable\", \"sortable\"], [\"prop\", \"Answers\", \"headerClass\", \"text-center\", \"cellClass\", \"text-center\", 3, \"width\", \"draggable\", \"sortable\"], [\"prop\", \"Mark\", \"name\", \"Mark\", \"headerClass\", \"text-center\", \"cellClass\", \"text-center\", 3, \"width\", \"draggable\", \"sortable\"], [\"name\", \"Action\", \"prop\", \"Id\", \"headerClass\", \"text-center\", \"cellClass\", \"text-center\", 3, \"maxWidth\", \"draggable\", \"sortable\"], [1, \"card\", \"card-border-warning\"], [1, \"col-sm-2\", \"col-12\"], [\"class\", \"col-sm-3 col-12\", 4, \"ngIf\"], [\"name\", \"Answer\", \"prop\", \"Answer\", \"headerClass\", \"text-center\", \"cellClass\", \"text-center\", 3, \"width\", \"draggable\", \"sortable\"], [1, \"card\", \"card-border-danger\"], [1, \"card\", \"card-border-primary\"], [\"name\", \"Left Side\", \"prop\", \"LeftSide\", 3, \"width\", \"draggable\", \"sortable\"], [\"name\", \"Right Side\", \"prop\", \"RightSide\", 3, \"width\", \"draggable\", \"sortable\"], [1, \"card\", \"card-border-inverse\"], [\"name\", \"Question\", \"prop\", \"Question\", 3, \"draggable\", \"sortable\"], [\"templateMCQ\", \"\"], [\"templateMCQEdit\", \"\"], [\"templateTrueFalse\", \"\"], [\"templateTrueFalseEdit\", \"\"], [\"templateFIG\", \"\"], [\"templateFIGEdit\", \"\"], [\"templateMatching\", \"\"], [\"templateMatchingEdit\", \"\"], [\"templateWritten\", \"\"], [\"templateQuestionSetup\", \"\"], [1, \"ctec-style-1\"], [1, \"error-text\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"], [1, \"mb-3\", \"col-lg-4\", \"col-md-4\", \"col-12\"], [\"type\", \"text\", \"formControlName\", \"duration\", \"numeric\", \"\", \"numericType\", \"number\", \"type\", \"text\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [1, \"mb-3\", \"col-md-5\", \"col-12\"], [1, \"col-form-label\", \"col-form-label-sm\"], [\"placeholder\", \"Set Start Date & Time\", \"type\", \"datetime-local\", \"formControlName\", \"startDate\", 1, \"form-control\", \"form-control-sm\"], [\"placeholder\", \"Set End Date & Time\", \"type\", \"datetime-local\", \"formControlName\", \"endDate\", 1, \"form-control\", \"form-control-sm\"], [1, \"mb-3\", \"col-md-2\", \"col-12\"], [\"type\", \"text\", \"formControlName\", \"quota\", \"numeric\", \"\", \"numericType\", \"number\", \"type\", \"text\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"formControlName\", \"random\", \"id\", \"random\", \"type\", \"checkbox\", 1, \"form-check-input\"], [\"for\", \"random\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"formControlName\", \"publish\", \"id\", \"publish\", \"type\", \"checkbox\", 1, \"form-check-input\"], [\"for\", \"publish\", 1, \"form-check-label\"], [\"type\", \"button\", 1, \"btn\", \"btn-link\", \"btn-sm\", \"text-underline\", 3, \"click\"], [\"colspan\", \"2\", 1, \"text-wrap\", \"text-break\", \"text-end\"], [\"scope\", \"col\", 1, \"text-wrap\", \"text-break\"], [1, \"mb-3\", \"col-lg-9\", \"col-12\"], [1, \"NgxEditor__Wrapper\"], [3, \"editor\", \"toolbar\"], [\"formControlName\", \"instructions\", 3, \"editor\"], [1, \"mb-3\", \"col-lg-3\", \"col-12\"], [\"class\", \"btn btn-theme btn-sm me-2\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"me-2\", 3, \"routerLink\"], [1, \"feather\", \"icon-arrow-left\"], [1, \"btn\", \"btn-theme\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"feather\", \"icon-save\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"feather\", \"icon-file-text\"], [\"type\", \"button\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"float-end\", 3, \"click\"], [1, \"feather\", \"icon-plus\"], [3, \"title\"], [1, \"btn\", \"btn-outline-danger\", \"btn-mini\", 3, \"click\"], [1, \"feather\", \"icon-trash\"], [1, \"btn\", \"btn-primary\", \"btn-mini\", \"ms-2\", 3, \"click\"], [1, \"feather\", \"icon-edit\"], [1, \"feather\", \"icon-file\"], [\"type\", \"button\", 1, \"btn\", \"btn-warning\", \"btn-sm\", \"btn-block\", \"float-end\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-block\", \"float-end\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-theme\", \"btn-sm\", \"btn-block\", \"float-end\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", \"btn-testz\", \"btn-sm\", \"btn-block\", \"float-end\", 3, \"click\"], [1, \"btn\", \"btn-danger\", \"btn-mini\", 3, \"click\"], [1, \"feather\", \"icon-ui-delete\"], [1, \"modal-header\"], [\"id\", \"modalTitle\", 1, \"modal-title\", \"float-start\", 3, \"innerHtml\"], [\"type\", \"button \", \"aria-label\", \"Close \", 1, \"close\", \"float-end\", \"btn\", \"btn-mini\", \"btn-danger\", \"btn\", \"btn-mini\", \"btn-outline-danger\", 3, \"click\"], [1, \"feather\", \"icon-x\"], [1, \"modal-body\"], [\"autocomplete\", \"off\", 3, \"formGroup\"], [\"for\", \"segmentId\", 1, \"col-sm-2\", \"col-12\", \"col-form-label\", \"col-form-label-sm\", \"required\"], [1, \"col-sm-6\", \"col-12\"], [\"formControlName\", \"segmentId\", \"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Select Segment\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"items\", \"ngClass\"], [1, \"card-body\", \"p-0\"], [1, \"card\", \"card-border-info\", \"mb-1\"], [1, \"row\", \"mt-2\"], [1, \"col-lg-5\", \"col-12\"], [\"accept\", \".xls,.xlsx\", \"type\", \"file\", 1, \"form-control\", \"form-control-sm\", 3, \"change\"], [\"mcqFileInput\", \"\"], [1, \"col-lg-3\", \"col-12\"], [\"type\", \"file\", \"class\", \"btn btn-warning btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"col-lg-4\", \"col-12\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"float-end\", \"me-2\", 3, \"click\"], [1, \"feather\", \"icon-download\"], [1, \"card\", \"card-border-primary\", \"mb-1\"], [\"class\", \"row\", 4, \"ngFor\", \"ngForOf\"], [1, \"modal-footer\", \"pt-0\"], [1, \"pe-4\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"me-2\", 3, \"click\"], [\"type\", \"file\", 1, \"btn\", \"btn-warning\", \"btn-sm\", 3, \"click\"], [1, \"col-12\"], [1, \"input-group\", \"input-group-sm\"], [1, \"input-group-text\", \"fw-bold\"], [\"rows\", \"2\", \"cols\", \"5\", \"placeholder\", \"Enter Question\", 1, \"form-control\", \"form-control-sm\", \"ctec-color-blac\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [1, \"input-group-text\", 3, \"click\"], [1, \"feather\", \"icon-plus\", \"text-success\"], [1, \"feather\", \"icon-x\", \"text-danger\"], [\"class\", \"col-sm-6 col-12\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"mt-2\", \"mb-3\"], [1, \"input-group-text\"], [\"type\", \"text\", \"numeric\", \"\", \"numericType\", \"decimal\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", 1, \"form-control\", \"form-control-sm\", 3, \"placeholder\", \"ngModelOptions\", \"ngModel\", \"ngModelChange\"], [\"type\", \"checkbox\", 3, \"checked\", \"disabled\", \"ngModel\", \"ngModelChange\"], [1, \"modal-title\", \"float-start\"], [\"rows\", \"2\", \"cols\", \"5\", \"placeholder\", \"Enter Question\", \"formControlName\", \"question\", 1, \"form-control\", \"form-control-sm\", \"ctec-color-blac\"], [1, \"col-lg-6\", \"col-md-6\", \"col-12\", \"mt-2\", \"mb-3\"], [\"type\", \"text\", \"numeric\", \"\", \"numericType\", \"decimal\", \"formControlName\", \"mark\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"segmentId\", 1, \"input-group-text\"], [\"type\", \"checkbox\", 3, \"checked\", \"disabled\", \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [1, \"modal-body\", \"pb-0\"], [\"tfqFileInput\", \"\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"float-end\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"row\", \"mb-2\"], [\"size\", \"small\", \"defaultBgColor\", \"#E82D4C\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-lg-2\", \"col-md-3\", \"col-12\", \"mt-2\", \"mb-3\"], [\"labelOff\", \"False\", \"labelOn\", \"True\", \"size\", \"small\", \"defaultBgColor\", \"#E82D4C\", \"formControlName\", \"answer\"], [1, \"col-lg-4\", \"col-md-4\", \"col-12\", \"mt-2\", \"mb-3\"], [1, \"modal-title\", \"float-start\", 3, \"innerHtml\"], [\"figqFileInput\", \"\"], [\"type\", \"text\", \"placeholder\", \"Enter answer\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"Enter answer\", \"formControlName\", \"answer\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-lg-3\", \"col-md-3\", \"col-12\", \"mt-2\", \"mb-3\"], [1, \"col-lg-5\", \"col-md-5\", \"col-12\", \"mt-2\", \"mb-3\"], [\"matchingqFileInput\", \"\"], [1, \"modal-footer\"], [\"rows\", \"2\", \"cols\", \"5\", \"placeholder\", \"Enter left hand side\", 1, \"form-control\", \"form-control-sm\", \"ctec-color-blac\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [1, \"col-sm-9\", \"col-12\"], [\"rows\", \"2\", \"cols\", \"5\", \"placeholder\", \"Enter right hand side\", 1, \"form-control\", \"form-control-sm\", \"ctec-color-blac\", 3, \"ngModel\", \"ngModelChange\"], [\"rows\", \"2\", \"cols\", \"5\", \"placeholder\", \"Enter left hand side\", \"formControlName\", \"leftSide\", 1, \"form-control\", \"form-control-sm\", \"ctec-color-blac\"], [\"rows\", \"2\", \"cols\", \"5\", \"placeholder\", \"Enter right hand side\", \"formControlName\", \"rightSide\", 1, \"form-control\", \"form-control-sm\", \"ctec-color-blac\"], [1, \"col-lg-2\", \"col-md-2\", \"col-12\", \"mt-2\", \"mb-3\"], [\"wqFileInput\", \"\"], [1, \"table\", \"table-light\", \"table-striped\", \"table-hover\"], [\"scope\", \"col\", 1, \"text-center\"], [1, \"feather\", \"icon-check\"], [1, \"min-width-300\"], [\"bindLabel\", \"Name\", \"bindValue\", \"Id\", \"placeholder\", \"Select Segment\", 1, \"form-control\", \"form-control-sm\", 3, \"clearable\", \"clearOnBackspace\", \"ngModel\", \"items\", \"ngModelChange\", \"change\"], [\"type\", \"text\", \"numeric\", \"\", \"numericType\", \"number\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModel\", \"ngModelChange\"], [1, \"text-center\"], [\"class\", \"btn btn-danger btn-sm me-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-success btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fa\", \"fa-minus\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"fa\", \"fa-plus\"]],\n  template: function CertificationTestEntryComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r335 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"block-ui\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 0);\n      i0.ɵɵelementStart(4, \"div\", 2);\n      i0.ɵɵelementStart(5, \"div\", 3);\n      i0.ɵɵelementStart(6, \"div\", 4);\n      i0.ɵɵelementStart(7, \"h5\", 5);\n      i0.ɵɵtext(8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(9, \"div\", 6);\n      i0.ɵɵelementStart(10, \"form\", 7);\n      i0.ɵɵelementStart(11, \"div\", 0);\n      i0.ɵɵelementStart(12, \"div\", 8);\n      i0.ɵɵelementStart(13, \"div\", 0);\n      i0.ɵɵelementStart(14, \"div\", 9);\n      i0.ɵɵelementStart(15, \"label\", 10);\n      i0.ɵɵtext(16, \"Course \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(17, \"ng-select\", 11, 12);\n      i0.ɵɵlistener(\"click\", function CertificationTestEntryComponent_Template_ng_select_click_17_listener() {\n        i0.ɵɵrestoreView(_r335);\n\n        const _r0 = i0.ɵɵreference(18);\n\n        return ctx.handleSelectClick(_r0);\n      })(\"change\", function CertificationTestEntryComponent_Template_ng_select_change_17_listener($event) {\n        return ctx.OnCourseChange($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(19, CertificationTestEntryComponent_small_19_Template, 2, 1, \"small\", 13);\n      i0.ɵɵtemplate(20, CertificationTestEntryComponent_div_20_Template, 2, 1, \"div\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(21, CertificationTestEntryComponent_div_21_Template, 5, 4, \"div\", 15);\n      i0.ɵɵtemplate(22, CertificationTestEntryComponent_div_22_Template, 4, 0, \"div\", 16);\n      i0.ɵɵtemplate(23, CertificationTestEntryComponent_div_23_Template, 4, 0, \"div\", 16);\n      i0.ɵɵtemplate(24, CertificationTestEntryComponent_div_24_Template, 5, 4, \"div\", 17);\n      i0.ɵɵtemplate(25, CertificationTestEntryComponent_div_25_Template, 5, 0, \"div\", 16);\n      i0.ɵɵtemplate(26, CertificationTestEntryComponent_div_26_Template, 5, 0, \"div\", 16);\n      i0.ɵɵtemplate(27, CertificationTestEntryComponent_div_27_Template, 3, 0, \"div\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(28, \"div\", 18);\n      i0.ɵɵelementStart(29, \"table\", 19);\n      i0.ɵɵelementStart(30, \"thead\");\n      i0.ɵɵelementStart(31, \"tr\");\n      i0.ɵɵelementStart(32, \"th\", 20);\n      i0.ɵɵtext(33, \"#\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(34, \"th\", 20);\n      i0.ɵɵtext(35, \"Segment\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(36, \"th\", 20);\n      i0.ɵɵtext(37, \"MCQ\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(38, \"th\", 20);\n      i0.ɵɵtext(39, \"True/False\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(40, \"th\", 20);\n      i0.ɵɵtext(41, \"Fill in the gap\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(42, \"th\", 20);\n      i0.ɵɵtext(43, \"Matching\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(44, \"th\", 20);\n      i0.ɵɵtext(45, \"Writing\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(46, \"tbody\");\n      i0.ɵɵtemplate(47, CertificationTestEntryComponent_tr_47_Template, 15, 7, \"tr\", 21);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(48, CertificationTestEntryComponent_tfoot_48_Template, 14, 5, \"tfoot\", 22);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(49, \"div\", 0);\n      i0.ɵɵtemplate(50, CertificationTestEntryComponent_div_50_Template, 6, 3, \"div\", 23);\n      i0.ɵɵtemplate(51, CertificationTestEntryComponent_div_51_Template, 5, 3, \"div\", 24);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(52, \"div\");\n      i0.ɵɵelementStart(53, \"div\", 25);\n      i0.ɵɵelementStart(54, \"div\", 4);\n      i0.ɵɵelementStart(55, \"div\", 0);\n      i0.ɵɵelementStart(56, \"div\", 26);\n      i0.ɵɵelementStart(57, \"h5\");\n      i0.ɵɵtext(58, \"MCQ Questions\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(59, \"div\", 27);\n      i0.ɵɵtemplate(60, CertificationTestEntryComponent_button_60_Template, 3, 0, \"button\", 28);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(61, CertificationTestEntryComponent_div_61_Template, 4, 0, \"div\", 29);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(62, \"div\", 6);\n      i0.ɵɵelementStart(63, \"ngx-datatable\", 30);\n      i0.ɵɵelementStart(64, \"ngx-datatable-column\", 31);\n      i0.ɵɵtemplate(65, CertificationTestEntryComponent_ng_template_65_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(66, \"ngx-datatable-column\", 33);\n      i0.ɵɵtemplate(67, CertificationTestEntryComponent_ng_template_67_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(68, \"ngx-datatable-column\", 34);\n      i0.ɵɵtemplate(69, CertificationTestEntryComponent_ng_template_69_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(70, \"ngx-datatable-column\", 35);\n      i0.ɵɵtemplate(71, CertificationTestEntryComponent_ng_template_71_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(72, \"ngx-datatable-column\", 36);\n      i0.ɵɵtemplate(73, CertificationTestEntryComponent_ng_template_73_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(74, \"ngx-datatable-column\", 37);\n      i0.ɵɵtemplate(75, CertificationTestEntryComponent_ng_template_75_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(76, \"ngx-datatable-column\", 38);\n      i0.ɵɵtemplate(77, CertificationTestEntryComponent_ng_template_77_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(78, \"ngx-datatable-column\", 39);\n      i0.ɵɵtemplate(79, CertificationTestEntryComponent_ng_template_79_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(80, \"ngx-datatable-column\", 40);\n      i0.ɵɵtemplate(81, CertificationTestEntryComponent_ng_template_81_Template, 6, 0, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(82, \"div\", 41);\n      i0.ɵɵelementStart(83, \"div\", 4);\n      i0.ɵɵelementStart(84, \"div\", 0);\n      i0.ɵɵelementStart(85, \"div\", 26);\n      i0.ɵɵelementStart(86, \"h5\");\n      i0.ɵɵtext(87, \"True/False Questions\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(88, \"div\", 42);\n      i0.ɵɵtemplate(89, CertificationTestEntryComponent_button_89_Template, 3, 0, \"button\", 28);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(90, CertificationTestEntryComponent_div_90_Template, 4, 0, \"div\", 43);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(91, \"div\", 6);\n      i0.ɵɵelementStart(92, \"ngx-datatable\", 30);\n      i0.ɵɵelementStart(93, \"ngx-datatable-column\", 31);\n      i0.ɵɵtemplate(94, CertificationTestEntryComponent_ng_template_94_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(95, \"ngx-datatable-column\", 33);\n      i0.ɵɵtemplate(96, CertificationTestEntryComponent_ng_template_96_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(97, \"ngx-datatable-column\", 44);\n      i0.ɵɵtemplate(98, CertificationTestEntryComponent_ng_template_98_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(99, \"ngx-datatable-column\", 39);\n      i0.ɵɵtemplate(100, CertificationTestEntryComponent_ng_template_100_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(101, \"ngx-datatable-column\", 40);\n      i0.ɵɵtemplate(102, CertificationTestEntryComponent_ng_template_102_Template, 6, 0, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(103, \"div\", 45);\n      i0.ɵɵelementStart(104, \"div\", 4);\n      i0.ɵɵelementStart(105, \"div\", 0);\n      i0.ɵɵelementStart(106, \"div\", 26);\n      i0.ɵɵelementStart(107, \"h5\");\n      i0.ɵɵtext(108, \"Fill in the gaps Questions\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(109, \"div\", 42);\n      i0.ɵɵtemplate(110, CertificationTestEntryComponent_button_110_Template, 3, 0, \"button\", 28);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(111, CertificationTestEntryComponent_div_111_Template, 4, 0, \"div\", 43);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(112, \"div\", 6);\n      i0.ɵɵelementStart(113, \"ngx-datatable\", 30);\n      i0.ɵɵelementStart(114, \"ngx-datatable-column\", 31);\n      i0.ɵɵtemplate(115, CertificationTestEntryComponent_ng_template_115_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(116, \"ngx-datatable-column\", 33);\n      i0.ɵɵtemplate(117, CertificationTestEntryComponent_ng_template_117_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(118, \"ngx-datatable-column\", 44);\n      i0.ɵɵtemplate(119, CertificationTestEntryComponent_ng_template_119_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(120, \"ngx-datatable-column\", 39);\n      i0.ɵɵtemplate(121, CertificationTestEntryComponent_ng_template_121_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(122, \"ngx-datatable-column\", 40);\n      i0.ɵɵtemplate(123, CertificationTestEntryComponent_ng_template_123_Template, 6, 0, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(124, \"div\", 46);\n      i0.ɵɵelementStart(125, \"div\", 4);\n      i0.ɵɵelementStart(126, \"div\", 0);\n      i0.ɵɵelementStart(127, \"div\", 26);\n      i0.ɵɵelementStart(128, \"h5\");\n      i0.ɵɵtext(129, \"Matching Questions\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(130, \"div\", 42);\n      i0.ɵɵtemplate(131, CertificationTestEntryComponent_button_131_Template, 3, 0, \"button\", 28);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(132, CertificationTestEntryComponent_div_132_Template, 4, 0, \"div\", 43);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(133, \"div\", 6);\n      i0.ɵɵelementStart(134, \"ngx-datatable\", 30);\n      i0.ɵɵelementStart(135, \"ngx-datatable-column\", 31);\n      i0.ɵɵtemplate(136, CertificationTestEntryComponent_ng_template_136_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(137, \"ngx-datatable-column\", 47);\n      i0.ɵɵtemplate(138, CertificationTestEntryComponent_ng_template_138_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(139, \"ngx-datatable-column\", 48);\n      i0.ɵɵtemplate(140, CertificationTestEntryComponent_ng_template_140_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(141, \"ngx-datatable-column\", 39);\n      i0.ɵɵtemplate(142, CertificationTestEntryComponent_ng_template_142_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(143, \"ngx-datatable-column\", 40);\n      i0.ɵɵtemplate(144, CertificationTestEntryComponent_ng_template_144_Template, 6, 0, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(145, \"div\", 49);\n      i0.ɵɵelementStart(146, \"div\", 4);\n      i0.ɵɵelementStart(147, \"div\", 0);\n      i0.ɵɵelementStart(148, \"div\", 26);\n      i0.ɵɵelementStart(149, \"h5\");\n      i0.ɵɵtext(150, \"Written Questions\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(151, \"div\", 42);\n      i0.ɵɵtemplate(152, CertificationTestEntryComponent_button_152_Template, 3, 0, \"button\", 28);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(153, CertificationTestEntryComponent_div_153_Template, 4, 0, \"div\", 43);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(154, \"div\", 6);\n      i0.ɵɵelementStart(155, \"ngx-datatable\", 30);\n      i0.ɵɵelementStart(156, \"ngx-datatable-column\", 31);\n      i0.ɵɵtemplate(157, CertificationTestEntryComponent_ng_template_157_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(158, \"ngx-datatable-column\", 50);\n      i0.ɵɵtemplate(159, CertificationTestEntryComponent_ng_template_159_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(160, \"ngx-datatable-column\", 39);\n      i0.ɵɵtemplate(161, CertificationTestEntryComponent_ng_template_161_Template, 2, 2, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(162, \"ngx-datatable-column\", 40);\n      i0.ɵɵtemplate(163, CertificationTestEntryComponent_ng_template_163_Template, 3, 0, \"ng-template\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(164, CertificationTestEntryComponent_ng_template_164_Template, 44, 12, \"ng-template\", null, 51, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(166, CertificationTestEntryComponent_ng_template_166_Template, 36, 11, \"ng-template\", null, 52, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(168, CertificationTestEntryComponent_ng_template_168_Template, 44, 12, \"ng-template\", null, 53, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(170, CertificationTestEntryComponent_ng_template_170_Template, 37, 10, \"ng-template\", null, 54, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(172, CertificationTestEntryComponent_ng_template_172_Template, 44, 12, \"ng-template\", null, 55, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(174, CertificationTestEntryComponent_ng_template_174_Template, 40, 10, \"ng-template\", null, 56, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(176, CertificationTestEntryComponent_ng_template_176_Template, 44, 12, \"ng-template\", null, 57, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(178, CertificationTestEntryComponent_ng_template_178_Template, 40, 10, \"ng-template\", null, 58, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(180, CertificationTestEntryComponent_ng_template_180_Template, 44, 12, \"ng-template\", null, 59, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(182, CertificationTestEntryComponent_ng_template_182_Template, 34, 1, \"ng-template\", null, 60, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵtextInterpolate(ctx.formTitle);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.entryForm);\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"readonly\", ctx.isEdit)(\"ngClass\", i0.ɵɵpureFunction1(143, _c0, ctx.submitted && ctx.f.courseId.errors))(\"clearable\", false)(\"clearOnBackspace\", false)(\"items\", ctx.courseList);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.ExamExists);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f.courseId.errors);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.ExamExists);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.ExamExists);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.ExamExists);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.ExamExists);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.ExamExists);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.ExamExists);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.ExamExists);\n      i0.ɵɵadvance(20);\n      i0.ɵɵproperty(\"ngForOf\", ctx.segmentWiseQuestions);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.totalQuestions);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !ctx.ExamExists);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.ExamExists);\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"ngIf\", ctx.mcqList.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.ExamExists);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"rows\", ctx.mcqList)(\"loadingIndicator\", ctx.loadingMCQ)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"limit\", 5);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"width\", 100)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 100)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 50)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 50)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 50)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 50)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 50)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 50)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 140)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"ngIf\", ctx.truFalseList.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.ExamExists);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"rows\", ctx.truFalseList)(\"loadingIndicator\", ctx.loadingTFQ)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"limit\", 5);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"width\", 100)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 100)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 100)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 50)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 140)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"ngIf\", ctx.figList.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.ExamExists);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"rows\", ctx.figList)(\"loadingIndicator\", ctx.loadingFIG)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"limit\", 5);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"width\", 100)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 200)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 100)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 50)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 140)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"ngIf\", ctx.matchingList.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.ExamExists);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"rows\", ctx.matchingList)(\"loadingIndicator\", ctx.loadingMatching)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"limit\", 5);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"width\", 100)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 175)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 175)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 50)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 140)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"ngIf\", ctx.writtenList.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.ExamExists);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"rows\", ctx.writtenList)(\"loadingIndicator\", ctx.loadingWritten)(\"columnMode\", ctx.ColumnMode.force)(\"headerHeight\", 40)(\"footerHeight\", 50)(\"limit\", 5);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"width\", 100)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"width\", 50)(\"draggable\", false)(\"sortable\", false);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maxWidth\", 80)(\"draggable\", false)(\"sortable\", false);\n    }\n  },\n  directives: [i8.BlockUIComponent, i3.ɵNgNoValidate, i3.NgControlStatusGroup, i3.FormGroupDirective, i9.NgSelectComponent, i3.NgControlStatus, i3.FormControlName, i10.NgClass, i11.DefaultClassDirective, i10.NgIf, i3.DefaultValueAccessor, i12.NumericDirective, i3.CheckboxControlValueAccessor, i10.NgForOf, i13.MenuComponent, i13.NgxEditorComponent, i6.RouterLink, i14.DatatableComponent, i14.DataTableColumnDirective, i14.DataTableColumnCellDirective, i3.NgModel, i15.UiSwitchComponent],\n  encapsulation: 2,\n  data: {\n    animation: [trigger('inOutAnimation', [transition(':enter', [style({\n      height: 0,\n      opacity: 0\n    }), animate('1s ease-out', style({\n      height: 300,\n      opacity: 1\n    }))]), transition(':leave', [animate('1s ease-out', style({\n      height: 0,\n      opacity: 0\n    }))])])]\n  }\n});\n\n__decorate([BlockUI()], CertificationTestEntryComponent.prototype, \"blockUI\", void 0);", "map": null, "metadata": {}, "sourceType": "module"}