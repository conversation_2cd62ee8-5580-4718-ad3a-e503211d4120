﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class CourseSegment : NumberAuditableEntity
    {
        [Required, StringLength(250)]
        public string Name { get; set; }
        public bool Active { get; set; }

        public virtual ICollection<SegmentWiseQsSetup> QuestionForExamSetups { get; set; }

    }
}
