<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <add name="DefaultConnection" connectionString="Data Source=DESKTOP-M52F3I4; Initial Catalog=BracLMS;Integrated Security=SSPI;" providerName="System.Data.SqlClient"/>
  </connectionStrings>
  <appSettings>
    <add key="appUser" value="BacBon 001"/>
    <add key="logPath" value="Logs"/>
    <add key="basePath" value="D:\Development\LMS BRAC\Brac.LMS.Media.API"/>
    <add key="destinationPath" value="D:\Development\LMS BRAC\Brac.LMS.Media.API\Files\TranscodedVideos"/>
    <add key="interval" value="30"/>
  </appSettings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/>
  </startup>
</configuration>
