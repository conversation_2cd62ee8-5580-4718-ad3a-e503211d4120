﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.5.0.2" targetFramework="net472" />
  <package id="bootstrap" version="5.1.2" targetFramework="net472" />
  <package id="EntityFramework" version="6.4.4" targetFramework="net472" />
  <package id="jQuery" version="3.6.0" targetFramework="net472" />
  <package id="jQuery.Validation" version="1.19.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.Cors" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.Identity.Core" version="2.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.Identity.EntityFramework" version="2.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.Identity.Owin" version="2.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.Razor" version="3.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Cors" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.HelpPage" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Owin" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.7" targetFramework="net472" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="3.6.0" targetFramework="net472" />
  <package id="Microsoft.jQuery.Unobtrusive.Validation" version="3.2.12" targetFramework="net472" />
  <package id="Microsoft.Owin" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Cors" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security.Cookies" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security.Facebook" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security.Google" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security.MicrosoftAccount" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security.OAuth" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security.Twitter" version="4.2.0" targetFramework="net472" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net472" />
  <package id="Modernizr" version="2.8.3" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net472" />
  <package id="Owin" version="1.0" targetFramework="net472" />
  <package id="WebGrease" version="1.6.0" targetFramework="net472" />
</packages>