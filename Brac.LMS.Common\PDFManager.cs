﻿using DocumentFormat.OpenXml.Wordprocessing;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms.DataVisualization.Charting;
using Font = iTextSharp.text.Font;

namespace Brac.LMS.Common
{
    public enum PDFAlignment
    {
        General = Element.ALIGN_BASELINE, Left = Element.ALIGN_LEFT, Center = Element.ALIGN_CENTER, Right = Element.ALIGN_RIGHT, Justified = Element.ALIGN_JUSTIFIED, Justified_All = Element.ALIGN_JUSTIFIED_ALL
    }

    public class ChartModel
    {
        public string X { get; set; }
        public object Y { get; set; }
    }

    public static class PDFManager
    {

        public static PdfPCell GetTableHeaderCell(object txt, float fontSize, bool sideBorderLess, bool borderLess, PDFAlignment alignment)
        {
            try
            {
                PdfPCell cell = new PdfPCell(new Phrase(Convert.ToString(txt), FontFactory.GetFont("Tahoma", fontSize, Font.BOLD, Color("#2C3E50"))))
                {
                    UseAscender = true,
                    BackgroundColor = Color("#E5E7E9"),
                    BorderColor = BaseColor.BLACK,
                    HorizontalAlignment = (int)alignment,
                    VerticalAlignment = Element.ALIGN_MIDDLE,
                    Padding = 5
                };
                if (borderLess)
                    cell.Border = 0;

                else if (sideBorderLess)
                {
                    cell.BorderWidthLeft = 0;
                    cell.BorderWidthRight = 0;
                }

                return cell;
            }
            catch (Exception ex)
            {
                throw new Exception("Error on GetPDFHeaderCell: " + ex.Message);
            }

        }

        public static PdfPCell GetTableDataCell(object cellValue, PDFAlignment alignment, bool borderLess, bool sideBorderLess, float fontSize, float[] leftToRightPadding = null, bool isBold = false, string fontName = "Tahoma", int colspan = 1, string fontColor = null)
        {
            PdfPCell cell = null;
            try
            {
                if (!FontFactory.IsRegistered(fontName) && fontName != "Tahoma")
                {
                    var fontPath = System.Web.Hosting.HostingEnvironment.MapPath("\\") + "App_Data\\Monotype Corsiva.ttf";
                    FontFactory.Register(fontPath, fontName);
                }

                var FontColour = string.IsNullOrEmpty(fontColor) ? BaseColor.BLACK : Color(fontColor);
                int FontStyle = iTextSharp.text.Font.NORMAL;


                var font = FontFactory.GetFont(fontName, BaseFont.IDENTITY_H, BaseFont.EMBEDDED, fontSize, FontStyle, FontColour);
                if (isBold) font.SetStyle(Font.BOLD);
                cell = new PdfPCell(new Phrase(Convert.ToString(cellValue), font))
                {
                    Colspan = colspan,
                };

                cell.UseAscender = true;
                cell.HorizontalAlignment = (int)alignment;
                cell.VerticalAlignment = Element.ALIGN_MIDDLE;
                if (leftToRightPadding != null)
                {
                    cell.PaddingLeft = leftToRightPadding[0];
                    cell.PaddingTop = leftToRightPadding[1];
                    cell.PaddingRight = leftToRightPadding[2];
                    cell.PaddingBottom = leftToRightPadding[3];
                }
                else
                    cell.Padding = 4;

                if (borderLess)
                {
                    cell.Border = 0;
                }
                else if (sideBorderLess)
                {
                    cell.BorderWidthLeft = 0;
                    cell.BorderWidthRight = 0;
                    cell.BorderWidthTop = 0;
                }
                cell.SetLeading(0, 2);
                return cell;
            }
            catch (Exception ex)
            {
                throw new Exception("Error on GetPDFDataCell: " + ex.Message);
            }

        }

        public static PdfPCell GetTableImageCell(Bitmap image, PDFAlignment alignment = PDFAlignment.Left, bool borderLess = true, bool sideBorderLess = true, float[] leftToRightPadding = null)
        {
            try
            {
                var img = iTextSharp.text.Image.GetInstance(image, BaseColor.WHITE);
                PdfPCell cell = new PdfPCell();
                cell.Image = img;

                cell.UseAscender = true;
                cell.HorizontalAlignment = (int)alignment;
                if (leftToRightPadding != null)
                {
                    cell.PaddingLeft = leftToRightPadding[0];
                    cell.PaddingTop = leftToRightPadding[1];
                    cell.PaddingRight = leftToRightPadding[2];
                    cell.PaddingBottom = leftToRightPadding[3];
                }
                else
                    cell.Padding = 4;

                if (borderLess)
                {
                    cell.Border = 0;
                }
                if (sideBorderLess)
                {
                    cell.BorderWidthLeft = 0;
                    cell.BorderWidthRight = 0;
                }
                return cell;
            }
            catch (Exception ex)
            {
                throw new Exception("Error on GetTableImageCell: " + ex.Message);
            }

        }

        public static PdfPCell GetPDFDataCell(object txt, int colspan = 1, string bgColor = "", bool borderLess = true, bool sideBorderLess = true, float fontSize = 10f, string fontName = "Tahoma", string fontColor = "#000000", string borderColor = null, PDFAlignment alignment = PDFAlignment.Left, float[] leftToRightPadding = null, bool isBold = false, float[] leftToRightBorder = null)
        {
            PdfPCell cell = null;
            try
            {
                cell = new PdfPCell(new Phrase(Convert.ToString(txt), isBold ? FontFactory.GetFont(fontName, fontSize, Font.BOLD, Color(fontColor)) : FontFactory.GetFont(fontName, fontSize, Color(fontColor))))
                {
                    Colspan = colspan,
                    UseAscender = true,
                    HorizontalAlignment = (int)alignment
                };

                if (leftToRightPadding != null)
                {
                    cell.PaddingLeft = leftToRightPadding[0];
                    cell.PaddingTop = leftToRightPadding[1];
                    cell.PaddingRight = leftToRightPadding[2];
                    cell.PaddingBottom = leftToRightPadding[3];
                }
                else
                    cell.Padding = 4;

                if (borderLess)
                {
                    cell.Border = 0;
                }
                if (!borderLess && sideBorderLess)
                {
                    cell.BorderWidthLeft = 0;
                    cell.BorderWidthRight = 0;
                }

                if (leftToRightBorder != null)
                {
                    cell.BorderWidthLeft = leftToRightBorder[0];
                    cell.BorderWidthTop = leftToRightBorder[1];
                    cell.BorderWidthRight = leftToRightBorder[2];
                    cell.BorderWidthBottom = leftToRightBorder[3];
                }

                if (!string.IsNullOrEmpty(borderColor)) cell.BorderColor = Color(borderColor);


                if (!string.IsNullOrEmpty(bgColor)) cell.BackgroundColor = Color(bgColor);
                return cell;
            }
            catch (Exception ex)
            {
                throw new Exception("Error on GetPDFDataCell: " + ex.Message);
            }

        }

        public static PdfPCell GetPDFDataCellWithContent(PdfPTable[] elements, int colspan = 1, bool borderLess = true, float[] leftToRightPadding = null)
        {
            try
            {
                PdfPCell cell = new PdfPCell();
                cell.Colspan = colspan;
                cell.UseAscender = true;

                if (leftToRightPadding != null)
                {
                    cell.PaddingLeft = leftToRightPadding[0];
                    cell.PaddingTop = leftToRightPadding[1];
                    cell.PaddingRight = leftToRightPadding[2];
                    cell.PaddingBottom = leftToRightPadding[3];
                }
                else
                    cell.Padding = 4;

                if (borderLess)
                {
                    cell.Border = 0;
                }

                foreach (var element in elements)
                {
                    cell.AddElement(element);
                }
                return cell;
            }
            catch (Exception ex)
            {
                throw new Exception("Error on GetPDFDataCell: " + ex.Message);
            }

        }

        public static BaseColor Color(string hex)
        {
            return new BaseColor(System.Drawing.ColorTranslator.FromHtml(hex));
        }

        public static PdfPTable GetTextLineElement(string txt, float fontSize = 10f, string fontName = "Tahoma", float[] leftToRightPadding = null, string fontColor = "#000000", string bgColor = "#ffffff", float tableWidth = 100, bool isBold = false, bool isUnderlined = false, PDFAlignment alignment = PDFAlignment.Left, bool borderless = true, float[] leftToRightBorder = null, string borderColor = null)
        {
            try
            {
                var pdfTable = new PdfPTable(1) { WidthPercentage = tableWidth };
                var font = FontFactory.GetFont(fontName, fontSize, Color(fontColor));
                if (isBold) font.SetStyle(Font.BOLD);
                if (isUnderlined) font.SetStyle(Font.UNDERLINE);
                if (isUnderlined && isBold) font.SetStyle(Font.BOLD | Font.UNDERLINE);
                PdfPCell cell = new PdfPCell(new Phrase(txt ?? " ", font));

                cell.UseAscender = true;
                cell.HorizontalAlignment = (int)alignment;
                cell.Border = 0;
                cell.BackgroundColor = Color(bgColor);
                if (leftToRightPadding != null)
                {
                    cell.PaddingLeft = leftToRightPadding[0];
                    cell.PaddingTop = leftToRightPadding[1];
                    cell.PaddingRight = leftToRightPadding[2];
                    cell.PaddingBottom = leftToRightPadding[3];
                }

                if (borderless) cell.Border = 0;

                if (leftToRightBorder != null)
                {
                    cell.BorderWidthLeft = leftToRightBorder[0];
                    cell.BorderWidthTop = leftToRightBorder[1];
                    cell.BorderWidthRight = leftToRightBorder[2];
                    cell.BorderWidthBottom = leftToRightBorder[3];
                }

                if (!string.IsNullOrEmpty(borderColor)) cell.BorderColor = Color(borderColor);
                cell.SetLeading(0, 2);
                pdfTable.AddCell(cell);
                return pdfTable;
            }
            catch (Exception ex)
            {
                throw new Exception("Error on GetTextLineElement: " + ex.Message);
            }

        }

        public static PdfPTable GetImageContent(Bitmap image, PDFAlignment alignment = PDFAlignment.Left, bool borderLess = true, bool sideBorderLess = true, float[] leftToRightPadding = null, float tableWidth = 100)
        {
            try
            {
                var pdfTable = new PdfPTable(1) { WidthPercentage = tableWidth };
                var img = iTextSharp.text.Image.GetInstance(image, BaseColor.WHITE);
                PdfPCell cell = new PdfPCell();
                cell.Image = img;

                cell.HorizontalAlignment = (int)alignment;
                if (leftToRightPadding != null)
                {
                    cell.PaddingLeft = leftToRightPadding[0];
                    cell.PaddingTop = leftToRightPadding[1];
                    cell.PaddingRight = leftToRightPadding[2];
                    cell.PaddingBottom = leftToRightPadding[3];
                }
                else
                    cell.Padding = 4;

                if (borderLess)
                {
                    cell.Border = 0;
                }
                if (sideBorderLess)
                {
                    cell.BorderWidthLeft = 0;
                    cell.BorderWidthRight = 0;
                }
                pdfTable.AddCell(cell);
                return pdfTable;
            }
            catch (Exception ex)
            {
                throw new Exception("Error on GetTableImageCell: " + ex.Message);
            }

        }

        public static Bitmap GraphImage(List<ChartModel> items, string title, int width, int height, int interval, string xTitle = null, string yTitle = null)
        {
            var chart = new Chart
            {
                Width = width,
                Height = height,
                AntiAliasing = AntiAliasingStyles.All,
                TextAntiAliasingQuality = TextAntiAliasingQuality.High,
                Palette = ChartColorPalette.None,
                PaletteCustomColors = new System.Drawing.Color[] { System.Drawing.Color.CornflowerBlue }
            };

            chart.Titles.Add(title);
            chart.Titles[0].Font = new System.Drawing.Font("Arial", 9f, FontStyle.Bold);

            chart.ChartAreas.Add("");
            chart.ChartAreas[0].AxisX.Title = xTitle;
            chart.ChartAreas[0].AxisY.Title = yTitle;
            chart.ChartAreas[0].AxisX.TitleFont = new System.Drawing.Font("Arial", 8f);
            chart.ChartAreas[0].AxisY.TitleFont = new System.Drawing.Font("Arial", 8f);
            chart.ChartAreas[0].AxisX.LabelStyle.Font = new System.Drawing.Font("Arial", 10f);
            chart.ChartAreas[0].AxisX.MajorGrid.LineWidth = 0;
            chart.ChartAreas[0].AxisY.MajorGrid.LineDashStyle = ChartDashStyle.Dash;
            chart.ChartAreas[0].AxisY.MajorGrid.LineColor = System.Drawing.Color.Black;
            chart.ChartAreas[0].AxisY.Interval = interval;
            chart.ChartAreas[0].BackColor = System.Drawing.Color.White;
            chart.Series.Add("");
            chart.Series[0].ChartType = SeriesChartType.Column;
            chart.Series[0].CustomProperties = "PointWidth=0.5";


            foreach (var item in items)
            {
                chart.Series[0].Points.AddXY(item.X, item.Y);
            }

            var chartimage = new MemoryStream();
            chart.SaveImage(chartimage, ChartImageFormat.Png);
            return new Bitmap(chartimage);
        }

    }
}
