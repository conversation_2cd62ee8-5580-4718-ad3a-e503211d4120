﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using Microsoft.AspNet.Identity.Owin;
using Microsoft.Owin.Security;
using Microsoft.Owin.Security.Cookies;
using Microsoft.Owin.Security.OAuth;
using Brac.LMS.Media.API.Models;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Newtonsoft.Json;
using System.Data.Entity;

namespace Brac.LMS.Media.API.Providers
{
    public class ApplicationOAuthProvider : OAuthAuthorizationServerProvider
    {
        private readonly string _publicClientId;

        public ApplicationOAuthProvider(string publicClientId)
        {
            if (publicClientId == null)
            {
                throw new ArgumentNullException("publicClientId");
            }

            _publicClientId = publicClientId;
        }

        public override async Task GrantResourceOwnerCredentials(OAuthGrantResourceOwnerCredentialsContext context)
        {
            try
            {
                var userManager = context.OwinContext.GetUserManager<ApplicationUserManager>();

                ApplicationUser user = await userManager.FindAsync(context.UserName, context.Password);

                var data = await context.Request.ReadFormAsync();

                if (user == null || user.UserType.ToString() != data["usertype"])
                {
                    context.SetError("invalid_grant", "The user name or password is incorrect.");
                    return;
                }

                if (!user.Active)
                {
                    context.SetError("invalid_grant", "This user has been de-activated by the Administration. Please contact with the Administration");
                    return;
                }

                var dbContext = context.OwinContext.Get<ApplicationDbContext>();

                var userGroupName = "";

                if (user.UserGroupId.HasValue)
                {
                    userGroupName = await dbContext.UserGroups.Where(x => x.Id == user.UserGroupId.Value).Select(x => x.Name).FirstOrDefaultAsync();
                }

                user.LastLogOn = DateTime.UtcNow;
                await userManager.UpdateAsync(user);

                ClaimsIdentity oAuthIdentity = await user.GenerateUserIdentityAsync(userManager,
                   OAuthDefaults.AuthenticationType);

                var roles = await userManager.GetRolesAsync(user.Id);

                AuthenticationProperties properties = CreateProperties(user, roles, userGroupName);
                AuthenticationTicket ticket = new AuthenticationTicket(oAuthIdentity, properties);
                context.Validated(ticket);
            }
            catch (Exception ex)
            {
                context.SetError("Autorization Error", ex.Message);
            }
        }

        public override Task TokenEndpoint(OAuthTokenEndpointContext context)
        {
            foreach (KeyValuePair<string, string> property in context.Properties.Dictionary)
            {
                context.AdditionalResponseParameters.Add(property.Key, property.Value);
            }

            return Task.FromResult<object>(null);
        }

        public override Task ValidateClientAuthentication(OAuthValidateClientAuthenticationContext context)
        {
            // Resource owner password credentials does not provide a client ID.
            if (context.ClientId == null)
            {
                context.Validated();
            }

            return Task.FromResult<object>(null);
        }

        public override Task ValidateClientRedirectUri(OAuthValidateClientRedirectUriContext context)
        {
            if (context.ClientId == _publicClientId)
            {
                Uri expectedRootUri = new Uri(context.Request.Uri, "/");

                if (expectedRootUri.AbsoluteUri == context.RedirectUri)
                {
                    context.Validated();
                }
            }

            return Task.FromResult<object>(null);
        }

        public static AuthenticationProperties CreateProperties(string userName)
        {
            IDictionary<string, string> data = new Dictionary<string, string>
            {
                { "userName", userName }
            };
            return new AuthenticationProperties(data);
        }

        public static AuthenticationProperties CreateProperties(ApplicationUser user, IList<string> roles, string userGroupName)
        {

            IDictionary<string, string> data = new Dictionary<string, string>
            {
                {
                    "Id",user.Id
                },
                {
                    "UserName", user.UserName
                },
                {
                    "Email", user.Email
                },
                {
                    "FirstName", user.FirstName ?? ""
                },
                {
                    "LastName", user.LastName ?? ""
                },
                {
                    "ImagePath", user.ImagePath ?? ""
                },
                {
                    "UserType", user.UserType.ToString()
                },
                {
                    "Gender", user.Gender.ToString()
                },
                {
                    "Roles", JsonConvert.SerializeObject(roles)
                },
                {
                    "UserGroup", userGroupName ?? "Super Admin"
                }
            };
            return new AuthenticationProperties(data);
        }
    }
}