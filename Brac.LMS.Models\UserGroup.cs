﻿
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class UserGroup : NumberAuditableEntity
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public int NoOfRoles { get; set; }
        public virtual ICollection<UserGroupRole> Roles { get; set; }
    }

    public class UserGroupRole
    {
        [Key, Column(Order = 1)]
        public long UserGroupId { get; set; }
        public virtual UserGroup UserGroup { get; set; }

        [Key, Column(Order = 2)]
        public string RoleId { get; set; }
        public virtual ApplicationRole Role { get; set; }
    }
}
