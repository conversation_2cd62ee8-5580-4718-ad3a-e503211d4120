﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Microsoft.AspNet.Identity.Owin;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [RoutePrefix("api/external-course")]
    public class ExternalCourseController : ApplicationController
    {
        private readonly IExternalCourseService _service;

        public ExternalCourseController()
        {
            _service = new ExternalCourseService();
        }

        [Authorize(Roles = "Admin"), HttpPost, Route("create-or-update")]
        public async Task<IHttpActionResult> ExternalCourseCreateOrUpdate()
        {
            try
            {
                var _nservice = new ExternalCourseService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
                var model = Newtonsoft.Json.JsonConvert.DeserializeObject<ExternalCourseModel>(System.Web.HttpContext.Current.Request.Form["Model"]);
                return Ok(await _nservice.ExternalCourseCreateOrUpdate(model, CurrentUser));
            }
            catch (Exception ex)
            {
                return Ok(new { Success = false, ex.Message });
            }
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("list")]
        public async Task<IHttpActionResult> GetExternalCourseList(string name, long? categoryId, int size, int pageNumber)
        {
            return Ok(await _service.GetExternalCourseList(name, categoryId, size, pageNumber, CurrentUser));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("download-list-in-excel/{timeZoneOffset}")]
        public async Task<HttpResponseMessage> GetExternalCourseListExcel(int timeZoneOffset)
        {
            byte[] byteArray = await _service.GetExternalCourseListExcel(timeZoneOffset);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.
            //response.Content.Headers.Add("x-filename", "Student_List.xls");
            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }

        [Authorize(Roles = "Admin"), HttpGet, Route("dropdown-list")]
        public async Task<IHttpActionResult> GetExternalCourseDropDownList()
        {
            return Ok(await _service.GetExternalCourseDropDownList(CurrentUser));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get/{id}")]
        public async Task<IHttpActionResult> GetExternalCourseById(Guid id)
        {
            return Ok(await _service.GetExternalCourseById(id, CurrentUser));
        }

        #region Trainee Panel APIs
        [Authorize(Roles = "Trainee"), HttpGet, Route("get-more-courses")]
        public async Task<IHttpActionResult> GetMoreCourseList(int limit, DateTime keyDate)
        {
            return Ok(await _service.GetCourseList(limit, keyDate));
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("get-courses")]
        public async Task<IHttpActionResult> GetCourseList(string name, long? categoryId, int size, int pageNumber)
        {
            return Ok(await _service.GetCourseList(name, categoryId, size, pageNumber));
        }

        [Authorize(Roles = "Trainee"), HttpGet, Route("get-course-preview/{id}")]
        public async Task<IHttpActionResult> GetCoursePreview(Guid id)
        {
            return Ok(await _service.GetCoursePreview(id));
        }
        #endregion
    }
}
