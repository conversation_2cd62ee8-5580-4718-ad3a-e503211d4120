﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{

    public class TraineeEvaluationExam : AuditableEntity
    {
        public Guid TraineeId { get; set; }
        public virtual Trainee Trainee { get; set; }

        public Guid ExamId { get; set; }
        public virtual EvaluationExam Exam { get; set; }

        public int TotalMarks { get; set; }
        public float GainedMarks { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }

        [StringLength(128)]
        public string CheckerId { get; set; }
        public virtual ApplicationUser Checker { get; set; }

        public DateTime? MarkedOn { get; set; }

        public ExamStatus Status { get; set; }
        public bool AutoSubmission { get; set; }
        public bool? Terminated { get; set; }


        public Guid? GradingPolicyId { get; set; }
        public virtual GradingPolicy GradingPolicy { get; set; }

        public int GradingGroup { get; set; }
        public GradeResult? Result { get; set; }
        public string Grade { get; set; }
        public int GainedPercentage { get; set; }

        public string CheckerComments { get; set; }

        public DateTime? PublishDate { get; set; }

        [StringLength(128)]
        public string PublisherId { get; set; }
        public virtual ApplicationUser Publisher { get; set; }

    }

    public class TraineeEvaluationExamAttempt
    {
        [Key, Column(Order = 1)]
        public Guid TraineeId { get; set; }
        public virtual Trainee Trainee { get; set; }

        [Key, Column(Order = 2)]
        public Guid ExamId { get; set; }
        public virtual EvaluationExam Exam { get; set; }

        public int Attempt { get; set; }
        public int ExtendedQuota { get; set; }
        
    }
}
