﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class MCQQuestion : NumberAuditableEntity
    {
        [Required]
        public string Question { get; set; }

        [Required]
        public string Option1 { get; set; }

        [Required]
        public string Option2 { get; set; }

        [Required]
        public string Option3 { get; set; }

        [Required]
        public string Option4 { get; set; }

        [Required]
        public string Answers { get; set; }

        public float Mark { get; set; }

        public Guid ExamId { get; set; }
        public virtual CourseExam Exam { get; set; }

        public long SegmentId { get; set; }
        public virtual CourseSegment Segment { get; set; }

        [NotMapped]
        public bool Updated { get; set; }
    }

    public class TrueFalseQuestion : NumberAuditableEntity
    {
        [Required]
        public string Question { get; set; }

        public bool Answer { get; set; }

        public float Mark { get; set; }

        public Guid ExamId { get; set; }
        public virtual CourseExam Exam { get; set; }

        public long SegmentId { get; set; }
        public virtual CourseSegment Segment { get; set; }

        [NotMapped]
        public bool Updated { get; set; }
    }

    public class FIGQuestion : NumberAuditableEntity
    {
        [Required]
        public string Question { get; set; }

        [Required]
        public string Answer { get; set; }

        public float Mark { get; set; }

        public Guid ExamId { get; set; }
        public virtual CourseExam Exam { get; set; }

        public long SegmentId { get; set; }
        public virtual CourseSegment Segment { get; set; }

        [NotMapped]
        public bool Updated { get; set; }
    }

    public class MatchingQuestion : NumberAuditableEntity
    {
        [Required]
        public string LeftSide { get; set; }

        [Required]
        public string RightSide { get; set; }

        public float Mark { get; set; }

        public Guid ExamId { get; set; }
        public virtual CourseExam Exam { get; set; }

        public long SegmentId { get; set; }
        public virtual CourseSegment Segment { get; set; }

        [NotMapped]
        public bool Updated { get; set; }
    }

    public class WrittenQuestion : NumberAuditableEntity
    {
        [Required]
        public string Question { get; set; }
        public float Mark { get; set; }

        public Guid ExamId { get; set; }
        public virtual CourseExam Exam { get; set; }

        public long SegmentId { get; set; }
        public virtual CourseSegment Segment { get; set; }

        [NotMapped]
        public bool Updated { get; set; }
    }

    public class MCQEvaluationQuestion : NumberAuditableEntity
    {
        [Required]
        public string Question { get; set; }

        [Required]
        public string Option1 { get; set; }

        [Required]
        public string Option2 { get; set; }

        [Required]
        public string Option3 { get; set; }

        [Required]
        public string Option4 { get; set; }

        [Required]
        public string Answers { get; set; }

        public float Mark { get; set; }

        public Guid ExamId { get; set; }
        public virtual EvaluationExam Exam { get; set; }

        [NotMapped]
        public bool Updated { get; set; }
    }


    public class TrueFalseEvaluationQuestion : NumberAuditableEntity
    {
        [Required]
        public string Question { get; set; }

        public bool Answer { get; set; }

        public float Mark { get; set; }

        public Guid ExamId { get; set; }
        public virtual EvaluationExam Exam { get; set; }

        [NotMapped]
        public bool Updated { get; set; }
    }

    public class FIGEvaluationQuestion : NumberAuditableEntity
    {
        [Required]
        public string Question { get; set; }

        [Required]
        public string Answer { get; set; }

        public float Mark { get; set; }

        public Guid ExamId { get; set; }
        public virtual EvaluationExam Exam { get; set; }

        [NotMapped]
        public bool Updated { get; set; }
    }

    public class MatchingEvaluationQuestion : NumberAuditableEntity
    {
        [Required]
        public string LeftSide { get; set; }

        [Required]
        public string RightSide { get; set; }

        public float Mark { get; set; }

        public Guid ExamId { get; set; }
        public virtual EvaluationExam Exam { get; set; }

        [NotMapped]
        public bool Updated { get; set; }
    }

    public class WrittenEvaluationQuestion : NumberAuditableEntity
    {
        [Required]
        public string Question { get; set; }
        public float Mark { get; set; }

        public Guid ExamId { get; set; }
        public virtual EvaluationExam Exam { get; set; }

        [NotMapped]
        public bool Updated { get; set; }
    }

}
