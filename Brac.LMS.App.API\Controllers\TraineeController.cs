﻿using Brac.LMS.App.Services;
using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using FastReport;
using FastReport.Export.Pdf;
using Microsoft.AspNet.Identity.Owin;
using Microsoft.Owin;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Web.Http;

namespace Brac.LMS.App.API.Controllers
{
    [RoutePrefix("api/trainee")]
    public class TraineeController : ApplicationController
    {
        private ApplicationUserManager _userManager;
        private readonly ITraineeService _service;
        private readonly ApplicationDbContext _context;


        public TraineeController()
        {
            _service = new TraineeService();
            _context = new ApplicationDbContext();
        }

        public ApplicationUserManager UserManager
        {
            get
            {
                return _userManager ?? Request.GetOwinContext().GetUserManager<ApplicationUserManager>();
            }
            private set
            {
                _userManager = value;
            }
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("list")]
        public async Task<IHttpActionResult> GetTraineeList(string name, long? divisionId, long? departmentId, int size, int pageNumber)
        {
            return Ok(await _service.GetTraineeList(name, divisionId, departmentId, size, pageNumber));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("dropdown-list")]
        public async Task<IHttpActionResult> GetTraineeDropdownList()
        {
            return Ok(await _service.GetTraineeDropDownList());
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("get/{id}")]
        public async Task<IHttpActionResult> GetTraineeById(Guid id)
        {
            return Ok(await _service.GetTraineeById(id));
        }

        [Authorize(Roles = "Admin"), HttpPost, Route("create-or-update")]
        public async Task<IHttpActionResult> CreateOrUpdateTrainee()
        {
            try
            {
                //var _nservice = new TraineeService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
                var model = Newtonsoft.Json.JsonConvert.DeserializeObject<TraineeModel>(System.Web.HttpContext.Current.Request.Form["Model"]);
                return Ok(await _service.CreateOrUpdateTrainee(model, User.Identity, UserManager));
            }
            catch (Exception ex)
            {
                return Ok(new
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                });
            }
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("download-sample-file")]
        public HttpResponseMessage DownloadSampleFile()
        {
            if (!File.Exists(Generator.UploadSampleTrainees))
            {
                return Request.CreateResponse(HttpStatusCode.NotFound);
            }
            else
            {
                HttpResponseMessage result = Request.CreateResponse(HttpStatusCode.OK);
                result.Content = new StreamContent(File.OpenRead(Generator.UploadSampleTrainees));
                result.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/vnd.ms-excel");
                return result;
            }
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("list-excel")]
        public async Task<HttpResponseMessage> GetTraineeListExcel(string name, long? divisionId, long? departmentId)
        {
            byte[] byteArray = await _service.GetTraineeListExcel(name, divisionId, departmentId);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }

        [Authorize(Roles = "Admin"), HttpGet, Route("query/{limit}/{query}")]
        public async Task<IHttpActionResult> QueryTrainee(string query, int limit)
        {
            return Ok(await _service.QueryTrainee(query, limit));
        }

        [Authorize(Roles = "Admin"), HttpPost, Route("query-by-excel")]
        public async Task<IHttpActionResult> QueryTraineeByExcel()
        {
            var _nservice = new TraineeService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _nservice.QueryTraineeByExcel());
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("dropdown-list")]
        public async Task<IHttpActionResult> GetTraineeDropDownList()
        {
            return Ok(await _service.GetTraineeDropDownList());
        }



        //[Authorize(Roles = "Admin"), HttpPost, Route("save-course-rating")]
        //public async Task<IHttpActionResult> CourseRatingSave(RatingSaveModel model)
        //{
        //    return Ok(await _service.CourseRatingSave(model, CurrentUser));
        //}


        [Authorize(Roles = "Admin"), HttpPost, Route("update-photo")]
        public async Task<IHttpActionResult> UpdatePhoto()
        {
            //var _nservice = new TraineeService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.UpdatePhoto(User.Identity, UserManager));
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("download-answersheet/{id}")]
        public async Task<HttpResponseMessage> GetAnswerSheetPDF(Guid id)
        {
            byte[] byteArray = await _service.GetAnswerSheetPDF(id);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
            //    return response;
            return response;
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("enroll-trainee-list-excel")]
        public async Task<HttpResponseMessage> GetTraineeListExcel(Guid courseId, long? divisionId)
        {
            byte[] byteArray = await _service.GetEnrolledTraineeListExcel(courseId, divisionId);
            var response = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new ByteArrayContent(byteArray),
            };

            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            return response;
        }

        #region Guest Trainee
        [Authorize(Roles = "Admin"), HttpPost, Route("create-or-update-guest")]
        public async Task<IHttpActionResult> CreateOrUpdateGuestTrainee(TraineeModel model)
        {
            //var _nservice = new TraineeService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            return Ok(await _service.CreateOrUpdateGuestTrainee(model, User.Identity, UserManager));
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("remove-invalid-guest")]
        public async Task<IHttpActionResult> RemoveInvalidGuestTrainee()
        {
            return Ok(await _service.RemoveInvalidGuestTrainee());
        }


        [Authorize(Roles = "Admin"), HttpGet, Route("guest-list")]
        public async Task<IHttpActionResult> GetGuestTraineeList(string name, long? divisionId, long? departmentId, int size, int pageNumber)
        {
            return Ok(await _service.GetGuestTraineeList(name, divisionId, departmentId, size, pageNumber));
        }

        [Authorize(Roles = "Admin"), HttpPost, Route("upload-guest")]
        public async Task<IHttpActionResult> UploadEmployee()
        {
            //var _nservice = new TraineeService(this.ControllerContext.ControllerDescriptor.ControllerName, this.ActionContext.ActionDescriptor.ActionName, CurrentUser);
            //return Ok(await _nservice.UploadGuestTrainee(User.Identity, Request.GetOwinContext()));
            return Ok(await _service.UploadGuestTrainee(User.Identity, UserManager));
        }

        [HttpGet, Route("download-guest-sample-file")]
        public HttpResponseMessage DownloadGuestSampleFile()
        {
            if (!File.Exists(Generator.UploadSampleGuestTrainee))
            {
                return Request.CreateResponse(HttpStatusCode.NotFound);
            }
            else
            {
                HttpResponseMessage result = Request.CreateResponse(HttpStatusCode.OK);
                result.Content = new StreamContent(File.OpenRead(Generator.UploadSampleGuestTrainee));
                result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
                return result;
            }
        }

        [Authorize(Roles = "Admin"), HttpGet, Route("guest-list-excel")]
        public async Task<HttpResponseMessage> GetGuestTraineeListExcel(string name, long? divisionId, long? departmentId)
        {
            byte[] byteArray = await _service.GetTraineeListExcel(name, divisionId, departmentId, LMS.Models.TraineeType.Guest);
            //Create a new response.
            var response = new HttpResponseMessage(HttpStatusCode.OK);
            //Assign byte array to response content.
            response.Content = new ByteArrayContent(byteArray);

            //Set MIME type.
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
            //    return response;
            return response;

        }
        #endregion

        #region Trainee Panel APIs
        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-profile")]
        public async Task<IHttpActionResult> GetProfile()
        {
            return Ok(await _service.GetProfile(User.Identity));
        }

        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-my-course-study-report")]
        public async Task<HttpResponseMessage> GetMyCourseStudyReport(ReportType reportType)
        {
            try
            {
                byte[] byteArray = reportType == ReportType.Excel ? await _service.GetMyCourseStudyReportExcel(CurrentUser) : await _service.GetMyCourseStudyReportPdf(CurrentUser);
                //Create a new response.
                var response = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    //Assign byte array to response content.
                    Content = new ByteArrayContent(byteArray)
                };

                switch (reportType)
                {
                    case ReportType.Pdf:
                        //Set MIME type.
                        response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");
                        break;
                    case ReportType.Excel:
                        //Set MIME type.
                        response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");
                        break;
                }
                //Add default file name that can be used by client code for both MIME transfer and AJAX Blob.

                //    return response;
                return response;
            }
            catch (Exception)
            {
                return new HttpResponseMessage(HttpStatusCode.BadRequest);
            }
        }


        [Authorize(Roles = "Trainee, Guest"), HttpGet, Route("get-user-profile/{userId}")]
        public async Task<IHttpActionResult> GetUserProfile(string userId)
        {
            return Ok(await _service.GetUserProfile(userId));
        }
        #endregion
    }
}
