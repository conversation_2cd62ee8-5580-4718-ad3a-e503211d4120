﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.App.ViewModels
{
    public class ExamAnserSaveModel
    {
        public Guid TraineeEvaluationExamId { get; set; }
        public Guid ExamId { get; set; }
        public Guid TraineeExamId { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public bool AutoSubmission { get; set; }
        public bool? Terminated { get; set; }

        public List<QuestionAnswerModel> MCQList { get; set; }
        public List<TFQuestionAnswerModel> TFQList { get; set; }
        public List<QuestionAnswerModel> FIGQList { get; set; }
        public List<QuestionAnswerModel> MatchingQList { get; set; }
        public List<QuestionAnswerModel> WQList { get; set; }
    }

    public class QuestionAnswerModel
    {
        public long QuestionId { get; set; }
        public string Answered { get; set; }
    }

    public class TFQuestionAnswerModel
    {
        public long QuestionId { get; set; }
        public bool Answered { get; set; }
    }


    public class MockTestAnserSaveModel
    {
        public Guid ExamId { get; set; }
        public Guid TraineeMockTestId { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public bool AutoSubmission { get; set; }
        public List<QuestionAnswerModel> MCQList { get; set; }
    }
}
