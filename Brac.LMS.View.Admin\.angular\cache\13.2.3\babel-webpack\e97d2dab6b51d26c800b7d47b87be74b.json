{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../../theme/shared/components/card/card.component\";\nexport let TblBasicComponent = /*#__PURE__*/(() => {\n  class TblBasicComponent {\n    constructor() {}\n\n    ngOnInit() {}\n\n  }\n\n  TblBasicComponent.ɵfac = function TblBasicComponent_Factory(t) {\n    return new (t || TblBasicComponent)();\n  };\n\n  TblBasicComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TblBasicComponent,\n    selectors: [[\"app-tbl-basic\"]],\n    decls: 403,\n    vars: 0,\n    consts: [[1, \"row\"], [1, \"col-md-6\"], [\"cardTitle\", \"Basic Table\", \"blockClass\", \"table-border-style\"], [1, \"table-responsive\"], [1, \"table\"], [\"cardTitle\", \"Inverse Table\", \"blockClass\", \"table-border-style\"], [1, \"table\", \"table-inverse\"], [\"cardTitle\", \"Hover Table\", \"blockClass\", \"table-border-style\"], [1, \"table\", \"table-hover\"], [\"cardTitle\", \"Dark Table\", \"blockClass\", \"table-border-style\"], [1, \"table\", \"table-dark\"], [1, \"col-xl-12\"], [\"cardTitle\", \"Striped Table\", \"blockClass\", \"table-border-style\"], [1, \"table\", \"table-striped\"], [\"cardTitle\", \"Contextual Classes\", \"blockClass\", \"table-border-style\"], [1, \"table-active\"], [1, \"table-success\"], [1, \"table-warning\"], [1, \"table-danger\"], [1, \"table-info\"], [\"cardTitle\", \"Background Utilities\", \"blockClass\", \"table-border-style\"], [1, \"bg-primary\"], [1, \"bg-success\"], [1, \"bg-warning\"], [1, \"bg-danger\"], [1, \"bg-info\"]],\n    template: function TblBasicComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵelementStart(2, \"app-card\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3);\n        i0.ɵɵelementStart(4, \"table\", 4);\n        i0.ɵɵelementStart(5, \"thead\");\n        i0.ɵɵelementStart(6, \"tr\");\n        i0.ɵɵelementStart(7, \"th\");\n        i0.ɵɵtext(8, \"#\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"th\");\n        i0.ɵɵtext(10, \"First Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"th\");\n        i0.ɵɵtext(12, \"Last Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"th\");\n        i0.ɵɵtext(14, \"Username\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"tbody\");\n        i0.ɵɵelementStart(16, \"tr\");\n        i0.ɵɵelementStart(17, \"td\");\n        i0.ɵɵtext(18, \"1\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"td\");\n        i0.ɵɵtext(20, \"Mark\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"td\");\n        i0.ɵɵtext(22, \"Otto\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"td\");\n        i0.ɵɵtext(24, \"@mdo\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"tr\");\n        i0.ɵɵelementStart(26, \"td\");\n        i0.ɵɵtext(27, \"2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"td\");\n        i0.ɵɵtext(29, \"Jacob\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"td\");\n        i0.ɵɵtext(31, \"Thornton\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(32, \"td\");\n        i0.ɵɵtext(33, \"@fat\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"tr\");\n        i0.ɵɵelementStart(35, \"td\");\n        i0.ɵɵtext(36, \"3\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(37, \"td\");\n        i0.ɵɵtext(38, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"td\");\n        i0.ɵɵtext(40, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(41, \"td\");\n        i0.ɵɵtext(42, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"div\", 1);\n        i0.ɵɵelementStart(44, \"app-card\", 5);\n        i0.ɵɵelementStart(45, \"div\", 3);\n        i0.ɵɵelementStart(46, \"table\", 6);\n        i0.ɵɵelementStart(47, \"thead\");\n        i0.ɵɵelementStart(48, \"tr\");\n        i0.ɵɵelementStart(49, \"th\");\n        i0.ɵɵtext(50, \"#\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(51, \"th\");\n        i0.ɵɵtext(52, \"First Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(53, \"th\");\n        i0.ɵɵtext(54, \"Last Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(55, \"th\");\n        i0.ɵɵtext(56, \"Username\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(57, \"tbody\");\n        i0.ɵɵelementStart(58, \"tr\");\n        i0.ɵɵelementStart(59, \"td\");\n        i0.ɵɵtext(60, \"1\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(61, \"td\");\n        i0.ɵɵtext(62, \"Mark\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(63, \"td\");\n        i0.ɵɵtext(64, \"Otto\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(65, \"td\");\n        i0.ɵɵtext(66, \"@mdo\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(67, \"tr\");\n        i0.ɵɵelementStart(68, \"td\");\n        i0.ɵɵtext(69, \"2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(70, \"td\");\n        i0.ɵɵtext(71, \"Jacob\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(72, \"td\");\n        i0.ɵɵtext(73, \"Thornton\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(74, \"td\");\n        i0.ɵɵtext(75, \"@fat\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(76, \"tr\");\n        i0.ɵɵelementStart(77, \"td\");\n        i0.ɵɵtext(78, \"3\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(79, \"td\");\n        i0.ɵɵtext(80, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(81, \"td\");\n        i0.ɵɵtext(82, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(83, \"td\");\n        i0.ɵɵtext(84, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(85, \"div\", 1);\n        i0.ɵɵelementStart(86, \"app-card\", 7);\n        i0.ɵɵelementStart(87, \"div\", 3);\n        i0.ɵɵelementStart(88, \"table\", 8);\n        i0.ɵɵelementStart(89, \"thead\");\n        i0.ɵɵelementStart(90, \"tr\");\n        i0.ɵɵelementStart(91, \"th\");\n        i0.ɵɵtext(92, \"#\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(93, \"th\");\n        i0.ɵɵtext(94, \"First Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(95, \"th\");\n        i0.ɵɵtext(96, \"Last Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(97, \"th\");\n        i0.ɵɵtext(98, \"Username\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(99, \"tbody\");\n        i0.ɵɵelementStart(100, \"tr\");\n        i0.ɵɵelementStart(101, \"td\");\n        i0.ɵɵtext(102, \"1\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(103, \"td\");\n        i0.ɵɵtext(104, \"Mark\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(105, \"td\");\n        i0.ɵɵtext(106, \"Otto\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(107, \"td\");\n        i0.ɵɵtext(108, \"@mdo\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(109, \"tr\");\n        i0.ɵɵelementStart(110, \"td\");\n        i0.ɵɵtext(111, \"2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(112, \"td\");\n        i0.ɵɵtext(113, \"Jacob\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(114, \"td\");\n        i0.ɵɵtext(115, \"Thornton\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(116, \"td\");\n        i0.ɵɵtext(117, \"@fat\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(118, \"tr\");\n        i0.ɵɵelementStart(119, \"td\");\n        i0.ɵɵtext(120, \"3\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(121, \"td\");\n        i0.ɵɵtext(122, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(123, \"td\");\n        i0.ɵɵtext(124, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(125, \"td\");\n        i0.ɵɵtext(126, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(127, \"div\", 1);\n        i0.ɵɵelementStart(128, \"app-card\", 9);\n        i0.ɵɵelementStart(129, \"div\", 3);\n        i0.ɵɵelementStart(130, \"table\", 10);\n        i0.ɵɵelementStart(131, \"thead\");\n        i0.ɵɵelementStart(132, \"tr\");\n        i0.ɵɵelementStart(133, \"th\");\n        i0.ɵɵtext(134, \"#\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(135, \"th\");\n        i0.ɵɵtext(136, \"First Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(137, \"th\");\n        i0.ɵɵtext(138, \"Last Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(139, \"th\");\n        i0.ɵɵtext(140, \"Username\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(141, \"tbody\");\n        i0.ɵɵelementStart(142, \"tr\");\n        i0.ɵɵelementStart(143, \"td\");\n        i0.ɵɵtext(144, \"1\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(145, \"td\");\n        i0.ɵɵtext(146, \"Mark\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(147, \"td\");\n        i0.ɵɵtext(148, \"Otto\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(149, \"td\");\n        i0.ɵɵtext(150, \"@mdo\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(151, \"tr\");\n        i0.ɵɵelementStart(152, \"td\");\n        i0.ɵɵtext(153, \"2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(154, \"td\");\n        i0.ɵɵtext(155, \"Jacob\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(156, \"td\");\n        i0.ɵɵtext(157, \"Thornton\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(158, \"td\");\n        i0.ɵɵtext(159, \"@fat\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(160, \"tr\");\n        i0.ɵɵelementStart(161, \"td\");\n        i0.ɵɵtext(162, \"3\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(163, \"td\");\n        i0.ɵɵtext(164, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(165, \"td\");\n        i0.ɵɵtext(166, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(167, \"td\");\n        i0.ɵɵtext(168, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(169, \"div\", 11);\n        i0.ɵɵelementStart(170, \"app-card\", 12);\n        i0.ɵɵelementStart(171, \"div\", 3);\n        i0.ɵɵelementStart(172, \"table\", 13);\n        i0.ɵɵelementStart(173, \"thead\");\n        i0.ɵɵelementStart(174, \"tr\");\n        i0.ɵɵelementStart(175, \"th\");\n        i0.ɵɵtext(176, \"#\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(177, \"th\");\n        i0.ɵɵtext(178, \"First Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(179, \"th\");\n        i0.ɵɵtext(180, \"Last Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(181, \"th\");\n        i0.ɵɵtext(182, \"Username\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(183, \"tbody\");\n        i0.ɵɵelementStart(184, \"tr\");\n        i0.ɵɵelementStart(185, \"td\");\n        i0.ɵɵtext(186, \"1\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(187, \"td\");\n        i0.ɵɵtext(188, \"Mark\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(189, \"td\");\n        i0.ɵɵtext(190, \"Otto\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(191, \"td\");\n        i0.ɵɵtext(192, \"@mdo\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(193, \"tr\");\n        i0.ɵɵelementStart(194, \"td\");\n        i0.ɵɵtext(195, \"2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(196, \"td\");\n        i0.ɵɵtext(197, \"Jacob\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(198, \"td\");\n        i0.ɵɵtext(199, \"Thornton\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(200, \"td\");\n        i0.ɵɵtext(201, \"@fat\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(202, \"tr\");\n        i0.ɵɵelementStart(203, \"td\");\n        i0.ɵɵtext(204, \"3\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(205, \"td\");\n        i0.ɵɵtext(206, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(207, \"td\");\n        i0.ɵɵtext(208, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(209, \"td\");\n        i0.ɵɵtext(210, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(211, \"div\", 1);\n        i0.ɵɵelementStart(212, \"app-card\", 14);\n        i0.ɵɵelementStart(213, \"div\", 3);\n        i0.ɵɵelementStart(214, \"table\", 4);\n        i0.ɵɵelementStart(215, \"thead\");\n        i0.ɵɵelementStart(216, \"tr\");\n        i0.ɵɵelementStart(217, \"th\");\n        i0.ɵɵtext(218, \"#\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(219, \"th\");\n        i0.ɵɵtext(220, \"First Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(221, \"th\");\n        i0.ɵɵtext(222, \"Last Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(223, \"th\");\n        i0.ɵɵtext(224, \"Username\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(225, \"tbody\");\n        i0.ɵɵelementStart(226, \"tr\", 15);\n        i0.ɵɵelementStart(227, \"td\");\n        i0.ɵɵtext(228, \"1\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(229, \"td\");\n        i0.ɵɵtext(230, \"Mark\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(231, \"td\");\n        i0.ɵɵtext(232, \"Otto\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(233, \"td\");\n        i0.ɵɵtext(234, \"@mdo\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(235, \"tr\");\n        i0.ɵɵelementStart(236, \"td\");\n        i0.ɵɵtext(237, \"2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(238, \"td\");\n        i0.ɵɵtext(239, \"Jacob\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(240, \"td\");\n        i0.ɵɵtext(241, \"Thornton\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(242, \"td\");\n        i0.ɵɵtext(243, \"@fat\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(244, \"tr\", 16);\n        i0.ɵɵelementStart(245, \"td\");\n        i0.ɵɵtext(246, \"3\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(247, \"td\");\n        i0.ɵɵtext(248, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(249, \"td\");\n        i0.ɵɵtext(250, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(251, \"td\");\n        i0.ɵɵtext(252, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(253, \"tr\");\n        i0.ɵɵelementStart(254, \"td\");\n        i0.ɵɵtext(255, \"4\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(256, \"td\");\n        i0.ɵɵtext(257, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(258, \"td\");\n        i0.ɵɵtext(259, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(260, \"td\");\n        i0.ɵɵtext(261, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(262, \"tr\", 17);\n        i0.ɵɵelementStart(263, \"td\");\n        i0.ɵɵtext(264, \"5\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(265, \"td\");\n        i0.ɵɵtext(266, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(267, \"td\");\n        i0.ɵɵtext(268, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(269, \"td\");\n        i0.ɵɵtext(270, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(271, \"tr\");\n        i0.ɵɵelementStart(272, \"td\");\n        i0.ɵɵtext(273, \"6\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(274, \"td\");\n        i0.ɵɵtext(275, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(276, \"td\");\n        i0.ɵɵtext(277, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(278, \"td\");\n        i0.ɵɵtext(279, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(280, \"tr\", 18);\n        i0.ɵɵelementStart(281, \"td\");\n        i0.ɵɵtext(282, \"7\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(283, \"td\");\n        i0.ɵɵtext(284, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(285, \"td\");\n        i0.ɵɵtext(286, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(287, \"td\");\n        i0.ɵɵtext(288, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(289, \"tr\");\n        i0.ɵɵelementStart(290, \"td\");\n        i0.ɵɵtext(291, \"8\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(292, \"td\");\n        i0.ɵɵtext(293, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(294, \"td\");\n        i0.ɵɵtext(295, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(296, \"td\");\n        i0.ɵɵtext(297, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(298, \"tr\", 19);\n        i0.ɵɵelementStart(299, \"td\");\n        i0.ɵɵtext(300, \"9\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(301, \"td\");\n        i0.ɵɵtext(302, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(303, \"td\");\n        i0.ɵɵtext(304, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(305, \"td\");\n        i0.ɵɵtext(306, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(307, \"div\", 1);\n        i0.ɵɵelementStart(308, \"app-card\", 20);\n        i0.ɵɵelementStart(309, \"div\", 3);\n        i0.ɵɵelementStart(310, \"table\", 10);\n        i0.ɵɵelementStart(311, \"thead\");\n        i0.ɵɵelementStart(312, \"tr\");\n        i0.ɵɵelementStart(313, \"th\");\n        i0.ɵɵtext(314, \"#\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(315, \"th\");\n        i0.ɵɵtext(316, \"First Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(317, \"th\");\n        i0.ɵɵtext(318, \"Last Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(319, \"th\");\n        i0.ɵɵtext(320, \"Username\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(321, \"tbody\");\n        i0.ɵɵelementStart(322, \"tr\", 21);\n        i0.ɵɵelementStart(323, \"td\");\n        i0.ɵɵtext(324, \"1\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(325, \"td\");\n        i0.ɵɵtext(326, \"Mark\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(327, \"td\");\n        i0.ɵɵtext(328, \"Otto\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(329, \"td\");\n        i0.ɵɵtext(330, \"@mdo\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(331, \"tr\");\n        i0.ɵɵelementStart(332, \"td\");\n        i0.ɵɵtext(333, \"2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(334, \"td\");\n        i0.ɵɵtext(335, \"Jacob\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(336, \"td\");\n        i0.ɵɵtext(337, \"Thornton\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(338, \"td\");\n        i0.ɵɵtext(339, \"@fat\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(340, \"tr\", 22);\n        i0.ɵɵelementStart(341, \"td\");\n        i0.ɵɵtext(342, \"3\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(343, \"td\");\n        i0.ɵɵtext(344, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(345, \"td\");\n        i0.ɵɵtext(346, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(347, \"td\");\n        i0.ɵɵtext(348, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(349, \"tr\");\n        i0.ɵɵelementStart(350, \"td\");\n        i0.ɵɵtext(351, \"4\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(352, \"td\");\n        i0.ɵɵtext(353, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(354, \"td\");\n        i0.ɵɵtext(355, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(356, \"td\");\n        i0.ɵɵtext(357, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(358, \"tr\", 23);\n        i0.ɵɵelementStart(359, \"td\");\n        i0.ɵɵtext(360, \"5\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(361, \"td\");\n        i0.ɵɵtext(362, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(363, \"td\");\n        i0.ɵɵtext(364, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(365, \"td\");\n        i0.ɵɵtext(366, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(367, \"tr\");\n        i0.ɵɵelementStart(368, \"td\");\n        i0.ɵɵtext(369, \"6\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(370, \"td\");\n        i0.ɵɵtext(371, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(372, \"td\");\n        i0.ɵɵtext(373, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(374, \"td\");\n        i0.ɵɵtext(375, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(376, \"tr\", 24);\n        i0.ɵɵelementStart(377, \"td\");\n        i0.ɵɵtext(378, \"7\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(379, \"td\");\n        i0.ɵɵtext(380, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(381, \"td\");\n        i0.ɵɵtext(382, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(383, \"td\");\n        i0.ɵɵtext(384, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(385, \"tr\");\n        i0.ɵɵelementStart(386, \"td\");\n        i0.ɵɵtext(387, \"8\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(388, \"td\");\n        i0.ɵɵtext(389, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(390, \"td\");\n        i0.ɵɵtext(391, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(392, \"td\");\n        i0.ɵɵtext(393, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(394, \"tr\", 25);\n        i0.ɵɵelementStart(395, \"td\");\n        i0.ɵɵtext(396, \"9\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(397, \"td\");\n        i0.ɵɵtext(398, \"Larry\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(399, \"td\");\n        i0.ɵɵtext(400, \"the Bird\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(401, \"td\");\n        i0.ɵɵtext(402, \"@twitter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementEnd();\n      }\n    },\n    directives: [i1.CardComponent],\n    styles: [\"\"]\n  });\n  return TblBasicComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module"}