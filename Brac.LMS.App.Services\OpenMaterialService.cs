﻿using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using ClosedXML.Excel;
using DocumentFormat.OpenXml.Presentation;
using FastReport.Data;
using iTextSharp.text.pdf;
using iTextSharp.text;
using Newtonsoft.Json;
using Org.BouncyCastle.Utilities.Collections;
using SendEmailApp;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.Entity.Validation;
using System.IO;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;
using System.Web.Hosting;
using WMPLib;

namespace Brac.LMS.App.Services
{
    public class OpenMaterialService : IOpenMaterialService
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditLogHelper _auditLogHelper;
        private readonly AuditLog audit;
        private readonly ApplicationUser _appUser;
        public OpenMaterialService()
        {
            _context = new ApplicationDbContext();
        }

        public OpenMaterialService(string controller, string method, ApplicationUser applicationUser)
        {
            _auditLogHelper = new AuditLogHelper(controller, method);
            audit = _auditLogHelper.auditLog;
            _appUser = applicationUser;
            audit.CreatedBy = _appUser.Id;
            _context = new ApplicationDbContext();
        }
        public async Task<APIResponse> MaterialCreateOrUpdate(OpenMaterialModel model, IIdentity identity)
        {
            OpenMaterial item = null;

            bool isEdit = true;
            try
            {
                if (await _context.OpenMaterials.AnyAsync(x => x.Id != model.Id && x.Title == model.Title && x.MaterialType == model.MaterialType))
                    throw new Exception("Already exists in this course: " + model.Title);

                if (model.Id != Guid.Empty)
                {
                    item = await _context.OpenMaterials.FindAsync(model.Id);
                    if (item == null) throw new Exception("Material not found");
                }
                else
                {
                    item = new OpenMaterial();
                    isEdit = false;

                    if (!HttpContext.Current.Request.Files.AllKeys.Contains("Image"))
                        return new APIResponse
                        {
                            Status = ResponseStatus.Warning,
                            Message = "No cover image found"
                        };

                    if (!HttpContext.Current.Request.Files.AllKeys.Contains("File"))
                        return new APIResponse
                        {
                            Status = ResponseStatus.Warning,
                            Message = "No file found"
                        };
                }




                foreach (string key in HttpContext.Current.Request.Files.Keys)
                {
                    switch (key)
                    {
                        case "Image":
                            item.ImagePath = Utility.SaveImage(DateTime.Today.ToString("yyMMddHms") + "_" + Utility.RandomString(3, false), "/Files/OpenMaterial/Images/", HttpContext.Current.Request.Files.Get(key), item.ImagePath, 400, 210);
                            break;
                        case "File":
                            switch (model.MaterialType)
                            {
                                case MaterialType.Document:
                                    item = SaveDocumentFile("DOC" + Utility.RandomString(4, false) + DateTime.Now.ToString("yyyMMddHHmmss"), "/Files/OpenMaterial/Documents/", HttpContext.Current.Request.Files.Get(key), item);
                                    break;
                                case MaterialType.Video:
                                    item = SaveVideoFile("VID" + Utility.RandomString(4, false) + DateTime.Now.ToString("yyyMMddHHmmss"), "/Files/OpenMaterial/Videos/", HttpContext.Current.Request.Files.Get(key), item);
                                    break;
                            }
                            break;
                    }
                }

                item.MaterialType = model.MaterialType;
                item.Title = model.Title;
                item.CategoryId = model.CategoryId;
                item.AllowFor = model.AllowFor;
                item.DivisionId = model.DivisionId;
                item.DepartmentId = model.DepartmentId;
                item.UnitId = model.UnitId;
                item.Active = model.Active;
                item.Order = model.Order;
                if (item.Trainees == null) item.Trainees = new List<Trainee>();
                item.Trainees.Clear();

                if (model.Trainees.Any())
                    item.Trainees = await _context.Trainees.Where(x => model.Trainees.Contains(x.Id)).ToListAsync();

                item.SetAuditTrailEntity(identity);
                if (!isEdit)
                {
                    item.Id = Guid.NewGuid();
                    _context.OpenMaterials.Add(item);
                }
                else
                {
                    _context.Entry(item).State = EntityState.Modified;
                }

                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                };

            }
            catch (DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())
                };
            }
            catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse { Status = ResponseStatus.Error, Message = ex.Message };
            }
        }

        private OpenMaterial SaveVideoFile(string fileName, string partialPath, HttpPostedFile hpf, OpenMaterial item)
        {
            try
            {
                string serverPath = HostingEnvironment.MapPath("~"), extension, filePath;

                if (!Directory.Exists(serverPath + partialPath)) Directory.CreateDirectory(serverPath + partialPath);

                if (!string.IsNullOrEmpty(item.FilePath) && File.Exists(serverPath + item.FilePath))
                {
                    File.Delete(serverPath + item.FilePath);
                }

                string[] mediaExtensions = { ".AVI", ".MP4", ".DIVX", ".WMV" };

                extension = Path.GetExtension(hpf.FileName);
                if (mediaExtensions.Contains(extension.ToUpper()))
                {
                    if (hpf.ContentLength > 524288000)
                    {
                        throw new Exception("File size exceeded. Max file size is 500MB. Your selected file size is " + hpf.ContentLength / (1024 * 1024) + ".");
                    }
                    filePath = partialPath + fileName + extension;

                    if (File.Exists(serverPath + filePath))
                        File.Delete(serverPath + filePath);

                    hpf.SaveAs(serverPath + filePath);

                    WindowsMediaPlayer wmp = new WindowsMediaPlayer();
                    IWMPMedia mediainfo = wmp.newMedia(serverPath + filePath);

                    //var info = new FFmpegMediaInfo(serverPath + filePath, Generator.FFMPEG_EXE_PATH, Generator.FFPROBE_EXE_PATH);

                    item.FileSizeKb = (long)(hpf.ContentLength / 1024);
                    item.VideoDurationSecond = (int)mediainfo.duration;
                    item.FileType = ServiceHelper.GetFileType(serverPath + filePath);

                    item.FilePath = filePath;
                    return item;
                }
                else throw new Exception("Only AVI, MP4, DIVX or WMV video files are allowed to upload.");
            }
            catch (Exception ex)
            {
                throw new Exception("SaveVideoFile Error: " + ex.Message);
            }
        }

        private OpenMaterial SaveDocumentFile(string fileName, string partialPath, HttpPostedFile hpf, OpenMaterial item)
        {
            try
            {
                string serverPath = HostingEnvironment.MapPath("~"), extension, filePath;

                if (!Directory.Exists(serverPath + partialPath)) Directory.CreateDirectory(serverPath + partialPath);

                if (!string.IsNullOrEmpty(item.FilePath) && File.Exists(serverPath + item.FilePath))
                {
                    File.Delete(serverPath + item.FilePath);
                }

                string[] fileExtensions = { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".jpg", ".jpeg", ".png" };

                extension = Path.GetExtension(hpf.FileName);
                if (fileExtensions.Contains(extension.ToLower()))
                {
                    if (hpf.ContentLength > 102400000)
                    {
                        throw new Exception("File size exceeded. Max file size is 100MB. Your selected file size is " + hpf.ContentLength / 1000 + ".");
                    }
                    filePath = partialPath + fileName + extension;

                    if (File.Exists(serverPath + filePath))
                        File.Delete(serverPath + filePath);

                    hpf.SaveAs(serverPath + filePath);
                    item.FilePath = filePath;
                    item.FileSizeKb = (long)(hpf.ContentLength / 1024);
                    item.FileType = ServiceHelper.GetFileType(serverPath + filePath);

                    return item;
                }
                else throw new Exception("Only PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, JPG, JPEG or PNG files are allowed to upload.");
            }
            catch (Exception ex)
            {
                throw new Exception("SaveDocumentFile Error: " + ex.Message);
            }
        }


        public async Task<APIResponse> GetMaterialList(string name, long? categoryId, int size, int pageNumber)
        {
            try
            {
                var query = _context.OpenMaterials.AsQueryable();
                if (!string.IsNullOrEmpty(name)) query = query.Where(x => x.Title.Contains(name) || x.MaterialType.ToString().Contains(name));
                if (categoryId.HasValue) query = query.Where(x => x.CategoryId == categoryId);
                var filteredQuery = query.Count() <= pageNumber * size ? query : query.OrderByDescending(x => x.Active).ThenBy(x => x.Order).ThenByDescending(x => new { x.MaterialType, x.Title })
                .Skip(pageNumber * size).Take(size);
                var data = await filteredQuery.Select(x => new
                {
                    x.Id,
                    x.Title,
                    x.FilePath,
                    x.FileSizeKb,
                    x.ImagePath,
                    Category = x.Category.Name,
                    AllowFor = x.AllowFor.ToString(),
                    MaterialType = x.MaterialType.ToString(),
                    Division = x.Division.Name,
                    Department = x.Department.Name,
                    Unit = x.Unit.Name,
                    Trainees = x.Trainees.Count(),
                    x.Active,
                    x.Order
                }).ToListAsync();


                var count = await ((((!string.IsNullOrEmpty(name)) || (categoryId.HasValue)) || ((!string.IsNullOrEmpty(name)) && (categoryId.HasValue))) ? filteredQuery.CountAsync() : _context.OpenMaterials.CountAsync());
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new { Records = data, Total = count }
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> GetMaterialById(Guid id)
        {
            try
            {
                var query = _context.OpenMaterials.Where(x => x.Id == id).AsQueryable();
                var data = await query
                            .Select(x => new
                            {
                                x.Id,
                                x.Title,
                                MaterialType = x.MaterialType.ToString(),
                                x.CreatedDate,
                                x.FilePath,
                                x.S3Path,
                                x.ImagePath,
                                x.CategoryId,
                                x.DepartmentId,
                                x.DivisionId,
                                x.UnitId,
                                AllowFor = x.AllowFor.ToString(),
                                Trainees = x.Trainees.Select(t => new { t.Id, t.PIN, t.Name, t.Position }).ToList(),
                                x.Active,
                                x.Order
                            })
                            .FirstOrDefaultAsync();
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> DeleteMaterialById(Guid id)
        {
            try
            {
                var item = await _context.OpenMaterials.FindAsync(id);
                if (item == null)
                    return new APIResponse
                    {
                        Status = ResponseStatus.Warning,
                        Message = "This material not found"
                    };

                if (item.Trainees != null) item.Trainees.Clear();
                _context.Entry(item).State = EntityState.Deleted;

                await _context.SaveChangesAsync();

                if (File.Exists(HostingEnvironment.MapPath("~") + item.FilePath))
                    File.Delete(HostingEnvironment.MapPath("~") + item.FilePath);

                if (File.Exists(HostingEnvironment.MapPath("~") + item.ImagePath))
                    File.Delete(HostingEnvironment.MapPath("~") + item.ImagePath);

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully Deleted!"
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }
        }

        public async Task<APIResponse> SaveEvaluationActivity(string title, string contentType, ApplicationUser user)
        {
            bool isEdit=false;
            try
            {
                TraineeEvaluationActivity evaluationActivity = _context.TraineeEvaluationActivities.Where(x => x.Title == title && x.TraineeId==user.Trainee.Id).FirstOrDefault();
                List<OpenMaterial> openMaterial = await _context.OpenMaterials.Where(x => x.Title.Equals(title) && x.Active == true).ToListAsync();
                EvaluationExam evaluationExam = _context.EvaluationExams.Where(x => x.ExamName.Equals(title) && x.Active == true).FirstOrDefault();
                int totalContents = openMaterial.Count() + (evaluationExam != null ? 1 : 0);
                if (evaluationActivity == null)
                {
                    TraineeEvaluationActivity item = new TraineeEvaluationActivity()
                    {
                        Title = title,
                        NoOfContents = totalContents,
                        NoOfContentsCompleted = 1,
                        Progress = (int)Math.Round((double)(100 * 1) / totalContents),
                        VideoCompleted = contentType == "Video" ? true : false,
                        DocumentCompleted = contentType == "Doc" ? true : false,
                        ExamCompleted = contentType == "Exam" ? true : false,
                        FirstStudyDate = DateTime.Now.ToUniversalTime(),
                        LastStudyDate = DateTime.Now.ToUniversalTime(),
                        ExamCompletedDate = DateTime.Now.ToUniversalTime(),
                        TraineeId = user.Trainee.Id
                    };
                    item.SetAuditTrailEntity(user.User.Identity);
                    _context.TraineeEvaluationActivities.Add(item);
                }
                else
                {
                    isEdit = true;
                    int count = 0;
                    if ((evaluationActivity.VideoCompleted == false) && contentType == "Video")
                        count++;
                    if ((evaluationActivity.DocumentCompleted == false) && contentType == "Doc")
                        count++;
                    if ((evaluationActivity.ExamCompleted == false) && contentType == "Exam")
                        count++;
                    evaluationActivity.NoOfContents = totalContents;
                    evaluationActivity.NoOfContentsCompleted = evaluationActivity.NoOfContentsCompleted + count;
                    if (contentType == "Video")
                    {
                        evaluationActivity.LastStudyDate = DateTime.UtcNow;
                        evaluationActivity.VideoCompleted = true;
                    }
                    if (contentType == "Doc") 
                    { 
                        evaluationActivity.LastStudyDate = DateTime.UtcNow;
                        evaluationActivity.DocumentCompleted = true;
                    }
                    if (contentType == "Exam")
                    {
                        evaluationActivity.ExamCompleted = true;
                        evaluationActivity.ExamCompletedDate = DateTime.UtcNow;
                    }
                    evaluationActivity.Progress = (int)Math.Round((double)(100 * evaluationActivity.NoOfContentsCompleted) / totalContents);
                    _context.Entry(evaluationActivity).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                };
            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.Message
                };
            }

        }

        public async Task<byte[]> GetTimeWiseEvaluationStudyReportExcel(DateTime startDate, DateTime endDate)
        {
            IXLWorkbook wb = new XLWorkbook();
            IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
            int rowNo = 0, colNo;

            try
            {
                var evaluationActivities = await _context.TraineeEvaluationActivities
                    .Where(x => x.ExamCompleted == true
                        && DbFunctions.TruncateTime(x.ExamCompletedDate) >= DbFunctions.TruncateTime(startDate)
                        && DbFunctions.TruncateTime(x.ExamCompletedDate) <= DbFunctions.TruncateTime(endDate))
                    .ToListAsync();

                var company = await _context.Configurations.FirstOrDefaultAsync();
                var traineeIds = evaluationActivities.Select(x => x.TraineeId).ToList();
                var evaluationTitles = evaluationActivities.Select(x => x.Title).ToList();
                var trainees = await _context.Trainees.Where(x => traineeIds.Contains(x.Id)).ToListAsync();
                var evaluations = await _context.OpenMaterials.Where(x => evaluationTitles.Contains(x.Title)).ToListAsync();
                var traineeExams = await _context.TraineeEvaluationExams.Where(x => evaluationTitles.Contains(x.Exam.ExamName) && traineeIds.Contains(x.TraineeId))
                    .Select(x => new { x.TraineeId, x.Exam.ExamName, x.GainedMarks, x.ExamId, x.MarkedOn, x.TotalMarks, x.Grade, x.Result, x.GainedPercentage })
                    .OrderByDescending(x => x.GainedMarks).ToListAsync();

                var headerColumns = new List<string> { "PIN", "Name", "Title", "Study Start From", "Last Study Date", "Pass Date", "Gained Marks", "Total Marks", "Score %", "Grade", "Result" };

                ExcelManager.GetTextLineElement(company?.Name, ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                ExcelManager.GetTextLineElement(company?.Address + " , " + company?.ContactNo, ++rowNo, ws, fontSize: 8, isBold: false, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

                rowNo++;
                ExcelManager.GetTextLineElement("Time Wise Learning Hour's Study Report", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                rowNo++;
                colNo = 1;

                if (evaluationActivities.Count > 0)
                {
                    ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);

                    foreach (var item in evaluationActivities)
                    {
                        rowNo++;
                        colNo = 1;
                        var trainee = trainees.FirstOrDefault(x => x.Id == item.TraineeId);
                        var evaluation = evaluations.FirstOrDefault(x => x.Title == item.Title);
                        var evaluationExam = _context.EvaluationExams.FirstOrDefault(x => x.ExamName == item.Title);
                        var traineeExam = traineeExams.FirstOrDefault(x => x.TraineeId == trainee?.Id && x.ExamName == item.Title);
                        string title = evaluation?.Title ?? evaluationExam?.ExamName ?? string.Empty;
                        var traineeEvaluationActivity = evaluationActivities.FirstOrDefault(x => x.TraineeId == trainee?.Id && x.Title == item.Title);

                        ExcelManager.GetTableDataCell(trainee?.PIN, 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(trainee?.Name, 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(title, 10, rowNo, colNo++, ws);
                        ExcelManager.GetTableDataCell(traineeEvaluationActivity?.FirstStudyDate?.ToLocalTime().ToString("dd-MMM-yyyy") ?? "", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeEvaluationActivity?.LastStudyDate?.ToLocalTime().ToString("dd-MMM-yyyy") ?? "", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeExam?.MarkedOn?.ToLocalTime().ToString("dd-MMM-yyyy") ?? "", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
                        ExcelManager.GetTableDataCell(traineeExam?.GainedMarks ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeExam?.TotalMarks ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeExam?.GainedPercentage ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(traineeExam?.Grade ?? "", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                        ExcelManager.GetTableDataCell(
                            traineeExam?.Result != null && Enum.IsDefined(typeof(GradeResult), traineeExam.Result)
                                ? Enum.Parse(typeof(GradeResult), traineeExam.Result.ToString()).ToString()
                                : "N/A",
                            10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
                    }

                    for (int i = 0; i < headerColumns.Count; i++)
                    {
                        ws.Column(i + 1).AdjustToContents();
                    }
                }
                else
                {
                    ExcelManager.GetTextLineElement("No record found", ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
                }

                // Closing the Document
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
            catch (Exception ex)
            {
                LogControl.LogException(ex);

                ExcelManager.GetTextLineElement("Error: " + ex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
                using (var ms = new MemoryStream())
                {
                    wb.SaveAs(ms);
                    return ms.ToArray();
                }
            }
        }

        //public async Task<byte[]> GetTimeWiseEvaluationStudyReportExcel(DateTime startDate, DateTime endDate)
        //{
        //    IXLWorkbook wb = new XLWorkbook();
        //    IXLWorksheet ws = wb.Worksheets.Add("Sheet1");
        //    int rowNo = 0, colNo;

        //    try
        //    {
        //        var evaluationActivities = await _context.TraineeEvaluationActivities
        //            .Where(x => x.ExamCompleted == true
        //                && DbFunctions.TruncateTime(x.ExamCompletedDate) >= DbFunctions.TruncateTime(startDate)
        //                && DbFunctions.TruncateTime(x.ExamCompletedDate) <= DbFunctions.TruncateTime(endDate))
        //            .ToListAsync();
        //        var company = await _context.Configurations.FirstOrDefaultAsync();
        //        var traineeIds = evaluationActivities.Select(x => x.TraineeId).ToList();
        //        var evaluationTitles = evaluationActivities.Select(x => x.Title).ToList();
        //        var trainees = await _context.Trainees.Where(x => traineeIds.Contains(x.Id)).ToListAsync();
        //        var evlaution = await _context.OpenMaterials.Where(x => evaluationTitles.Contains(x.Title)).ToListAsync();
        //        //var certificateExams = await _context.CourseExams.Where(x => x.ExamType == ExamType.CertificationTest && x.CourseId == courseId).Select(x => new { x.Id, x.ExamName }).ToListAsync();
        //        var traineeExams = await _context.TraineeEvaluationExams.Where(x => evaluationTitles.Contains(x.Exam.ExamName) && traineeIds.Contains(x.TraineeId))
        //           .Select(x => new { x.TraineeId, x.Exam.ExamName, x.GainedMarks, x.ExamId, x.MarkedOn, x.TotalMarks, x.Grade, x.Result, x.GainedPercentage }).OrderByDescending(x => x.GainedMarks).ToListAsync();
        //        var headerColumns = new List<string> { "PIN", "Name","Title",  "Study Start From", "Last Study Date", "Pass Date", "Gained Marks", "Total Marks", "Score %", "Grade", "Result" };


        //        ExcelManager.GetTextLineElement(company.Name, ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

        //        ExcelManager.GetTextLineElement(company.Address + " , " + company.ContactNo, ++rowNo, ws, fontSize: 8, isBold: false, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);

        //        rowNo++;

        //        ExcelManager.GetTextLineElement("Time Wise Learning Hour's Study Report", ++rowNo, ws, fontSize: 14, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
        //        //ExcelManager.GetTextLineElement("Trainee: " + trainee, ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
        //        rowNo++;
        //        colNo = 1;
        //        if (evaluationActivities.Count > 0)
        //        {
        //            ExcelManager.GetTableHeaderCell(headerColumns, ++rowNo, ws, alignment: XLAlignmentHorizontalValues.Center);

        //            foreach (var item in evaluationActivities)
        //            {
        //                rowNo++;
        //                colNo = 1;
        //                var trainee = trainees.FirstOrDefault(x => x.Id == item.TraineeId);
        //                var evluation = _context.OpenMaterials.FirstOrDefault(x => x.Title == item.Title);
        //                var evluationExam = _context.EvaluationExams.FirstOrDefault(x => x.ExamName == item.Title);
        //                var traineeExam = traineeExams.FirstOrDefault(x => x.TraineeId == trainee.Id && x.ExamName == item.Title);
        //                string Title = string.Empty;
        //                if (evluation != null)
        //                {
        //                    Title = evluation.Title;
        //                }
        //                else if (evluationExam != null)
        //                {
        //                    Title = evluationExam.ExamName;
        //                }
        //                var TraineeEvaluationActivity = evaluationActivities.FirstOrDefault(x => x.TraineeId == trainee.Id && x.Title == item.Title);
        //                ExcelManager.GetTableDataCell(trainee?.PIN, 10, rowNo, colNo++, ws);
        //                ExcelManager.GetTableDataCell(trainee?.Name, 10, rowNo, colNo++, ws);
        //                ExcelManager.GetTableDataCell(Title, 10, rowNo, colNo++, ws);
        //                ExcelManager.GetTableDataCell(TraineeEvaluationActivity?.FirstStudyDate?.AddHours(6), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
        //                ExcelManager.GetTableDataCell(TraineeEvaluationActivity?.LastStudyDate?.AddHours(6), 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
        //                ExcelManager.GetTableDataCell(traineeExam?.MarkedOn?.ToString("dd-MMM-yyyy") ?? "", 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Center);
        //                ExcelManager.GetTableDataCell(traineeExam?.GainedMarks ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
        //                ExcelManager.GetTableDataCell(traineeExam?.TotalMarks ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
        //                ExcelManager.GetTableDataCell(traineeExam?.GainedPercentage ?? 0, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
        //                ExcelManager.GetTableDataCell(traineeExam?.Grade, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);
        //                ExcelManager.GetTableDataCell(traineeExam?.Result, 10, rowNo, colNo++, ws, XLAlignmentHorizontalValues.Right);

        //            }

        //            for (int i = 0; i < headerColumns.Count; i++)
        //            {
        //                ws.Column(i + 1).AdjustToContents();
        //            }
        //        }
        //        else
        //        {
        //            ExcelManager.GetTextLineElement("No record found", ++rowNo, ws, fontSize: 10, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: headerColumns.Count);
        //        }

        //        // Closing the Document
        //        using (var ms = new MemoryStream())
        //        {
        //            wb.SaveAs(ms);

        //            return ms.ToArray();
        //        }
        //    }
        //    catch (Exception ibex)
        //    {
        //        LogControl.LogException(ibex);

        //        ExcelManager.GetTextLineElement("Error: " + ibex.Message, ++rowNo, ws, fontSize: 18, isBold: true, alignment: XLAlignmentHorizontalValues.Center, colspan: 6);
        //        using (var ms = new MemoryStream())
        //        {
        //            wb.SaveAs(ms);
        //            return ms.ToArray();
        //        }
        //    }
        //}

        public async Task<byte[]> GetTimeWiseEvaluationStudyReportPdf(DateTime startDate, DateTime endDate)
        {
            var document = new iTextSharp.text.Document(iTextSharp.text.PageSize.A4, 36, 36, 100, 36);
            document.SetPageSize(iTextSharp.text.PageSize.A4.Rotate());
            var reportStream = new MemoryStream();

            try
            {
                PdfWriter writer = PdfWriter.GetInstance(document, reportStream);
                writer.PageEvent = new PDFWriterEvents(true, true);
                writer.CloseStream = false;

                // Openning the Document
                document.Open();
                var evaluationActivities = await _context.TraineeEvaluationActivities
                    .Where(x => x.ExamCompleted == true
                        && DbFunctions.TruncateTime(x.ExamCompletedDate) >= DbFunctions.TruncateTime(startDate)
                        && DbFunctions.TruncateTime(x.ExamCompletedDate) <= DbFunctions.TruncateTime(endDate))
                    .ToListAsync();
                var company = await _context.Configurations.FirstOrDefaultAsync();
                var traineeIds = evaluationActivities.Select(x => x.TraineeId).ToList();
                var evaluationTitles = evaluationActivities.Select(x => x.Title).ToList();
                var trainees = await _context.Trainees.Where(x => traineeIds.Contains(x.Id)).ToListAsync();
                //var evlaution = await _context.OpenMaterials.Where(x => evaluationTitles.Contains(x.Title)).ToListAsync();
                //var certificateExams = await _context.CourseExams.Where(x => x.ExamType == ExamType.CertificationTest && x.CourseId == courseId).Select(x => new { x.Id, x.ExamName }).ToListAsync();
                var traineeExams = await _context.TraineeEvaluationExams.Where(x => evaluationTitles.Contains(x.Exam.ExamName) && traineeIds.Contains(x.TraineeId))
                   .Select(x => new { x.TraineeId, x.Exam.ExamName, x.GainedMarks, x.ExamId, x.MarkedOn, x.TotalMarks, x.Grade, x.Result, x.GainedPercentage }).OrderByDescending(x => x.GainedMarks).ToListAsync();
                #region Page Header
                PdfPTable header = new PdfPTable(1);
                header.TotalWidth = 180;
                header.DefaultCell.Border = 0;
                header.AddCell(PDFManager.GetTextLineElement(company.Name, 12f, isBold: true));
                header.AddCell(PDFManager.GetTextLineElement(company.Address + " , " + company.ContactNo, isBold: false, fontSize: 8, leftToRightPadding: new float[] { 4, 2, 4, 2 }));
                header.WriteSelectedRows(0, 3, writer.PageSize.Width - document.LeftMargin - document.RightMargin - 180, writer.PageSize.GetTop(10), writer.DirectContent);
                if (!string.IsNullOrEmpty(company.LogoPath))
                {
                    Image img = Image.GetInstance(HostingEnvironment.MapPath("~") + company.LogoPath);
                    img.SetAbsolutePosition(document.LeftMargin, writer.PageSize.GetTop(70));
                    img.ScaleAbsolute(200f, 60f);
                    document.Add(img);
                }
                PdfPTable line = new PdfPTable(1);
                line.TotalWidth = writer.PageSize.Width;
                line.DefaultCell.Border = 0;
                line.AddCell(new Paragraph(new Chunk(new iTextSharp.text.pdf.draw.LineSeparator(0.0F, 100.0F, BaseColor.BLACK, Element.ALIGN_CENTER, 3))));
                line.WriteSelectedRows(0, 1, 0, writer.PageSize.GetTop(header.TotalHeight + 10), writer.DirectContent);
                #endregion
                document.Add(PDFManager.GetTextLineElement("Time Wise Learning Hour's Study Report", fontSize: 16, isBold: true, isUnderlined: false, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                #region Table
                var table = new PdfPTable(11) { WidthPercentage = 100 };
                table.SetWidths(new float[] { 10, 15, 15, 15, 15, 10, 10, 10, 10, 10, 10 });
                table.AddCell(PDFManager.GetTableHeaderCell("PIN", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Trainee Name", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Course Name", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("First Study Date", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Last Study Date", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Pass Date", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Gained Marks", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Total Marks", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Score %", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Grade", 8f, true, false, PDFAlignment.Center));
                table.AddCell(PDFManager.GetTableHeaderCell("Result", 8f, true, false, PDFAlignment.Center));
                if (evaluationActivities.Count > 0)
                {
                    foreach (var item in evaluationActivities)
                    {
                        var trainee = trainees.FirstOrDefault(x => x.Id == item.TraineeId);
                        var evluation = _context.OpenMaterials.FirstOrDefault(x => x.Title == item.Title);
                        var evluationExam = _context.EvaluationExams.FirstOrDefault(x => x.ExamName == item.Title);
                        var traineeExam = traineeExams.FirstOrDefault(x => x.TraineeId == trainee.Id && x.ExamName == item.Title);
                        string Title = string.Empty;
                        if (evluation != null)
                        {
                            Title=evluation.Title;
                        }
                        else if (evluationExam != null)
                        {
                            Title = evluationExam.ExamName;
                        }
                        //var traineeExamByTraineeIdList = traineeExams.Where(x => x.TraineeId == trainee.Id && x.CourseId == course.Id).Select(x => x).ToList();
                        //var traineeExam = traineeExamByTraineeIdList.Where(x => x.GainedMarks == traineeExamByTraineeIdList.Max(i => i.GainedMarks)).FirstOrDefault();
                        var TraineeEvaluationActivity = evaluationActivities.FirstOrDefault(x => x.TraineeId == trainee.Id && x.Title == item.Title);
                        table.AddCell(PDFManager.GetTableDataCell(trainee?.PIN, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(trainee?.Name, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(Title, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(TraineeEvaluationActivity?.FirstStudyDate?.ToLocalTime(), PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(TraineeEvaluationActivity?.LastStudyDate?.ToLocalTime(), PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(traineeExam?.MarkedOn?.ToLocalTime().ToString("dd-MMM-yyyy") ?? "", PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(traineeExam?.GainedMarks ?? 0, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(traineeExam?.TotalMarks ?? 0, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(traineeExam?.GainedPercentage ?? 0, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(traineeExam?.Grade, PDFAlignment.Center, false, true, 8));
                        table.AddCell(PDFManager.GetTableDataCell(traineeExam?.Result != null && Enum.IsDefined(typeof(GradeResult), traineeExam.Result)
                                ? Enum.Parse(typeof(GradeResult), traineeExam.Result.ToString()).ToString()
                                : "N/A", PDFAlignment.Center, false, true, 8));
                    }
                }
                document.Add(table);
                #endregion

                // Closing the Document
                document.Close();
                return reportStream.ToArray();

            }
            catch (DocumentException dex)
            {


                document.Add(PDFManager.GetTextLineElement(dex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
            // Catching System.IO.IOException if any
            catch (Exception ioex)
            {


                document.Add(PDFManager.GetTextLineElement(ioex.Message, fontSize: 16, isBold: true, isUnderlined: true, alignment: PDFAlignment.Center, leftToRightPadding: new float[] { 0, 10, 0, 20 }));
                document.Close();
                return reportStream.ToArray();
            }
        }
    }

    public interface IOpenMaterialService
    {
        Task<APIResponse> GetMaterialList(string name, long? categoryId, int size, int pageNumber);
        Task<APIResponse> GetMaterialById(Guid id);
        Task<APIResponse> DeleteMaterialById(Guid id);
        Task<APIResponse> MaterialCreateOrUpdate(OpenMaterialModel model, IIdentity identity);
        Task<APIResponse> SaveEvaluationActivity(string title, string contentType, ApplicationUser user);
        Task<byte[]> GetTimeWiseEvaluationStudyReportExcel(DateTime startDate, DateTime endDate);
        Task<byte[]> GetTimeWiseEvaluationStudyReportPdf(DateTime startDate, DateTime endDate);
    }
}


