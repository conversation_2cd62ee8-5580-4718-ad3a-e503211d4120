{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { GuestUserEntryComponent } from './guest-user-entry.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: GuestUserEntryComponent\n}];\nexport let GuestUserEntryRoutingModule = /*#__PURE__*/(() => {\n  class GuestUserEntryRoutingModule {}\n\n  GuestUserEntryRoutingModule.ɵfac = function GuestUserEntryRoutingModule_Factory(t) {\n    return new (t || GuestUserEntryRoutingModule)();\n  };\n\n  GuestUserEntryRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: GuestUserEntryRoutingModule\n  });\n  GuestUserEntryRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [[RouterModule.forChild(routes)], RouterModule]\n  });\n  return GuestUserEntryRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module"}